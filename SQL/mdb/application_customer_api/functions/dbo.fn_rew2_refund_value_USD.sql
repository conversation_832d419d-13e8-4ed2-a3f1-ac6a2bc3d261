-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- W<PERSON><PERSON>	| 2011-12-09	| 
-- Sirilak			| 2012-01-17	| Change @config_redeem_point_per_dollar from int to decimal(19,2)
-- Chate C.			| 2013-07-10	| Add a logic to handle BNPL processed in the system.
--------------------------------|---------------|----------------------------------------------------------
/* TEST
	SELECT dbo.fn_rew2_refund_value_USD(22.71,13.32,20305,500) 
	--Before:	result = 30.61
	--After:	result = 31.22
*/
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_refund_value_USD]
(
	  @booking_value			DECIMAL(19,2)			
	 ,@credit_card_value			DECIMAL(19,2)				 
	 ,@booking_points_used			INT
	 ,@config_redeem_point_per_dollar	DECIMAL(19,2)
	 ,@is_BNPL_Pending_Payment		BIT 	
)
RETURNS DECIMAL(19,2)
AS
BEGIN
	DECLARE @refund_value DECIMAL(19,2) = 0
	
	SET @refund_value = (@credit_card_value + (@booking_points_used/@config_redeem_point_per_dollar)) - @booking_value
	
	IF @is_BNPL_Pending_Payment = 1 
		AND @refund_value < 0
	BEGIN	
		SET @refund_value = 0
	END	
	
	RETURN @refund_value
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_refund_value_USD] TO [customer_api_user]
GO
