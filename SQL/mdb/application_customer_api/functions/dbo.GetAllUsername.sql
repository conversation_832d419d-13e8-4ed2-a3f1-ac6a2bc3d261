-- =============================================
-- Author:		Saran B.
-- Create date: 23/02/2011
-- =============================================
CREATE FUNCTION [dbo].[GetAllUsername]
(
	@userId uniqueidentifier
)
RETURNS varchar(MAX)
AS
BEGIN
	declare @name varchar(max)=''

	select @name = @name+','+username
	from auth_user_mapping (nolock)
	where user_id = @userId

	return substring(@name,2,LEN(@name))
END



GO

