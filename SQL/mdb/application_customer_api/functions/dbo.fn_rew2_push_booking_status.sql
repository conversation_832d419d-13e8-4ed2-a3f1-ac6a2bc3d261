-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wichai Atchariyabunyong	| 2011-12-09	| 
--------------------------------|---------------|----------------------------------------------------------
/*
--select dbo.[fn_rew2_push_booking_status](5,'2011-12-15')
*/
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_push_booking_status]
(
	 @booking_status_id		int
	,@affected_date			datetime
)
RETURNS tinyint
AS
BEGIN
	RETURN CASE 
		WHEN (@booking_status_id in (1,2,3) or (@booking_status_id = 4 and CONVERT(date,GETDATE()) < CONVERT(date,@affected_date) ))	THEN 1	--PENDING
		WHEN (@booking_status_id = 4 and  CONVERT(date,GETDATE()) >= CONVERT(date,@affected_date))					THEN 2	--COMPLETE
		WHEN (@booking_status_id in (5,6,7,8) and  CONVERT(date,GETDATE()) < CONVERT(date,@affected_date))				THEN 3	--CXL PENDING
		WHEN (@booking_status_id in (5,6,7,8) and  CONVERT(date,GETDATE()) >= CONVERT(date,@affected_date))				THEN 4	--CXL COMPLETE
		ELSE 99 --ERROR
		END
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_push_booking_status] TO [customer_api_user]
GO
