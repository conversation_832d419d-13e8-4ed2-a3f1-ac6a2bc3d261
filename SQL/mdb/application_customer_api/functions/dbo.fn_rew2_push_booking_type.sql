-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wichai <PERSON>	| 2011-12-09	| 
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_push_booking_type]
(
	@booking_points_used			int
	,@booking_value				decimal(19,2)			
	,@credit_card_value			decimal(19,2)			


)
RETURNS tinyint
AS
BEGIN
/*
	RETURN CASE 
		WHEN  @booking_points_used = 0 AND @booking_value = @credit_card_value THEN 1	-- Normal Booking
		WHEN  @booking_points_used > 0 AND @booking_value <> @credit_card_value	THEN 2	-- Redeem Booking
		ELSE  99 --ERROR																
		END
*/		
	RETURN CASE 
		WHEN  @booking_points_used = 0 AND @booking_value = @credit_card_value  THEN 1	-- Normal Booking
		WHEN  @booking_points_used = 0 AND @booking_value <> @credit_card_value THEN 98	-- Error in Normal Booking
		WHEN  @booking_points_used > 0						THEN 2	-- Redeem Booking
		ELSE  99 --ERROR																
		END	
		
		
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_push_booking_type] TO [customer_api_user]
GO
