-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Chate C.			| 2013-07-10	| Initial version
--						| A function to check if it is BNPL booking with pending payment.
-- Test 
--  SELECT [dbo].[fn_rew2_Is_BNPL_Pending_Payment](1) as [value]	 
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_Is_BNPL_Pending_Payment]
(
	  @booking_id INT
)
RETURNS BIT
AS
BEGIN
	DECLARE @Is_Exist BIT = 0

	SELECT	TOP(1) @Is_Exist = 1 
	FROM	dbo.ebe_booking AS b
	INNER JOIN dbo.ebe_workflow_state AS ews
		ON b.workflow_state_id = ews.workflow_state_id
	INNER JOIN dbo.ebe_workflow_phases AS ewp
		ON ews.workflow_phase_id = ewp.workflow_phase_id
	INNER JOIN dbo.ebe_booking_summary AS bs
		ON b.booking_id = bs.booking_id
			
	WHERE	b.booking_id = @booking_id
	AND	ewp.workflow_phase_id < 3
	AND	bs.charge_option_id = 2
			
			
	RETURN @Is_Exist
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_Is_BNPL_Pending_Payment] TO [customer_api_user]
GO
