-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wichai <PERSON>	| 2011-12-09	| 
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_rew_group_status]
(
	  @rew_booking_status	tinyint
	, @rew_booking_type	tinyint
)
RETURNS char(1)
AS
BEGIN
	RETURN CASE 
		WHEN (@rew_booking_status IS NULL OR @rew_booking_status = 4)	THEN 'A'
		WHEN (@rew_booking_status = 3)					THEN 'B'
		WHEN (@rew_booking_status = 1 AND @rew_booking_type = 1)	THEN 'C'		
		WHEN (@rew_booking_status = 1 AND @rew_booking_type = 2)	THEN 'D'
		WHEN (@rew_booking_status = 2 AND @rew_booking_type = 1)	THEN 'E'
		WHEN (@rew_booking_status = 2 AND @rew_booking_type = 2)	THEN 'F'
		ELSE 'Z' --ERROR
		END
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_rew_group_status] TO [customer_api_user]
GO
