-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wichai <PERSON>	| 2011-12-09	| 
-- Sirilak			| 2012-01-17	| Change @config_redeem_point_per_dollar from int to decimal(19,2)
--------------------------------|---------------|----------------------------------------------------------
/* TEST
	SELECT dbo.fn_rew2_redeem_value_USD(20305,500) 
	--Before:	result = 40.00
	--After:	result = 40.61
*/
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_redeem_value_USD]
(
	 @booking_points_used			int
	,@config_redeem_point_per_dollar	decimal(19,2)
)
RETURNS decimal(19,2)
AS
BEGIN
	RETURN (@booking_points_used/@config_redeem_point_per_dollar)
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_redeem_value_USD] TO [customer_api_user]
GO
