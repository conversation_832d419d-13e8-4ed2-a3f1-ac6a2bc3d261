-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wichai <PERSON>	| 2011-12-09	| CREATED
-- Sirilak			| 2012-01-17	| Change @config_redeem_point_per_dollar from int to decimal(19,2)
--------------------------------|---------------|----------------------------------------------------------
/* TEST
	SELECT dbo.fn_rew2_refund_value_point(99,500)

*/
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_refund_value_point]
(
	  @Refund_value_USD			decimal(19,2)
	 ,@config_redeem_point_per_dollar	decimal(19,2)
)
RETURNS int
AS
BEGIN
	RETURN (@Refund_value_USD * @config_redeem_point_per_dollar)
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_refund_value_point] TO [customer_api_user]
GO
