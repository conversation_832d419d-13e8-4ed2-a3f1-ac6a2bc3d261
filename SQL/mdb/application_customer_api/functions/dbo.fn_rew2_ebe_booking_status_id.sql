




-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- W<PERSON><PERSON>	| 2011-12-09	| 
-- Chate C.			| 2012-10-12	| Refactoring code format. 
--------------------------------|---------------|----------------------------------------------------------

CREATE FUNCTION [dbo].[fn_rew2_ebe_booking_status_id]
(
	  @booking_id int
)
RETURNS int
AS
BEGIN

	RETURN 
	(
		SELECT		ewp.workflow_phase_id
		FROM		dbo.ebe_booking ebe(NOLOCK)
		INNER JOIN	dbo.ebe_workflow_state ews(NOLOCK)
					ON ebe.workflow_state_id = ews.workflow_state_id
		INNER JOIN	dbo.ebe_workflow_phases ewp(NOLOCK)
					ON ews.workflow_phase_id = ewp.workflow_phase_id
		WHERE		ebe.booking_id = @booking_id

	)
END





GO

GRANT EXECUTE ON [dbo].[fn_rew2_ebe_booking_status_id] TO [customer_api_user]
GO
