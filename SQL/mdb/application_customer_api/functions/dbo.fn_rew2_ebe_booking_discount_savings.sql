-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- W<PERSON><PERSON>	| 2011-12-09	| 
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_ebe_booking_discount_savings]
(
	  @booking_id int
)
RETURNS DECIMAL(19,2)
AS
BEGIN
	RETURN (
		SELECT  ISNULL(e.discount_savings,0)
		FROM	dbo.ebe_booking e (NOLOCK)
		WHERE	e.booking_id = @booking_id
		AND	e.Promotion_code is not null and LTRIM(RTRIM(Promotion_code)) <> ''
		)
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_ebe_booking_discount_savings] TO [customer_api_user]
GO
