-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- W<PERSON><PERSON>	| 2011-12-09	| 
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_annual_expiry_date]
(
	 @affected_date		datetime
	,@config_annual_expiry_period	tinyint
)
RETURNS date
AS
BEGIN
	RETURN CONVERT(DATE,CONVERT(VARCHAR(4),DATEPART(YEAR,@affected_date)+@config_annual_expiry_period)+'-01-01')
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_annual_expiry_date] TO [customer_api_user]
GO
