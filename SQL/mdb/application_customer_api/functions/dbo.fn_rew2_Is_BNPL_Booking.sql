-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Chate C.			| 2013-07-10	| Initial version
--						| A function to check if it is BNPL booking.	 
--------------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[fn_rew2_Is_BNPL_Booking]
(
	  @booking_id INT
)
RETURNS BIT
AS
BEGIN
	DECLARE @Is_Exist BIT = 0

	SELECT	TOP(1) @Is_Exist = 1 
	FROM	dbo.ebe_booking_summary AS bs					
	WHERE	bs.booking_id = @booking_id	
	AND	bs.charge_option_id = 2			
			
	RETURN @Is_Exist
END



GO

GRANT EXECUTE ON [dbo].[fn_rew2_Is_BNPL_Booking] TO [customer_api_user]
GO
