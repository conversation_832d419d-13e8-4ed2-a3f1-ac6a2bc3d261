
CREATE FUNCTION [dbo].[fn_rew_decrypted_decode] 
(
	@InStr varchar(2000)
)  
RETURNS varchar(2000)  AS  
BEGIN    
	DECLARE @functionReturnValue varchar(2000);
	DECLARE @oStr varchar(2000);
	DECLARE @i int = 0;
	DECLARE @jInt int = 0;
	DECLARE @xxx int = 0;

	IF LEN(@InStr) > 1
	BEGIN
		SET @oStr = '';
		SET @jInt = ASCII(SUBSTRING(@InStr, LEN(@InStr) - 1, 1)) - 55;
		SET @InStr = SUBSTRING(@InStr, 0, LEN(@InStr) - 1) + SUBSTRING(@InStr, LEN(@InStr), 1);  
		
		WHILE @i < LEN(@InStr)
			BEGIN
				SET @xxx = ASCII(SUBSTRING(@InStr, @i + 1, 1));
				SET @oStr = @oStr + char(CAST(ASCII(SUBSTRING(@InStr, @i + 1, 1)) - (POWER((-1), (@i * @jInt))) * (@jInt + @i) AS int));	
				SET @i = @i + 1;
			END     

			SET @functionReturnValue = @oStr;
		END
	ELSE
	BEGIN
		SET @functionReturnValue = @InStr;
	END

	RETURN @functionReturnValue
END




GO

