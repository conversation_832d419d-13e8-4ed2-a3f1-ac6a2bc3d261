CREATE TYPE user_role_table_v2 AS TABLE (UserId uniqueidentifier, RoleId uniqueidentifier, RoleName nvarchar(200), ObjectType char, ObjectName nvarchar(200), ObjectID int, rec_status int, rec_created_when datetime, rec_created_by uniqueidentifier, rec_modified_when datetime, rec_modified_by uniqueidentifier, ObjectNameById nvarchar(200));
GRANT EXECUTE ON TYPE ::[dbo].[user_role_table_v2] TO [customer_api_user]