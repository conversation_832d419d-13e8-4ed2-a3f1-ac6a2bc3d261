CREATE TYPE auth_assigned_role_audit_table_type AS TABLE (Id int, UserId uniqueidentifier, AssignDate datetime, AssignedRoleId uniqueidentifier, AssignedUserId uniqueidentifier, AssignedObjectType char, AssignedObjectId int, ActivityId int, rec_status int, rec_created_by uniqueidentifier, rec_created_when datetime, rec_modified_by uniqueidentifier, rec_modified_when datetime);
GRANT EXECUTE ON TYPE ::[dbo].[auth_assigned_role_audit_table_type] TO [customer_api_user]