
--------------------------------|---------------|----------------------------------------------------------
-- Author                       | Date          | Comment
--------------------------------|---------------|----------------------------------------------------------
-- Anurag A                     | 2019-02-22    | SP for associating verified phone number
--------------------------------|---------------|----------------------------------------------------------
--  Test helper
-- EXEC_TEST
-- EXECUTE AS LOGIN = 'customer_api_user'
-- EXEC dbo.associate_verified_phone_number_v1 '9F6AABF7-B995-4371-8535-4BA6957D9DC8',7,'phone://+917030615522','7030615522|91','+917030615522','_?eqg5GYhTpYk+leS/Hk58Aw==_','_?O5aGd1o4ugHE1SRYt9a74w==_','9F6AABF7-B995-4371-8535-4BA6957D9DC8',1
-- REVERT;
-- END_EXEC_TEST
--------------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[associate_verified_phone_number_v1]
	@user_id uniqueidentifier
	,@authentication_type_id tinyint
	,@uriEncrypted varchar(512)
	,@phoneNumberInternal varchar(200)
	,@phoneNumberE164 varchar(200)
	,@phoneNumberInternalEncrypted varchar(200)
	,@phoneNumberE164Encrypted varchar(200)
	,@rec_created_by uniqueidentifier
	,@rec_status int = 1
AS
BEGIN
  SET NOCOUNT ON
	SET XACT_ABORT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	BEGIN TRY
		BEGIN TRAN
			DECLARE @deletedRC INT = 0
			DECLARE @modifiedAum INT = 0
			DELETE FROM	dbo.auth_user_mapping
			WHERE	username in (@phoneNumberInternal,@phoneNumberE164,@phoneNumberInternalEncrypted,@phoneNumberE164Encrypted)
			AND		[authentication_type_id] = @authentication_type_id
			AND		rec_status = 1
			AND     user_id != @user_id

			SET @deletedRC = @@ROWCOUNT
			IF (@deletedRC > 0)
			BEGIN
					-- Log into auth_user_mapping_history
					INSERT INTO dbo.auth_user_mapping_history
					(
						logtime
						, user_id
						, authentication_type_id
						, activityid
						, rec_status
						, rec_created_when
						, rec_created_by

					)
					VALUES
					(
						GETDATE()
						, @user_id
						, @authentication_type_id
						, 12 --Remove User mapping (API)
						, 1
						, GETDATE()
						, @user_id

					)
			END

		IF EXISTS
	    (
		    SELECT	*
		    FROM	dbo.auth_user_mapping AS um
		    WHERE	um.[user_id] = @user_id
		    AND	um.authentication_type_id = @authentication_type_id
	    )
	    BEGIN
		    UPDATE	dbo.auth_user_mapping
		    SET	uri = @uriEncrypted
			, username = @phoneNumberE164Encrypted
			, rec_status = 1
			, rec_modified_by = @rec_created_by
			, rec_modified_when = GETDATE()
			, expired_since = DATEADD(day, 90, GETDATE())
			, password_last_changed = GETDATE()
		    WHERE	[user_id] = @user_id
		    AND	authentication_type_id = @authentication_type_id
		    SET @modifiedAum = @@ROWCOUNT
	    END
	    ELSE
	    BEGIN
		    INSERT INTO dbo.auth_user_mapping
		    (
			    [user_id]
			    , authentication_type_id
			    , uri
			    , username
			    , rec_status
			    , rec_created_when
			    , rec_created_by
			    , expired_since
			    , password_last_changed
		    )
		    VALUES
		    (
			    @user_id
			    , @authentication_type_id
			    , @uriEncrypted
			    , @phoneNumberE164Encrypted
			    , @rec_status
			    , GETDATE()
			    , @rec_created_by
			    , DATEADD(day, 90, GETDATE())
			    , GETDATE()
		    )
		    SET @modifiedAum = @@ROWCOUNT
	    END

		COMMIT TRAN
		SELECT @modifiedAum AS modifiedAum
	END TRY
	BEGIN CATCH
		DECLARE @err_msg NVARCHAR(2048) = ERROR_MESSAGE()
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRAN
		END
		RAISERROR(@err_msg, 16, 1)
	END CATCH
END


GO


GRANT EXECUTE ON [dbo].[associate_verified_phone_number_v1] TO [customer_api_user]
GO
