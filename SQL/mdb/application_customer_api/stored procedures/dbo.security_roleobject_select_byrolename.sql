/*
<Monkey>
	<InstanceType Name="MDB">
		<Database Name="Application_Security" />
	</InstanceType>
	<ScriptSubType>NEW_PROC</ScriptSubType>
	<ObjectName>security_roleobject_select_byrolename</ObjectName>
	<TestScript>SELECT 1</TestScript>	
</Monkey>
*/
---------------------------------------------------------------------------------------------------
-- Author	| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- <PERSON><PERSON><PERSON><PERSON>	| 2015-04-13	| Selects the role object list by role name
----------------|---------------|----------------------------------------------------------
-- Test: EXEC security_roleobject_select_byrolename 'EEAE36AF-FA88-4D42-8CF1-D2417088A8CA', 'IT-DEV-BDP', '0'
----------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[security_roleobject_select_byrolename]
	@userId uniqueidentifier
	, @roleName varchar(50)
	, @objectType char
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	
	DECLARE @RoleId uniqueidentifier
	
	SELECT	@RoleId = r.RoleId
	FROM	dbo.auth_roles AS r
	WHERE	r.RoleName = @roleName
	
	IF @RoleId IS NOT NULL
	BEGIN
		EXEC	dbo.security_roleobject_select_byroleid @userId, @RoleId, @objectType
	END
END




GO

