-----------------------------------------------------------------------------------------------------------------
-- Author			 | Date				| Comment
----------------------------------------|---------------|----------------------------------------------------------
-- kankit	        | 2017-03-16 | SP to check if user already exist in DB
-- kankit	        | 2017-05-22 | return if user isBlocked
-- Andrey S.        | 2018-10-07 | v3, extend PII columns size WFAPI-2152
-- Anurag A.        | 2019-02-01 | v4, included flag is_verified if found in auth_user_mapping
-- Weerapong M.     | 2020-11-19 | v5, add filter by rec_status = 1, order by created when
----------------------------------------|---------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN ='customer_api_user'
EXEC dbo.[rew_checkIfUserDataExist_v5] '77777777,+636952747241','952747240|66,+66952747241',7
REVERT;
END_EXEC_TEST
*/
----------------|---------------|------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[rew_checkIfUserDataExist_v5]
    @userDataContactTable VARCHAR(8000) = NULL,
    @userDataUserMapping  VARCHAR(8000) = NULL,
    @authType             INT
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
DECLARE @result_auth_user_mapping TABLE (   userid uniqueidentifier,
											uri varchar(512),
											username varchar(400),
											memberid INT,
                                            isBlocked INT,
                                            is_verified INT
										)
    IF (@userDataUserMapping IS NOT NULL)
	BEGIN
        INSERT  INTO @result_auth_user_mapping (userid,
                                                uri,
                                                username,
                                                isBlocked,
                                                is_verified
                                                )
                SELECT  TOP 1   aum.user_id, aum.uri, aum.username, au.IsBlocked,1
                FROM    dbo.auth_user_mapping aum
        		LEFT JOIN dbo.auth_users au
        		ON au.UserId = aum.user_id
                WHERE  EXISTS (
                            SELECT  strval
                            FROM    dbo.SPLIT_v2 (@userDataUserMapping,',') b
                            WHERE   aum.username = b.strval
                            AND     aum.rec_status = 1
                                 )
                AND authentication_type_id = @authType
                ORDER BY aum.rec_created_when

		 Update @result_auth_user_mapping set memberid = (
		 SELECT TOP 1 Memberid FROM dbo.rew_members WHERE userId = (SELECT userid FROM @result_auth_user_mapping))
	END


	IF (NOT EXISTS ( SELECT userid FROM @result_auth_user_mapping ) AND @userDataContactTable IS NOT NULL)
    BEGIN

        INSERT  INTO @result_auth_user_mapping (
           memberid
        ) SELECT TOP 1 Memberid
        FROM dbo.rew_contacts a
        WHERE EXISTS (
                    SELECT  strval
                    FROM    dbo.SPLIT_v2 (@userDataContactTable,',') b
                    WHERE   a.contact_method_value = b.strval
                    AND     a.rec_status = 1
        )
        And contact_method_id IN (3 , 5)
        ORDER BY a.rec_created_when

        Update @result_auth_user_mapping set userid = (
                         SELECT TOP 1 userid FROM dbo.rew_members WHERE memberid = (SELECT memberid FROM @result_auth_user_mapping))
      ,is_verified=0

    END
	       SELECT TOP 1	    userid,
							uri,
							username,
							isBlocked,
							memberid,
	                    is_verified
			FROM @result_auth_user_mapping
END
GO

GRANT EXECUTE ON [dbo].[rew_checkIfUserDataExist_v5] TO [customer_api_user]
GO

