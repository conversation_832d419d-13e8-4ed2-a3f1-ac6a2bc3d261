---------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|-------------------------------------------------------------------
-- Dash Fu        | 2016-11-09 | Set verification flag.
-- <PERSON><PERSON>.      | 2018-10-07 | v1, extend PII columns size WFAPI-2152
-- Andrew <PERSON>.      | 2018-11-07 | v2, Use user id
------------------|------------|-------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN ='customer_api_user'
EXEC [security_usermapping_update_isverified_v2] '3C8ABB05-6354-4B12-9C14-2E82823945C4',2,'3C8ABB05-6354-4B12-9C14-2E82823945C4',1
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[security_usermapping_update_isverified_v2]
	@UserId uniqueidentifier
	, @authentication_type_id tinyint
	, @rec_modified_by uniqueidentifier
	, @is_verified bit
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @deletedRC INT = 0

	UPDATE		dbo.auth_user_mapping
	SET			is_verified = @is_verified,
				rec_modified_by = @rec_modified_by,
				rec_modified_when = GETDATE()
	WHERE		user_id = @UserId
	AND			authentication_type_id = @authentication_type_id

	SET @deletedRC = @@ROWCOUNT
	SELECT @deletedRC AS RESULT
END
GO

GRANT EXECUTE ON [dbo].[security_usermapping_update_isverified_v2] TO [customer_api_user]
GO
