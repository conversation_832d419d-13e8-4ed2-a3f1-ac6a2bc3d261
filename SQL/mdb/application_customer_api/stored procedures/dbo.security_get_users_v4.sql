---------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------------
-- <PERSON>y <PERSON>.              | 2018-05-10  | Add onlyActive param, by default returns only active records
-- Andrey <PERSON>.              | 2018-04-24  | Bulk version of get auth_user by UUID
-- Kit<PERSON><PERSON>   | 2018-11-19  | Return Manager and is_single_task_consumption in the result
-- Warot A.               | 2019-06-12  | Return department_id and location_id
---------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
  DECLARE @UserIds uuid_table_type
  INSERT INTO @UserIds (uuid)
  VALUES ('CC9E7952-745F-4A90-808F-532B599E422C'), ('B7FB0322-3ADC-4C6E-945B-532B59E5154B')
  EXEC [dbo].[security_get_users_v4] @UserIds
REVERT
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_get_users_v4]
  @users [dbo].[uuid_table_type] READONLY,
  @onlyActive BIT = 1
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
      UserId,
      EmailAddress,
      DisplayName,
      Manager,
      is_single_task_consumption,
      department_id,
      location_id,
      rec_status,
      rec_created_when,
      rec_created_by,
      rec_modified_when,
      rec_modified_by
    FROM dbo.auth_users
    WHERE UserId IN ( SELECT uuid FROM @users ) AND ( @onlyActive = 0 OR rec_status = 1 )
  END
GO

GRANT EXECUTE ON [dbo].[security_get_users_v4] TO [customer_api_user]
GO
