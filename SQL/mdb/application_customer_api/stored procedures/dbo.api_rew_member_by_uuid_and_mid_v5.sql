/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 3k

END_SQL_OBJECT_INFO
*/
-----------------------------------------------------------------------------------------------------------
-- Author                | Date        | Comment
-------------------------|-------------|----------------------------------------------------------
-- Andrew <PERSON>          | 2019-03-14  | Initial version
-- Varakorn Koschakosai  | 2019-12-17  | Change to exec api_rew_member_by_uuid_v10
-- Weerapong Mua         | 2022-06-22  | bump and pass @override_rec_status, @load_roles into sub SP
-- Weerapong Muangmai    | 2025-02-03  | Change to exec api_rew_member_by_uuid_v11
-------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC  [dbo].[api_rew_member_by_uuid_and_mid_v5] 3,1,1
REVERT;
END_EXEC_TEST
*/
--------------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[api_rew_member_by_uuid_and_mid_v5]
		@memberId INT,
		@override_rec_status BIT = 0,
		@load_roles BIT = 1
AS
	BEGIN
		SET NOCOUNT ON
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
		DECLARE @UserId UNIQUEIDENTIFIER;
    SELECT
            @UserId =
            UserId
    FROM   dbo.rew_members rew
    WHERE  MemberId = @memberId and (rew.rec_status = 1 OR @override_rec_status = 1);
    exec dbo.api_rew_member_by_uuid_v11  @UserId, @override_rec_status, @load_roles
	END
GO

GRANT EXECUTE ON [dbo].[api_rew_member_by_uuid_and_mid_v5] TO [customer_api_user]
GO
