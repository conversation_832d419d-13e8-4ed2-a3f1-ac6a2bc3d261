
---------------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- A.<PERSON> | 2017-07-07	| Check for rec_status in auth_user_in_role.
-- GyÃ¶rgy <PERSON>	| 2015-04-13	| Selects the role object list by role id
----------------|---------------|----------------------------------------------------------
-- MONKEY_TEST EXEC security_roleobject_select_byroleid '3F000333-2F9F-4B8E-A588-000197D82340', '732F908B-2794-4A95-BABD-873EA675BA8E', 'R'
----------------|---------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[security_roleobject_select_byroleid_v2]
    @userId uniqueidentifier
    , @roleId uniqueidentifier
    , @objectType char
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    DECLARE @now datetime = GETDATE()

    SELECT		uir.ObjectId
    FROM		dbo.auth_user_in_role AS uir
    INNER JOIN	dbo.auth_roles AS r
				ON r.roleid = uir.roleid
    WHERE		r.rec_status = 1
    AND			uir.RoleId = @roleId
    AND			uir.ObjectType = @objectType
    AND			uir.userid = @userId
    AND			uir.rec_status = 1

    UNION

    SELECT		uir.ObjectId
    FROM		dbo.auth_user_in_role AS uir
    INNER JOIN	dbo.auth_roles AS r
				ON r.roleid = uir.roleid
    INNER JOIN	dbo.auth_user_outofoffice_mapping AS om
				ON om.userid = uir.UserId
    WHERE		r.rec_status = 1
    AND		uir.RoleId = @roleId
    AND		uir.ObjectType = @objectType
    AND		uir.rec_status = 1
    AND		om.AssignedUserid = @userId
    AND		om.rec_status = 1
    AND		om.startdate >= @now
    AND		om.enddate <= @now
END



GO

GRANT EXECUTE ON [dbo].[security_roleobject_select_byroleid_v2] TO [customer_api_user]
GO
