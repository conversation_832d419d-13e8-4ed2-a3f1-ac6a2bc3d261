
--------------------------|-------------|----------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------
-- Saran B.               | 2016-05-20  | -
-- Nattwat W.             | 2019-02-14  | Move from BackOffice
--------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
  DECLARE @userId uniqueidentifier = '45bf6875-2703-414e-b17c-0db5bdf7132f'
  EXEC dbo.backoffice_assigned_roles_select_v1 @userId
REVERT
END_EXEC_TEST
*/

CREATE PROCEDURE [dbo].[backoffice_assigned_roles_select_v1]
	@userId uniqueidentifier
AS
BEGIN
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	select distinct b.RoleId, b.RoleName, b.ObjectType, c.objectName
	from dbo.auth_role_assigned_roles a
	inner join dbo.auth_roles b
		on a.AssignedRoleId = b.roleId and b.objecttype is not null
	inner join dbo.auth_object_type c
		on b.objecttype = c.objecttype
	where a.roleId in (select distinct roleId from dbo.auth_user_in_role where userid = @userId)
	order by b.RoleName

END

--Modified by Pongtham W. @ Aug 31, 2011 - added distinct



GO

GRANT EXECUTE ON [dbo].[backoffice_assigned_roles_select_v1] TO [customer_api_user]
GO
