----------------------------------------------------------------------------------------------
-- Author                       | Date       | Comment
--------------------------------|------------|------------------------------------------------
-- <PERSON><PERSON><PERSON>     | 2017-11-14 | Return all properties and is nha flag column
-- <PERSON><PERSON><PERSON>     | 2017-11-08 | Get userId by memberId and add property status to the return field
-- S. <PERSON>                    | 2017-07-14 | Omit inactive user in roles entries
-- Sur<PERSON><PERSON><PERSON>ch, Sarit       | 2017-06-01 | Get list of hotel
-- Nuttipong, T			            | 2017-11-12 | Modify sp to return property images
-- <PERSON><PERSON>			            | 2017-11-18 | new unlive, flagout or deletion logic
-- <PERSON><PERSON>			            | 2017-12-01 | Fix duplicated property images
-- <PERSON><PERSON>.			                | 2018-01-12 | Performance Tuning - NHA host timeout
-- Andrew L		                  | 2018-01-07 | Fix duplicated column name
-- Mrun S.		                  | 2018-12-20 | Remove 'R' role dependency
-- Varakorn K.                  | 2019-07-01 | Remove condition to check Hotel_ycs_flag as 0
-- Virayut S.                   | 2019-07-31 | Modify filter to return inactive property
----------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[ycs41_get_hotel_list_v7] NULL,'02AA1C0B-A812-4DCB-A48A-F7F604E4DEE3'
REVERT;
END_EXEC_TEST
*/

----------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[ycs41_get_hotel_list_v7]
    @member_id INT = NULL,
    @user_id UNIQUEIDENTIFIER = NULL
AS
BEGIN

    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    DECLARE @hotel TABLE
    (
    hotel_id int NOT NULL PRIMARY KEY
    )
    IF @member_id IS NOT NULL
    BEGIN
        SELECT @user_id = UserId
        FROM    dbo.rew_members
        WHERE   memberId = @member_id
        AND rec_status = 1
    END
        INSERT INTO @hotel (hotel_id)
        SELECT DISTINCT au.ObjectID AS hotel_id
        FROM    dbo.auth_user_in_role  au
        WHERE   au.UserId = @user_id
        AND rec_status = 1
        AND au.objecttype = 'H'
    ;
    DECLARE @pic TABLE
    (
       	  hotel_id INT NOT NULL PRIMARY KEY
        , picture_caption_title NVARCHAR(300)
        , picture_location NVARCHAR(300)
    )
    INSERT INTO @pic (hotel_id, picture_caption_title, picture_location)
    SELECT    a.hotel_id
            , a.picture_caption_title
            , a.picture_location
    FROM (
        SELECT    hp.hotel_id
                , hp.picture_caption_title
                , hp.picture_location
                , ROW_NUMBER () OVER(PARTITION BY hp.hotel_id ORDER BY  hp.picture_order_id ASC, hp.picture_id DESC) AS ranking
        FROM        dbo.hotel_pictures AS hp
        INNER JOIN  @hotel au
                ON  hp.hotel_id = au.hotel_id
        WHERE       hp.picture_type_id = 6
        AND         hp.rec_status = 1
        AND         ISNULL(hp.picture_location,'') <> ''
    ) AS a
    WHERE a.ranking = 1
    ;
    SELECT      DISTINCT h.hotel_id
            , h.hotel_name
            , y.first_live_date
            , IIF(ISNULL(y.Rec_status, 0) = 0 , 0, 1) AS active_status
            , h.rec_status AS hotel_active_status
            , h.is_non_hotel_accommodation_mode AS is_nha
            , p.picture_caption_title
            , p.picture_location
    FROM        @hotel hau
    INNER JOIN  dbo.product_hotels h
            ON hau.hotel_id = h.hotel_id
    INNER JOIN  dbo.ycs4_hotel y
            ON h.hotel_id = y.hotel_id
    LEFT JOIN   @pic p
            ON h.hotel_id = p.hotel_id
    WHERE
            NOT (y.Rec_status IS NULL OR y.Rec_status = -1)
    AND
            NOT (h.rec_status IS NULL OR h.rec_status = -1)
END
GO

GRANT EXECUTE ON [dbo].[ycs41_get_hotel_list_v7] TO [customer_api_user]
GO
