---------------------------------------------------------------------------------------------------
-- Author					| Date			| Comment
--------------------------------|---------------|--------------------------------------------------
-- <PERSON><PERSON>			| 2015-11-05	| Fetches distinct role_id by user_id
--------------------------------|---------------|--------------------------------------------------
-- Test : EXEC dbo.security_roleid_select_byuserid 'EEAE36AF-FA88-4D42-8CF1-D2417088A8CA'
--------------------------------|---------------|--------------------------------------------------
CREATE PROCEDURE [dbo].[security_roleid_select_byuserid]
	@userId uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	
	SELECT 	DISTINCT RoleId
	FROM	dbo.auth_user_in_role AS uir	
	WHERE	uir.rec_status = 1	
	AND	uir.UserId = @userId
END




GO

