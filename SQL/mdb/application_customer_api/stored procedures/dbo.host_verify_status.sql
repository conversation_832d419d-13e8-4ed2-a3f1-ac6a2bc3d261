
/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- Andrew <PERSON> 		        | 09/07/2018	| First version
-- Weerapong Mua 		        | 13/08/2021	| Copy from CDB
------------------------------------------------|---------------|------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @memberuuids AS [uuid_table_type];
INSERT INTO @memberuuids (uuid)
  (SELECT DISTINCT TOP 1000
     UserId
   FROM dbo.auth_users)
EXEC [dbo].[host_verify_status] @memberuuids
REVERT;
END_EXEC_TEST

------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[host_verify_status]
    @memberUUIDs [uuid_table_type] READONLY
AS
BEGIN
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

SELECT	aum.user_id as u
		,aum.authentication_type_id as i
		,aum.is_verified as v 
FROM		dbo.auth_user_mapping aum
INNER JOIN	@memberuuids as memberUUIDs 
				ON memberUUIDs.uuid = aum.user_id
WHERE  aum.authentication_type_id in (2,7) 
		and rec_status = 1

END

GO

GRANT EXECUTE ON [dbo].[host_verify_status] TO [customer_api_user]
GO
