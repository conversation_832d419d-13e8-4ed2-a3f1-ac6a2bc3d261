/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- Po<PERSON><PERSON>   | 2010-10-08  | To validate the data in temporary table and execute sp alert or sp finalize.
-- Pong<PERSON> Wiangwang   | 2012-10-16  | Change validation either get user id or email address
-- Sirichai Ch.         | 2012-11-26  | Check the permission to upload the object id 0
-- Nasakol P.           | 2018-01-10  | Support MDC by selecting the inserted rows to auth_assigned_role_audit
-- Kit<PERSON><PERSON> | 2018-12-21  | Add userId_email_table as a parameter and remove join auth_users with emailAddress
-- Kittisak Wongkraphan | 2019-02-20  | Initial backoffice_upload_auth_user_in_role_v1 from backoffice_upload_auth_user_in_role_v4_process
-- Abhishek P.          | 2024-12-19  | v2, Added validation for ObjectType 'K' - only one ObjectId per UserId and allow User to assign ObjectType K for YCS Channel Manager role
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
  DECLARE @upload upload_auth_user_in_role_table_type
  INSERT INTO @upload (line, userId, roleId, objectType, objectId)
  VALUES (1, '2bb76b22-3c29-4451-b3d2-24ad0d0888aa', 'a7047b2c-58d7-4053-a056-8fcb948b436a', 0, 0), (2, 'af7a0145-6d3f-410f-ae8b-b97b97b011d2', 'a7047b2c-58d7-4053-a056-8fcb948b436a', 0, 0)
  EXECUTE AS LOGIN = 'customer_api_user'
  EXEC dbo.backoffice_upload_auth_user_in_role_v2 @upload, '9f6b60f7-4215-4da6-b030-fb6047db6c4c'
REVERT;
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_upload_auth_user_in_role_v2]
  @upload       upload_auth_user_in_role_table_type READONLY,
  @uploadBy     UNIQUEIDENTIFIER
AS
BEGIN
  SET NOCOUNT ON;
  SET XACT_ABORT ON;
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

  DECLARE @uploadWhen DATETIME = GETDATE()

  --===================================
  -- Part 1. Validate Data 
  --===================================
  DECLARE @tempAssignRole AS TABLE (
    assignedRoleId uniqueidentifier NOT NULL
    , objectType char(1) NOT NULL
  );

  INSERT INTO @tempAssignRole (
    assignedRoleId
    , objectType
  )
  SELECT  DISTINCT arar.AssignedRoleId
          , ar.ObjectType
  FROM    dbo.auth_users au
  INNER JOIN  dbo.auth_user_in_role auir
        ON    au.UserId = auir.UserId
  INNER JOIN  dbo.auth_role_assigned_roles arar
        ON    auir.RoleId = arar.RoleId
  INNER JOIN  dbo.auth_roles ar
        ON    ar.RoleId = arar.AssignedRoleId
  WHERE   ar.ObjectType IS NOT NULL
  AND     au.userId = @uploadBy

  -- Check if specific AssignedRoleId exists, if yes add it with ObjectType 'K' - this is Channel Manager Use case
  IF EXISTS (
    SELECT 1 
    FROM @tempAssignRole 
    WHERE assignedRoleId = 'A467CAA1-5587-44D6-AD0B-868B6ACC544C'
  )
  BEGIN
    INSERT INTO @tempAssignRole (assignedRoleId, objectType)
    VALUES ('A467CAA1-5587-44D6-AD0B-868B6ACC544C', 'K')
  END

  --9. Validate that for ObjectType 'K', each UserId can only have one ObjectId
  SELECT    u.line AS line
            , 'ObjectId' AS field
            , 'A UserId can only have one ObjectId for ObjectType = ''K''.' AS [message]
  FROM    @upload u
  WHERE   u.ObjectType = 'K'
  AND     EXISTS (
            SELECT 1
            FROM dbo.auth_user_in_role t
            WHERE t.UserId = u.UserId
              AND t.ObjectType = 'K'
              AND t.ObjectID <> u.ObjectId
              AND t.rec_status = 1
          )
  
  UNION ALL

  --10. Validate that the new records don't create multiple ObjectIds for the same UserId with ObjectType 'K'
  SELECT    u1.line AS line
            , 'ObjectId' AS field
            , 'Cannot insert multiple ObjectIds for the same UserId when ObjectType = ''K''.' AS [message]
  FROM    @upload u1
  WHERE   u1.ObjectType = 'K'
  AND     EXISTS (
            SELECT 1
            FROM @upload u2
            WHERE u2.ObjectType = 'K'
              AND u2.UserId = u1.UserId
              AND u2.ObjectId <> u1.ObjectId
          )
  
  UNION ALL

  --1. check valid userid
  SELECT    line AS line
            , 'UserId / Email' AS field
            , 'User does not exist.' AS [message]
  FROM    @upload u
  WHERE   NOT EXISTS (
            SELECT *
            FROM dbo.auth_users
            WHERE userid = u.UserId
          )
  AND    u.UserId IS NOT NULL
  
  UNION ALL
  
  --2. check valid roleid
  SELECT    line AS line
            , 'RoleId' AS field
            , 'RoleId is invalid.' AS [message]
  FROM    @upload u
  WHERE   NOT EXISTS (
            SELECT *
            FROM dbo.auth_roles
            WHERE RoleId = u.RoleId
          )
  
  UNION ALL
  
  --3. check valid object type
  SELECT    line AS line
            , 'ObjectType' AS field
            , 'ObjectType is invalid.' AS [message]
  FROM    @upload u
  WHERE   NOT EXISTS (
            SELECT *
            FROM dbo.auth_object_type
            WHERE ObjectType = u.ObjectType
          )
  
  UNION ALL
  
  --4. check valid object id
  SELECT    line AS line
            , 'ObjectId' AS field
            , 'ObjectId is invalid.' AS [message]
  FROM    @upload u
  WHERE   NOT EXISTS (
            SELECT *
            FROM dbo.GetObjectList(u.ObjectType, u.ObjectId)
          )
  AND u.ObjectId <> 0
  
  UNION ALL
  
  --5. check duplicate
  SELECT    line AS line
            , 'ObjectId' AS field
            , 'User is already in this role.' AS [message]
  FROM    @upload u
  INNER JOIN  dbo.auth_user_in_role uir
        ON u.userid = uir.userid
        AND u.roleid = uir.roleid
        AND u.objecttype = uir.objecttype
        AND u.objectid = uir.objectid
  
  UNION ALL
  
  --6. check duplicate in the same file
  SELECT    u.line AS line
            , 'ObjectId' AS field
            , 'Duplicate data in the same file.' AS [message]
  FROM    @upload u
  INNER JOIN  @upload uir
        ON u.userid = uir.userid
        AND u.roleid = uir.roleid
        AND u.objecttype = uir.objecttype
        AND u.objectid = uir.objectid
  WHERE u.line <> uir.line
  
  UNION ALL
  
  --7. check that this user has permission to assign role
  SELECT    u.line AS line
            , 'Uploaded by' AS field
            , 'You do not have permission to assign this role.' AS [message]
  FROM    @upload u
  WHERE   NOT EXISTS (
            SELECT  *
            FROM  @tempAssignRole
            WHERE  objectType = u.ObjectType
            AND  assignedRoleId = u.RoleId
          )

  UNION ALL

  --8. Check that this user allow to assign the object Id 0  
  SELECT    AUIRU.line AS line
            , 'Uploaded by' AS field
            , 'You do not have permission to assign this role with the given object id (0).' AS [message]
  FROM    @upload  AUIRU
  WHERE   NOT EXISTS (
            SELECT *
            FROM  dbo.auth_user_in_role AUIR
            INNER JOIN  dbo.auth_role_permissions ARP
                ON AUIR.RoleId = ARP.RoleId
            WHERE ARP.PermissionId = '8f1e22cf-dcf9-4c44-9d8c-1298016fb836' --"Upload ObjectID Zero" Permission.
            AND AUIR.UserId = @uploadBy
            AND AUIRU.ObjectID = 0
          )
  AND AUIRU.ObjectId  = 0

  ORDER BY  line, field
  
  
  --===================================
  -- Part 2. Merge Data
  --===================================
  IF @@ROWCOUNT = 0
  BEGIN
    BEGIN TRANSACTION;
    MERGE dbo.auth_user_in_role AS T
    USING (
      SELECT  UserID
        , RoleId
        , ObjectType
        , ObjectID
        , @uploadBy AS uploaded_by
        , @uploadWhen AS uploaded_when
      FROM  @upload
      ) AS S
      ON S.UserID = T.UserID
        AND S.RoleId = T.RoleId
        AND S.ObjectType = T.ObjectType
        AND S.ObjectId = T.ObjectId
    WHEN NOT MATCHED BY Target
      THEN
        INSERT (
          UserId
          , RoleId
          , ObjectType
          , ObjectID
          , rec_status
          , rec_created_when
          , rec_created_by
          )
        VALUES (
          UserId
          , RoleId
          , ObjectType
          , ObjectID
          , 1
          , uploaded_when
          , uploaded_by
          );
    --===================================
    -- Part 3. Insert audit
    --=================================== 
    
    DECLARE @OutputTbl [auth_assigned_role_audit_table_type]
      
    INSERT INTO [dbo].[auth_assigned_role_audit] (
      [UserId]
      , [AssignDate]
      , [AssignedRoleId]
      , [AssignedUserId]
      , [AssignedObjectType]
      , [AssignedObjectId]
      , [ActivityId]
      , [rec_status]
      , [rec_created_by]
      , [rec_created_when]
      , [rec_modified_by]
      , [rec_modified_when]
      )
    OUTPUT   INSERTED.Id, INSERTED.UserId, INSERTED.AssignDate, INSERTED.AssignedRoleId, INSERTED.AssignedUserId, INSERTED.AssignedObjectType,
        INSERTED.AssignedObjectId, INSERTED.ActivityId, INSERTED.rec_status, INSERTED.rec_created_by, INSERTED.rec_created_when, 
        INSERTED.rec_modified_by, INSERTED.rec_modified_when
      INTO @OutputTbl(Id, UserId, AssignDate, AssignedRoleId, AssignedUserId, AssignedObjectType, AssignedObjectId,
        ActivityId, rec_status, rec_created_by, rec_created_when, rec_modified_by, rec_modified_when)
    SELECT  @uploadBy AS uploaded_by
      , @uploadWhen AS uploaded_when
      , RoleId
      , UserId
      , ObjectType
      , ObjectID
      , 10
      , 1 -- Assign roles vis upload functions      
      , @uploadBy AS uploaded_by
      , @uploadWhen AS uploaded_when
      , NULL
      , NULL
    FROM @upload
    
    SELECT * FROM @OutputTbl
    IF @@TRANCOUNT > 0
      COMMIT TRANSACTION;
    ELSE
      ROLLBACK TRANSACTION;
  END
END
GO

GRANT EXECUTE ON [dbo].[backoffice_upload_auth_user_in_role_v2] TO [customer_api_user]
GO 