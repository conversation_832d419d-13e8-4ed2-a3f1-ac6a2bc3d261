/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Noppharut Phi            | 2022-08-18    | create new read_customer_distinct_role_id_by_userid_objecttype_objectid_v1
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_customer_distinct_role_id_by_userid_objecttype_objectid_v1] '3BDD0247-7381-405D-8263-0342796257A9', 'R', 10000;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_customer_distinct_role_id_by_userid_objecttype_objectid_v1]
    @user_id    uniqueidentifier,
    @object_type CHAR(1),
    @object_id   INT = NULL
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT  DISTINCT RoleId as role_id
FROM     dbo.auth_user_in_role r1
WHERE    UserId = @user_id
  AND      rec_status = 1
  AND      ObjectType = @object_type
  AND      (@object_id IS NULL OR (ObjectId = @object_id))
    OPTION (OPTIMIZE FOR (@user_id UNKNOWN));
END
GO

GRANT EXECUTE ON [dbo].[read_customer_distinct_role_id_by_userid_objecttype_objectid_v1] TO [customer_api_user]
GO
