/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>at<PERSON>           | 2019-06-06    | Replaces the verified contact for the given user.
-- Yevhen <PERSON>atulin           | 2019-07-19    | Delete primary email instead of soft delete.
-- <PERSON>          | 2019-07-25    | Added whitelabel parameter
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
    EXECUTE AS LOGIN = 'customer_api_user'
    INSERT INTO dbo.auth_users (UserId, DisplayName, EmailAddress, IsBlocked, IsDeleted, IsIpRestriced, IsGeoRestricted, FailedLoginAttempts, rec_status, rec_created_when, rec_created_by) VALUES ('00000000-0000-0000-0000-000000000000', '', '<EMAIL>', 0, 0, 0, 0, 0, 1, GETDATE(), '00000000-0000-0000-0000-000000000000');
    DECLARE @random_contact_id int = ABS(CHECKSUM(NewId())) % 2147483647;
    DELETE FROM dbo.rew_contacts WHERE contact_id = @random_contact_id;
    EXEC [dbo].[sync_user_mappings_v5] DEFAULT, 2, '00000000-0000-0000-0000-000000000000', 'agoda://<EMAIL>', '<EMAIL>', '<EMAIL>', '00000000-0000-0000-0000-000000000000', @random_contact_id;
    REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[sync_user_mappings_v5]
    @users_to_unassign dbo.uuid_table_type READONLY,
    @auth_type int,
    @user_id uniqueidentifier,
    @uri varchar(512),
    @lowercase_username varchar(400),
    @original_username varchar(400),
    @modified_by uniqueidentifier,
    @contact_id int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    DECLARE @member_id int = (SELECT MemberId FROM dbo.rew_members WHERE UserId = @user_id);
    DECLARE @whitelabel_id smallint = (SELECT whitelabel_id FROM dbo.rew_members WHERE MemberId = @member_id);

    BEGIN TRANSACTION;
    DELETE     aum
    FROM	     dbo.auth_user_mapping aum
    INNER JOIN @users_to_unassign u
    ON         aum.user_id = u.uuid
    WHERE      aum.authentication_type_id = @auth_type;

    INSERT INTO dbo.auth_user_mapping
    (
                user_id
                , authentication_type_id
                , uri
                , username
                , rec_status
                , rec_created_when
                , rec_created_by
                , expired_since
                , password_last_changed
                , whitelabel_id
    )
    VALUES
    (
                @user_id
                , @auth_type
                , @uri
                , @lowercase_username
                , 1
                , GETDATE()
                , @modified_by
                , DATEADD(day, 90, GETDATE())
                , GETDATE()
                , @whitelabel_id
    );

    IF @auth_type = 2
    BEGIN
        UPDATE dbo.auth_users
        SET    EmailAddress = @original_username
        WHERE  UserId = @user_id;
    END;

    -- Insert unverified primary email contact
    IF @auth_type = 2
    BEGIN
        EXEC dbo.sync_user_contact_v2 @member_id, @contact_id, 1, @original_username, NULL, @modified_by;
    END;

    -- Insert unverified home contact for phone number
    IF @auth_type = 7
    BEGIN
        EXEC dbo.sync_user_contact_v2 @member_id, @contact_id, 3, @original_username, NULL, @modified_by;
    END;
        
    COMMIT TRANSACTION;
        
END
GO

GRANT EXECUTE ON [dbo].[sync_user_mappings_v5] TO [customer_api_user]
GO
     
        
        
