---------------------------------------------------------------------------------------------------
-- Author		      | Date		    | Comment
--------------------------------|-------------------------------------------------------------------------
-- Sirichai Ch.		| 2012-03-06	| Get all Department
-- Mrun S         | 2019-02-05  | Rename to backoffice_auth_user_department_all_v1
-------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_user_department_all_v1]
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[backoffice_auth_user_department_all_v1]

AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	
	SELECT	ad.department_id
		, ad.department_name
		, ad.rec_status
		, ad.rec_created_when
		, ad.rec_created_by
		, ad.rec_modified_when
		, ad.rec_modified_by
		, ISNULL(au.EmailAddress,'') 'EmailCreatedBy'
		, ISNULL(au2.EmailAddress,'') 'EmailModifiedBy'
	FROM	dbo.auth_user_department ad	
	LEFT JOIN	dbo.auth_users au
			ON au.userid = ad.rec_created_by  AND au.rec_status > 0
	LEFT JOIN	dbo.auth_users au2
			ON au2.userid = ad.rec_modified_by AND au2.rec_status > 0
	WHERE	ad.rec_status > 0
	ORDER	BY ad.department_name 


END

GO

GRANT EXECUTE ON [dbo].[backoffice_auth_user_department_all_v1] TO [customer_api_user]
GO
