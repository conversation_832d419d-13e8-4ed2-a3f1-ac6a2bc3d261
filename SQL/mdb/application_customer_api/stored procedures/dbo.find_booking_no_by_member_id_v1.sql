/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Weerapong M              | 2021-28-04    | Initail sp to count number of booking by member id
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[find_booking_no_by_member_id_v1] 100;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[find_booking_no_by_member_id_v1]
    @member_id int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT COUNT(1) as booking_no
    FROM dbo.ebe_itinerary
    WHERE memberid = @member_id

END
GO

GRANT EXECUTE ON [dbo].[find_booking_no_by_member_id_v1] TO [customer_api_user]
GO
