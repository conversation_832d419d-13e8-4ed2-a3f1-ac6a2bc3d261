---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Taweekiat P.		| 2016-02-29	| Split: Condition 1 of auth_user_in_role_get
-- A<PERSON>		| 2017-12-06	| Make authenticationType optional
-- Nattawat J.		| 2022-04-29	| Add whiteLabelId
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST EXEC dbo.auth_user_in_role_get_by_object_type_v2 '6EC5F244-8FAE-4200-974C-AAB9DCE88E54', 2, 'H', 1
---------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.auth_user_in_role_get_by_object_type_v2 '7FA5015F-5478-4E9E-AB0E-ABB93B5B36F8', 2, 'H', 1
REVERT;
END_EXEC_TEST
*/

CREATE PROCEDURE [dbo].[auth_user_in_role_get_by_object_type_v2]
	@roleId uniqueidentifier
	, @authenticationType int
	, @objectType char(1)
	, @whiteLabelId smallint
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED


			SELECT		DISTINCT 
						ar.UserId
						,r.RoleId
						,r.RoleName
			INTO		#role
			FROM		dbo.auth_user_in_role ar
			INNER JOIN	dbo.auth_roles r
							ON r.RoleId = ar.RoleId
			WHERE		ar.RoleId = @roleId
			AND			ar.ObjectType = @objectType
			AND			ar.rec_status > 0
			AND			r.rec_status > 0

  IF(@authenticationType IS NOT NULL)
  BEGIN
			SELECT		DISTINCT 
						r.UserId
						,au.DisplayName
						,au.Emailaddress
						,am.username
						,r.RoleId
						,r.RoleName
			FROM		dbo.auth_users au
			INNER JOIN	#role r
							ON r.UserId = au.UserID
			INNER JOIN	dbo.auth_user_mapping am
							ON au.UserId = am.user_id
			WHERE		au.whitelabel_id = ISNULL(@whiteLabelId, au.whitelabel_id)
			AND			au.rec_status > 0
			AND			am.authentication_type_id = @authenticationType
			AND			am.rec_status > 0
			OPTION (MAXDOP 1)
  END
  ELSE
  BEGIN
			SELECT		DISTINCT 
						r.UserId
						,au.DisplayName
						,au.Emailaddress
						,am.username
						,r.RoleId
						,r.RoleName
			FROM		dbo.auth_users au
			INNER JOIN	#role r
							ON r.UserId = au.UserID
			INNER JOIN	dbo.auth_user_mapping am
							ON au.UserId = am.user_id
			WHERE		au.whitelabel_id = ISNULL(@whiteLabelId, au.whitelabel_id)
			AND			au.rec_status > 0
			AND			am.rec_status > 0
			OPTION (MAXDOP 1)
  END

  DROP TABLE #role
END



GO

GRANT EXECUTE ON [dbo].[auth_user_in_role_get_by_object_type_v2] TO [customer_api_user]
GO