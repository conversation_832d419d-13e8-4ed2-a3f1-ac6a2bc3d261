
/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- Andrew <PERSON> 		| 12/12/2018	| First version
------------------------------------------------|---------------|------------------------------------------

MONKEY_TEST


DECLARE @memberids AS [id_table_type]
INSERT INTO @memberids (id)
SELECT   1
UNION 
SELECT   2
UNION 
SELECT   3
EXEC [dbo].[host_names] @memberids

END_MONKEY_TEST

------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[host_names]
    @memberIDs [id_table_type] READONLY
AS
BEGIN
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

SELECT	id as m
		,rew.first_name as f
		,rew.last_name as l
FROM @memberIDs m
LEFT JOIN	dbo.rew_members as rew
				ON m.id = rew.memberid
END

GO

GRANT EXECUTE ON [dbo].[host_names] TO [customer_api_user]
GO
