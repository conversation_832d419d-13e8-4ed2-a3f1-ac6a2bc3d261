/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 150

END_SQL_OBJECT_INFO
*/
------------------------------------------------------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|----------------------------------------------------------------------------------------------------------------
-- Andrew L       | 2018-09-01 | Initial
-- <PERSON><PERSON>.      | 2018-10-07 | v1, extend PII columns size WFAPI-2152, memberCode as string
-- Andrew L       | 2018-12-07 | v2 Add display name and skip v2 due to it being on QA
-- Andrew L       | 2019-01-28 | v3 Add role back for now
-- Ahmad S        | 2019-02-13 | v5 Add whitelabel_id
-- Yevhen V.	  | 2019-03-11 | Allow to override a member/contact id
-- Yevhen V.	  | 2019-04-03 | Add origin
-- Nimish J.	  | 2019-08-06 | Added new parameters to insert the Title and Blacklist status of a user being migrated from other whiteLabels
-- Weerapong M.	  | 2022-05-27 | v10, add rec_modify_when and by, Bump api_member_contact_insert_v1_4 to api_member_contact_insert_v1_5
-- Weerapong M.	  | 2022-07-22 | v11, add rec_modify_when and by, Bump api_member_contact_insert_v1_5 to api_member_contact_insert_v1_6
-- Weerapong M.	  | 2022-08-04 | v12, drop api_member_contact_insert_v1_6
-- Weerapong M.	  | 2022-12-02 | v13, fix default language id
-- Abhishek P.    | 2025-06-26 | v14, Updated to use auth_user_in_role_adduser_byroleid_v2
------------------|------------|----------------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
DECLARE @rec_created_when datetime = GETDATE()
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.api_customer_insert_simplified_v14 '<EMAIL>', '1f46ce05-a1fb-4f28-8521-47e443ab6661', 'first', 'last', '', '{}', 0, null, '1f46ce05-a1fb-4f28-8521-47e443ab6533', @rec_created_when, '1f46ce05-a1fb-4f28-8521-47e443ab6533', @rec_created_when, '<Result><Mappings><Username><EMAIL></Username><Uri>agoda</Uri><Type>2</Type><Verified>0</Verified></Mappings></Result>', 1, 'US', 90048866, 'Mr.', 1, 0
REVERT;
END_EXEC_TEST
*/
----------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[api_customer_insert_simplified_v14]
		 @email NVARCHAR(400)
		,@userId UNIQUEIDENTIFIER
		,@first_name NVARCHAR(255)
		,@last_name NVARCHAR(255)
		,@displayName varchar(400)
		,@properties VARCHAR(max)
		,@is_newsletter BIT
		,@mobile  NVARCHAR(100)
		,@rec_created_by UNIQUEIDENTIFIER
		,@rec_created_when DATETIME
		,@rec_modify_by UNIQUEIDENTIFIER
		,@rec_modify_when DATETIME
		,@uris xml
		,@whitelabel_id smallint
		,@origin char(2)
		,@memberId int = null
		,@title NVARCHAR(20) = null
        	,@rec_status int = 1
		,@isBlacklisted bit = 0
AS
BEGIN
		SET XACT_ABORT ON
		SET NOCOUNT ON
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

		DECLARE
@default_giftcard_status tinyint = 1
		,@default_giftcard_level tinyint = 0;


BEGIN TRY
BEGIN TRANSACTION

		INSERT INTO dbo.auth_users
		(
			UserId
			, DisplayName
			, EmailAddress
			, IsBlocked
			, IsDeleted
			, IsIpRestriced
			, IsGeoRestricted
			, FailedLoginAttempts
			, rec_status
			, rec_created_when
			, rec_created_by
			, rec_modified_when
			, rec_modified_by
			, whitelabel_id
		)
		VALUES
			(
				@userId
				, @displayName
				, @email
				, 0
				, 0
				, 0
				, 0
				, 0
				, @rec_status
				, @rec_created_when
				, @rec_created_by
				, @rec_modify_when
				, @rec_modify_by
				, @whitelabel_id
			)

		INSERT INTO dbo.auth_user_mapping
		(
			[user_id]
			, authentication_type_id
			, uri
			, username
			, is_verified
			, expired_since
			, rec_status
			, password_last_changed
			, whitelabel_id
		) SELECT
                    @UserID,
                    x.value('Type[1]','int') AS authentication_type_id,
                    x.value('Uri[1]','VARCHAR(512)') AS uri,
                    x.value('Username[1]','VARCHAR(400)') AS uri,
                    x.value('Verified[1]','tinyint') AS is_verified,
                    DATEADD(day, 9000, getdate()),
                    @rec_status,
                    @rec_created_when,
                    @whitelabel_id

          FROM @uris.nodes('//Mappings') XmlData(x)


		IF @memberId IS NOT NULL
BEGIN
				SET IDENTITY_INSERT agoda_core.dbo.rew_members ON;
INSERT INTO dbo.rew_members
(
  MemberID
, membercode
, first_name
, last_name
, properties
, nationality_id
, language_id
, is_newsletter
, rec_status
, rec_created_by
, rec_created_when
, rec_modify_by
, rec_modify_when
, is_point_eligible
, giftcard_status
, giftcard_level
, UserId
, lastupdated_when
, whitelabel_id
, origin
, title
, is_blacklisted
)
VALUES
    (
      @memberId
    , @userId
    , @first_name
    , @last_name
    , @properties
    , 0
    , 0
    , @is_newsletter
    , @rec_status
    , @rec_created_by
    , @rec_created_when
    , @rec_modify_by
    , @rec_modify_when
    , 0
    , @default_giftcard_status
    , @default_giftcard_level
    , @userId
    , @rec_created_when
    , @whitelabel_id
    , @origin
    , @title
    , @isBlacklisted
    );
SET IDENTITY_INSERT agoda_core.dbo.rew_members OFF;
END
ELSE
BEGIN
INSERT INTO dbo.rew_members
(
  membercode
, first_name
, last_name
, properties
, nationality_id
, language_id
, is_newsletter
, rec_status
, rec_created_by
, rec_created_when
, rec_modify_by
, rec_modify_when
, is_point_eligible
, giftcard_status
, giftcard_level
, UserId
, lastupdated_when
, whitelabel_id
, origin
, title
, is_blacklisted
)
VALUES
    (
      @userId
    , @first_name
    , @last_name
    , @properties
    , 0
    , 1
    , @is_newsletter
    , @rec_status
    , @rec_created_by
    , @rec_created_when
    , @rec_modify_by
    , @rec_modify_when
    , 0
    , @default_giftcard_status
    , @default_giftcard_level
    , @userId
    , @rec_created_when
    , @whitelabel_id
    , @origin
    , @title
    , @isBlacklisted

    );
SELECT @memberId = CAST(SCOPE_IDENTITY() AS INT);
END;

EXEC dbo.auth_user_in_role_adduser_byroleid_v2 '732F908B-2794-4A95-BABD-873EA675BA8E',@memberId,'R',@userId, @rec_created_by

		INSERT INTO dbo.rew_member_newsletter_status (
			memberId
			,[status]
			,rec_status
			,rec_created_by
			,rec_created_when
		)
		VALUES (
			@memberId
			,ISNULL(@is_newsletter, 0)
			,@rec_status
			,@rec_created_by
			,GETDATE()
		)

		COMMIT TRANSACTION

SELECT @memberId AS memberId
     ,CAST(@userId AS VARCHAR(255)) AS memberCode
     ,@userId as uuid
END TRY

BEGIN CATCH
			DECLARE @err_msg NVARCHAR(2048) = ERROR_MESSAGE ()

			IF XACT_STATE() <> 0
BEGIN
ROLLBACK TRANSACTION
END

			;THROW 51000, @err_msg, 1;
END CATCH
END
GO

GRANT EXECUTE ON [dbo].[api_customer_insert_simplified_v14] TO [customer_api_user]
GO
