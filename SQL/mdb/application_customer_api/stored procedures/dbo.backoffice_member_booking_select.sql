/*
-------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- Saran B.		| 2012-06-19	| select member's booking.
-- Chate C.		| 2012-08-30	| Refactoring code format.
					| Modified conversion (ON c.hotel_id = convert(int,h.[object_id]) )
-- Saran B.		| 2012-12-12	| Added the join clause for ebe_booking_hotel_room.
-- Saran B.		| 2012-12-24	| Added the no_of_rooms field.
-- Saran B.		| 2013-01-09	| Added geo2_city, geo2_country.
-- Krit B.		| 2015-10-19	| Added subtype_id = 0 for new hotel page review.
-- <PERSON><PERSON>.	| 2018-04-03	| Add TOP 20000 clause (WFAPI-1684)
----------------|---------------|----------------------------------------------------------
-- MONKEY_TEST: Exec [backoffice_member_booking_select] 3,  1
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[backoffice_member_booking_select]
	@memberId int, 
	@url_version int
AS
BEGIN

	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	WITH booking_cte 
	(
		  row_no
		, booking_id
		, credit_card_value
	)
	AS 
	(
			SELECT    ROW_NUMBER() OVER
						( 
							PARTITION BY	  A.MemberID
									, A.booking_id 
							ORDER BY	  A.point_activity_id DESC
						)	AS row_num
				, A.booking_id
				, ISNULL(credit_card_value, 0)
			FROM    dbo.rew2_point_activity AS A 	
			WHERE   A.point_activity_subtype_id = 10
			AND	A.MemberID = @memberId
			AND	A.rec_status = 1
	)
	
	SELECT TOP 20000 a.booking_id
			, b.booking_date
			, c.checkin_date
			, c.checkout_date
			, d.room_type_name
			, a.credit_card_value
			, dbo.fn_rew2_ebe_booking_status_id(a.booking_id)		AS booking_status_id
			, ISNULL(dbo.fn_rew2_ebe_booking_status(a.booking_id),  '')	AS Booking_status
			, b.cancellation_policy_code
			, b.cancellation_policy
			, c.hotel_id
			, e.hotel_name
			, f.address_i
			, f.address_ii
			, city.city_name as city
			, country.country_name as country
			, g.Is_review
			, @memberId AS memberId
			, h.url
			, i.last4digits
			, cardholder_name
			, g.hotel_review_id
			, c.no_of_rooms
	FROM		booking_cte a 
	INNER JOIN 	dbo.ebe_booking b
				ON a.booking_id = b.booking_id
	INNER JOIN 	dbo.ebe_booking_hotel c
				ON a.booking_id = c.booking_id
	INNER JOIN 	dbo.ebe_booking_hotel_room d
				ON a.booking_id = d.booking_id
				AND d.room_no = 1
	INNER JOIN 	dbo.product_hotels e
				ON c.hotel_id = e.hotel_id
	INNER JOIN 	dbo.product_address f
				ON c.hotel_id = f.product_id 
				AND address_type_id = 2
	LEFT JOIN dbo.geo2_city city 
				ON city.city_id = e.city_id 
	LEFT JOIN dbo.geo2_country country 
				ON country.country_id = e.country_id 
	INNER JOIN 	dbo.ebe_booking_creditcards i
				ON b.itinerary_id = i.itinerary_id
	LEFT JOIN  	dbo.rew_review_pending g
				ON a.booking_id = g.booking_id
	LEFT JOIN  	dbo.afm_urlmapping h
				ON c.hotel_id = h.[object_id] 
				AND h.mappingtype_id = 7 
				AND h.subtype_id = 0
				AND h.storefront_id = 3 
				AND redirect_urlmapping_id IS NULL 
				AND url_version = @url_version 
	
	WHERE		a.row_no = 1
	ORDER BY	a.booking_id
	
END






GO

GRANT EXECUTE ON [dbo].[backoffice_member_booking_select] TO [customer_api_user]
GO
