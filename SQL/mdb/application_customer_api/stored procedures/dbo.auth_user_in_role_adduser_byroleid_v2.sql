/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 750

END_SQL_OBJECT_INFO
*/
---------------------------------------------------------------------------------------------------
-- Author				        | Date		    | Comment
--------------------------------|---------------|------------------------------------------
-- Sir<PERSON><PERSON>		| 2011-10-19	| Add new user to auth_user_in_role by roleid
-- Gy<PERSON><PERSON>				    | 2016-01-22	|	Transaction was removed
-- <PERSON><PERSON><PERSON><PERSON>				    | 2017-02-07	|	Track changes
-- Ab<PERSON><PERSON><PERSON> P.                  | 2025-06-26    | Added validation for ObjectType 'K' - only one ObjectId per UserId
------------------------|---------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.auth_user_in_role_adduser_byroleid_v2 '00000000-0000-0000-0000-000000000000', 71656, 'H', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000'
REVERT;
END_EXEC_TEST
*/
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_user_in_role_adduser_byroleid_v2]
	@roleid uniqueidentifier
	, @objectId int
	, @objecttype char
	, @userId uniqueidentifier
	, @rec_created_by uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON;
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

-- Validate that for ObjectType 'K', each UserId can only have one ObjectId
IF @objecttype = 'K'
BEGIN
		IF EXISTS (
			SELECT 1
			FROM dbo.auth_user_in_role t
			WHERE t.UserId = @userId
			  AND t.ObjectType = 'K'
			  AND t.ObjectID <> @objectId
			  AND t.rec_status = 1
		)
BEGIN
			 THROW 50000, 'A UserId can only have one ObjectId for ObjectType = ''K''.', 1;
			RETURN;
END
END

	IF EXISTS
	(
		SELECT	*
		FROM	dbo.auth_user_in_role
		WHERE	Userid = @userId
		AND	RoleId = @roleId
		AND	ObjectType = @objecttype
		AND	ObjectID = @objectId
	)
BEGIN
UPDATE	dbo.auth_user_in_role
SET	rec_status = 1
  , rec_modified_when = GETDATE()
  , rec_modified_by = @rec_created_by
WHERE	Userid = @userId
  AND	RoleId = @roleId
  AND	ObjectType = @objecttype
  AND	ObjectID = @objectId
END
ELSE
BEGIN
INSERT INTO	dbo.auth_user_in_role
(
  UserId
, RoleId
, ObjectType
, ObjectID
, rec_status
, rec_created_when
, rec_created_by
)
VALUES
    (
      @userId
    , @RoleId
    , @objectType
    , @objectId
    , 1
    , GETDATE()
    , @rec_created_by
    )
END
END
GO

GRANT EXECUTE ON [dbo].[auth_user_in_role_adduser_byroleid_v2] TO [customer_api_user]
GO
