-------------------------------------------------------------------------------------------
-- Author		| Date			| Comments   
----------------|---------------|----------------------------------------------------------
-- Andrew <PERSON>.| 2019-12-03	| Initial version
-------------------------------------------------------------------------------------------
/*
EXEC_TEST
    EXECUTE AS LOGIN = 'customer_api_user'
    EXEC dbo.blacklist_insert @revoked = '2019-01-01', @token_id = 'token_id', @user_id = '00000000-0000-0000-1111-111111111111'
    REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[blacklist_insert]
    @revoked        datetime
    , @token_id     varchar(1000)
    , @user_id      uniqueidentifier
AS
BEGIN

    SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED


    INSERT INTO dbo.capi_blacklisted_tokens
    (
        revoked
        , token_id
        , user_id
    )
    VALUES   
    (
        @revoked
        , @token_id
        , @user_id
    )

END
GO

GRANT EXECUTE ON [dbo].[blacklist_insert] TO [customer_api_user]
GO
