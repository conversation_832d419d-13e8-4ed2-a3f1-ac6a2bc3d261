---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- E<PERSON><PERSON>            | 2011-11-25  | Select users to move to relations
-- <PERSON>      | 2011-12-28  | Refactor for coding standards
-- Kit<PERSON><PERSON> | 2019-01-17  | Remove @UserId from the parameter
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.backoffice_select_ldap_users_v1
REVERT
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_select_ldap_users_v1]
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT  username AS UserName
        , user_id AS UserId
    FROM    dbo.auth_user_mapping
    WHERE   authentication_type_id = 1
    AND rec_status = 1
    ORDER   BY UserName
END
GO

GRANT EXECUTE ON [dbo].[backoffice_select_ldap_users_v1] TO [customer_api_user]
GO
