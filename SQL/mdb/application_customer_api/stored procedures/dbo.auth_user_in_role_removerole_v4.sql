/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 5

END_SQL_OBJECT_INFO
*/
---------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|------------------------------------------------------
-- Sirichai C.              | 2012-08-07    | Security API- remove user from role
-- GyÃƒÂ¶rgy <PERSON>          | 2017-02-06    | Formatting to standards
-- GyÃƒÂ¶rgy <PERSON>          | 2017-02-07    | Track changes
-- Siravitch Ch.            | 2017-12-15    | Add Xact_abort on
-- Kit<PERSON><PERSON>     | 2018-01-29    | Dump inserted auth_assigned_role_audit as a result
--                          |               | Combine the separated versions to be only one version
--                          |               | Add @auditTable as a parameter
-- Nutthawat Pradujdecha    | 2019-05-15    | Add whitelabel support \
-- Abhishek Pandey          | 2024-11-07    | Add support for User Shadow CM Portal
----------------------------|---------------|------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[auth_user_in_role_removerole_v4]
   @roleid = '15F01F08-C81A-4D2D-8607-389FE4C46A5B'
    , @objectId = 12548
    , @objecttype ='H'
    , @userId = '15F01F08-C81A-4D2D-8607-389FE4C46A5B'
    , @removeBy = '15F01F08-C81A-4D2D-8607-389FE4C46A5B'
    , @whitelabel_id = 1
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_user_in_role_removerole_v4]
(
  @roleid         uniqueidentifier
  , @objectId     int
  , @objecttype   char
  , @userId       uniqueidentifier
  , @removeBy     uniqueidentifier
  , @whitelabel_id int
  , @auditString  NVARCHAR(MAX) = NULL
)
AS
BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	    DECLARE @parentUserId UNIQUEIDENTIFIER

            SELECT @parentUserId = parent_user_id
            FROM dbo.cm_portal_user_mapping
            WHERE child_user_id = @userId
            AND rec_status = 1

        IF @parentUserId IS NOT NULL
        BEGIN
            SET @userId = @parentUserId
        END

	--Result
	--0, success
	--1, removeby id is not exists.
	
	DECLARE @result smallint = 0
		, @cr nchar(2) = NCHAR(13) + NCHAR(10)


	
	CREATE TABLE #temp (
	    Id                   [int]
	  , UserId             [uniqueidentifier]
	  , AssignDate         [datetime]
	  , AssignedRoleId     [uniqueidentifier]
	  , AssignedUserId     [uniqueidentifier]
	  , AssignedObjectType [char](1)
	  , AssignedObjectId   [int]
	  , ActivityId         [int]
	  , rec_status         [int]
	  , rec_created_by     [uniqueidentifier]
	  , rec_created_when   [datetime]
	)

    DECLARE @userWhitelabelId int = ( SELECT whitelabel_id FROM dbo.auth_users WHERE UserId = @userId )
    DECLARE @notNull_whitelabel_id int = ISNULL(@whitelabel_id, 1)
	BEGIN TRY
	IF EXISTS (SELECT * FROM dbo.auth_users WHERE USERID = @removeBy AND rec_status = 1 AND whitelabel_id = @notNull_whitelabel_id)
	BEGIN
		BEGIN TRAN
			-- Transform the @auditString to be data in #temp table
			IF @auditString IS NOT NULL
			BEGIN
				SET @auditString = 'SELECT '+ @auditString
				SET @auditString = REPLACE (@auditString,'|', ' UNION SELECT ')
				SET @auditString = 'INSERT INTO #temp' + @cr
				+ ' (' + @cr
				+ '  Id' + @cr
				+ '  , UserId' + @cr
				+ '  , AssignDate' + @cr
				+ '  , AssignedRoleId' + @cr
				+ '  , AssignedUserId' + @cr
				+ '  , AssignedObjectType' + @cr
				+ '  , AssignedObjectId' + @cr
				+ '  , ActivityId' + @cr
				+ '  , rec_status' + @cr
				+ '  , rec_created_by' + @cr
				+ '  , rec_created_when' + @cr
				+ ' )' + @cr + @auditString

			EXECUTE sp_executesql @auditString
			END
	
		-- Log into auth_assigned_role_audit
		-- By checking whether there is the audit data from MDB or not
		IF EXISTS ( SELECT 1 FROM #temp )
		BEGIN
		
			SET IDENTITY_INSERT Agoda_core.dbo.auth_assigned_role_audit ON

			INSERT INTO dbo.auth_assigned_role_audit
			(
			  Id
			  , UserId
			  , AssignDate
			  , AssignedRoleId
			  , AssignedUserId
			  , AssignedObjectType
			  , AssignedObjectId
			  , ActivityId
			  , rec_status
			  , rec_created_by
			  , rec_created_when
			)
			SELECT Id
			       , UserId
			       , AssignDate
			       , AssignedRoleId
			       , AssignedUserId
			       , AssignedObjectType
			       , AssignedObjectId
			       , ActivityId
			       , rec_status
			       , rec_created_by
			       , rec_created_when
			FROM #temp

			SET IDENTITY_INSERT Agoda_core.dbo.auth_assigned_role_audit OFF
			
		END
		ELSE
		BEGIN
			INSERT INTO dbo.auth_assigned_role_audit
			(
			  UserId
			  , AssignDate
			  , AssignedRoleId
			  , AssignedUserId
			  , AssignedObjectType
			  , AssignedObjectId
			  , ActivityId
			  , rec_status
			  , rec_created_by
			  , rec_created_when
			)
			OUTPUT
			  INSERTED.Id
			  , INSERTED.UserId
			  , INSERTED.AssignDate
			  , INSERTED.AssignedRoleId
			  , INSERTED.AssignedUserId
			  , INSERTED.AssignedObjectType
			  , INSERTED.AssignedObjectId
			  , INSERTED.ActivityId
			  , INSERTED.rec_status
			  , INSERTED.rec_created_by
			  , INSERTED.rec_created_when
			INTO #temp
			(
			  Id
			  , UserId
			  , AssignDate
			  , AssignedRoleId
			  , AssignedUserId
			  , AssignedObjectType
			  , AssignedObjectId
			  , ActivityId
			  , rec_status
			  , rec_created_by
			  , rec_created_when
			)
			SELECT  @removeBy
			        , GETDATE()
			        , @roleid
			        , @userId --removed userid
			        , objecttype
			        , objectid
			        , 11 --Remove role (API)
			        , 1
			        , @removeBy
			        , GETDATE()
			FROM  dbo.auth_user_in_role
			WHERE Userid = @userId
			AND   RoleId = @roleId
			AND   (@objecttype IS NULL OR ObjectType = @objecttype)
			AND   (@objectId IS NULL OR ObjectId = @objectId)
		END
		
		INSERT INTO dbo.ycs4_hotel_sensitive_change_tracking
		(
		  hotel_id
		  , [type_id]
		  , new_value
		  , remark
		  , created_by
		  , created_when
		)
		SELECT  uir.objectId
		  , hst.[type_id]
		  , 'user_id: ' + CONVERT(varchar(36), @userId) + ' | role_id: ' + CONVERT(varchar(36), @roleId)
		  , 'Deleted'
		  , @removeBy
		  , GETDATE()
		FROM  dbo.ycs4_hotel_sensitive_type AS hst
		INNER JOIN  dbo.auth_user_in_role AS uir
		      ON    uir.RoleId = @roleId
		      AND   uir.UserId = @userId
		      AND   (@objecttype IS NULL OR uir.ObjectType = @objecttype)
		      AND   (@objectId IS NULL OR uir.ObjectId = @objectId)
		WHERE hst.source_type = 'auth_user_in_role'
		AND   hst.ref_source_type_id = 1
		AND   uir.ObjectType = 'H'

        IF (ISNULL(@userWhitelabelId, 1) = @notNull_whitelabel_id)
        BEGIN
            DELETE FROM dbo.auth_user_in_role
            WHERE Userid = @userId
            AND   RoleId = @roleId
            AND   (@objecttype IS NULL OR ObjectType = @objecttype)
            AND   (@objectId IS NULL OR ObjectId = @objectId)
        END
		ELSE
        BEGIN
            SET @result = 2 --userid user is not exists.
        END
		COMMIT TRAN
	END
	ELSE
	BEGIN
		SET @result = 1 --removeby user is not exists.
	END
	END TRY
	BEGIN CATCH
		DECLARE @err_msg nvarchar(2048) = ERROR_MESSAGE()
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRAN
		END
		;THROW 51000, @err_msg, 1;
END CATCH
	
	SELECT @result AS RESULT
	
	SELECT	Id                 
		,UserId             
		,AssignDate         
		,AssignedRoleId     
		,AssignedUserId     
		,AssignedObjectType 
		,AssignedObjectId   
		,ActivityId         
		,rec_status         
		,rec_created_by     
		,rec_created_when   
	FROM #temp
	
	DROP TABLE #temp
	
END
GO

GRANT SELECT ON [dbo].[cm_portal_user_mapping] TO [customer_api_user]
GO

GRANT SELECT ON [dbo].[ycs4_hotel_sensitive_change_tracking] TO [customer_api_user]
GO

GRANT SELECT ON [dbo].[ycs4_hotel_sensitive_type] TO [customer_api_user]
GO

GRANT INSERT ON [dbo].[auth_assigned_role_audit] TO [customer_api_user]
GO

GRANT INSERT ON [dbo].[ycs4_hotel_sensitive_change_tracking] TO [customer_api_user]
GO

GRANT EXECUTE ON [dbo].[auth_user_in_role_removerole_v4] TO [customer_api_user]
GO
