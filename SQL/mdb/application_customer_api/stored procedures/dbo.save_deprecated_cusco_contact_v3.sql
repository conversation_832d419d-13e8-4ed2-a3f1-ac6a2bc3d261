/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>           | 2019-06-10    | Replaces the unverified contact for the given user.
-- Adi <PERSON>           | 2021-03-23    | Add logic to only update member_id from 0 or NULL value
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @random_contact_id int = ABS(CHECKSUM(NewId())) % 2147483647;
EXEC [dbo].[save_deprecated_cusco_contact_v3] @random_contact_id, 1, 1, 'test', 'remark', 1, '00000000-0000-0000-0000-000000000000', 'US', 1;
REVERT;
END_EXEC_TEST

-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_deprecated_cusco_contact_v3]
    @contact_id int,
    @member_id int,
    @contact_type int,
    @contact_value nvarchar(400),
    @contact_remark nvarchar(512),
    @rec_status int,
    @modified_by uniqueidentifier,
    @origin char(2),
    @whitelabel_id smallint
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @existing_contact_type int = (SELECT contact_method_id FROM dbo.rew_contacts WHERE contact_id = @contact_id);
    DECLARE @existing_contact_value nvarchar(400) = (SELECT contact_method_value FROM dbo.rew_contacts WHERE contact_id = @contact_id);

    IF @existing_contact_type IS NOT NULL
    BEGIN
        IF @existing_contact_type = 1 AND (@existing_contact_type <> @contact_type OR @existing_contact_value <> @contact_value)
        BEGIN
            DECLARE @error_msg varchar(max) = CONCAT('Primary email can not be changed for the contact id #', @contact_id);
            THROW 50001, @error_msg, 1;
        END;

        UPDATE dbo.rew_contacts
        SET    contact_method_id = @contact_type
               , memberId = IIF(memberId = 0 OR memberId IS NULL, @member_id, memberId)
               , contact_method_value = @contact_value
               , contact_method_remark = @contact_remark
               , rec_status = @rec_status
               , rec_modify_by = @modified_by
               , rec_modify_when = GETDATE()
               , lastupdated_when = GETDATE()
        WHERE  contact_id = @contact_id;
    END
    ELSE
    BEGIN
        SET IDENTITY_INSERT agoda_core.dbo.rew_contacts ON;
        INSERT INTO dbo.rew_contacts
        (
                    contact_id
                    , memberId
                    , contact_method_id
                    , contact_method_value
                    , contact_method_remark
                    , rec_status
                    , rec_created_by
                    , rec_created_when
                    , lastupdated_when
                    , whitelabel_id
                    , origin
        )
        VALUES
        (
                    @contact_id
                    , @member_id
                    , @contact_type
                    , @contact_value
                    , @contact_remark
                    , @rec_status
                    , @modified_by
                    , GETDATE()
                    , GETDATE()
                    , @whitelabel_id
                    , @origin
        );
        SET IDENTITY_INSERT agoda_core.dbo.rew_contacts OFF;
    END;

    SELECT contact_id
           , memberId
           , contact_method_id
           , contact_method_value
           , contact_method_remark
           , rec_status
           , rec_created_by
           , rec_created_when
           , rec_modify_by AS rec_modified_by
           , rec_modify_when AS rec_modified_when
           , whitelabel_id
    FROM   dbo.rew_contacts
    WHERE  contact_id = @contact_id;
END
GO

GRANT EXECUTE ON [dbo].[save_deprecated_cusco_contact_v3] TO [customer_api_user]
GO
