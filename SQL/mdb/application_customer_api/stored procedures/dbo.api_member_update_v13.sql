/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1k

END_SQL_OBJECT_INFO
*/
-----------------------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|---------------------------------------------------------------------------------
-- Saran B.       | 2012-05-22 | update reward members.
-- <PERSON><PERSON><PERSON><PERSON> S.    | 2012-08-30 | reformat code to meet standard
-- <PERSON><PERSON>    | 2014-03-25 | #19438 Created V2 to include the two new columns is_point_eligible and is_cc_onfile_opt_out
-- Rati P         | 2017-10-18 | To support MDC async
-- Rati P         | 2017-10-19 | do not insert rew_member_newsletter_status if @is_newsletter is null
-- Natavit R.     | 2018-06-08 | V4, update lastupdated_when column
-- Andrey S.      | 2018-10-07 | v5, extend PII columns size WFAPI-2152
-- Andrey S.      | 2018-12-04 | v6, sync with DisplayName
-- Andrey S.      | 2018-12-04 | v8, Legacy properties for now
-- Yevhen V.      | 2019-07-18 | avoid duplicate records in rew_member_newsletter_status
-- Nimish J.      | 2019-08-22 | added title field to
-- Andrey S.      | 2020-08-04 | move newsletter out of transaction
-- Weerapong M.   | 2025-01-17 | add wallet_birthdate_hash and wallet_country_hash on table rew_members
------------------|------------|---------------------------------------------------------------------------------
/* 
EXEC_TEST 
	DECLARE @rec_created_when datetime = GETDATE()
	EXECUTE AS LOGIN ='customer_api_user'
	EXEC [dbo].[api_member_update_v13] 4, 'Etan', 'Quek','Etan Quek',0,null,null,0,1,'{}', 0, 1,@rec_created_when,'44AD1786-3570-4510-B8B9-BB35C7C78B08','','44AD1786-3570-4510-B8B9-BB35C7C78B08',null,null
	REVERT;
END_EXEC_TEST
*/
----------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[api_member_update_v13]
	@memberId int
	, @first_name nvarchar(255)
	, @last_name nvarchar(255)
	, @displayName varchar(400) --Remove this next version
	, @ccof bit --Remove this next version
	, @birthday SMALLDATETIME --Remove this next version
	, @prius INT --Remove this next version
	, @nationalityId INT --Remove this next version
	, @languageId INT --Remove this next version
	, @properties varchar(max)
	, @is_newsletter bit
	, @rec_status int
	, @when DATETIME
	, @rec_modified_by uniqueidentifier
	, @title varchar(20) = ''
	, @userId uniqueidentifier
	, @wallet_birthdate_hash binary(16) = NULL
  , @wallet_country_hash binary(16) = NULL
AS
BEGIN
  SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	BEGIN TRY
		BEGIN TRANSACTION

			UPDATE	dbo.rew_members
			SET	first_name = @first_name
				, last_name = @last_name
				, properties = @properties
				, is_cc_onfile_opt_out = @ccof
				, birth_date = @birthday
				, prefer_partner_loyalty_program_id = @prius
				, nationality_id = @nationalityId
				, language_id = @languageId
				, is_newsletter = @is_newsletter
				, rec_status = @rec_status
				, rec_modify_by = @rec_modified_by
				, rec_modify_when = @when
				, lastupdated_when = @when
				, title = @title
				, wallet_birthdate_hash = @wallet_birthdate_hash
				, wallet_country_hash = @wallet_country_hash
			WHERE	MemberID = @memberId

			--Remove this next version
			UPDATE dbo.auth_users set DisplayName = @displayName, rec_modified_when = @when where userid = @userId

		COMMIT TRANSACTION
	END TRY

	BEGIN CATCH
		IF XACT_STATE() <> 0
			BEGIN
				ROLLBACK TRANSACTION
			END
        ;THROW;
	END CATCH

    UPDATE dbo.rew_member_newsletter_status
    SET    rec_status = 1
         , [status] = @is_newsletter
         , rec_modified_by = @rec_modified_by
         , rec_modified_when = @when
    WHERE  memberId = @memberId;

    IF @@ROWCOUNT = 0
    BEGIN
            INSERT INTO dbo.rew_member_newsletter_status
            (
                memberId
                , [status]
                , rec_status
                , rec_created_by
                , rec_created_when
            )
            VALUES
            (
                @memberId
                , @is_newsletter
                , 1
                , @rec_modified_by
                , @when
            )
    END;

END
GO

GRANT EXECUTE ON [dbo].[api_member_update_v13] TO [customer_api_user]
GO
