


---------------------------------------------------------------------------------------------------
-- Author		      | Date		            | Comment
------------------|---------------------|----------------------------------------------------------
-- Sir<PERSON>ai Ch.		| 2012-03-08	        | Get all Location
-- Mrun S         | 2019-02-05          | Rename to backoffice_auth_user_location_all_v1
-------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_user_location_all_v1]
REVERT;
END_EXEC_TEST
*/
------------------------|---------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[backoffice_auth_user_location_all_v1]

AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	
	SELECT     al.location_id, al.location_name, al.rec_status, al.rec_created_when, al.rec_created_by, al.rec_modified_when, al.rec_modified_by,
		   ISNULL(au.EmailAddress,'') 'EmailCreatedBy',
		   ISNULL(au2.EmailAddress,'') 'EmailModifiedBy'
	FROM       dbo.auth_user_location al	
	LEFT JOIN	dbo.auth_users au
			ON au.userid = al.rec_created_by AND au.rec_status > 0
	LEFT JOIN	dbo.auth_users au2
			ON au2.userid = al.rec_modified_by  AND au2.rec_status > 0
	WHERE al.rec_status > 0
	ORDER BY al.location_name 


END




GO

GRANT EXECUTE ON [dbo].[backoffice_auth_user_location_all_v1] TO [customer_api_user]
GO
