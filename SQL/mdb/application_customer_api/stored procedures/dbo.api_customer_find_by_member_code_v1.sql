/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2018-12-20    | Fetch an user identifier by member code.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[api_customer_find_by_member_code_v1] '1111';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[api_customer_find_by_member_code_v1]
    @member_code varchar(50)
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT UserId
           , MemberId
           , rec_status
    FROM   dbo.rew_members
    WHERE  MemberCode = @member_code;
END
GO

GRANT EXECUTE ON [dbo].[api_customer_find_by_member_code_v1] TO [customer_api_user]
GO
