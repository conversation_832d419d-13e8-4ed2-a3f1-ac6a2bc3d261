/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>atulin           | 2019-06-06    | Deletes the verified contacts for the given users.
-- Yevhen Vatulin           | 2019-07-19    | Don't delete the unverified phone because of NHA flow
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[delete_user_mappings_v3] DEFAULT, 1;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[delete_user_mappings_v3]
    @users_to_unassign dbo.uuid_table_type READONLY,
    @auth_type int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    BEGIN TRANSACTION;
    
    DELETE     aum
    FROM	     dbo.auth_user_mapping aum
    INNER JOIN @users_to_unassign u
    ON         aum.user_id = u.uuid
    WHERE      aum.authentication_type_id = @auth_type;

    -- Delete unverified primary email contact
    IF @auth_type = 2
    BEGIN
        DELETE     rc
        FROM	     dbo.rew_contacts rc
        INNER JOIN dbo.rew_members rm
                ON rm.MemberID = rc.MemberID
        INNER JOIN @users_to_unassign u
                ON u.uuid = rm.UserId
        WHERE      rc.contact_method_id = 1;
    END;
    
    COMMIT TRANSACTION;
END
GO

GRANT EXECUTE ON [dbo].[delete_user_mappings_v3] TO [customer_api_user]
GO
