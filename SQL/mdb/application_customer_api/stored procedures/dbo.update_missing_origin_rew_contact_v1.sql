/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Anura<PERSON>         | 2021-14-04    | Update missing origin on CAPI rew_contact MDB table
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
	  DECLARE @origin_updates user_origin_update_contacts_type
	  DECLARE @modified_when smalldatetime
	  SET @modified_when = GETDATE()
	  INSERT INTO @origin_updates ( contact_id,origin_to_update)
	  VALUES (1234, 'TH')
EXEC [dbo].[update_missing_origin_rew_contact_v1] @origin_updates,@modified_when;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[update_missing_origin_rew_contact_v1]
    @origin_updates dbo.user_origin_update_contacts_type READONLY,
    @modified_when smalldatetime
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    DECLARE @modified_by uniqueidentifier
    SET @modified_by = '1f46ce05-a1fb-4f28-8521-47e443ab6533'

    /* Update origin in rew_contacts table if missing*/
    UPDATE     rwc
    SET        rwc.origin = originup.origin_to_update,
               rwc.rec_modify_when = @modified_when,
               rwc.rec_modify_by = @modified_by
    FROM       dbo.rew_contacts AS rwc
    INNER JOIN @origin_updates AS originup
    ON         originup.contact_id = rwc.contact_id
    WHERE rwc.origin = 'XX' OR rwc.origin IS NULL

END
GO

GRANT EXECUTE ON [dbo].[update_missing_origin_rew_contact_v1] TO [customer_api_user]
GO
