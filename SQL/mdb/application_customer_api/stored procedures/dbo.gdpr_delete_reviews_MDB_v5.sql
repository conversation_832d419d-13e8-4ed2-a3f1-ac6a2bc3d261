-------------------------------------------------------------------------------------------------------------------------------------
-- Name				| Date			| Desc
-------------------------------------------------------------------------------------------------------------------------------------
--J <PERSON><PERSON><PERSON>		| 2018-03-16	| delete 3 review tables with pii, only for restriction endpoint
-- Rati P			| 2018-03-20	| Performance Tuning 
-------------------------------------------------------------------------------------------------------------------------------------
-- Test : EXEC dbo.[gdpr_delete_reviews_MDB_v5] 6, '8D3F6DA3-5BC6-498A-88B9-AA2B49387B4F'
-------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[gdpr_delete_reviews_MDB_v5]
(
	@MemberID int
	, @requester uniqueidentifier
)
AS
BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @err_msg nvarchar(2000) = ''
		, @err_number int = 0

	BEGIN TRY
		BEGIN TRANSACTION
		IF EXISTS (SELECT * FROM dbo.rew_members WHERE MemberID = @MemberID)
		    BEGIN

		            SELECT user_review_id
                    INTO     #REVIEW
                    FROM dbo.review_user_reviews
                    WHERE member_id = @MemberID

                    UPDATE rev
                    SET member_name = ''
                        , review_title = ''
                        , review_positives = ''
                        , review_negatives = ''
                        , review_comments = ''
                        , review_tip_text = ''
                        , rec_status = -1
                        , rec_modify_by = 'CAPIErasure'
                        , rec_modify_when = GETDATE()
                    FROM dbo.rev_hotels_v3 rev
                    WHERE   EXISTS (SELECT * FROM #REVIEW r WHERE rev.user_review_id = r.user_review_id)
                    UPDATE rev
                    SET member_firstname = ''
                        , member_lastname = ''
                        , rec_status = -1
                        , rec_modified_when = GETDATE()
                        , rec_modified_by = @requester
                    FROM dbo.review_user_reviews AS rev
                    INNER JOIN #REVIEW AS r
                        ON rev.user_review_id = r.user_review_id
                    UPDATE  ra
                    SET ra.review_answer_content = ''
                        , ra.rec_modified_when = GETDATE()
                        , ra.rec_modified_by = @requester
                        , ra.rec_status = -1
                    FROM    dbo.review_user_answers ra
                    INNER JOIN dbo.review_migration_key_mapping m
                        ON ra.review_question_id = m.question_id
                    INNER JOIN  dbo.review_migration_key k
                            ON  m.migration_key_id = k.migration_key_id
                    WHERE k.migration_key_id BETWEEN 10 and 12
                    AND EXISTS (SELECT * FROM #REVIEW r WHERE ra.user_review_id = r.user_review_id)

			        SELECT @err_msg AS err_msg, @err_number AS err_number
		    END
		ELSE BEGIN
			SELECT 'There is no MemberID = ' + CONVERT(varchar(10),@MemberID) + ' exists in the dbo.rew_members' AS err_msg
				, 998 AS err_number
		END
		COMMIT TRANSACTION

	END TRY
	BEGIN CATCH

		IF XACT_STATE() <> 0
			BEGIN
			    ROLLBACK TRANSACTION
			END

		SET @err_msg = ERROR_MESSAGE()
		SET @err_number = ERROR_NUMBER()
		RAISERROR(@err_msg, 16, 1)
	END CATCH

END




GO

GRANT EXECUTE ON [dbo].[gdpr_delete_reviews_MDB_v5] TO [customer_api_user]
GO
