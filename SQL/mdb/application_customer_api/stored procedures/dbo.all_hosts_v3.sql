/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- <PERSON> 		| 15/05/2018	| Get NHA hosts
-- <PERSON> 		| 09/07/2018	| Filter hotels
-- <PERSON> 		| 14/02/2019	| Only return user id
------------------------------------------------|---------------|------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC  [dbo].[all_hosts_v3]
REVERT;
END_EXEC_TEST

------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[all_hosts_v3]
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
     DISTINCT aur.userid AS u
     ,rew.MemberId AS m
     ,rew.first_name AS f
     ,rew.last_name AS l
    FROM dbo.auth_user_in_role AS aur 
    INNER join dbo.rew_members as rew 
          on aur.UserId = rew.UserId
    WHERE roleid IN ('86a9719d-cab0-43ff-bcc1-b3a490375257', 'a2c5af8b-90e9-47d6-a75d-b6e0805e9ce3') 
    and aur.rec_status = 1

  END

GO

GRANT EXECUTE ON [dbo].[all_hosts_v3] TO [customer_api_user]
GO
