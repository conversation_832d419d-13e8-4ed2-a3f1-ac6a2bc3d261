
----------------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
----------------------------------------------------------------------------------------------------------
-- Pipat J.		| 2012-02-09	| To update the data from auth_user_relations to auth_user_in_role
-- GyÃ¶rgy <PERSON>		| 2015-04-07	| Formatting to coding standards, added distinct to the select
-- GyÃ¶rgy <PERSON>		| 2015-09-28	| Rewrite using merge
-- Atiruj P.		| 2016-05-11	| Rewrite to use update and insert
-- GyÃ¶rgy <PERSON>		| 2017-02-03	| Track changes
-- <PERSON><PERSON><PERSON>	| 2017-02-22	| Romoved test code
-- Su<PERSON><PERSON>	| 2017-05-02	| Changed to ensure created_by is not null
----------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_upload_auth_user_relations_finalize_v1]
REVERT;
END_EXEC_TEST
*/
----------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_upload_auth_user_relations_finalize_v1]
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	DECLARE @ObjectType char(1) = 'H'
		, @roleid uniqueidentifier = '78ABBE95-B5DF-4894-839C-BDC4F8B65BB9'

	CREATE TABLE #temp_auth_user_relations
	(
		UserId uniqueidentifier NOT NULL
		, roleid uniqueidentifier NOT NULL
		, ObjectType char(1) NOT NULL
		, ObjectId  int NOT NULL
		, rec_status	int NOT NULL
		, rec_created_when	datetime NOT NULL
		, rec_created_by	uniqueidentifier NOT NULL
		, rec_modified_when	datetime NULL
		, rec_modified_by	uniqueidentifier NULL
		, PRIMARY KEY(UserId, RoleId, ObjectType, ObjectID)
	)
	INSERT INTO  #temp_auth_user_relations
	(
			UserId 
			, roleid
			, ObjectType
			, ObjectId  
			, rec_status	
			, rec_created_by	
			, rec_created_when	
			, rec_modified_by	
			, rec_modified_when	
	)
	SELECT DISTINCT	ur.UserId
			, @roleid AS roleid
			, ur.ObjectType 
			, ur.ObjectId 
			, ur.rec_status
			, ur.rec_created_by 
			, ur.rec_created_when
			, ur.rec_modified_by
			, ur.rec_modified_when
	FROM		dbo.auth_user_relations AS ur
	WHERE		ur.ObjectType = @ObjectType 

	INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
	(
			hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	)
	SELECT		SRC.ObjectId
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), SRC.UserId) + ' | role_id: ' + CONVERT(varchar(36), SRC.RoleId)
			, CASE WHEN SRC.rec_status = 1 THEN 'Added' ELSE 'Deleted' END
			, ISNULL(SRC.rec_modified_by, SRC.rec_created_by)
			, GETDATE()
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	#temp_auth_user_relations AS SRC
				ON EXISTS
				(
					SELECT	*
					FROM	dbo.auth_user_in_role AS uir 
					WHERE	uir.UserId = SRC.userID
					AND	uir.ObjectID = SRC.ObjectID
					AND	uir.roleid = SRC.roleid
					AND	uir.ObjectType = SRC.ObjectType
					AND	uir.rec_status <> SRC.rec_status
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		SRC.objectType = 'H'

	UPDATE		TGT
	SET		rec_status = src.rec_status
			, rec_modified_by = src.rec_modified_by
			, rec_modified_when = src.rec_modified_when
	FROM		dbo.auth_user_in_role AS TGT
	INNER JOIN	#temp_auth_user_relations AS SRC
				ON TGT.UserId = SRC.userID
				AND TGT.ObjectID = SRC.ObjectID
				AND TGT.roleid = SRC.roleid
				AND TGT.ObjectType = SRC.ObjectType
	WHERE		TGT.rec_status <> SRC.rec_status

	INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
	(
			hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	)
	SELECT		A.ObjectId
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), A.UserId) + ' | role_id: ' + CONVERT(varchar(36), A.RoleId)
			, 'Added'
			, ISNULL(A.rec_modified_by, A.rec_created_by)
			, GETDATE()
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	#temp_auth_user_relations AS A
				ON NOT EXISTS
				(
					SELECT	*
					FROM	dbo.auth_user_in_role AS B
					WHERE	A.roleid = B.roleid
					AND	A.ObjectType = B.ObjectType
					AND	A.userID = B.UserId
					AND	A.objectID = B.ObjectID
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		A.objectType = 'H'

	INSERT INTO	dbo.auth_user_in_role 
	(
		UserId
		, RoleId
		, ObjectType
		, ObjectID
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
	)
	SELECT	UserId
		, RoleId
		, ObjectType
		, ObjectID
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
	FROM	#temp_auth_user_relations AS A
	WHERE	NOT EXISTS
	(
		SELECT	*
		FROM	dbo.auth_user_in_role AS B
		WHERE	A.roleid = B.roleid
		AND	A.ObjectType = B.ObjectType
		AND	A.userID = B.UserId
		AND	A.objectID = B.ObjectID
	)
	
	INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
	(
			hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	)
	SELECT		TGT.ObjectId
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), TGT.UserId) + ' | role_id: ' + CONVERT(varchar(36), TGT.RoleId)
			, 'Deleted'
			, ISNULL(rec_modified_by, '00000000-0000-0000-0000-000000000000')
			, GETDATE()
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	dbo.auth_user_in_role AS TGT
				ON NOT EXISTS
				(
					SELECT	*
					FROM	#temp_auth_user_relations AS SRC
					WHERE	TGT.userID = SRC.userID
					AND	TGT.objectID = SRC.objectID
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		TGT.objectType = 'H'
	AND		TGT.RoleId = @roleid

	DELETE	TGT
	FROM	dbo.auth_user_in_role AS TGT
	WHERE	TGT.RoleId = @roleid
	AND	TGT.objectType = @ObjectType
	AND	NOT EXISTS
	(
		SELECT	*
		FROM	#temp_auth_user_relations AS SRC
		WHERE	TGT.userID = SRC.userID
		AND	TGT.objectID = SRC.objectID
	)

	DROP TABLE #temp_auth_user_relations
END
go


GRANT EXECUTE ON [dbo].[backoffice_upload_auth_user_relations_finalize_v1] TO [customer_api_user]
GO
