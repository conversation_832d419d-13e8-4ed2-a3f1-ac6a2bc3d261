/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
---------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------------
-- <PERSON>y <PERSON>.              | 2018-03-14  | Create SP for GetRoleObjectsByUserId
-- Andrey S.              | 2018-04-03  | Add optional rec_status ignore param
-- Kittis<PERSON>   | 2018-11-19  | Return skill_code in the result
-- Kit<PERSON><PERSON>   | 2019-01-11  | Join auth_object_type to return ObjectName
-- Abhis<PERSON><PERSON>        | 2024-11-07  | Add support for User Shadow CM Portal
---------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
  EXEC [dbo].[auth_get_role_objects_by_user_id_v5] 'F84A4775-DC10-4F81-8634-A2B8AE92939F'
  EXEC [dbo].[auth_get_role_objects_by_user_id_v5] 'F84A4775-DC10-4F81-8634-A2B8AE92939F', 'H'
  EXEC [dbo].[auth_get_role_objects_by_user_id_v5] 'F84A4775-DC10-4F81-8634-A2B8AE92939F', 'H', 'a467caa1-5587-44d6-ad0b-868b6acc544c'
  EXEC [dbo].[auth_get_role_objects_by_user_id_v5] 'F84A4775-DC10-4F81-8634-A2B8AE92939F', 'H', 'a467caa1-5587-44d6-ad0b-868b6acc544c', 4407
REVERT
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_get_role_objects_by_user_id_v5]
      @userId         UNIQUEIDENTIFIER
    , @objectType     CHAR(1) = NULL
    , @roleId         UNIQUEIDENTIFIER = NULL
    , @allResStatuses BIT = 0
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    DECLARE @parentUserId UNIQUEIDENTIFIER

SELECT @parentUserId = parent_user_id
FROM dbo.cm_portal_user_mapping
WHERE child_user_id = @userId
  AND rec_status = 1

    IF @parentUserId IS NOT NULL
BEGIN
      SET @userId = @parentUserId
END

SELECT
    au.ObjectID,
    au.ObjectType,
    aot.ObjectName,
    au.RoleId,
    ar.RoleName,
    au.rec_created_when,
    au.rec_created_by,
    au.rec_modified_when,
    au.rec_modified_by,
    au.rec_status,
    au.skill_code
FROM dbo.auth_user_in_role AS au
         INNER JOIN dbo.auth_roles AS ar
                    ON ar.RoleId = au.RoleId AND (@allResStatuses = 1 OR ar.rec_status = 1)
         LEFT JOIN dbo.auth_object_type AS aot
                   ON au.ObjectType = aot.ObjectType
WHERE au.UserId = @userId
  AND (@objectType IS NULL OR (au.objecttype = @objectType))
  AND (@roleId IS NULL OR (au.roleid = @roleId))
  AND (@allResStatuses = 1 OR au.rec_status = 1)
END
GO

GRANT SELECT ON [dbo].[cm_portal_user_mapping] TO [customer_api_user]
GO

GRANT EXECUTE ON [dbo].[auth_get_role_objects_by_user_id_v5] TO [customer_api_user]
GO
