/*
SQL_OBJECT_INFO

estimated_execution_count: 1k
estimated_execution_time_unit: minute
sp_timeout_configuration: 3s

END_SQL_OBJECT_INFO
*/

/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Weerapong Mua            | 2025-07-25    | clone from read_customer_nha_hotels_v1
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_customer_nha_hotels_by_limit_v1] '856C401B-3E32-429A-A8C0-FEB9F4539224', 1000;
EXEC [dbo].[read_customer_nha_hotels_by_limit_v1] 'A502C68F-4395-4AF3-9868-DE66C63A6FC8', 1000;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_customer_nha_hotels_by_limit_v1]
    @user_id uniqueidentifier
    , @limit int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT
        DISTINCT TOP(@limit) r.ObjectID AS hotel_id
    FROM       dbo.auth_user_in_role r
    INNER JOIN dbo.product_hotels h
            ON h.hotel_id = r.ObjectID
    WHERE      r.UserId = @user_id
    AND        r.rec_status = 1
    AND        r.ObjectType = 'H'
    AND        h.is_non_hotel_accommodation_mode = 1
    AND        h.rec_status = 1
    AND        r.RoleId IN ('86a9719d-cab0-43ff-bcc1-b3a490375257', 'a2c5af8b-90e9-47d6-a75d-b6e0805e9ce3')
    OPTION (OPTIMIZE FOR (@user_id UNKNOWN));
END
GO

GRANT EXECUTE ON [dbo].[read_customer_nha_hotels_by_limit_v1] TO [customer_api_user]
GO
