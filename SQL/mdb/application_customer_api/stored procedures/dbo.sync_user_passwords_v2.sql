/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 100

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>at<PERSON>           | 2019-06-06    | Update password of each mapping for the given user.
-- Terapat P<PERSON>iri          | 2024-12-08    | Increase password_hash column size.
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[sync_user_passwords_v2] '00000000-0000-0000-0000-000000000000', DEFAULT, '', '00000000-0000-0000-0000-000000000000';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[sync_user_passwords_v2]
    @user_id uniqueidentifier,
    @mappings dbo.user_mapping_table READONLY,
    @password_hash nvarchar(1024),
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    BEGIN TRANSACTION;
    UPDATE     aum
    SET        aum.uri = m.uri
               , rec_modified_when = GETDATE()
               , rec_modified_by = @modified_by
               , expired_since = DATEADD(YEAR, 30, GETDATE())
               , password_last_changed = GETDATE()
    FROM       dbo.auth_user_mapping AS aum
    INNER JOIN @mappings AS m
    ON         m.auth_type = aum.authentication_type_id
    WHERE      aum.user_id = @user_id;

    INSERT INTO dbo.auth_password_history
    (
                user_id
                , pwhash
                , rec_created_by
                , rec_created_when
    )
    VALUES
    (
                @user_id
                , @password_hash
                , @modified_by
                , GETDATE()
    );
    COMMIT TRANSACTION;
END
GO

GRANT EXECUTE ON [dbo].[sync_user_passwords_v2] TO [customer_api_user]
GO
