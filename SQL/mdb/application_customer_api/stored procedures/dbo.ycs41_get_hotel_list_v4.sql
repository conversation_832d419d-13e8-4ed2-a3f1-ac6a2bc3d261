----------------------------------------------------------------------------------------------
-- Author                       | Date       | Comment
--------------------------------|------------|------------------------------------------------
-- <PERSON><PERSON><PERSON>     | 2017-11-14 | Return all properties and is nha flag column 
-- <PERSON><PERSON><PERSON>     | 2017-11-08 | Get userId by memberId and add property status to the return field
-- S. <PERSON>                    | 2017-07-14 | Omit inactive user in roles entries
-- Sur<PERSON><PERSON><PERSON>ch, Sarit       | 2017-06-01 | Get list of hotel
-- Nuttipong, T			| 2017-11-12 | Modify sp to return property images
-- <PERSON><PERSON>			| 2017-11-18 | new unlive, flagout or deletion logic
-- <PERSON><PERSON>			| 2017-12-01 | Fix duplicated property images
-- Rat<PERSON>.			| 2018-01-12 | Performance Tuning - NHA host timeout
-- Andrew L		| 2018-01-07 | Fix duplicated column name
----------------------------------------------------------------------------------------------
/*
EXEC_TEST

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[ycs41_get_hotel_list_v4] NULL,'02AA1C0B-A812-4DCB-A48A-F7F604E4DEE3'
REVERT;

END_EXEC_TEST
*/

----------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[ycs41_get_hotel_list_v4]
    @member_id INT = NULL,
    @user_id UNIQUEIDENTIFIER = NULL

AS

BEGIN

  
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    DECLARE @hotel TABLE 
    (
	hotel_id int NOT NULL PRIMARY KEY 
    )
	IF @member_id IS NOT NULL
	BEGIN
		SELECT	DISTINCT @user_id = UserId
		FROM	dbo.auth_user_in_role
		WHERE	ObjectID = @member_id
		AND	ObjectType = 'R'
		AND	rec_status = 1
	END

		INSERT INTO @hotel (hotel_id)
		SELECT DISTINCT au.ObjectID AS hotel_id
		FROM	dbo.auth_user_in_role  au
		WHERE	au.UserId = @user_id
		AND	rec_status = 1
		AND	au.objecttype = 'H'
	
	;WITH CTE_PIC
	AS (
	
		SELECT		hotel_id
				, picture_caption_title
				, picture_location
		FROM	(
				SELECT	hp.hotel_id
					, picture_caption_title
					, hp.picture_location
					, picture_type_id
					, ROW_NUMBER () OVER(PARTITION BY hp.hotel_id ORDER BY  hp.picture_order_id ASC, hp.picture_id DESC) AS rank
				FROM	dbo.hotel_pictures AS hp 
				INNER JOIN @hotel au 
					ON hp.hotel_id = au.hotel_id 
				WHERE	hp.picture_type_id = 6
				AND	hp.rec_status = 1
				AND	ISNULL(hp.picture_location,'') <> ''
			) AS a
		WHERE	a.rank = 1
	)

	SELECT		DISTINCT h.hotel_id
			, h.hotel_name
			, y.first_live_date
			, IIF(ISNULL(y.Rec_status, 0) = 0 AND ISNULL(Hotel_ycs_flag, 0) <> 0, 0, 1)  AS active_status
			, h.rec_status AS hotel_active_status
			, h.is_non_hotel_accommodation_mode AS is_nha
			, p.picture_caption_title
			, p.picture_location 
	FROM 		@hotel hau
	INNER JOIN	dbo.product_hotels h
			ON hau.hotel_id = h.hotel_id
	INNER JOIN 	dbo.ycs4_hotel y
			ON h.hotel_id = y.hotel_id
	LEFT JOIN 	CTE_PIC p 
			ON h.hotel_id = p.hotel_id 
	WHERE 		
			NOT (
				(y.Rec_status IS NULL OR y.Rec_status = 0) 
				AND 	(Hotel_ycs_flag IS NULL OR Hotel_ycs_flag = 0) 
				AND 	h.rec_status = 0
			) -- flagout
	AND		NOT (h.rec_status IS NULL OR h.rec_status = -1)

END



GO

GRANT EXECUTE ON [dbo].[ycs41_get_hotel_list_v4] TO [customer_api_user]
GO
