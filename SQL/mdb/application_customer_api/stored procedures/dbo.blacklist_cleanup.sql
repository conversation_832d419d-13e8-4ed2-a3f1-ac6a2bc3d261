/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
-------------------------------------------------------------------------------------------
-- Author		| Date			| Comments
----------------|---------------|----------------------------------------------------------
-- Nitiwat J.   | 2024-08-16	| Clean up the expired token for whitelabel only
-- Nitiwat J.   | 2024-08-28    | Return the deleted rows as OUTPUT
-------------------------------------------------------------------------------------------
/*
EXEC_TEST
    DECLARE @deleted_rows INT;
    EXECUTE AS LOGIN = 'customer_api_user'
    EXEC dbo.[blacklist_cleanup_v2] @until='2024-05-19', @deleted_rows=@deleted_rows OUTPUT;
    REVERT;
END_EXEC_TEST
 */
-------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[blacklist_cleanup_v2] (
    @until datetime,
    @dcbo_batch_group varchar(100) = 'capi_blacklisted_tokens',
    @deleted_rows INT OUTPUT
)
AS
BEGIN

    SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SET @deleted_rows = 0

    DECLARE @batch_size INT, @delay_time varchar(15);
    DECLARE @blacklist_ids TABLE (blacklist_id INT);

    SELECT blacklist_id
    INTO #temp
    FROM dbo.capi_blacklisted_tokens
    WHERE token_id IS NOT NULL
    AND revoked < @until
    AND (whitelabel_id != 1 AND whitelabel_id IS NOT NULL);

    WHILE EXISTS (SELECT 1 FROM #temp)
    BEGIN

        EXEC Agoda_DBA.[dbo].[repl_get_batch_size_by_group_V2] @dcbo_batch_group
            , @row_per_batch = @batch_size OUTPUT
            , @delay_sec = @delay_time OUTPUT

        BEGIN TRANSACTION

            SELECT TOP (@batch_size) blacklist_id
            INTO #batch_temp
            FROM #temp

            DELETE bltk
            OUTPUT DELETED.blacklist_id,
                DELETED.revoked,
                DELETED.user_id,
                DELETED.token_id,
                DELETED.whitelabel_id
            INTO agoda_dataupload.dbo.capi_blacklisted_tokens_backup (
                blacklist_id,
                revoked,
                user_id,
                token_id,
                whitelabel_id
            ) FROM dbo.capi_blacklisted_tokens AS bltk
            INNER JOIN #batch_temp AS blid
            ON bltk.blacklist_id = blid.blacklist_id;

            -- Checking affected rows on the target table with batch_size
            SET @deleted_rows = @deleted_rows + @@ROWCOUNT;
                SELECT @deleted_rows;
            IF @@ROWCOUNT > @batch_size
            BEGIN
                IF @@TRANCOUNT <> 0
                BEGIN
                    ROLLBACK TRANSACTION
                END

                DECLARE @err_msg nvarchar(2000) = 'Affected rows exceed the permitted row counts allowed of ' + CONVERT(nvarchar(10),@batch_size) + ' !'
                ;THROW 51000, @err_msg, 1;
            END

            DELETE t
			FROM #temp t
			INNER JOIN #batch_temp b
			ON b.blacklist_id = t.blacklist_id

            DROP TABLE #batch_temp

            COMMIT TRANSACTION
            WAITFOR DELAY @delay_time

    END
END
GO

GRANT EXECUTE ON [dbo].[blacklist_cleanup_v2] TO [customer_api_user]
GO

