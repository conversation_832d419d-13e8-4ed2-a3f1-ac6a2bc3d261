
-------------------------------------------------------------------------------------------
-- Author       | Date          | Comment
----------------|---------------|----------------------------------------------------------
-- Andrey S.    | 2017-07-18    | Get single Market Manager for a specific property id
----------------|---------------|----------------------------------------------------------
/*
MONKEY_TEST EXEC  [dbo].[security_user_in_role_get_mm_by_property] 71656,'94660C38-43B6-4F00-8503-01D85DD71E21'
*/
-------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[security_user_in_role_get_mm_by_property]
   @propertyId INT,
   @mmId uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT      TOP 1 auth_user.EmailAddress
    FROM			dbo.auth_users AS auth_user
    LEFT JOIN		dbo.auth_user_in_role AS user_in_role
						ON  auth_user.UserId = user_in_role.UserId
							AND user_in_role.RoleId = @mmId
    WHERE       user_in_role.ObjectID = @propertyId
    ORDER BY    ISNULL(auth_user.rec_modified_when, auth_user.rec_created_when) DESC
END



GO

GRANT EXECUTE ON [dbo].[security_user_in_role_get_mm_by_property] TO [customer_api_user]
GO
