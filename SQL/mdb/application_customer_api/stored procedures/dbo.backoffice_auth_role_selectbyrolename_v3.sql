
---------------------------------------------------------------------------------------------------
-- Author		              | Date		    | Comment
--------------------------------|-------------------------------------------------------------------------
-- Sir<PERSON><PERSON> | 02/17/2011	| Get All Authentication Role by RoleName
-- Pongtham Wiangwang	    | 10/16/2012	| Created v2 - Added object type
-- Mrun S.                | 02/28/2018  | Added whitelabel_id support
-------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_role_selectbyrolename_v3] 'IT%', 1
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[backoffice_auth_role_selectbyrolename_v3]
	@RoleName VARCHAR(50),
	@whitelabel_id SMALLINT 
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT RoleId
		, RoleName
		, ISNULL(ObjectType, '') AS ObjectType
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
		, ISNULL((
				SELECT emailAddress
				FROM dbo.auth_users
				WHERE UserId = ar.rec_created_by
				AND ISNULL(whitelabel_id, 1) = @whitelabel_id
				), '') AS 'EmailCreatedBy'
		, ISNULL((
				SELECT emailAddress
				FROM dbo.auth_users
				WHERE UserId = ar.rec_modified_by
				AND  ISNULL(whitelabel_id, 1) = @whitelabel_id
				), '') AS 'EmailModifiedBy'
	FROM dbo.auth_roles ar
	WHERE RoleName LIKE @RoleName + '%'
		AND rec_status > 0
	ORDER BY RoleName
END
GO

GRANT EXECUTE ON [dbo].[backoffice_auth_role_selectbyrolename_v3] TO [customer_api_user]
GO
