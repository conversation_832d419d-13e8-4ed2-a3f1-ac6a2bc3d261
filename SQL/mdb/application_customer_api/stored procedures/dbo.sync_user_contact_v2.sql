/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>at<PERSON>           | 2019-06-06    | Replaces the unverified contact for the given user.
-- Yevhen <PERSON>atulin           | 2019-07-19    | Delete primary email instead of soft delete.
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @random_contact_id int = ABS(CHECKSUM(NewId())) % 2147483647;
DELETE FROM rew_contacts WHERE contact_id = @random_contact_id;
EXEC [dbo].[sync_user_contact_v2] 1, @random_contact_id, 1, '', '', '00000000-0000-0000-0000-000000000000';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[sync_user_contact_v2]
    @member_id int,
    @contact_id int,
    @contact_type int,
    @contact_value nvarchar(400),
    @contact_remark nvarchar(512),
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    DECLARE @origin char(2) = (SELECT origin FROM dbo.rew_members WHERE MemberId = @member_id);
    DECLARE @whitelabel_id smallint = (SELECT whitelabel_id FROM dbo.rew_members WHERE MemberId = @member_id);

    BEGIN TRANSACTION;
    IF EXISTS(SELECT 1 FROM dbo.rew_contacts WHERE contact_id = @contact_id)
    BEGIN
        UPDATE dbo.rew_contacts
        SET    contact_method_id = @contact_type
               , contact_method_value = @contact_value
               , contact_method_remark = @contact_remark
               , rec_modify_by = @modified_by
               , rec_modify_when = GETDATE()
               , lastupdated_when = GETDATE()
        WHERE  contact_id = @contact_id;
    END
    ELSE
    BEGIN
        -- NOTE: Don't touch contacts that were created by Cusco, except primary emails
        IF @contact_type = 1
        BEGIN
            DELETE FROM dbo.rew_contacts WHERE MemberId = @member_id AND contact_method_id = @contact_type;
        END
        ELSE
        BEGIN
            UPDATE dbo.rew_contacts
            SET    rec_status = -1
                   , rec_modify_by = @modified_by
                   , rec_modify_when = GETDATE()
                   , lastupdated_when = GETDATE()
            WHERE  MemberId = @member_id
            AND    contact_method_id = @contact_type
            AND    rec_created_by <> 'F82F8511-1DA4-4180-8118-20283C1BE13B';
        END;

        SET IDENTITY_INSERT agoda_core.dbo.rew_contacts ON;
        INSERT INTO dbo.rew_contacts
        (
                    contact_id
                    , contact_method_id
                    , memberId
                    , contact_method_value
                    , contact_method_remark
                    , rec_status
                    , rec_created_by
                    , rec_created_when
                    , lastupdated_when
                    , whitelabel_id
                    , origin
        )
        VALUES
        (
                    @contact_id
                    , @contact_type
                    , @member_id
                    , @contact_value
                    , @contact_remark
                    , 1
                    , @modified_by
                    , GETDATE()
                    , GETDATE()
                    , @whitelabel_id
                    , @origin
        );
        SET IDENTITY_INSERT agoda_core.dbo.rew_contacts OFF;
    END;
    COMMIT TRANSACTION;
END
GO

GRANT EXECUTE ON [dbo].[sync_user_contact_v2] TO [customer_api_user]
GO
