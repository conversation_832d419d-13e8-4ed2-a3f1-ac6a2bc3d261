
---------------------------------------------------------------------------------------------------
-- Author		        | Date			| Comment
------------------------|---------------|----------------------------------------------------------
-- Saran <PERSON>i	| 2011-12-8		| Initiated.
------------------------|---------------|----------------------------------------------------------
-- exec api_exchange_rate_fetch 'USD'
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[api_exchange_rate_fetch]
(
	@currency_code char(3)
)
AS 
BEGIN

	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	SELECT 
		a.real_exchange_rate,
		a.rate_exchange_rate,
		a.site_exchange_rate,
		a.uplift_exchange_rate,
		a.destination_currency_code,
		a.destination_exchange_rate,
		a.rate_quote_id,
		a.uplift_exchange_rate_a_domestic,
		a.uplift_exchange_rate_a_international,
		b.NoDecimal
	FROM 
		dbo.afm_exchangerates a
		inner join dbo.afm_currency b 
					on a.currency_id = b.currency_id 
	WHERE 
		a.rec_status = 1
		and b.currency_code = @currency_code
END





GO

GRANT EXECUTE ON [dbo].[api_exchange_rate_fetch] TO [customer_api_user]
GO
