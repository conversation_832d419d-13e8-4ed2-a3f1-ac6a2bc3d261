---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- GyÃƒÆ’Ã‚Â¶rgy <PERSON>		| 2016-05-19	| Selects a customer token by user id
-- <PERSON>	          | 2019-28-01  | Add token data
------------------------|---------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.[security_customertoken_select_byuserid_v2] '9E66EFC2-7349-4AE5-94F7-5570AD40201C',null,'aaa'
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_customertoken_select_byuserid_v2]
	@user_id uniqueidentifier
	, @device_id uniqueidentifier = NULL
	, @data varchar(max) = NULL
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF @device_id IS NULL
	BEGIN
		SELECT	token_id
			, [user_id]
			, device_id
			, device_description
			, data
			, expiry
			, issued_when
			, [status]
			, status_description
		FROM	dbo.auth_user_authenticationtoken
		WHERE	[user_id] = @user_id and (@data is null OR data = @data )
	END
	ELSE
	BEGIN
		SELECT	token_id
			, [user_id]
			, device_id
			, device_description
			, data
			, expiry
			, issued_when
			, [status]
			, status_description
		FROM	dbo.auth_user_authenticationtoken
		WHERE	[user_id] = @user_id
		AND	device_id = @device_id
		and (@data is null OR data = @data )
	END
END





GO

GRANT EXECUTE ON [dbo].[security_customertoken_select_byuserid_v2] TO [customer_api_user]
GO
