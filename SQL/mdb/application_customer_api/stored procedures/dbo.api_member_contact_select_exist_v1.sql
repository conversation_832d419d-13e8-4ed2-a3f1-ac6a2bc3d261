-------------------------------------------------------------------------------------------
-- Author    | Date       | Comment
-------------|------------|----------------------------------------------------------------
-- Saran B.  | 2013-03-12 | select member's contact.
-- <PERSON><PERSON>. | 2018-10-07 | v1, extend PII columns size WFAPI-2152
-- Mrun S.   | 2018-15-11 | add encrypted query params
-------------|---------------|-------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.api_member_contact_select_exist_v1 5050776, '<EMAIL>', '<EMAIL>'
REVERT;
END_EXEC_TEST
*/
-------------|---------------|-------------------------------------------------------------

CREATE PROCEDURE [dbo].[api_member_contact_select_exist_v1]
	@ContactId int,
	@EmailAddress nvarchar(400),
	@encrypted_EmailAddress nvarchar(400)
AS
BEGIN

	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	SELECT	CAST
		(
			CASE	WHEN EXISTS
				(	
					SELECT		*
					FROM		dbo.rew_contacts a
					INNER JOIN	dbo.rew_members b
								ON a.MemberID = b.MemberID
								AND a.contact_method_id in (1, 8)
								AND (a.contact_method_value = @EmailAddress OR a.contact_method_value = @encrypted_EmailAddress)
								AND a.rec_status = 1
					WHERE		a.contact_id <> @ContactId
								AND a.rec_status = 1
				) THEN 1 
				ELSE 0 
			END 
			AS BIT
		)

END
GO

GRANT EXECUTE ON [dbo].[api_member_contact_select_exist_v1] TO [customer_api_user]
GO
