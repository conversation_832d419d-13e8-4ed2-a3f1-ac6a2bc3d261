/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- Andrew <PERSON> 		| 09/07/2018	| First version
------------------------------------------------|---------------|------------------------------------------

EXEC_TEST
DECLARE @hotelIds AS [id_table_type];
INSERT INTO @hotelIds values(1)

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[update_hotel_nha_status] @hotelIds, @hotelIds, @hotelIds
REVERT;
END_EXEC_TEST

------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[update_hotel_nha_status]
    @unverifiedHotelIds [id_table_type] READONLY,
    @verifiedHotelIds [id_table_type] READONLY,
    @topHotelIds [id_table_type] READONLY

AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

  UPDATE dbo.product_hotels 
  SET nha_host_level = 0 
  FROM dbo.product_hotels h
  INNER JOIN @unverifiedHotelIds t
  on h.hotel_id = t.id

  UPDATE dbo.product_hotels 
  SET nha_host_level = 1 
  FROM dbo.product_hotels h
  INNER JOIN @verifiedHotelIds t
  on h.hotel_id = t.id

  UPDATE dbo.product_hotels 
  SET nha_host_level = 2 
  FROM dbo.product_hotels h
  INNER JOIN @topHotelIds t
  on h.hotel_id = t.id

  END

GO

GRANT EXECUTE ON [dbo].[update_hotel_nha_status] TO [customer_api_user]
GO
