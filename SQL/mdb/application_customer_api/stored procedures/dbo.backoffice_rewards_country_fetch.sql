
---------------------------------------------------------------------------------------------------
-- Author		        | Date			| Comment
------------------------|---------------|----------------------------------------------------------
-- Sara<PERSON>	| 23/07/2012	| Initiated.
-- Sara<PERSON>	| 17/01/2013	| Changed the rec_status condition.
------------------------|---------------|----------------------------------------------------------
-- exec [backoffice_rewards_country_fetch] 1
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_rewards_country_fetch] 
	@language_id INT
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT b.country_id
		,ISNULL(b.country_name, a.country_name) AS country_name
	FROM dbo.geo2_country a
	LEFT JOIN dbo.geo2_country_language b 
				ON a.country_id = b.country_id
				AND language_id = @language_id
				AND b.rec_status = 1
	WHERE a.enable_nationality = 1
		AND a.rec_status = 1
	ORDER BY b.country_name
END




GO

GRANT EXECUTE ON [dbo].[backoffice_rewards_country_fetch] TO [customer_api_user]
GO
