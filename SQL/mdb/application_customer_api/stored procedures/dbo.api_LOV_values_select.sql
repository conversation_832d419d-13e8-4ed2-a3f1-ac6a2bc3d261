
/*
---------------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- Saran B.		| 2012-06-20	| selects LOV values.
-- Chate C.		| 2012-08-30	| Refactoring code format.
----------------|---------------|----------------------------------------------------------
Exec [api_LOV_values_select] 2, 1
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_LOV_values_select]
	@lov_set_id int,
	@language_id int
AS
BEGIN

	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
	
	SELECT		b.lov_value_id
			,b.display_value
	FROM		core_lov_values a
	INNER JOIN	core_lov_values_language b
				ON a.lov_value_id = b.lov_value_id
				AND b.language_id = @language_id
	WHERE		a.lov_set_id = @lov_set_id
	AND		a.rec_status > 0
	ORDER BY	default_sequence

	
END





GO

GRANT EXECUTE ON [dbo].[api_LOV_values_select] TO [customer_api_user]
GO
