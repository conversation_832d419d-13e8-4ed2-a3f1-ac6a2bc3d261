CREATE PROCEDURE [dbo].[auth_users_mapping_remove_v2]
(
  @UserId uniqueidentifier,
  @authentication_type_id tinyint,
  @removeBy uniqueidentifier,
  @history_id bigint = null
)
AS
BEGIN
	--Result
	--0, success
	--1, removeby id is not exists.
	
	SET NOCOUNT ON
	SET XACT_ABORT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	DECLARE @result SMALLINT = 0
	BEGIN TRY


	IF EXISTS(SELECT * FROM dbo.auth_users WHERE USERID = @removeBy AND rec_status = 1)
	BEGIN
		BEGIN TRAN
	
			DELETE FROM dbo.auth_user_mapping
			WHERE USER_ID = @UserId
			AND   authentication_type_id = @authentication_type_id
			AND   rec_status = 1
	
			-- Log into auth_user_mapping_history
			IF @history_Id IS NULL
			BEGIN
				INSERT INTO dbo.auth_user_mapping_history (
				  logtime
				  , user_id
				  , authentication_type_id
				  , ActivityId
				  , rec_status
				  , rec_created_when
				  , rec_created_by
				)
				VALUES (
				  GETDATE()
				  , @userid
				  , @authentication_type_id
				  , 12 --Remove User mapping (API)
				  , 1
				  , GETDATE()
				  , @removeBy
				)
				SET @history_Id = SCOPE_IDENTITY()
			END
			ELSE
			BEGIN
				SET IDENTITY_INSERT agoda_core.dbo.auth_user_mapping_history ON
	
				INSERT INTO dbo.auth_user_mapping_history (
				  logtime
				  , user_id
				  , authentication_type_id
				  , ActivityId
				  , rec_status
				  , rec_created_when
				  , rec_created_by
				  , history_id
				)
				VALUES (
				  GETDATE()
				  , @userid
				  , @authentication_type_id
				  , 12 --Remove User mapping (API)
				  , 1
				  , GETDATE()
				  , @removeBy
				  , @history_id
				)
	
				SET IDENTITY_INSERT agoda_core.dbo.auth_user_mapping_history OFF
			END
		COMMIT TRAN
	END
	ELSE
	BEGIN
		SET @result = 1 --removeby use is not exists.
	END
	END TRY
	BEGIN CATCH
		DECLARE @err_msg NVARCHAR(2048) = ERROR_MESSAGE()
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRAN
		END

		RAISERROR(@err_msg, 16, 1)
	END CATCH

	SELECT  @result AS RESULT
		, @history_id AS history_id

END


GO

GRANT EXECUTE ON [dbo].[auth_users_mapping_remove_v2] TO [customer_api_user]
GO
