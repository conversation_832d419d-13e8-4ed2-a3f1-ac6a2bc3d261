/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 600

END_SQL_OBJECT_INFO
*/
-------------------------------------------------------------------------------------------
-- Author       | Date          | Comment
----------------|---------------|----------------------------------------------------------
-- <PERSON>y S.    | 2017-07-11    | Check for hotel rec_status before adding user with role
-- Nutthawat P. | 2019-04-26    | Add whitelabel support
-- Abhishek P.  | 2025-06-26    | Updated to use auth_user_in_role_adduser_byroleid_v2
----------------|---------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[auth_user_in_role_adduser_byroleid_check_blocked_v3]
   @roleid = '15F01F08-C81A-4D2D-8607-389FE4C46A5B'
    , @objectId = 12548
    , @objecttype ='H'
    , @userId = '15F01F08-C81A-4D2D-8607-389FE4C46A5B'
    , @rec_created_by = '15F01F08-C81A-4D2D-8607-389FE4C46A5B'
    , @whitelabel_id = 1
REVERT;
END_EXEC_TEST
*/
----------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_user_in_role_adduser_byroleid_check_blocked_v3]
    @roleid uniqueidentifier
    , @objectId int
    , @objecttype char
    , @userId uniqueidentifier
    , @rec_created_by uniqueidentifier
    , @whitelabel_id int
AS
BEGIN
    SET NOCOUNT ON;
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

DECLARE @result int = 1
    DECLARE @userWhitelabelId int = ( SELECT whitelabel_id FROM dbo.auth_users WHERE UserId = @userId )


    IF (ISNULL(@userWhitelabelId, 1) != @whitelabel_id)
BEGIN
      SET @result = 0
END
ELSE IF (@objecttype = 'H')
BEGIN
        DECLARE @hotelStatus int = ( SELECT rec_status FROM dbo.product_hotels WHERE hotel_id = @objectId )
        IF (@hotelStatus < 0)
BEGIN
            SET @result = 0
END
ELSE
BEGIN
EXEC [dbo].[auth_user_in_role_adduser_byroleid_v2] @roleid, @objectId, @objecttype, @userId, @rec_created_by
END
END
ELSE
BEGIN
EXEC [dbo].[auth_user_in_role_adduser_byroleid_v2] @roleid, @objectId, @objecttype, @userId, @rec_created_by
END

SELECT @result as result
END
GO

GRANT EXECUTE ON [dbo].[auth_user_in_role_adduser_byroleid_check_blocked_v3] TO [customer_api_user]
GO
