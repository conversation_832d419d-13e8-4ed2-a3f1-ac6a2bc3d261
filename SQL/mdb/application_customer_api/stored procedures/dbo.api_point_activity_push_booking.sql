
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
--------------------------------|---------------|----------------------------------------------------------
-- <PERSON><PERSON><PERSON><PERSON>		| 2017-08-15	| Migrate from [dbo].[gp_rew_point_activity_push_booking_v4])
-- Ankit/Rati P.				| 2017-04-10	| Add SET XACT_ABORT ON 

---------------------------------------------------------------------------------------------------------
-- TEST :
---------------------------------------------------------------------------------------------------------

CREATE PROCEDURE  [dbo].[api_point_activity_push_booking](
	@MemberID	int,
	@point_activity_type_id	int,
	@storefront_id		int,
	@booking_id		int,
	@booking_status_id	int,
	@Booking_Date		datetime,
	@affected_when		datetime,		-- with @booking_status_id=4 30Day After CheckOut Date
	@booking_value		money,
	@booking_points_used	int,
	@credit_card_value	money,
	@special_offer_id	int,
	@remarks		nvarchar(500),
	@nPointTime		int = 1,
	@refund_value           decimal(18,2),
	@is_user_logged_in      bit,
	@Is_BNPL_booking        bit,
	@payment_model          int,                          
	@affiliate_model        int,
	@discount_savings       decimal(18,2),                   
	@promotion_code         varchar(20) = NULL,
	@booking_date_until     smalldatetime
)
AS
BEGIN
	SET NOCOUNT ON
	SET XACT_ABORT ON 


	DECLARE
		@message	nvarchar(100),
		@point_activity_id	int,
		@isSuccess	bit = 0		
	
	SET @isSuccess = 0
	SET @point_activity_id = 0
	
	EXEC dbo.rew2_point_activity_main_v2
		 @booking_status_id		= @booking_status_id			--smallint		= 0
		,@booking_points_used		= @booking_points_used			--int			= null
		,@MemberID			= @MemberID				--int		
		,@storefront_id			= @storefront_id			--int			= null
		,@booking_id			= @booking_id				--int			= null
		,@booking_date			= @booking_date				--smalldatetime		= null
		,@departure_date		= @affected_when	--***		--date			= null
		,@booking_value			= @booking_value			--decimal(19,2)		= null
		,@credit_card_value		= @credit_card_value			--decimal(19,2)		= null
		,@promotion_guid		= NULL			--***		--uniqueidentifier	= null
		,@promotion_source_id		= NULL			--***		--int			= null
		,@promotion_expiry_date		= NULL			--***		--date			= null
		,@nPointTime			= @nPointTime				--tinyint			= null
		,@mergeid			= NULL			--***		--int			= null
		,@remarks			= @remarks				--nvarchar(500)		= null
		,@point_activity_subtype_id	= 10			--***		--int			= null --For @is_process_booking = 0
		,@points_affected		= NULL			--***		--int			= null
		,@affected_date			= NULL			--***		--datetime		= null
		,@is_process_booking		= 1					--bit			= 1
		,@run_mode			= 'NORMAL'				--varchar(50)		= 'Normal'
		,@message			= @message			OUTPUT 	--nvarchar(100)		= null
		,@point_activity_id		= @point_activity_id		OUTPUT	--int			= null
		,@isSuccess			= @isSuccess			OUTPUT	--bit			= null	
		
		,@refund_value           	= @refund_value        			--decimal(18,2),
		,@is_user_logged_in      	= @is_user_logged_in   			--bit,
		,@Is_BNPL_booking        	= @Is_BNPL_booking     			--bit,
		,@payment_model          	= @payment_model       			--int,                          
		,@affiliate_model        	= @affiliate_model     			--int,
		,@discount_savings       	= @discount_savings    			--decimal(18,2),                   
		,@promotion_code         	= @promotion_code      			--varchar(20),
		,@booking_date_until     	= @booking_date_until  		
	
	SELECT @message AS message, @point_activity_id AS pointActivityId, @isSuccess AS isSuccess
END


GO

GRANT EXECUTE ON [dbo].[api_point_activity_push_booking] TO [customer_api_user]
GO
