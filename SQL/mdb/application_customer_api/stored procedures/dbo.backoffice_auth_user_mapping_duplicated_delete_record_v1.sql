---------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|------------------------------------------------------
-- <PERSON><PERSON><PERSON>.             | 2012-10-31    | delete functionality
-- Kit<PERSON><PERSON>     | 2017-11-28    | Add history_Id as a parameter
-- Thanapol R.              | 2018-06-19    | delete both reward member and auth user
-- Anurag A.                | 2018-12-11    | Merged delete and unblock functionality together. Taken AffectedUserID,ActionedBy, newRecStatus,oldRecStatus, newEmail,CustomerMapping(including contact method value, uri) _del appended/removed_for_unblock and encrypted as inputs instead of manipulating inside proc.
-- Mrun S.                  | 2018-12-21    | remove unused SP param
----------------------------|---------------|------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
exec dbo.backoffice_auth_user_mapping_duplicated_delete_record_v1 1, '<Result><Mappings><ContactMethods><ContactMethod contactType="1" new="anurag.agnihotri@agoda.com_del" old="<EMAIL>"/></ContactMethods><EmailAddress>anurag.agnihotri@agoda.com_del</EmailAddress><UserNames><UserName id="D309993F-1D11-41C5-9D38-D01385850CC4" value="aagnihotri_del" authType="1" uri="http://test1.com_del"></UserName><UserName id="D309993F-1D11-41C5-9D38-D01385850CC4" value="testaagnihotri_del" authType="2" uri="http://test2.com_del"></UserName></UserNames></Mappings></Result>'
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_user_mapping_duplicated_delete_record_v1]
(
@whitelabelId int,
@CustomerMapping XML
)
AS
  BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	BEGIN TRY

		BEGIN TRANSACTION

			DELETE USR_MAP
            FROM   dbo.auth_user_mapping USR_MAP
            INNER JOIN @CustomerMapping.nodes('//Mappings/UserNames/UserName') UserNames(columndata)
            ON
                USR_MAP.username = usernames.columndata.value('./@value', 'VARCHAR(400)')
                AND USR_MAP.authentication_type_id = usernames.columndata.value('./@authTypeId', 'int')
                AND USR_MAP.whitelabel_id = @whitelabelId
            WHERE  USR_MAP.rec_status = -1;

		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
			
		DECLARE @err_msg NVARCHAR(2048) = ERROR_MESSAGE()
	
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRANSACTION
		END

		;THROW 51000, @err_msg, 1;

	END CATCH
  END

GO

GRANT EXECUTE ON [dbo].[backoffice_auth_user_mapping_duplicated_delete_record_v1] TO [customer_api_user]
GO
