---------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|-------------------------------------------------------------------
-- Andrey S.      | 2020-04-02 | migrate from existing raw query
---------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[auth_token_role_mappings_v1]
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_token_role_mappings_v1]
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT ar.RoleId,
	 ar.SeqId,
	 ar.RoleName,
	 ap.PermissionId,
	 ap.PermissionName,
	 ac.ComponentId,
	 ac.ComponentName,
	 ar.rec_created_when
	FROM dbo.auth_roles ar
	LEFT JOIN dbo.auth_role_permissions arp ON ar.RoleId = arp.RoleId
	LEFT JOIN dbo.auth_permissions ap ON arp.PermissionId = ap.PermissionId
	LEFT JOIN dbo.auth_components ac ON ac.ComponentId = ap.ComponentId
END
GO

GRANT EXECUTE ON [dbo].[auth_token_role_mappings_v1] TO [customer_api_user]
GO
