---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Taweekiat P.		| 2016-02-29	| Split: Condition 1 of auth_user_in_role_get
-- GyÃƒÂ¶rgy <PERSON>	| 2016-04-26	| Fix formatting
-- Pawat D.			| 2016-08-25	| Remove OPTION RECOMPILE
-- A.<PERSON>		| 2017-12-06	| Make authenticationType optional
-- <PERSON>ta<PERSON> J.		| 2022-04-29	| Add whiteLabelId
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST EXEC auth_user_in_role_get_by_role_id_v2 '6EC5F244-8FAE-4200-974C-AAB9DCE88E54', 2, 1
---------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.auth_user_in_role_get_by_role_id_v2 '6EC5F244-8FAE-4200-974C-AAB9DCE88E54', 2, 1
REVERT;
END_EXEC_TEST
*/

CREATE PROCEDURE [dbo].[auth_user_in_role_get_by_role_id_v2]
	@roleId uniqueidentifier
	, @authenticationType int
	, @whiteLabelId smallint
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF(@authenticationType IS NOT NULL)
	BEGIN
			SELECT		DISTINCT
					ar.UserId
					, au.DisplayName
					, au.Emailaddress
					, am.username
					, r.RoleId
					, r.RoleName
			FROM		dbo.auth_user_in_role AS ar
			INNER JOIN	dbo.auth_roles AS r
						ON r.RoleId = ar.RoleId
			INNER JOIN	dbo.auth_users AS au
						ON ar.UserId = au.UserID
			INNER JOIN	dbo.auth_user_mapping AS am
						ON au.UserId = am.user_id
			WHERE	ar.RoleId = @roleId
			AND		ar.rec_status > 0
			AND		r.rec_status > 0
			AND		au.whitelabel_id = ISNULL(@whiteLabelId, au.whitelabel_id)
			AND		au.rec_status > 0
			AND		am.authentication_type_id = @authenticationType
			AND		am.rec_status > 0
			OPTION (MAXDOP 1)
  END
  ELSE
  BEGIN
			SELECT		DISTINCT
					ar.UserId
					, au.DisplayName
					, au.Emailaddress
					, am.username
					, r.RoleId
					, r.RoleName
			FROM		dbo.auth_user_in_role AS ar
			INNER JOIN	dbo.auth_roles AS r
						ON r.RoleId = ar.RoleId
			INNER JOIN	dbo.auth_users AS au
						ON ar.UserId = au.UserID
			INNER JOIN	dbo.auth_user_mapping AS am
						ON au.UserId = am.user_id
			WHERE		ar.RoleId = @roleId
			AND		ar.rec_status > 0
			AND		au.whitelabel_id = ISNULL(@whiteLabelId, au.whitelabel_id)
			AND		r.rec_status > 0
			AND		au.rec_status > 0
			AND		am.rec_status > 0
			OPTION (MAXDOP 1)
  END
END



GO

GRANT EXECUTE ON [dbo].[auth_user_in_role_get_by_role_id_v2] TO [customer_api_user]
GO
