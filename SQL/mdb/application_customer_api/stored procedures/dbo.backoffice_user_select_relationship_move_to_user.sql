---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- E<PERSON><PERSON>            | 2011-10-18  | Select users to assign the relationship
-- Nicolas <PERSON>      | 2011-12-28  | Refactor for coding standards
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'=
EXEC dbo.backoffice_user_select_relationship_move_to_user 1070, 'H'
REVERT
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_user_select_relationship_move_to_user]
    @ObjectId int,  
    @ObjectType char(1)
AS
BEGIN   
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    
    SELECT  DISTINCT username AS UserName
        , user_id AS UserId
    FROM    dbo.auth_user_mapping
    WHERE   authentication_type_id = 1
    AND rec_status = 1
    AND user_id NOT IN
        (
            SELECT  UserId
            FROM    dbo.auth_user_relations
            WHERE   ObjectId = @ObjectId
            AND ObjectType = @ObjectType
        )
    ORDER   BY UserName
END
GO

GRANT EXECUTE ON [dbo].[backoffice_user_select_relationship_move_to_user] TO [customer_api_user]
GO
