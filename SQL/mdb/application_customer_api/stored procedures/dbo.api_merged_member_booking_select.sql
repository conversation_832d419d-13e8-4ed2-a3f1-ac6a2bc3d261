

/*
-------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
--------------- |---------------|----------------------------------------------------------
-- Saran B.		| 2012-08-14	| get booking id and credit card value
----------------|---------------|----------------------------------------------------------
-- Ni<PERSON><PERSON><PERSON> S.	| 2012-08-30	| reformat code to meet standard 
----------------|---------------|----------------------------------------------------------
-- Saran B.		| 2013-01-04	| Remove credit_card_value
----------------|---------------|----------------------------------------------------------
Exec [api_merged_member_booking_select] 1005446
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_merged_member_booking_select]
	@mergeId int
AS
BEGIN

	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;


	SELECT  DISTINCT A.booking_id
		--, ISNULL(credit_card_value, 0) AS credit_card_value
	FROM    dbo.rew2_merge_point_activity AS A 	
	WHERE   A.point_activity_subtype_id = 10
	AND	A.MergeId = @mergeId
	AND	A.rec_status = 1

END






GO

GRANT EXECUTE ON [dbo].[api_merged_member_booking_select] TO [customer_api_user]
GO
