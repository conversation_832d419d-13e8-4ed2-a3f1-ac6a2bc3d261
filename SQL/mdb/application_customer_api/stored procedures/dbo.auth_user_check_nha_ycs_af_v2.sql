-------------------------------------------------------------------------------------------------------------------------------------
-- Name			| Date			| Desc
-------------------------------------------------------------------------------------------------------------------------------------
--J <PERSON><PERSON>um| 2018-03-09     | check whether user is NHA/YCS/AFFLIATES
--Nasakol P.    | 2018-04-10     | add more roles about ycs
-------------------------------------------------------------------------------------------------------------------------------------
-- MONKEY_TEST : EXEC dbo.auth_user_check_nha_ycs_af_v2 '<EMAIL>'
-------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_user_check_nha_ycs_af_v2]
(
	@emailAddress varchar(100)
)
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
 
	SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END AS IsNHA
	FROM dbo.auth_users au
	INNER JOIN  dbo.auth_user_in_role auir
	ON au.UserId = auir.UserId
	WHERE au.EmailAddress = @emailAddress
	AND auir.rec_status = 1
	AND (RoleId = 'A2C5AF8B-90E9-47D6-A75D-B6E0805E9CE3'    -- Host
        OR RoleId = '6ba94885-84b9-4a20-afa3-6675dfecc1c4'  -- Affiliate Admin

        -- YCS
        OR RoleId = 'CA5F7FFD-9359-4072-8230-069FE9701CE1'
        OR RoleId = 'B8FA338A-9DAC-4B80-B593-1860F2E1932E'
        OR RoleId = '2D0FC68B-D99A-4B7A-8AF4-1BFCF4D80225'
        OR RoleId = '4AAB6B51-E127-46F9-B1A9-349A0BCE9ABB'
        OR RoleId = 'B99E936F-0E7B-4F1E-B110-3D9E1382D5AE'
        OR RoleId = 'A9AA3DDE-B94C-4662-8D85-67DA31C98C3A'
        OR RoleId = 'E36CB13A-7324-4375-A428-67E073CC35CE'
        OR RoleId = 'DE5FAC78-41DE-418B-92FF-6884DD68DEA4'
        OR RoleId = 'A855ECC7-47BA-4752-88EC-6A66226D71BF'
        OR RoleId = 'F35BE138-A2D9-486C-BC2E-79B5461B0A1C'
        OR RoleId = '6B28B259-A5A4-4DB3-9EDF-7FEF381387B9'
        OR RoleId = 'A467CAA1-5587-44D6-AD0B-868B6ACC544C'
        OR RoleId = 'A6EF5C4C-1826-4342-A066-8A9534D972A1'
        OR RoleId = '32C8C5C8-F25C-499A-AC07-9339C9A71285'
        OR RoleId = 'C055FBE8-EFBD-4E6B-BBD5-AF25B51DAE95'
        OR RoleId = 'F90E7F80-7924-47ED-A8D4-BC10BD74BF34'
        OR RoleId = '47E1E705-8FCC-4344-A77F-D1F83DB3E864'
        OR RoleId = 'A28A0F02-9E46-4432-AE41-DD56DCB2E68E'
        OR RoleId = 'D982F02D-6177-4065-9A74-DF42B86711B5'

        -- YCS Connectivity Team
        OR RoleId = '1917678F-6956-4741-9D6E-B9767C6CB576'
        OR RoleId = 'CDE96FC0-105A-4453-8F16-C54B8DCE68EF'
		)
END



GO

GRANT EXECUTE ON [dbo].[auth_user_check_nha_ycs_af_v2] TO customer_api_user
GO
