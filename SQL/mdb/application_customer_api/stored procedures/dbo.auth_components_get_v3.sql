---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Saran ARP.		| 2011-01-31	|  Get the component related with the user.
-- Teedanai		| 2011-07-29	| Add OrderId for sorting
-- Sirichai Ch.		| 2012-07-09	| Gets related component for the specific user (for menu)
-- Pakorn W		| 2017-06-21	| remove ObjectID and ObjectType from temp and change distinct to group by statement
-- Rati P		| 2019-10-09 | performance tuning
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST: exec dbo.[auth_components_get_v3]  '750E1E53-E400-44E5-86EF-19EC23E2EABE', 'A64A8D89-F1CD-4107-8F37-577AECE7D9F0',1
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_components_get_v3]
	@userId uniqueidentifier,
	@systemId uniqueidentifier,
	@languageId int
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @Permission TABLE
	(
		  PermissionId uniqueidentifier
	)

	DECLARE @Role TABLE
	(
		  RoleId uniqueidentifier
	)

	INSERT INTO @Role
	(
		RoleId
	)
	-- GET all own role
	SELECT AUIR.RoleId
	FROM	dbo.auth_user_in_role AUIR
	WHERE	AUIR.UserId = @userId
	AND  AUIR.rec_status = 1
	UNION
	-- GET all OOO replacement role
	SELECT AUIR.RoleId
	FROM	dbo.auth_user_outofoffice_mapping AUOM
	INNER JOIN	dbo.auth_user_in_role AS AUIR
					ON  AUIR.UserId = AUOM.UserID
	WHERE  AUOM.assignedUserId = @userId
	AND		GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate
	AND		AUIR.out_of_office = 1
	AND		AUIR.rec_status = 1
	AND		AUOM.rec_status = 1

	-- Get all permissions related with the user.
	;WITH
		Permission AS
		(
				SELECT		AP.PermissionId
				FROM		dbo.auth_permissions AP
				INNER JOIN	dbo.auth_role_permissions ARP
							ON AP.PermissionId = ARP.PermissionId
							AND  ARP.rec_status = 1
				INNER JOIN	dbo.auth_roles AR
							ON ARP.RoleId = AR.RoleId
							AND  AR.rec_status = 1
				INNER JOIN	@Role AUIR
							ON AR.RoleId = AUIR.RoleId
				WHERE		AP.rec_status = 1

		)
	INSERT INTO @Permission
	(
		PermissionId
	)
		SELECT	PermissionId
		FROM	Permission AP
		GROUP BY PermissionId

	-- Get the systems
	SELECT		ASY.SystemId
			, ASY.SystemName
			, ASY.Url
			, CASE WHEN ASY.OrderId IS NULL THEN 0 ELSE ASY.OrderId END AS OrderID
			, ASY.OrderId OrderIDwithNull
			, CASE WHEN ASY.SystemId = @systemId THEN 0 ELSE 1 END AS Decode
	FROM		@permission AS AP
	INNER JOIN	dbo.auth_permissions AS ARP
				ON AP.PermissionId = ARP.PermissionId
	INNER JOIN	dbo.auth_components AS AR
				ON ARP.ComponentId = AR.ComponentId
	INNER JOIN	dbo.auth_subsystems AS AUIR
				ON AR.SubSystemId = AUIR.SubSystemId
	INNER JOIN	dbo.auth_systems AS ASY
				ON AUIR.SystemId = ASY.SystemId
	WHERE		AR.rec_status = 1
	AND		AUIR.rec_status = 1
	AND		ASY.rec_status = 1
	GROUP BY 		ASY.SystemId
			, ASY.SystemName
			, ASY.Url
			, CASE WHEN ASY.OrderId IS NULL THEN 0 ELSE ASY.OrderId END
			, ASY.OrderId
			, CASE WHEN ASY.SystemId = @systemId THEN 0 ELSE 1 END
	ORDER BY	Decode, OrderID, ASY.SystemName

	-- Get the components
	SELECT		AUIR.SubSystemId
			, ISNULL(AST.SubSystemName, AUIR.SubSystemName) AS SubSystemName
			, AR.ComponentId
			, ISNULL(ACT.ComponentName, AR.ComponentName) AS ComponentName
			, AR.url
			, ISNULL(AUIR.OrderId,0) AS SubSystemOrderId
			, ISNULL(AR.OrderId,0) AS ComponentOrderId
	FROM		@permission AP
	INNER JOIN	dbo.auth_permissions ARP
				ON AP.PermissionId = ARP.PermissionId
	INNER JOIN	dbo.auth_components AR
				ON ARP.ComponentId = AR.ComponentId

	INNER JOIN	dbo.auth_subsystems AUIR
			ON AR.SubSystemId = AUIR.SubSystemId

	INNER JOIN	dbo.auth_systems ASY
			ON AUIR.SystemId = ASY.SystemId

	LEFT JOIN	dbo.auth_subsystem_translations AST
				ON AUIR.SubSystemId = AST.SubSystemId
				AND  AST.LanguageId = @languageId
				AND AST.rec_status = 1
	LEFT JOIN	dbo.auth_component_translations ACT
				ON AR.ComponentId = ACT.ComponentId
				AND ACT.LanguageId = @languageId
				AND ACT.rec_status = 1
	WHERE		AR.rec_status = 1
	AND		AUIR.rec_status = 1
	AND		ASY.rec_status = 1
	AND		ASY.systemId = @systemId
	GROUP BY
		AUIR.SubSystemId
			, ISNULL(AST.SubSystemName, AUIR.SubSystemName)
			, AR.ComponentId
			, ISNULL(ACT.ComponentName, AR.ComponentName)
			, AR.url
			, ISNULL(AUIR.OrderId,0)
			, ISNULL(AR.OrderId,0)
	ORDER BY	SubSystemOrderId, SubSystemName, ComponentOrderId, ComponentName

END
GO

GRANT EXECUTE ON [dbo].[auth_components_get_v3] TO [customer_api_user]
GO
