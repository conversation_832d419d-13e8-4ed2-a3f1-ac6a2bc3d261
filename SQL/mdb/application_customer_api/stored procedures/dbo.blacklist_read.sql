
-------------------------------------------------------------------------------------------
-- Author		| Date			| Comments
----------------|---------------|----------------------------------------------------------
-- Andrew <PERSON>.| 2019-12-03	| Initial version
-------------------------------------------------------------------------------------------
/*
EXEC_TEST
    EXECUTE AS LOGIN = 'customer_api_user'
    EXEC dbo.blacklist_read @since = '2019-01-01';
    REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[blacklist_read]
    @since  datetime
AS
BEGIN

    SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT  TOP 10000 bt.revoked
                    , bt.token_id
                    ,bt.user_id
    FROM    dbo.capi_blacklisted_tokens AS bt
    WHERE   bt.revoked >= @since
    ORDER BY bt.revoked

END
GO

GRANT EXECUTE ON [dbo].[blacklist_insert] TO [customer_api_user]
GO
