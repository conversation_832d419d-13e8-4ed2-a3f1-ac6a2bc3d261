/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
-------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
----------------|---------------|----------------------------------------------------------
-- <PERSON><PERSON>.	| 2017-06-07	| Create
-- Sharma <PERSON>.	| 2019-02-15	| Updating the logic to add granular control over roles/uuid/hotel combination
-- Kasemtan T.  | 2024-10-22    | Update hotel name source from product_base to product_hotels
----------------|---------------|----------------------------------------------------------
/*
    EXEC_TEST EXECUTE AS LOGIN = 'customer_api_user'; EXEC dbo.security_user_in_role_block_by_property_v2 48521,'F190AD1A-9D85-49E5-9FAB-055B9CC2DA2B','F190AD1A-9D85-49E5-9FAB-055B9CC2DA2A', 'F190AD1A-9D85-49E5-9FAB-055B9CC2DA2C',1,0; REVERT;
*/
-------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_user_in_role_block_by_property_v2]
   @PropertyId INT,
   @AppId uniqueidentifier,
   @RoleId uniqueidentifier,
   @userToBeBlockedUUID uniqueidentifier,
   @isFraudulent BIT,
   @recStatus BIT
AS
BEGIN
    SET NOCOUNT ON
    SET XACT_ABORT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    -- update flag
    UPDATE  [dbo].[auth_user_in_role]
    SET     is_fraudulent       = @isFraudulent,
            rec_status          = @recStatus,
            rec_modified_by     = @AppId,
            rec_modified_when   = GETDATE()
    WHERE   ObjectId = @PropertyId
        AND ObjectType = 'H'
        AND RoleId = @RoleId
        AND UserId = @userToBeBlockedUUID
        AND rec_status <> -1 -- to eliminate updating deleted records

    -- return list of affected users with info for mail sending
    SELECT      u.UserId,
                MIN(p.communication_language_id) as LanguageId,
                MIN(h.hotel_name) as HotelName,
                MIN(u.EmailAddress) as EmailAddress,
                MIN(u.DisplayName) as DisplayName
    FROM        [dbo].[auth_user_in_role] as r
    INNER JOIN  [dbo].[product_base] as p
				ON p.product_id = r.ObjectID
    INNER JOIN  [dbo].[auth_users] as u
				ON u.UserId = r.UserId
    INNER JOIN  [dbo].[product_hotels] as h
				ON h.hotel_id = r.ObjectID
    WHERE       r.ObjectId = @PropertyId
    AND     r.ObjectType = 'H'
    AND     r.RoleId = @RoleId
    AND     u.isDeleted = 0
    AND     u.UserId = @userToBeBlockedUUID
    GROUP BY    u.UserId
END
GO

GRANT EXECUTE ON [dbo].[security_user_in_role_block_by_property_v2] TO [customer_api_user]
GO
