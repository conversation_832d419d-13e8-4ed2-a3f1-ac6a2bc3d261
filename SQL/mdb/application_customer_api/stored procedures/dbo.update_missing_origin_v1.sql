/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Anura<PERSON>         | 2021-25-03    | Update missing origin on CAPI MDB tables
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
	  DECLARE @origin_updates user_origin_update_type
	  INSERT INTO @origin_updates (user_id, member_id,origin_to_update)
	  VALUES ('97f1329c-1c8e-461c-854a-ef12cdd8b41f',1234, 'TH')
EXEC [dbo].[update_missing_origin_v1] @origin_updates, '1f46ce05-a1fb-4f28-8521-47e443ab6533';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[update_missing_origin_v1]
    @origin_updates dbo.user_origin_update_type READONLY,
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    /* Update origin in rew_members table if missing*/
    UPDATE     rwm
    SET        rwm.origin = originup.origin_to_update,
               rwm.rec_modify_when = GETDATE(),
               rwm.rec_modify_by = @modified_by
    FROM       dbo.rew_members AS rwm
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = rwm.UserId
    WHERE rwm.origin = 'XX' OR rwm.origin IS NULL

    /* Update origin in rew_contacts table if missing*/
    UPDATE     rwc
    SET        rwc.origin = originup.origin_to_update,
               rwc.rec_modify_when = GETDATE(),
               rwc.rec_modify_by = @modified_by
    FROM       dbo.rew_contacts AS rwc
    INNER JOIN @origin_updates AS originup
    ON         originup.member_id = rwc.MemberID
    WHERE rwc.origin = 'XX' OR rwc.origin IS NULL



END
GO

GRANT EXECUTE ON [dbo].[update_missing_origin_v1] TO [customer_api_user]
GO
