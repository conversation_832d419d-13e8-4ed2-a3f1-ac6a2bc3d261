-------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- E<PERSON><PERSON>            | 2011-11-18  | Select users with current relations
-- <PERSON>      | 2011-12-28  | Refactor for coding standards
-- Kit<PERSON><PERSON> | 2019-01-17  | Add @ObjectId as a parameter
-- Weerapong Mua        | 2022-09-28  | Optimize ObjectType = 'H' with ObjectId = Null
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.backoffice_user_select_current_relations_v3 'H'
REVERT
END_EXEC_TEST
*/

CREATE PROCEDURE [dbo].[backoffice_user_select_current_relations_v3]
    @ObjectType char(1),
    @ObjectId   INT = NULL
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    IF @ObjectID IS NULL
    BEGIN
        -- no necessity to join product_hotels or afl_affiliate
        SELECT m.username AS UserName, m.user_id AS UserId
        FROM dbo.auth_user_mapping AS m
        WHERE m.authentication_type_id = 1
        AND m.rec_status = 1
        AND EXISTS
        (
            SELECT * FROM dbo.auth_user_relations AS r
            WHERE r.UserId = m.user_id
            AND r.ObjectType = @ObjectType
        )
    END
    ELSE IF @ObjectType = 'H'
    BEGIN
        -- objectID is not null && @ObjectType = 'H'
        SELECT m.username AS UserName, m.user_id AS UserId
        FROM dbo.auth_user_mapping AS m
        WHERE m.authentication_type_id = 1
        AND m.rec_status = 1
        AND EXISTS
        (
            SELECT *
            FROM dbo.auth_user_relations AS r
            INNER JOIN dbo.product_hotels AS h
                ON r.ObjectId = h.Hotel_Id
            WHERE r.UserId = m.user_id
            AND r.ObjectType = @ObjectType
            AND r.ObjectId = @ObjectId
        )
    END
    ELSE IF @ObjectType = 'F'
    BEGIN
        -- objectID is not null && @ObjectType = 'F'
        SELECT m.username AS UserName, m.user_id AS UserId
        FROM dbo.auth_user_mapping AS m
        WHERE m.authentication_type_id = 1
        AND m.rec_status = 1
        AND EXISTS
        (
            SELECT *
            from dbo.auth_user_relations AS r
            INNER JOIN dbo.afl_affiliate AS h
                ON r.ObjectId = h.affliate_id
            WHERE r.UserId = m.user_id
            AND r.ObjectType = @ObjectType
            AND r.ObjectId = @ObjectId
        )
    END
END
GO

GRANT EXECUTE ON [dbo].[backoffice_user_select_current_relations_v3] TO [customer_api_user]
GO
