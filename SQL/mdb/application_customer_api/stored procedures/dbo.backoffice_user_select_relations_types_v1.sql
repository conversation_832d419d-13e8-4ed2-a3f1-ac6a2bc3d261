
------------------------------------------------------------------------------------------------------------------------------
-- Author		        | Date		    | Comment
--------------------|-------------|------------------------------------------------------------------------------------------
-- Efren <PERSON>		    | 2011-11-21	| Select users relations types
-- Nicolas Souquet	| 2011-12-12	| Refactor for coding standards
-- Pongtham W.		  | 2012-01-06	| Removed checking the objectID 0
-- Sir<PERSON><PERSON> Ch.		  | 2012-07-13	| Support Out of office
-- Mrun S.          | 2019-02-08  | Rename backoffice_user_select_relations_types_MDC to backoffice_user_select_relations_types_v1
-----------------------------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC backoffice_user_select_relations_types_v1 '437EF331-6550-497F-B81F-22088BD17FE0', '4149b9d5-a948-407c-8058-43b504cdc466'
REVERT;
END_EXEC_TEST
*/

-----------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_user_select_relations_types_v1]
	@UserId uniqueidentifier,
	@ComponentId uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

		SELECT		AUIR.ObjectType AS ObjectType
				, AOT.ObjectName AS ObjectName
		FROM		dbo.auth_permissions AS AP
		INNER JOIN	dbo.auth_role_permissions AS ARP
					ON AP.PermissionId = ARP.PermissionId
		INNER JOIN	dbo.auth_user_in_role AS AUIR
					ON ARP.RoleId = AUIR.RoleId
		INNER JOIN	dbo.auth_object_type AS AOT
					ON AOT.ObjectType = AUIR.ObjectType
		WHERE		AP.rec_status = 1
		AND		AUIR.rec_status = 1
		AND		AUIR.UserId = @UserId
		AND		AP.componentId = @componentId
		AND		AOT.ObjectType IN ('F','H')
		AND		ARP.rec_status = 1
	UNION
		SELECT		AUIR.ObjectType AS ObjectType
					, AOT.ObjectName AS ObjectName
		FROM		dbo.auth_permissions AS AP
		INNER JOIN	dbo.auth_role_permissions AS ARP
					ON AP.PermissionId = ARP.PermissionId
		CROSS JOIN	dbo.auth_user_outofoffice_mapping AS AUOM
		INNER JOIN	dbo.auth_user_in_role AS AUIR
					ON ARP.RoleId = AUIR.RoleId
					AND AUIR.rec_status = 1
					AND AUIR.UserId = AUOM.UserId
		INNER JOIN	dbo.auth_object_type AS AOT
					ON AOT.ObjectType = AUIR.ObjectType
		WHERE		AP.rec_status = 1
		AND		AUOM.assignedUserId = @userId	
		AND		AUOM.rec_status = 1
		AND		GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate	
		AND		AP.componentId = @componentId
		AND		AOT.ObjectType IN ('F','H')
		AND		ARP.rec_status = 1
END




GO

GRANT EXECUTE ON [dbo].[backoffice_user_select_relations_types_v1] TO [customer_api_user]
GO
