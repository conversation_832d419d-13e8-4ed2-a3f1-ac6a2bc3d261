
---------------------------------------------------------------------------------------------------
-- Author               | Date          | Comment
------------------------|---------------|----------------------------------------------------------
-- Christofer Ta        | 2017-02-01    | Fetch social info for a specific Agoda user
------------------------|---------------|----------------------------------------------------------
-- Test : EXEC [security_social_user_info_select_by_userid] '5D3C547F-5D8A-4630-B1C0-1286E03670CC'
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_social_user_info_select_by_userid]
       @user_id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

       SELECT user_info_id
           , user_id
           , app_user_id
           , app_id
           , user_info
    FROM   dbo.auth_social_user_info
    WHERE  user_id = @user_id 
    AND    is_active = 1

END




GO

GRANT EXECUTE ON [dbo].[security_social_user_info_select_by_userid] TO [customer_api_user]
GO
