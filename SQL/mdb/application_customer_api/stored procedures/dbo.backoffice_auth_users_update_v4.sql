
---------------------------------------------------------------------------------------------------
-- Author		        | Date		    | Comment
------------------------|---------------|----------------------------------------------------------
-- Sir<PERSON><PERSON> Ch. 		| 2012-03-06	| Updated the user details
-- Nasakol P.		    | 2018-10-10	| Update fields
------------------------|---------------|----------------------------------------------------------
/*
EXEC_TEST

EXECUTE AS LOGIN = 'customer_api_user'
exec dbo.backoffice_auth_users_update_v4
    @userId = '2D1E17C2-8849-468F-B914-0E85EB12DFD3',
    @Manager = null,
    @IsBlocked = 1,
    @rec_modified_by = '1f46ce05-a1fb-4f28-8521-47e443ab6533',
    @IsSingleTaskConsumption = 1,
    @DepartmentId = null,
    @LocationId = null
REVERT;

END_EXEC_TEST
*/
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_users_update_v4]
(
	@UserId				uniqueidentifier,
	@Manager			uniqueidentifier,
	@IsBlocked			bit,
	@rec_modified_by	uniqueidentifier,
	@IsSingleTaskConsumption bit = 1,
	@DepartmentId int = null,
	@LocationId int = null
)
AS
BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    BEGIN TRY
        BEGIN TRANSACTION;

        DECLARE @oldIsBlocked bit
        --Keep the isblock status for later use.
        SELECT @oldIsBlocked = IsBlocked FROM dbo.auth_users WHERE USERID = @UserId

        UPDATE dbo.auth_users
        SET Manager = @Manager,
            IsBlocked = @IsBlocked,
            rec_modified_by = @rec_modified_by,
            rec_modified_when = GETDATE(),
            is_single_task_consumption = @IsSingleTaskConsumption,
            Department_Id = @DepartmentId,
            location_id = @locationid,
            FailedLoginAttempts = CASE WHEN @IsBlocked = 0 AND @oldIsBlocked = 1 THEN 0 ELSE FailedLoginAttempts END
        WHERE UserId = @UserId

        COMMIT TRANSACTION

    END TRY
    BEGIN CATCH
        DECLARE @err_msg nvarchar(2000) = ERROR_MESSAGE()
        IF XACT_STATE() <> 0
        BEGIN
            ROLLBACK TRANSACTION
        END
        RAISERROR(@err_msg, 16, 1)
    END CATCH

END
GO

GRANT EXECUTE ON [dbo].[backoffice_auth_users_update_v4] TO [customer_api_user]
GO
