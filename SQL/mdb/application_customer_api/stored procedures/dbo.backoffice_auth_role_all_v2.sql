---------------------------------------------------------------------------------------------------
-- Author		              | Date		| Comment
-------------------------------------------------------------------------------------------------
-- <PERSON><PERSON><PERSON> | 01/31/2011	| Get All Authentication Role
-- Po<PERSON><PERSON> Wiangwang	    | 10/16/2012	| Created v2 - Added object type
-- Mrun S                 | 02/26/2019  | Rename to backoffice_auth_role_all_v2
------------------------|---------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_role_all_v2]
REVERT;
END_EXEC_TEST
*/
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_role_all_v2]
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	SELECT RoleId
		, RoleName
		, ISNULL(ObjectType, '') AS ObjectType
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
		, ISNULL((
				SELECT emailAddress
				FROM dbo.auth_users
				WHERE UserId = ar.rec_created_by
				), '') AS 'EmailCreatedBy'
		, ISNULL((
				SELECT emailAddress
				FROM dbo.auth_users
				WHERE UserId = ar.rec_modified_by
				), '') AS 'EmailModifiedBy'
	FROM dbo.auth_roles ar
	WHERE rec_status > 0
	ORDER BY RoleName
END

GO

GRANT EXECUTE ON [dbo].[backoffice_auth_role_all_v2] TO [customer_api_user]
GO
