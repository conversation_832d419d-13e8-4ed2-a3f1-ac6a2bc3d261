---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- E<PERSON><PERSON>            | 2011-11-17  | To validate the data in temporary table and execute sp alert or sp finalize.
-- <PERSON>      | 2011-12-28  | Refactor for coding standards
-- Pongtham W.          | 2012-01-06  | Checked the user permissions against the component (5)
-- GyÃ¶rgy <PERSON>        | 2015-09-30  | Check ambigous rows
-- Taweekiat P.         | 2016-08-25  | Split MERGE statement to UPDATE/INSERT
-- Kit<PERSON><PERSON> | 2019-01-22  | Remove username column of auth_user_mapping table
-- Kit<PERSON><PERSON> | 2019-02-13  | Initial backoffice_upload_auth_user_relations_v2 from backoffice_upload_auth_user_relations_v3_Process
-- Weerapong Mua        | 2022-02-13  | use backoffice_upload_auth_user_relations_finalize_v2
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
DECLARE @upload upload_auth_user_relations
INSERT INTO @upload (line, userId, objectType, objectId)
VALUES (1, '2bb76b22-3c29-4451-b3d2-24ad0d0888aa', 0, 0), (2, 'af7a0145-6d3f-410f-ae8b-b97b97b011d2', 0, 0)
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.backoffice_upload_auth_user_relations_v2 @upload, '9f6b60f7-4215-4da6-b030-fb6047db6c4c', '704fcd31-9931-4c07-9f17-741afef1dc00'
REVERT;
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_upload_auth_user_relations_v2]
  @upload       upload_auth_user_relations READONLY,
  @uploadBy     UNIQUEIDENTIFIER,
  @componentId  UNIQUEIDENTIFIER
AS
BEGIN
  SET XACT_ABORT ON
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
  DECLARE @uploadWhen DATETIME
  SET @uploadWhen = GETDATE()
  -- ===================================
  -- Part 1. Validate Data
  -- ===================================
    --1. check valid username (from its userId)
    SELECT  line AS line
            , 'UserId / Email' AS field
            , 'User does not exist.' AS [message]
    FROM  @upload u
    WHERE  NOT EXISTS
      (
        SELECT  *
        FROM  dbo.auth_user_mapping
        WHERE  user_id = u.userId
        AND  authentication_type_id = 1
      )
    UNION ALL
    --2. check valid ObjectType for hotel type
    SELECT  line AS line
            , 'ObjectType' AS field
            , 'ObjectType is invalid.' AS [message]
    FROM  @upload u
    WHERE  NOT EXISTS
      (
        SELECT  *
        FROM  dbo.auth_object_type
        WHERE  ObjectType = u.ObjectType
      )
    UNION ALL
    --3. check valid object id
    SELECT  line AS line
            , 'ObjectId' AS field
            , 'ObjectId is invalid.' AS [message]
    FROM  @upload u
    WHERE  NOT EXISTS
      (
        SELECT  *
        FROM  dbo.GetObjectList_v2(u.ObjectType, u.ObjectId)
      )
    UNION ALL
    --4. check for duplicate data in auth_user_relations_upload
    SELECT    u.line AS line
              , 'Duplicate Records' AS field
              , 'Duplicate record exists.' AS [message]
    FROM    @upload AS u
    INNER JOIN  @upload AS a
          ON u.ObjectId = a.ObjectId
          AND u.UserId = a.UserId
          AND u.ObjectType = a.ObjectType
          AND u.line <> a.line
    UNION ALL
    --5. check for user permission for object type H or F
    SELECT    line AS line
              , '-' AS field
              , 'You have no permission to upload for this ''' + objectType + ''' object type.' AS [message]
    FROM    @upload u
    WHERE    NOT EXISTS
      (
        SELECT    *
        FROM    dbo.auth_permissions AS AP
        INNER JOIN  dbo.auth_role_permissions AS ARP
            ON AP.PermissionId = ARP.PermissionId
        INNER JOIN  dbo.auth_user_in_role AS UIR
            ON ARP.RoleId = UIR.RoleId
            AND UIR.rec_status = 1
            AND UIR.ObjectType IN ('F','H')
        INNER JOIN  @upload AS URU
            ON URU.ObjectType = UIR.ObjectType
        WHERE UIR.UserId = @uploadBy
        AND   AP.ComponentId = @componentId
        AND   AP.rec_status = 1
        AND   ARP.rec_status = 1
      )
    UNION ALL
    --6. check for duplicate data in auth_user_relations_upload
    SELECT    u.line AS line
              , 'ObjectId' AS field
              , 'Ambigous record exists.' AS [message]
    FROM    @upload AS u
    INNER JOIN  @upload AS a
          ON u.ObjectId = a.ObjectId
          AND u.ObjectType = a.ObjectType
          AND u.line <> a.line
    ORDER BY line, field
  -- ===================================
  -- Part 2. Merge Data
  -- ===================================
  IF @@ROWCOUNT = 0
    BEGIN
    BEGIN TRY
      BEGIN TRANSACTION
      ;WITH CTE AS
      (
        SELECT  R.UserId
                , R.ObjectType
                , R.ObjectId
                , 1 AS rec_status
                , @uploadBy AS uploaded_by
                , @uploadWhen AS uploaded_when
        FROM    @upload AS R
        INNER JOIN  dbo.auth_user_mapping AS U
              ON R.UserId = U.user_id
        WHERE    U.authentication_type_id = 1
      )
      UPDATE    T
      SET    T.UserId = S.UserId
          , T.rec_status = S.rec_status
          , T.rec_modified_by = S.uploaded_by
          , T.rec_modified_when = S.uploaded_when
      FROM    dbo.auth_user_relations AS T
      INNER JOIN  CTE AS S
            ON S.ObjectType = T.ObjectType
            AND S.ObjectId = T.ObjectId
      WHERE    BINARY_CHECKSUM(T.UserId, T.rec_status, T.rec_modified_by, T.rec_modified_when) <> BINARY_CHECKSUM(S.UserId, S.rec_status, S.uploaded_by, S.uploaded_when);
      INSERT INTO  dbo.auth_user_relations
      (
          UserId
          , ObjectType
          , ObjectId
          , rec_status
          , rec_created_by
          , rec_created_when
      )
      SELECT  R.UserId
              , R.ObjectType
              , R.ObjectID
              , 1 AS rec_status
              , @uploadBy AS uploaded_by
              , @uploadWhen AS uploaded_when
      FROM    @upload AS R
      INNER JOIN  dbo.auth_user_mapping AS U
            ON R.UserId = U.user_id
      WHERE    U.authentication_type_id = 1
      AND    NOT EXISTS (  SELECT *
              FROM  dbo.auth_user_relations T
              WHERE  R.ObjectType = T.ObjectType
              AND  R.ObjectId = T.ObjectId);
      COMMIT TRANSACTION
      EXEC dbo.backoffice_upload_auth_user_relations_finalize_v2
    END TRY
    BEGIN CATCH
      DECLARE @err_msg nvarchar(2048) = ERROR_MESSAGE()
      IF XACT_STATE() <> 0
      BEGIN
        ROLLBACK TRANSACTION
      END
      ;THROW 51000, @err_msg, 1;
    END CATCH
  END
END
GO

GRANT EXECUTE ON [dbo].[backoffice_upload_auth_user_relations_v2] TO [customer_api_user]
GO
