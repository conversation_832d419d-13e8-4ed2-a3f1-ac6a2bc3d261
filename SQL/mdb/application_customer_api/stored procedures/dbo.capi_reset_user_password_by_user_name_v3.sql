-- ===============================================================================================================================================================================================================================================================================================================
-- AUTHOR:      Khunakon <PERSON>kha<PERSON>lanun
-- CREATE AT:   2018-04-20 10:08
-- DESCRIPTION: Reset a member password for phone number account
-- CHANGE:
--   * Logging change to history. [Khunakon Sukhakanlanun, 2018-05-10]
--   * Add 2 more params for future sync purpose between MDC and MDB. [Khunakon Sukhakanlanun, 2018-05-10]
--   * v3, extend PII columns size WFAPI-2152 + encryption support [Andrey S., 2018-10-07]
--
-- EXEC_TEST : EXEC dbo.capi_reset_user_password_by_user_name_v3 '+***********', '+***********', 'phone://+***********/$2a$10$JGLcXzMsQ8Bsz4TMn9dZ6O7XP/7zzEw5deUHtNCscdsJ1PE3Nn26', 90, 7, '$2a$10$JGLcXzMsQ8Bsz4TMn9dZ6O7XP/7zzEw5deUHtNCscdsJ1PE3Nn26', NULL, '92A39077-DE2E-41EE-B85B-3D271254A7B7', 6;
-- ===============================================================================================================================================================================================================================================================================================================

CREATE PROCEDURE [dbo].[capi_reset_user_password_by_user_name_v3]
    @user_name VARCHAR(400),
    @user_name_encrypted VARCHAR(400),
    @uri_encrypted VARCHAR(512),
    @password_expire_in_days INT,
    @authentication_type_id INT,
    @hash_password VARCHAR(128),
    @salt VARCHAR(36),
    @rec_modified_by UNIQUEIDENTIFIER,
    @activity_id INT

AS
    BEGIN

        SET NOCOUNT ON ;
        SET XACT_ABORT ON;
        SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED ;

        DECLARE @user_id UNIQUEIDENTIFIER,
                @row_efx INT = 0,
                @auth_user_mapping_history_id BIGINT,
                @auth_password_history_id INT

        SELECT @user_id = user_id
            FROM dbo.auth_user_mapping
            WHERE authentication_type_id = @authentication_type_id
            AND (username = @user_name OR username = @user_name_encrypted);

        IF @user_id IS NOT NULL
            BEGIN

                BEGIN TRANSACTION;

                    UPDATE dbo.auth_user_mapping
                    SET
                        uri = @uri_encrypted,
                        expired_since = DATEADD(DAY, @password_expire_in_days, GETDATE()),
                        rec_modified_by = @rec_modified_by,
                        password_last_changed = GETDATE(),
                        salt = @salt
                    WHERE
                        authentication_type_id = @authentication_type_id AND (username = @user_name OR username = @user_name_encrypted);

                    SET @row_efx = @@ROWCOUNT;

                    IF @row_efx > 0
                        BEGIN
                            -- log to history.
                            INSERT INTO dbo.auth_user_mapping_history
                            (
                	            logtime
                	            , [user_id]
                	            , authentication_type_id
                	            , ActivityId
                	            , rec_status
                	            , rec_created_when
                	            , rec_created_by
                	            , rec_modified_when
                	            , rec_modified_by

                            )
                            VALUES
                            (
                	            GETDATE()
                	            , @user_id
                	            , @authentication_type_id
                	            , @activity_id
                	            , 1
                	            , GETDATE()
                	            , @rec_modified_by
                	            , NULL
                	            , NULL
                            );

                            SELECT @auth_user_mapping_history_id = CAST(SCOPE_IDENTITY() AS BIGINT);


                        	INSERT INTO dbo.auth_password_history
                        	(
                        		user_id,
                        		pwhash,
                        		salt,
                        		rec_created_by,
                        		rec_created_when
                        	)
                        	VALUES
                        	(
                        		@user_id,
                        		@hash_password,
                        		@salt,
                        		@rec_modified_by,
                        		GETDATE()
                        	);

                        	SELECT @auth_password_history_id = CAST(SCOPE_IDENTITY() AS INT);
                        END

                COMMIT TRANSACTION;

            END

        SELECT
            @row_efx AS row_count,
            @auth_password_history_id AS auth_password_history_id,
            @auth_user_mapping_history_id AS auth_user_mapping_history_id;

    END
GO

GRANT EXECUTE ON [dbo].[capi_reset_user_password_by_user_name_v3] TO customer_api_user;
GO

