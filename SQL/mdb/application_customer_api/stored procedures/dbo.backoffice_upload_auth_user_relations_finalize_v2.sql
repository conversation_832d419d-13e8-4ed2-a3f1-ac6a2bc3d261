----------------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
----------------------------------------------------------------------------------------------------------
-- Pi<PERSON>t J.		| 2012-02-09	| To update the data from auth_user_relations to auth_user_in_role
-- György Giczi		| 2015-04-07	| Formatting to coding standards, added distinct to the select
-- György Giczi		| 2015-09-28	| Rewrite using merge
-- Atiruj P.		| 2016-05-11	| Rewrite to use update and insert
-- <PERSON><PERSON><PERSON><PERSON>		| 2017-02-03	| Track changes
-- <PERSON><PERSON><PERSON>	| 2017-02-22	| Romoved test code
-- Su<PERSON><PERSON>	| 2017-05-02	| Changed to ensure created_by is not null
-- Sornram Sangutai	| 2022-03-10	| Optimized for quicker release transaction on auth_user_in_role in DBPS-6091
----------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_upload_auth_user_relations_finalize_v2]
REVERT;
END_EXEC_TEST
*/
----------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_upload_auth_user_relations_finalize_v2]
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	DECLARE @ObjectType char(1) = 'H'
		, @roleid uniqueidentifier = '78ABBE95-B5DF-4894-839C-BDC4F8B65BB9'

	CREATE TABLE #temp_auth_user_relations
	(
		UserId uniqueidentifier NOT NULL
		, roleid uniqueidentifier NOT NULL
		, ObjectType char(1) NOT NULL
		, ObjectId  int NOT NULL
		, rec_status	int NOT NULL
		, rec_created_when	datetime NOT NULL
		, rec_created_by	uniqueidentifier NOT NULL
		, rec_modified_when	datetime NULL
		, rec_modified_by	uniqueidentifier NULL
		, PRIMARY KEY(UserId, RoleId, ObjectType, ObjectID)
	)
	INSERT INTO  #temp_auth_user_relations
	(
			UserId
			, roleid
			, ObjectType
			, ObjectId
			, rec_status
			, rec_created_by
			, rec_created_when
			, rec_modified_by
			, rec_modified_when
	)
	SELECT DISTINCT	ur.UserId
			, @roleid AS roleid
			, ur.ObjectType
			, ur.ObjectId
			, ur.rec_status
			, ur.rec_created_by
			, ur.rec_created_when
			, ur.rec_modified_by
			, ur.rec_modified_when
	FROM		dbo.auth_user_relations AS ur
	WHERE		ur.ObjectType = @ObjectType



	SELECT		SRC.ObjectId  as hotel_id
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), SRC.UserId) + ' | role_id: ' + CONVERT(varchar(36), SRC.RoleId) as new_value
			, CASE WHEN SRC.rec_status = 1 THEN 'Added' ELSE 'Deleted' END as remark
			, ISNULL(SRC.rec_modified_by, SRC.rec_created_by) as created_by
			, GETDATE() as created_when
	INTO	#c1
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	#temp_auth_user_relations AS SRC
				ON EXISTS
				(
					SELECT *
					FROM	dbo.auth_user_in_role AS uir
					WHERE	uir.UserId = SRC.userID
					AND	uir.ObjectID = SRC.ObjectID
					AND	uir.roleid = SRC.roleid
					AND	uir.ObjectType = SRC.ObjectType
					AND	uir.rec_status <> SRC.rec_status
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		SRC.objectType = 'H'

	INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
	(
			hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	)
	SELECT		hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	FROM		#c1

	-- UPDATE TGT

	SELECT			TGT.UserId, TGT.RoleId, TGT.ObjectType, TGT.ObjectID
					,src.rec_status
					,src.rec_modified_by
					,src.rec_modified_when
	INTO			#temp_upd
	FROM			#temp_auth_user_relations as SRC
	INNER JOIN		dbo.auth_user_in_role AS TGT
						ON TGT.UserId = SRC.userID
				AND TGT.ObjectID = SRC.ObjectID
				AND TGT.ObjectType = SRC.ObjectType
	WHERE	TGT.rec_status <> SRC.rec_status

	UPDATE		TGT
	SET		rec_status = src.rec_status
			, rec_modified_by = src.rec_modified_by
			, rec_modified_when = src.rec_modified_when
	FROM		dbo.auth_user_in_role AS TGT
	INNER JOIN	#temp_upd AS SRC
				ON TGT.UserId = SRC.userID
				AND TGT.ObjectID = SRC.ObjectID
				AND TGT.roleid = SRC.roleid
				AND TGT.ObjectType = SRC.ObjectType


	-- END UPDATE TGT

	SELECT		A.ObjectId  as hotel_id
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), A.UserId) + ' | role_id: ' + CONVERT(varchar(36), A.RoleId) as new_value
			, 'Added' as remark
			, ISNULL(A.rec_modified_by, A.rec_created_by) as created_by
			, GETDATE() as created_when
	INTO	#c2
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	#temp_auth_user_relations AS A
				ON NOT EXISTS
				(
					SELECT *
					FROM	dbo.auth_user_in_role AS B
					WHERE	A.roleid = B.roleid
					AND	A.ObjectType = B.ObjectType
					AND	A.userID = B.UserId
					AND	A.objectID = B.ObjectID
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		A.objectType = 'H'



	INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
	(
			hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	)
	SELECT		hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	FROM		#c2


	-- INSERT TGT


	SELECT	UserId
		, RoleId
		, ObjectType
		, ObjectID
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
	INTO	#temp_ins
	FROM	#temp_auth_user_relations AS A
	WHERE	NOT EXISTS
				(
					SELECT *
					FROM	dbo.auth_user_in_role AS B
					WHERE	A.roleid = B.roleid
					AND	A.ObjectType = B.ObjectType
					AND	A.userID = B.UserId
					AND	A.objectID = B.ObjectID
				)

	INSERT INTO	dbo.auth_user_in_role
	(
		UserId
		, RoleId
		, ObjectType
		, ObjectID
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
	)
	SELECT	UserId
		, RoleId
		, ObjectType
		, ObjectID
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
	FROM	#temp_ins AS A

	--END INSERT TGT


	SELECT		TGT.ObjectId  as hotel_id
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), TGT.UserId) + ' | role_id: ' + CONVERT(varchar(36), TGT.RoleId) as new_value
			, 'Deleted' as remark
			, ISNULL(rec_modified_by, '00000000-0000-0000-0000-000000000000') as created_by
			, GETDATE() as created_when
	INTO	#c3
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	dbo.auth_user_in_role AS TGT
				ON NOT EXISTS
				(
					SELECT	*
					FROM	#temp_auth_user_relations AS SRC
					WHERE	TGT.userID = SRC.userID
					AND	TGT.objectID = SRC.objectID
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		TGT.objectType = 'H'
	AND		TGT.RoleId = @roleid

	INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
	(
			hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	)
	SELECT		hotel_id
			, [type_id]
			, new_value
			, remark
			, created_by
			, created_when
	FROM		#c3



	--DEL TGT


	SELECT		UserId, RoleId, ObjectType, ObjectID
	INTO		#temp_del
	FROM	dbo.auth_user_in_role AS TGT
	WHERE	TGT.RoleId = @roleid
	AND	TGT.objectType = @ObjectType
	AND	NOT EXISTS
	(
		SELECT	*
		FROM	#temp_auth_user_relations AS SRC
		WHERE	TGT.userID = SRC.userID
		AND	TGT.objectID = SRC.objectID
	)



	DELETE			TGT
	FROM			dbo.auth_user_in_role AS TGT
	INNER JOIN		#temp_del AS del
					ON	TGT.UserId = del.userID
						AND TGT.ObjectID = del.ObjectID
						AND TGT.roleid = del.roleid
						AND TGT.ObjectType = del.ObjectType


	--END DEL TGT

	DROP TABLE #temp_auth_user_relations
	DROP TABLE #temp_del
	DROP TABLE #temp_ins
	DROP TABLE #temp_upd
	DROP TABLE #c1
	DROP TABLE #c2
	DROP TABLE #c3


END
GO

GRANT EXECUTE ON [dbo].[backoffice_upload_auth_user_relations_finalize_v2] TO [customer_api_user]
GO