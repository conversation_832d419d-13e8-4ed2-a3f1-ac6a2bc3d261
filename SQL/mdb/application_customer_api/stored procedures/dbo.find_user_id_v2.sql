-----------------------------------------------------------------------------------------------------------
-- Author			 | Date		   | Comment
---------------------|-------------|----------------------------------------------------------
-- <PERSON>      | 2018-11-20  | First version
-- <PERSON>       | 2019-02-21  | Add whitelabel_id
---------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC  [dbo].[find_user_id_v2] 'test','test','test',0,'all',1
REVERT;
END_EXEC_TEST
*/
--------------------------------|---------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[find_user_id_v2]
@username varchar(400),
@encryptedUsername varchar(400),
@lowerEncryptedUsername varchar(400),
@allRec TINYINT,
@mode varchar(10),
@whiteLabelId smallint
AS
BEGIN
  	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

 	IF @mode = 'all'
	BEGIN
			SELECT	au.UserId,au.EmailAddress as username
			FROM	dbo.auth_users au
			WHERE	EmailAddress in (@username,@encryptedUsername,@lowerEncryptedUsername)
			AND	(au.rec_status = 1 OR @allRec = 1)
			AND ISNULL(au.whitelabel_id, 1) = @whiteLabelId

			SELECT	aum.user_id AS UserId,aum.username,aum.authentication_type_id
			FROM	dbo.auth_user_mapping aum
			WHERE	aum.username IN (@username,@encryptedUsername,@lowerEncryptedUsername)
			AND	(aum.rec_status = 1 OR @allRec = 1)
			AND ISNULL(aum.whitelabel_id, 1) = @whiteLabelId

			SELECT	mem.UserId,con.contact_method_value as username
			FROM	dbo.rew_contacts con
			INNER JOIN dbo.rew_members as mem
				ON mem.MemberID= con.MemberID
			WHERE	contact_method_value IN (@username,@encryptedUsername,@lowerEncryptedUsername)
			AND	contact_method_id = 1
			AND	(con.rec_status = 1 OR @allRec = 1)
			AND ISNULL(con.whitelabel_id, 1) = @whiteLabelId
	END
	ELSE
		IF @mode = 'user'
			SELECT	au.UserId,au.EmailAddress AS username
			FROM	dbo.auth_users au
			WHERE	EmailAddress in (@username,@encryptedUsername,@lowerEncryptedUsername)
			AND	(au.rec_status = 1 OR @allRec = 1)
			AND ISNULL(au.whitelabel_id, 1) = @whiteLabelId
		ELSE
		IF @mode = 'mapping'
			SELECT	aum.user_id AS UserId,aum.username,aum.authentication_type_id
			FROM	dbo.auth_user_mapping aum
			WHERE	aum.username in (@username,@encryptedUsername,@lowerEncryptedUsername)
			AND	(aum.rec_status = 1 OR @allRec = 1)
			AND ISNULL(aum.whitelabel_id, 1) = @whiteLabelId
		ELSE
			SELECT	mem.UserId,con.contact_method_value as username
			FROM	dbo.rew_contacts con
			INNER JOIN dbo.rew_members as mem
				ON mem.MemberID= con.MemberID
			WHERE	contact_method_value in (@username,@encryptedUsername,@lowerEncryptedUsername)
			AND	contact_method_id = 1
			AND	(con.rec_status = 1 OR @allRec = 1)
			AND ISNULL(con.whitelabel_id, 1) = @whiteLabelId
END
GO

GRANT EXECUTE ON [dbo].[find_user_id_v2] TO [customer_api_user]
GO
