---------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|-------------------------------------------------------------------
-- J <PERSON> | 2018-03-09 | check whether user is NHA/YCS/AFFLIATES
-- Nasakol P.     | 2018-04-10 | add more roles about ycs
-- J <PERSON> | 2018-05-25 | replies exactly what this user is - can be multiple
-- Andrey S.      | 2018-10-07 | v4, extend PII columns size WFAPI-2152
------------------|------------|-------------------------------------------------------------------
-- MONKEY_TEST : EXEC dbo.auth_user_check_nha_ycs_af_v4 '<EMAIL>'
-------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[auth_user_check_nha_ycs_af_v4]
(
	@emailAddress varchar(400)
)
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
        CASE
            WHEN RoleId IN (
             -- YCS
             'CA5F7FFD-9359-4072-8230-069FE9701CE1',
             'B8FA338A-9DAC-4B80-B593-1860F2E1932E',
             '2D0FC68B-D99A-4B7A-8AF4-1BFCF4D80225',
             '4AAB6B51-E127-46F9-B1A9-349A0BCE9ABB',
             'B99E936F-0E7B-4F1E-B110-3D9E1382D5AE',
             'A9AA3DDE-B94C-4662-8D85-67DA31C98C3A',
             'E36CB13A-7324-4375-A428-67E073CC35CE',
             'DE5FAC78-41DE-418B-92FF-6884DD68DEA4',
             'A855ECC7-47BA-4752-88EC-6A66226D71BF',
             'F35BE138-A2D9-486C-BC2E-79B5461B0A1C',
             '6B28B259-A5A4-4DB3-9EDF-7FEF381387B9',
             'A467CAA1-5587-44D6-AD0B-868B6ACC544C',
             'A6EF5C4C-1826-4342-A066-8A9534D972A1',
             '32C8C5C8-F25C-499A-AC07-9339C9A71285',
             'C055FBE8-EFBD-4E6B-BBD5-AF25B51DAE95',
             'F90E7F80-7924-47ED-A8D4-BC10BD74BF34',
             '47E1E705-8FCC-4344-A77F-D1F83DB3E864',
             'A28A0F02-9E46-4432-AE41-DD56DCB2E68E',
             'D982F02D-6177-4065-9A74-DF42B86711B5',
             -- YCS Connectivity Team
             '1917678F-6956-4741-9D6E-B9767C6CB576',
             'CDE96FC0-105A-4453-8F16-C54B8DCE68EF')
            THEN 'YCS'

           WHEN RoleId = 'A2C5AF8B-90E9-47D6-A75D-B6E0805E9CE3'    -- Host
           THEN 'NHA'

           WHEN RoleId = '6ba94885-84b9-4a20-afa3-6675dfecc1c4'  -- Affiliate Admin
           THEN 'AFFILIATE'

           ELSE ''
       END AS Role
    FROM dbo.auth_users au
    INNER JOIN  dbo.auth_user_in_role auir
    ON au.UserId = auir.UserId
    WHERE au.EmailAddress = @emailAddress
    AND auir.rec_status = 1

END
GO

GRANT EXECUTE ON [dbo].[auth_user_check_nha_ycs_af_v4] TO [customer_api_user]
GO
