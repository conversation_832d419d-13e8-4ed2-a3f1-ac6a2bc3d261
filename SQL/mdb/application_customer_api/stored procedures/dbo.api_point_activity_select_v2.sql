
/*
---------------------------------------------------------------------------------------------------
-- Author	| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- Saran B.	| 2012-06-19	| select member's activity.
-- Saran B.	| 2013-01-09	| Added the city_name.
-- Tamas Varga	| 2014-06-24	| Created v2 to return is_user_logged_in.
----------------|---------------|----------------------------------------------------------
Exec [api_point_activity_select_v2] 3
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_point_activity_select_v2]
	@memberId int
AS
BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT		a.point_activity_id AS point_activity_id
			, dbo.fn_rew2_ebe_booking_status_id(a.booking_id) AS booking_status_id
			, point_activity_type_id  
			, a.memberid AS memberid
			, booking_summary.is_user_logged_in
			, a.booking_id AS booking_id
			, a.remarks AS remarks  
			, a.rec_created_when AS activity_date  
			, ISNULL(dbo.fn_rew2_ebe_booking_status(a.booking_id), '') AS Booking_status    
			, b.point_activity_subtype_name AS point_activity_type_name  
			, a.affected_date AS affected_when  
			, ISNULL(a.booking_value, 0) AS booking_value 
			, ISNULL(a.credit_card_value, 0) AS credit_card_value
			, CASE 
				WHEN a.points_affected < 0  THEN a.points_affected * (-1)
				ELSE 0 
			 END AS booking_points_used
			, CASE 
				WHEN a.points_affected > 0  THEN a.points_affected 
				ELSE 0 
			 END AS booking_points_earned
			, c.hotel_id
			, d.hotel_name
			, c.checkin_date
			, c.checkout_date
			, e.city_name
	FROM		dbo.fn_rew2_point_activity_summary(@memberId) a 
	LEFT JOIN	dbo.rew2_point_activity_subtypes b 
				ON a.point_activity_subtype_id = b.point_activity_subtype_id
	LEFT JOIN	dbo.ebe_booking_hotel c 
				ON a.booking_id = c.booking_id 
	LEFT JOIN	dbo.ebe_booking_summary AS booking_summary 
				ON a.booking_id = booking_summary.booking_id 
	LEFT JOIN	dbo.product_hotels d 
				ON c.hotel_id = d.hotel_id
	LEFT JOIN	dbo.geo2_city e 
				ON d.city_id = e.city_id 
	WHERE		NOT(b.point_activity_subtype_id IN (100, 12, 103) AND a.points_affected = 0)
	ORDER BY	a.point_activity_id     
END





GO

GRANT EXECUTE ON [dbo].[api_point_activity_select_v2] TO [customer_api_user]
GO
