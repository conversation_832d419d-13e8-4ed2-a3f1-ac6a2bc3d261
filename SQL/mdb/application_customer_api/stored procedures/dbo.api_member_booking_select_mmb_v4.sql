----------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
----------------------------------------------------------------------------------------------------------
-- Kumar <PERSON>            | 2017-07-20  | Create SP
-- Rati P.                | 2017-10-05  | Fix performance : change join order
-- Kittis<PERSON>   | 2018-03-13  | Make up fields name
-- <PERSON><PERSON> S.              | 2018-04-03  | Add TOP 20000 clause (WFAPI-1684)
----------------------------------------------------------------------------------------------------------
-- MONKEY_TEST EXEC dbo.api_member_booking_select_mmb_v4 12489570
-- Test : EXEC dbo.api_member_booking_select_mmb_v4 3
----------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[api_member_booking_select_mmb_v4]
    @member_Id INT
AS
  BEGIN
    SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT TOP 20000 EB.booking_id
            , EB.booking_date
            , EBH.checkin_date AS arrive_Date
            , EBH.checkout_date AS depart_Date
            , EBR.room_type_name
            , 0 AS credit_card_value
            , 'USD' AS currencyCode
            , EWP.workflow_phase_id AS booking_status_id
            , ISNULL(EWP.description,  '')  AS booking_Status
            , EB.cancellation_policy_code
            , EB.cancellation_policy
            , EBH.hotel_id
            , PH.hotel_name
            , PA.address_i AS hotel_address
            , PA.address_ii AS hotel_address2
            , PH.city_id
            , city.city_name AS hotel_Address_City
            , country.country_name AS hotel_Address_Country
            , RRP.Is_review AS has_reviewed
            , @member_Id AS member_Id
            , RRP.hotel_review_id AS review_id
            , EBH.no_of_rooms
            , BS.original_fully_auth_date
            , BSC.fully_auth_date
            , cast(BS.original_fully_charge_date AS datetime) AS bNPLFullyChargeDate
            , BS.charge_option_id
            , RUR.overall_review_score AS review_score
    FROM    dbo.ebe_itinerary AS EI
    INNER JOIN  dbo.ebe_booking AS EB
        ON EI.itinerary_id = EB.itinerary_id
    INNER JOIN  dbo.ebe_booking_hotel AS EBH
        ON EB.booking_id = EBH.booking_id
    INNER JOIN  dbo.ebe_booking_hotel_room AS EBR
        ON EB.booking_id = EBR.booking_id
    INNER JOIN  dbo.product_hotels AS PH
        ON EBH.hotel_id = PH.hotel_id
    LEFT JOIN   dbo.product_address AS PA
        ON EBH.hotel_id = PA.product_id
        AND PA.address_type_id = 2
        AND PA.language_id = 1
    LEFT JOIN   dbo.ebe_booking_summary AS BS
        ON EB.booking_id = BS.booking_id
    LEFT JOIN   dbo.geo2_city AS city
        ON city.city_id = PH.city_id
    LEFT JOIN   dbo.geo2_country AS country
        ON country.country_id = PH.country_id
    LEFT JOIN   dbo.rew_review_pending AS RRP
        ON EB.booking_id = RRP.booking_id
    LEFT JOIN   dbo.review_user_reviews AS RUR
        ON RUR.booking_id = EB.booking_id
        AND RUR.review_type_id IN (4, 105)
        AND RUR.rec_status = 1
    LEFT JOIN   dbo.ebe_booking_schedule AS BSC
        ON BSC.booking_id = EB.booking_id
    LEFT JOIN   dbo.ebe_workflow_state AS EWS
        ON EB.workflow_state_id = EWS.workflow_state_id
    LEFT JOIN   dbo.ebe_workflow_phases AS EWP
        ON EWS.workflow_phase_id = EWP.workflow_phase_id
    WHERE   EBR.room_no = 1
    AND     EBR.rec_status = 1
    AND     NOT(EB.workflow_state_id = 171)
    AND     EI.MemberId = @member_Id
  END
GO

GRANT EXECUTE ON [dbo].[api_member_booking_select_mmb_v4] TO [customer_api_user]
GO
