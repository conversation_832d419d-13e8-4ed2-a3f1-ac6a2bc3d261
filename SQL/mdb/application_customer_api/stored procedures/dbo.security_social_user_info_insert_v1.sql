

---------------------------------------------------------------------------------------------------
-- Author        | Date            | Comment
---------------------------------------------------------------------------------------------------
-- Dash Fu	| 2017-02-08      | INSERT social_user_info record.
-- Rati P.	| 2017-10-18	  | To support MDC async

---------------------------------------------------------------------------------------------------
-- EXEC [security_social_user_info_insert_v1] '4FD8D9EA-9BEF-4BAD-8952-0001EB78F5FB', 'o6_bmasdasdsad6_2sgVt7hMZOPfL', '200', 'user_info' 
-- EXEC [security_social_user_info_insert_v1] '13FAD933-5EC8-452A-89DA-00028C31B7DC', 'o6_bmasdasdsad6_2sgVt7hMZOPfL', '200', 'user_info' ,50

--SELECT * FROM dbo.auth_social_user_info 
--SELECT * FROM dbo.auth_users
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_social_user_info_insert_v1]
    @user_id     UNIQUEIDENTIFIER,
    @app_user_id VARCHAR (50),
    @app_id      SMALLINT,
    @user_info   NVARCHAR (4000),
    @user_info_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF @user_info_id IS NOT NULL
	BEGIN 

			SET IDENTITY_INSERT agoda_core.dbo.auth_social_user_info ON

			INSERT INTO dbo.auth_social_user_info 
			( 
			user_id
			, app_user_id
			, app_id
			, user_info
			, is_active
			, lastupdated_when
			, user_info_id
			)
			VALUES
			(
			@user_id
			, @app_user_id
			, @app_id
			, @user_info
			, 1
			, GETDATE()
			, @user_info_id
			)

			SET IDENTITY_INSERT agoda_core.dbo.auth_social_user_info OFF

			SELECT @user_info_id AS  user_info_id


	END 


	IF @user_info_id IS  NULL
	BEGIN 

	
			INSERT INTO dbo.auth_social_user_info 
			( 
			user_id
			, app_user_id
			, app_id
			, user_info
			, is_active
			, lastupdated_when

			)
			VALUES
			(
			@user_id
			, @app_user_id
			, @app_id
			, @user_info
			, 1
			, GETDATE()

			)

	
			SELECT @user_info_id = CAST(SCOPE_IDENTITY() AS INT)

			SELECT @user_info_id AS  user_info_id
	END 




END




GO

GRANT EXECUTE ON [dbo].[security_social_user_info_insert_v1] TO [customer_api_user]
GO
