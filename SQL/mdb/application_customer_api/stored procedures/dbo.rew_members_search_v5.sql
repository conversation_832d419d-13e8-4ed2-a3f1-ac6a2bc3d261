---------------------------------------------------------------------------------------------------
-- Author               | Date       | Comment
------------------------|------------|-------------------------------------------------------------
-- Wichai & Sirilak     | 2011-12-17 | 1. Change to use the new table rew2_point_activity
--                      |            | 2. Refactoring code (Add header, put dbo.)
-- Sirilak              | 2012-01-17 | Delete condition rew_contacts.rec_status = 1 when search by email
-- Sirilak              | 2012-05-09 | Refactoring to unbloat query cache by parameterizing the query
-- Nasakol P.           | 2018-10-16 | SELECT TOP 1000 in other case
-- Andrew <PERSON>.            | 2018-10-30 | Encryption support
-- <PERSON><PERSON>            | 2018-12-13 | Case insensitive search by username
-- Virayut S.           | 2020-02-03 | Filter by whiteLabelId and search by case insensitive username
------------------------|------------|-------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'

EXEC dbo.rew_members_search_v5 '015110','','','',''
EXEC dbo.rew_members_search_v5 '',10002595,'','',''
EXEC dbo.rew_members_search_v5 '',1000000,'','',''
EXEC dbo.rew_members_search_v5 '','','qa','qa','',''
EXEC dbo.rew_members_search_v5 '','','','','test','test',''
EXEC dbo.rew_members_search_v5 '','','','','','agoda','agoda'
EXEC dbo.rew_members_search_v5 '015110','','','','','','<EMAIL>','<EMAIL>'
EXEC dbo.rew_members_search_v5 '015110','1000000','qa','qa','test','test','agoda','agoda'
EXEC dbo.rew_members_search_v5 '5000003','10002595','dada','dada','agoda','agoda','<EMAIL>','<EMAIL>'

EXEC dbo.rew_members_search_v5
@membercode = ''
	, @bookingid  = ''
	, @firstname  = ''
	, @encryptedFirstname  = ''
	, @lastname= ''
	, @encryptedLastname = ''
	, @email  = '<EMAIL>'
	, @encryptedEmail  = '_?/+e8wC6Fox+njHEyHUOO8018GQkpnAY0nYcH2D8XMpo=_'

REVERT;

END_EXEC_TEST
*/
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[rew_members_search_v5]
	@membercode varchar(50) = NULL
	, @bookingid int = NULL
	, @firstname varchar(255) = NULL
	, @encryptedFirstname varchar(255) = NULL
	, @lastname varchar(255) = NULL
	, @encryptedLastname varchar(255) = NULL
	, @email varchar(400) = NULL
	, @encryptedEmail varchar(400) = NULL
	, @lowerEncryptedEmail varchar(400) = NULL
	, @whiteLabelId smallint = NULL
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE	 @sql nvarchar(max) = ''
	, @sql_condition nvarchar(max)
	, @cr nchar(2) = CHAR(13) + CHAR(10)
	, @has_condition bit = 0

	CREATE TABLE #userid_table
	(
		userid uniqueidentifier

	)


	--Correct Values
	SELECT	@membercode = CASE
													WHEN LEFT(ISNULL(@membercode,'0'),1) = '0' THEN REPLACE(SUBSTRING(ISNULL(@membercode,'0'),2,LEN(ISNULL(@membercode,'0')-1)),'%','')
													ELSE ISNULL(@membercode,'0')
			END
			, @bookingid = ISNULL(@bookingid,0)
			, @email = ISNULL(@email,'')
			, @encryptedEmail = ISNULL(@encryptedEmail,'')
			, @lowerEncryptedEmail = ISNULL(@lowerEncryptedEmail,'')
			, @whiteLabelId = ISNULL(@whiteLabelId,0)
			, @sql_condition = 'WHERE		1 = 1' + @cr

	SELECT	 @has_condition = CASE
															WHEN LEN(RTRIM(LTRIM(@membercode + @firstname + @lastname + @email))) > 0 THEN 1
															WHEN LEN(RTRIM(LTRIM(@membercode + @firstname + @lastname + @email))) = 0 AND @bookingid > 0 THEN 1
															ELSE 0
			END


	IF LEN(@email) > 0
		BEGIN

			INSERT INTO #userid_table (userid)
			SELECT user_id
			FROM   dbo.auth_user_mapping AS AUM
			WHERE 	AUM.username IN ( @email,@encryptedEmail,@lowerEncryptedEmail )

			UNION ALL

			SELECT RM.userid
			FROM   dbo.rew_contacts AS RC
							 INNER JOIN  dbo.rew_members AS RM
								 ON RC.memberid = RM.memberid
			WHERE 	RC.contact_method_value IN (  @email,@encryptedEmail)
				AND  RC.contact_method_id IN (1,8)


		END



	SELECT	@sql_condition = @sql_condition + CASE WHEN LEN(@membercode) > 0 THEN  + 'AND		RM.membercode = @_membercode' + @cr ELSE '' END
	SELECT	@sql_condition = @sql_condition + CASE WHEN LEN(@firstname) > 0 THEN 'AND		(RM.first_name = @_firstname OR RM.first_name = @_encryptedFirstname)' + @cr ELSE '' END
	SELECT	@sql_condition = @sql_condition + CASE WHEN LEN(@lastname) > 0 THEN 'AND		(RM.last_name = @_lastname OR RM.last_name = @_encryptedLastname) ' + @cr ELSE '' END
	---	SELECT	@sql_condition = @sql_condition + CASE WHEN LEN(@email) > 0 THEN 'AND		(AUM.username IN (@_email,@_encryptedEmail) OR RC.contact_method_value IN (@_email,@_encryptedEmail))' + @cr ELSE '' END
	SELECT	@sql_condition = @sql_condition + CASE WHEN @bookingid > 0 THEN 'AND		RPA.booking_id = @_bookingid' + @cr ELSE '' END
	SELECT	@sql_condition = @sql_condition + CASE WHEN @whiteLabelId > 0 THEN 'AND		RM.whitelabel_id = @_whiteLabelId' + @cr ELSE '' END



	SET	@sql =
	'SELECT		DISTINCT ' + CASE WHEN LEN(RTRIM(LTRIM(@membercode + @firstname + @lastname + @email))) = 0 AND @bookingid = 0 THEN ' TOP 100 ' ELSE ' TOP 1000 ' END + ' ISNULL(RM.memberid,0) AS memberid' + @cr
	+ '		, ISNULL(RM.membercode, '''') AS membercode' + @cr
	+ '		, ISNULL(RM.first_name, '''') AS firstname' + @cr
	+ '		, ISNULL(RM.last_name, '''') AS lastname' + @cr
	+ '		, ISNULL((SELECT TOP 1 contact_method_value FROM dbo.rew_contacts WHERE memberid = RM.memberid AND contact_method_id IN (1,8) AND rec_status = 1),' + ''''+ ''''+') AS email' + @cr
	+ '		, ISNULL(RM.point_balance, '''') AS balancedpoint' + @cr
	+ '		, ISNULL(RM.rec_created_when, '''') AS RegisterDate' + @cr
	+ '		, ISNULL(RM.nationality_id, '''') AS nationalityID' + @cr
	+ '		, ISNULL(RM.is_elite_status, '''') AS is_elite_status' + @cr
	+ '		, ISNULL(RM.title, '''') AS title' + @cr
	+ '		, ISNULL(GC.country_name, '''') AS nationality' + @cr
	+ '		, ISNULL(RM.password, '''') AS password' + @cr
	+ '		, ISNULL(RM.rec_status, 0) AS rec_status' + @cr
	+ '		, RM.rec_created_when' + @cr
	+ 'FROM		dbo.rew_members AS RM' + @cr
	+ 'LEFT JOIN	dbo.geo2_country AS GC' + @cr
	+ '			ON RM.nationality_id = GC.country_id ' + @cr
	+ CASE
		WHEN @has_condition = 1 AND @bookingid > 0 THEN 'INNER JOIN	dbo.rew2_point_activity AS RPA'
																										+ @cr + '			ON RM.memberid = RPA.memberid' + @cr
		ELSE ''
		END
	+ CASE
		WHEN @has_condition = 1 AND LEN(@email) > 0 THEN 'INNER JOIN	#userid_table UUID'
																										 + @cr + '			ON RM.userid = UUID.userid ' + @cr

		ELSE ''
		END
	+ CASE
		WHEN @has_condition = 1 THEN @sql_condition ELSE ''
		END


	EXEC sp_executesql
			@sql
			, N'@_membercode varchar(100), @_firstname varchar(1000),@_encryptedFirstname varchar(1000), @_lastname varchar(1000),@_encryptedLastname varchar(1000), @_email nvarchar(1000), @_encryptedEmail nvarchar(1000), @_bookingid int, @_whiteLabelId smallint'
			, @_membercode = @membercode
			, @_firstname = @firstname
			, @_encryptedFirstname = @encryptedFirstname
			, @_lastname = @lastname
			, @_encryptedLastname = @encryptedLastname
			, @_email = @email
			, @_encryptedEmail = @encryptedEmail
			, @_bookingid = @bookingid
			, @_whiteLabelId = @whiteLabelId

	DROP TABLE  #userid_table
END
GO

GRANT EXECUTE ON [dbo].[rew_members_search_v5] TO [customer_api_user]
GO


