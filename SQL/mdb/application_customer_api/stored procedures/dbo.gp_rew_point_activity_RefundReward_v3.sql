-------------------------------------------------------------------------------------------
-- Author		    | Date		       | Comment
----------------|----------------|----------------------------------------------------------
-- Natavit R.   | 2017-03-23     | V3, return output as a result set
----------------|----------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[gp_rew_point_activity_RefundReward_v3]
  (
    @MemberID               INT,
    @BookingID              INT,
    @Booking_Status_ID      INT,
    @StorefrontID           INT,
    @booking_value          MONEY,
    @Amount                 MONEY,
    @UserID                 UNIQUEIDENTIFIER,
    @Rewards_request_status INT OUTPUT, -- >( 1 = success,2= fail  etc..)
    @Rewards_request_reason NVARCHAR(100) OUTPUT,
    @Rewards_point          INT OUTPUT,
    @point_activity_id      INT OUTPUT
  )
AS
  BEGIN
    SET XACT_ABORT ON
    SET NOCOUNT ON

    BEGIN TRY
    BEGIN TRANSACTION

    SET @Rewards_point = 0

    DECLARE @booking_points_earned INT
    --			,@rew_booking_type		int
    --			,@rew_booking_status		int
    , @message NVARCHAR(100) = NULL
    , @isSuccess BIT = NULL
    , @now DATE = GETDATE()
    /*
        ---------------------------------------------------------------------------------------------------------------
        -- GET INFORMATION OF LAST BOOKING (POINT_ACTIVITY_SUBTYPE_ID = 10)
        ---------------------------------------------------------------------------------------------------------------

        SELECT	TOP 1
           @rew_booking_type		= rew_booking_type
          ,@rew_booking_status		= rew_booking_status
        FROM	dbo.rew2_point_activity
        WHERE	MemberID			= @MemberID
        AND	booking_id			= @BookingID
        AND	point_activity_subtype_id	= 10
        AND	rec_status			= 1
        ORDER BY point_activity_id DESC


        ---------------------------------------------------------------------------------------------------------------
        -- VALIDATE REWARD BOOKING
        ---------------------------------------------------------------------------------------------------------------
        IF  @rew_booking_type IS NULL
        BEGIN
          SET @message = 'No booking exist'
          RAISERROR (@message,11,1);
        END

        IF  @rew_booking_type = 1
        BEGIN
          SET @message = 'This bookingID ('+CONVERT(VARCHAR(20),@BookingID)+') is not redeem booking.'
          RAISERROR (@message,11,1);
        END
    */
    ------------------------------------------------------------------------------
    -- CALCULATE REFUND POINT
    ------------------------------------------------------------------------------
    SELECT @booking_points_earned = ISNULL(@Amount, 0) * ISNULL(SUM(CONVERT(INT, configuration_value)), 500)
    FROM dbo.afm_configuration ( NOLOCK )
    WHERE configuration_group_id = 3
          AND configuration_key = 'RPPDOL'

    EXEC dbo.rew2_point_activity_main_v2
        @booking_status_id = @booking_status_id      --smallint		= 0
        , @booking_points_used = 0    --***			--int			= null
        , @MemberID = @MemberID        --int
        , @storefront_id = @StorefrontID --***			--int			= null
        , @booking_id = @bookingid  --***			--int			= null
        , @booking_date = NULL          --***			--smalldatetime		= null
        , @departure_date = NULL          --***			--date			= null
        , @booking_value = @booking_value      --decimal(19,2)		= null
        , @credit_card_value = NULL          --***			--decimal(19,2)		= null
        , @promotion_guid = NULL          --***			--uniqueidentifier	= null
        , @promotion_source_id = NULL          --***			--int			= null
        , @promotion_expiry_date = NULL          --***			--date			= null
        , @nPointTime = NULL          --***			--tinyint		= null
        , @mergeid = NULL          --***			--int			= null
        , @remarks = NULL          --***			--nvarchar(500)		= null
        , @user_id = @userid  --***			--uniqueidentifier	= '00000000-0000-0000-0000-000000000000'
        , @point_activity_subtype_id = 12    --***			--int			= null --For @is_process_booking = 0
        , @points_affected = @booking_points_earned --***		--int			= null
        , @affected_date = @now    --***			--datetime		= null
        , @is_process_booking = 0    --***			--bit			= 1
        , @run_mode = 'Normal_except_Expire2' --***			--varchar(50)		= 'Normal'
        , @message = @message OUTPUT  --nvarchar(100)		= null	OUTPUT
        , @point_activity_id = @point_activity_id OUTPUT  --int			= null	OUTPUT
        , @isSuccess = @isSuccess OUTPUT  --bit			= null	OUTPUT

    IF @isSuccess = 0
      RAISERROR (@message, 11, 1);

    IF @@TRANCOUNT > 0
      COMMIT TRANSACTION

    SET @Rewards_request_status = 1
    SET @Rewards_request_reason = @message
    SET @Rewards_point = @booking_points_earned

    SELECT
      @point_activity_id      AS point_activity_id,
      @Rewards_request_status AS rewards_request_status,
      @Rewards_request_reason AS rewards_request_reason,
      @Rewards_point          AS rewards_point

    RETURN 0
    END TRY

    BEGIN CATCH
    IF @@TRANCOUNT > 0
      ROLLBACK TRANSACTION
    SET @Rewards_request_status = 2
    SET @Rewards_request_reason = @message
    SET @Rewards_point = 0
    RETURN -1
    END CATCH
  END


GO

GRANT EXECUTE ON [dbo].[gp_rew_point_activity_RefundReward_v3] TO [customer_api_user]
GO