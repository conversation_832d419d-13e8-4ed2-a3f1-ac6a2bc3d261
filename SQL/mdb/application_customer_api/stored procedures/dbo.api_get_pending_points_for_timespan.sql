/*<Monkey>
<InstanceType Name="MDB">
<Database Name="Application_Rewards" />
</InstanceType>
<ScriptSubType>NEW_PROC</ScriptSubType>
<ObjectName>api_get_pending_points_for_timespan</ObjectName>
<TestScript>EXEC dbo.api_get_pending_points_for_timespan 2782159, '2012-09-23', '2012-09-23'</TestScript>
</Monkey>*/
/*
---------------------------------------------------------------------------------------------------------------------
-- Author		| Date			| Comment
--------------- |---------------|------------------------------------------------------------------------------------
-- Peter Szabo	| 2015-01-09	| Get pending points for a given member in a given timespan
------------------------------------------------------------------------------------------------------------------
EXEC dbo.api_get_pending_points_for_timespan 2782159, '2012-09-23', '2012-09-23'
----------------|---------------|------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_get_pending_points_for_timespan]
	 @MemberId int
	 , @start_date date
	 , @end_date date
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
	
	SELECT		m.MemberId
			, ISNULL(SUM(pa.points_pending),0) as points_pending
	FROM		dbo.rew_members m
	LEFT JOIN	dbo.rew2_point_activity pa
					ON	m.MemberId = pa.MemberId
					AND	pa.departure_date BETWEEN @start_date AND @end_date
					AND	pa.rec_status = 1
	WHERE		m.MemberID = @MemberId
	GROUP BY	m.MemberId
END








GO

GRANT EXECUTE ON [dbo].[api_get_pending_points_for_timespan] TO [customer_api_user]
GO
