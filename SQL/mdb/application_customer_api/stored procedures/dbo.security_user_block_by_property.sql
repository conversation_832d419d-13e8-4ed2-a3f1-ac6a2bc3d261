
---------------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- Andrey S.	| 2017-06-06	| Create 
----------------|---------------|----------------------------------------------------------
/*
	MONKEY_TEST  EXEC dbo.security_user_block_by_property 1 , 0, '14820F31-5F5D-483F-9296-BA80839B3F29'

*/
----------------|---------------|----------------------------------------------------------


CREATE PROCEDURE [dbo].[security_user_block_by_property]
   @PropertyId int,
   @isBlocked BIT,
   @AppId uniqueidentifier
AS
BEGIN
		SET NOCOUNT ON
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

		DECLARE @userList TABLE (UserId uniqueidentifier)
		INSERT INTO @userList (UserId)
		(
			SELECT		u.UserId
			FROM		[dbo].[auth_users] AS u
			INNER JOIN  [dbo].[auth_user_relations] AS a ON a.UserId = u.UserId
			WHERE		a.ObjectId = @PropertyId
			AND			a.ObjectType = 'H'
		)

		IF EXISTS (SELECT * FROM @userList)
		BEGIN
			UPDATE	[dbo].[auth_users]
			SET		isBlocked			= @isBlocked
					,rec_modified_by	= @AppId
					,rec_modified_when	= GETDATE()
			WHERE	UserId IN (   SELECT  UserId FROM    @userList)
		END

		SELECT UserId, isBlocked FROM [dbo].[auth_users] WHERE UserId IN (  SELECT UserId FROM @userList    )
END



GO

GRANT EXECUTE ON [dbo].[security_user_block_by_property] TO [customer_api_user]
GO
