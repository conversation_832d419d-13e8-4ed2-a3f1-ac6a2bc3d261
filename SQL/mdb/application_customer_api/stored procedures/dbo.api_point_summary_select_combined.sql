/*
-------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- Saran B.		| 2012-06-19	| select member's summary point.
-- Saran B.		| 2012-12-06	| Added the has completed booking indicator.
-- Saran B.		| 2012-12-09	| Added the points booking affected year.
-- Saran B.		| 2013-01-15	| Added the variable table for helping with the points affected calculation.
-- Chate C.		| 2013-01-16	| did a bit code-refactoring.
----------------|---------------|----------------------------------------------------------
Exec [api_point_summary_select] 3
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_point_summary_select_combined]
    @memberId INT
AS
  BEGIN
    DECLARE @points_pending INT
    , @points_affected_today INT
    , @points_affected_year INT
    , @points_affected_month INT
    , @points_booking_affected_year INT

    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT @points_pending = SUM(points_pending)
    FROM dbo.rew2_point_activity
    WHERE MemberID = @memberId

    DECLARE @point_activity_summary TABLE
    (
      point_activity_id         BIGINT,
      points_affected           INT,
      point_activity_subtype_id INT,
      affected_date             DATETIME
    )

    INSERT INTO @point_activity_summary
    (
      point_activity_id
      , points_affected
      , point_activity_subtype_id
      , affected_date
    )
      SELECT
        point_activity_id,
        points_affected,
        point_activity_subtype_id,
        affected_date
      FROM dbo.fn_rew2_point_activity_summary(@MemberID)

    SELECT @points_affected_year = SUM(points_affected)
    FROM @point_activity_summary
    WHERE point_activity_subtype_id IN (10, 20, 30, 40, 41, 42, 43, 44, 70)
          AND YEAR(affected_date) = YEAR(GETDATE())
          AND affected_date <= GETDATE()

    SELECT @points_affected_month = SUM(points_affected)
    FROM @point_activity_summary
    WHERE point_activity_subtype_id IN (10, 20, 30, 40, 41, 42, 43, 44, 70)
          AND YEAR(affected_date) = YEAR(GETDATE())
          AND MONTH(affected_date) = MONTH(GETDATE())
          AND affected_date <= GETDATE()

    SELECT @points_affected_today = ISNULL(SUM(points_affected), 0)
    FROM @point_activity_summary
    WHERE point_activity_subtype_id IN (10, 20, 30, 40, 41, 42, 43, 44, 70)
          AND DATEDIFF(DAY, affected_date, GETDATE()) = 0

    SELECT @points_booking_affected_year = ISNULL(SUM(points_affected), 0)
    FROM @point_activity_summary
    WHERE YEAR(affected_date) = YEAR(GETDATE())
          AND affected_date <= GETDATE()
          AND point_activity_subtype_id IN (10, 20, 40)

    SELECT
      ISNULL(is_elite_status, 0)               AS is_elite_status,
      ISNULL(point_balance, 0)                 AS point_balance,
      ISNULL(points_expire_this_year, 0)       AS points_expire_this_year,
      ISNULL(points_earned, 0)                 AS points_earned,
      ISNULL(@points_pending, 0)               AS point_pending,
      ISNULL(@points_affected_today, 0)        AS point_affected_today,
      ISNULL(@points_affected_year, 0)         AS point_affected_year,
      ISNULL(@points_affected_month, 0)        AS point_affected_month,
      CAST((CASE
            WHEN EXISTS(
                SELECT *
                FROM @point_activity_summary AS PS
                  INNER JOIN dbo.rew2_point_activity AS P
                    ON PS.point_activity_id = P.point_activity_id
                WHERE P.point_activity_subtype_id = 10
                      AND P.rew_booking_status = 2
            )
              THEN 1
            ELSE 0
            END) AS BIT)                       AS hasCompletedBooking,
      ISNULL(@points_booking_affected_year, 0) AS points_booking_affected_year
    FROM dbo.rew_members
    WHERE MemberID = @memberId

    SELECT
      a.point_activity_id                                      AS point_activity_id,
      dbo.fn_rew2_ebe_booking_status_id(a.booking_id)          AS booking_status_id,
      point_activity_type_id,
      a.memberid                                               AS memberid,
      booking_summary.is_user_logged_in,
      a.booking_id                                             AS booking_id,
      a.remarks                                                AS remarks,
      a.rec_created_when                                       AS activity_date,
      ISNULL(dbo.fn_rew2_ebe_booking_status(a.booking_id), '') AS Booking_status,
      b.point_activity_subtype_name                            AS point_activity_type_name,
      a.affected_date                                          AS affected_when,
      ISNULL(a.booking_value, 0)                               AS booking_value,
      ISNULL(a.credit_card_value, 0)                           AS credit_card_value,
      CASE
      WHEN a.points_affected < 0
        THEN a.points_affected * (-1)
      ELSE 0
      END                                                      AS booking_points_used,
      CASE
      WHEN a.points_affected > 0
        THEN a.points_affected
      ELSE 0
      END                                                      AS booking_points_earned,
      c.hotel_id,
      d.hotel_name,
      c.checkin_date,
      c.checkout_date,
      e.city_name
    FROM dbo.fn_rew2_point_activity_summary(@memberId) a
      LEFT JOIN dbo.rew2_point_activity_subtypes b
        ON a.point_activity_subtype_id = b.point_activity_subtype_id
      LEFT JOIN dbo.ebe_booking_hotel c
        ON a.booking_id = c.booking_id
      LEFT JOIN dbo.ebe_booking_summary AS booking_summary
        ON a.booking_id = booking_summary.booking_id
      LEFT JOIN dbo.product_hotels d
        ON c.hotel_id = d.hotel_id
      LEFT JOIN dbo.geo2_city e
        ON d.city_id = e.city_id
    WHERE NOT (b.point_activity_subtype_id IN (100, 12, 103) AND a.points_affected = 0)
    ORDER BY a.point_activity_id
  END

GO

GRANT EXECUTE ON [dbo].[api_point_summary_select_combined] TO [customer_api_user]
GO