/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1k

END_SQL_OBJECT_INFO
*/
----------------------------------------------------------------------------------------------
-- Author                       | Date       | Comment
--------------------------------|------------|------------------------------------------------
-- Weerapong Muangmai           | 2023-11-10 | copy and optimize from ycs41_get_hotel_list_v7
-- Weerapong Muangmai           | 2023-11-17 | add 2 parameters and default top 100
----------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[get_hotelids_v2] 'EFC8994D-5381-4430-84A8-1E7C4B5A05D9'
EXEC [dbo].[get_hotelids_v2] 'EFC8994D-5381-4430-84A8-1E7C4B5A05D9', 1, 1
EXEC [dbo].[get_hotelids_v2] 'EFC8994D-5381-4430-84A8-1E7C4B5A05D9', 0, 1
EXEC [dbo].[get_hotelids_v2] 'EFC8994D-5381-4430-84A8-1E7C4B5A05D9', 1, 0
EXEC [dbo].[get_hotelids_v2] 'EFC8994D-5381-4430-84A8-1E7C4B5A05D9', 0, 0
REVERT;
END_EXEC_TEST
*/
----------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[get_hotelids_v2]
    @user_id UNIQUEIDENTIFIER,
    @ycs_rec_status INT = NULL,
    @is_nha BIT = NULL,
    @no_row INT = 100
AS
BEGIN

    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    DECLARE @hotel TABLE
    (
        hotel_id int NOT NULL PRIMARY KEY
    );

    INSERT INTO  @hotel
    SELECT DISTINCT au.ObjectID AS hotel_id
    FROM    dbo.auth_user_in_role  au
    WHERE   au.UserId = @user_id
    AND rec_status = 1
    AND au.objecttype = 'H'

    SELECT  TOP(@no_row)
            h.hotel_id
            , h.rec_status AS hotel_active_status
            , h.is_non_hotel_accommodation_mode AS is_nha
    FROM        @hotel hau
    INNER JOIN  dbo.product_hotels h
            ON hau.hotel_id = h.hotel_id
    INNER JOIN  dbo.ycs4_hotel y
            ON h.hotel_id = y.hotel_id
    WHERE
            NOT (y.Rec_status IS NULL OR y.Rec_status = -1)
    AND
            NOT (h.rec_status IS NULL OR h.rec_status = -1)
    AND
        (@ycs_rec_status is null OR h.rec_status = @ycs_rec_status)
    AND
        (@is_nha is null OR h.is_non_hotel_accommodation_mode = @is_nha)
END
GO

GRANT EXECUTE ON [dbo].[get_hotelids_v2] TO [customer_api_user]
GO
