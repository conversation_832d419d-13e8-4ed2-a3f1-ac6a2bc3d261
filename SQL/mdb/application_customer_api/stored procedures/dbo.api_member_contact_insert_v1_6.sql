---------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|-------------------------------------------------------------------
-- Saran B.       | 2012-05-28 | insert member's contact.
-- Chate <PERSON>.       | 2012-08-30 | Refactoring code forma.
-- Rati P .       | 2014-08-24 | return contact_id support MDC async
-- <PERSON>y S.      | 2018-10-07 | v1, extend PII columns size WFAPI-2152
-- Ahmad <PERSON>.       | 2019-02-13 | v1_3, add whitelabel_id
-- Yev<PERSON> V.	  | 2019-04-03 | Add origin
-- Weerapong M.	  | 2022-05-27 | v1_5, Add transaction
-- Weerapong M.	  | 2022-07-22 | v1_6, prevent duplicate insert
------------------|------------|-------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[api_member_contact_insert_v1_6]
	@memberId = 3,
	@contact_method_id = 11,
	@contact_method_value = 'test',
	@contact_method_remark = null ,
	@rec_created_by ='18B894AF-C305-4F15-9E8F-54B4AC430B89',
	@contact_id = NULL,
	@whitelabel_id = 1,
	@origin = 'US'
REVERT;
END_EXEC_TEST
*/
----------------|---------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[api_member_contact_insert_v1_6]
	@memberId int,
	@contact_method_id int,
	@contact_method_value nvarchar(400),
	@contact_method_remark nvarchar(100),
	@rec_created_by uniqueidentifier,
	@contact_id iNT  = NULL,
	@whitelabel_id smallint,
	@origin char(2)
AS
BEGIN
    SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

	IF @contact_id IS NOT NULL
	BEGIN
        IF NOT (EXISTS ( SELECT 1 FROM dbo.rew_contacts
                     WHERE contact_id = @contact_id AND  (memberId = @memberId OR memberId = 0)
                     AND whitelabel_id = @whitelabel_id
                     AND contact_method_id = @contact_method_id
                     AND contact_method_value = @contact_method_value))
        BEGIN
            SET IDENTITY_INSERT agoda_core.dbo.rew_contacts ON

            INSERT INTO dbo.rew_contacts
            (
                 contact_id
                 ,contact_method_id
                , memberId
                , contact_method_value
                , contact_method_remark
                , rec_status
                , rec_created_by
                , rec_created_when
                , whitelabel_id
                , origin
            )
            VALUES
            (
                @contact_id
                , @contact_method_id
                , @memberId
                , @contact_method_value
                , @contact_method_remark
                , 1
                , @rec_created_by
                , GETDATE()
                , @whitelabel_id
                , @origin
            )

            SET IDENTITY_INSERT agoda_core.dbo.rew_contacts OFF
        END
	END

	IF @contact_id IS  NULL
	BEGIN
		INSERT INTO dbo.rew_contacts
		(
			 contact_method_id
			, memberId
			, contact_method_value
			, contact_method_remark
			, rec_status
			, rec_created_by
			, rec_created_when
			, whitelabel_id
			, origin
		)
		VALUES
		(
			@contact_method_id
			, @memberId
			, @contact_method_value
			, @contact_method_remark
			, 1
			, @rec_created_by
			, GETDATE()
			, @whitelabel_id
			, @origin
		)

		SELECT @contact_id = CAST(SCOPE_IDENTITY() AS INT)

	END

	IF @in_transaction = 0 COMMIT TRANSACTION;

	-- SELECT THE LAST ID.
	SELECT   @contact_id as contact_id

END
GO

GRANT EXECUTE ON [dbo].[api_member_contact_insert_v1_6] TO [customer_api_user]
GO
