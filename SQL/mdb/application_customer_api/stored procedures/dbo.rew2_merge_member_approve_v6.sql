----------------------------------------------------------------------------------------------------------
-- Author	| Date		| Comment
----------------------------------------------------------------------------------------------------------
-- Saran B.	| 2012-06-19	| Merges Member
-- Chate C.	| 2012-10-19	| Reformat code to comply with Standard.
-- Saran B.	| 2012-12-06	| Update rec_status of the de-activated id.	
-- Saran B.	| 2012-12-12	| Update merged_to, deactivation_date, deactivation_reason of the de-activated id.
-- T<PERSON><PERSON><PERSON>.	| 2014-06-17	| Fix merge logic for column is_point_eligible and is_elite_status.
-- <PERSON>	| 2014-09-04	| TFS#21258: Created _v2 to call new version rew2_merge_member_transfer_v2
-- Thirakhom K.	| 2016-11-02	| Update MemberID in ebe_booking_dmc_data
-- Supacheep A. | 2016-11-08	| Remove transaction to use transaction in code instead, because the transaction in SP causes a zombie transaction problem.
-- Natavit R. 	| 2018-01-08	| Return MergeId, ProcessId, and affected rows
-- Natavit R.   | 2018-06-08    | V5, update lastupdated_when column and call rew2_merge_member_transfer_v4
-- Anurag  A.   | 2018-12-27    | V6, removed updating rec_status in rew_members and rew_contacts as it will be updated via delete flow afterwards
----------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.rew2_merge_member_approve_v6 
@ActiveId = '12944605'
, @DeActiveId = '12944609'
, @Reason = 'test'
, @IsStatementConfirm = 0
, @Approver = '803A2231-D04C-4B84-98EE-33A5B366415F'
REVERT;
END_EXEC_TEST
*/
----------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[rew2_merge_member_approve_v6]
	@ActiveId		varchar(20),
	@DeActiveId		varchar(20),
	@Reason			varchar(1000),
	@IsStatementConfirm	bit			= 1,
	@Approver		uniqueidentifier
AS
BEGIN 

  SET XACT_ABORT ON
  SET	NOCOUNT ON

	DECLARE	@ERRORCODE		int		= 0 
		,@MergeId		int
		,@step_id		int
		,@return		smallint
		,@error_number		int		
		,@error_message		nvarchar(2048)	= ''
		,@error_sp_name		varchar(2048)	= ''
		,@IsSuccess		int
		,@IsDeactiveIdEligible	bit
		,@IsDeactiveIdElite	bit
		,@eliteActivationDateAc	smalldatetime
		,@eliteActivationDateDe	smalldatetime
		,@ProcessId	int
		,@RowCount int

	SET @RowCount = 0

	--Remove transaction
	INSERT dbo.rew_merge_members 
	(
		MemberId1
		,MemberId2
		,MergeStatusId
		,IsStatementConfirm
		,Reason
		,Requester
		,RequestDate
		,Approver
		,ApproveDate
		,MergeTimeStamp
	)
	VALUES 
	(
		@DeActiveId
		,@ActiveId
		,2
		,@IsStatementConfirm
		,@Reason
		,@Approver
		,GETDATE()
		,@Approver
		,GETDATE()
		,GETDATE()
	)


	SELECT @MergeId = CAST(SCOPE_IDENTITY() AS int)
	-------------------------------------------------------------------------------------------------------
	-- Transfer data ( transaction, member-point ) to the Active Member
	-------------------------------------------------------------------------------------------------------

	SET	@step_id = 25
	EXEC	@return = dbo.rew2_merge_member_transfer_v4
		@MergeId	= @MergeId
		,@ActiveId	= @ActiveId
		,@DeActiveId	= @DeActiveId	
		,@error_message	= @error_message	OUTPUT
		,@error_sp_name	= @error_sp_name	OUTPUT
		,@error_step_id	= @step_id		OUTPUT			
		,@process_id	= @ProcessId		OUTPUT
	IF	@return = -1 RAISERROR (@error_message,11,1);

	SET @RowCount = @RowCount + @@ROWCOUNT

	-------------------------------------------------------------------------------------
	-- Replace Deactivate ID to New memberID in EBE_itinerary Table
	-------------------------------------------------------------------------------------
	UPDATE	dbo.ebe_itinerary 
	SET	MemberId = @ActiveId,
		rec_modify_when = GETDATE(),
		rec_modify_by	= @Approver
	WHERE	MemberId = @DeactiveId

	SET @RowCount = @RowCount + @@ROWCOUNT

	UPDATE	dbo.ebe_booking_dmc_data
	SET	MemberID = @ActiveId
		, lastupdated_when = GETDATE()
	WHERE	MemberID = @DeactiveId

	SET @RowCount = @RowCount + @@ROWCOUNT

	---------------------------------------------------------------------------------------
	-- MERGE is_point_eligible 
	---------------------------------------------------------------------------------------
	SET	@step_id = 35
	SELECT	@IsDeactiveIdEligible = is_point_eligible
	FROM	dbo.rew_members
	WHERE	MemberID = @DeActiveId

	IF	(@IsDeactiveIdEligible = 1)
	BEGIN
		UPDATE	dbo.rew_members
		SET	is_point_eligible= 1
			,rec_modify_when = GETDATE()
			,rec_modify_by	 = @Approver
			, lastupdated_when = GETDATE()
		WHERE	MemberID	 = @ActiveId
		
		SET @RowCount = @RowCount + @@ROWCOUNT
		
		UPDATE	dbo.rew_members
		SET	is_point_eligible = 0
			,rec_modify_when  = GETDATE()
			,rec_modify_by	  = @Approver
			, lastupdated_when = GETDATE()
		WHERE	MemberID	  = @DeActiveId
		
		SET @RowCount = @RowCount + @@ROWCOUNT
	END

	--------------------------------------------------------------------------------------
	-- MERGE is_elite_status
	--------------------------------------------------------------------------------------
	SET	@step_id = 45
	SELECT	@IsDeactiveIdElite = is_elite_status
	FROM	dbo.rew_members
	WHERE	MemberID = @DeActiveId

	IF	(@IsDeactiveIdElite = 1)
	BEGIN
		SELECT	@eliteActivationDateAc = elite_activation_date
		FROM	dbo.rew_members
		WHERE	MemberID = @ActiveId
		SELECT	@eliteActivationDateDe = elite_activation_date
		FROM	dbo.rew_members
		WHERE	MemberID = @DeActiveId

		IF	(@eliteActivationDateDe > @eliteActivationDateAc OR @eliteActivationDateAc IS NULL)
		BEGIN
			UPDATE	dbo.rew_members
			SET	elite_activation_date = @eliteActivationDateDe
			  , lastupdated_when = GETDATE()
			WHERE	MemberID = @ActiveId
			
			SET @RowCount = @RowCount + @@ROWCOUNT
		END

		UPDATE	dbo.rew_members
		SET	is_elite_status	= 1
			,rec_modify_when= GETDATE()
			,rec_modify_by	= @Approver
			, lastupdated_when = GETDATE()
		WHERE	MemberID	= @ActiveId
		
		SET @RowCount = @RowCount + @@ROWCOUNT
		
		UPDATE	dbo.rew_members
		SET	is_elite_status	= 0
			,rec_modify_when= GETDATE()
			,rec_modify_by	= @Approver
			, lastupdated_when = GETDATE()
		WHERE	MemberID	= @DeActiveId
		
		SET @RowCount = @RowCount + @@ROWCOUNT
	END
		
	---------------------------------------------------------------------------------------------------
	-- DELETE MEMBERS
	---------------------------------------------------------------------------------------------------
	UPDATE  dbo.rew_members
	SET	merged_to = @ActiveId,
		deactivation_date = GETDATE(),
		deactivation_reason = 'Auto-Merged',
		rec_modify_when = GETDATE(),
		rec_modify_by	= @Approver,
		lastupdated_when = GETDATE()
	WHERE	MemberId = @DeactiveId
	
	SET @RowCount = @RowCount + @@ROWCOUNT

	SET	@step_id = 50
		
	UPDATE	dbo.rew_review_pending 
	SET	MemberID	= @ActiveId,
		rec_modify_when = GETDATE(),
		rec_modify_by	= @Approver
	WHERE	MemberId	= @DeactiveId
	
	SET @RowCount = @RowCount + @@ROWCOUNT

	SELECT @MergeId AS 'merge_id'
		, @ProcessId AS 'process_id'
		, @RowCount AS 'row_count'
END


GO


GRANT EXECUTE ON [dbo].[rew2_merge_member_approve_v6] TO [customer_api_user]
GO
