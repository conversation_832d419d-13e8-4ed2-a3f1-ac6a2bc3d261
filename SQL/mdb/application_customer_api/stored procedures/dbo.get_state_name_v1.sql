---------------------------------------------------------------------------------------------------
-- Author		            | Date		      | Comment
----------------------------|-----------------|----------------------------------------------------------
-- Si<PERSON> 			    | 2020-09-01	  |  Fetch state name from state_id and language_id
----------------------------|-----------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @state_ids AS [id_table_type];
INSERT INTO @state_ids (id) VALUES (2020)
EXEC [dbo].[get_state_name_v1] @state_ids, 22
REVERT;
END_EXEC_TEST
*/
----------------------------|-----------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[get_state_name_v1]
  @state_ids id_table_type READONLY,
  @language_id int
AS
BEGIN

    SET NOCOUNT ON 
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
 
    SELECT
        s.id as state_id,
        ISNULL(gsl.state_name, gs.state_name) AS state_name
    FROM @state_ids as s
      INNER JOIN dbo.geo2_state as gs ON s.id = gs.state_id
        LEFT JOIN dbo.geo2_state_language AS gsl ON gs.state_id = gsl.state_id AND gsl.language_id = @language_id
END
GO

GRANT EXECUTE ON [dbo].[get_state_name_v1] TO [customer_api_user]
GO
