
-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 21/12/2010
-- Description:	Get information to create drop down langauge
-- [afm_configuration_select_v1] 67
-- =============================================
CREATE PROCEDURE [dbo].[afm_configuration_select_v2] 
	-- Add the parameters for the stored procedure here
	@storefrontid int
	,@machine_name varchar(20)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	with cte
	as ( SELECT row_number() over (partition by configuration_key order by machine_name desc ) row_num,
		 configuration_id, configuration_key, configuration_value, machine_name
    FROM afm_configuration (nolock)
    WHERE configuration_group_id = 67 and rec_status = 1 
    and (machine_name = '' or machine_name = @machine_name))
	select configuration_id, configuration_key, configuration_value, machine_name from 
	cte where ROW_NUM = 1  
	
	--SELECT configuration_key, configuration_value FROM afm_configuration (NOLOCK)
	-- WHERE configuration_group_id = @storefrontid AND ISNULL(machine_name,'') ='' AND rec_status = 1
END





GO

