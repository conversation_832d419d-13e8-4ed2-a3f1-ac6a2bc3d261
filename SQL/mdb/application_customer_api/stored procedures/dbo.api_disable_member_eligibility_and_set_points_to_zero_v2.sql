------------------------|---------------|---------------------------------------------------------------------
-- Author				| Date			| Comment
------------------------|---------------|---------------------------------------------------------------------
-- Peter <PERSON>			| 2015-01-14	| LOT-65: Set member to ineligible and set rewards points to 0
-- Natavit R.     | 2018-06-08    | V2, update lastupdated_when column
------------------------|---------------|---------------------------------------------------------------------
/*
EXEC_TEST 
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.api_disable_member_eligibility_and_set_points_to_zero_v2
			@UserID = '47210991-8B17-4772-A7D6-0000011826B9'
			, @MemberID = 6002995
			, @remarks = 'Rewards API disable member for GiftCards'
			, @not_points_expire = 0
			, @giftcard_status = 1
			, @giftcard_level = 1
REVERT;
END_EXEC_TEST
*/			
------------------------|---------------|---------------------------------------------------------------------

CREATE PROCEDURE [dbo].[api_disable_member_eligibility_and_set_points_to_zero_v2](
	@UserID					uniqueidentifier 
	, @MemberID				int	
	, @remarks				nvarchar(500)
	, @not_points_expire	bit = 0
	, @giftcard_status		tinyint
	, @giftcard_level		tinyint
)
AS
BEGIN

  SET XACT_ABORT ON
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
	
	DECLARE		@return				smallint
			, @points_affected		int			
			, @point_activity_subtype_id	int = 700
			, @point_activity_id		int
			, @affected_date		datetime = GETDATE()
			, @message			nvarchar(2048)	
			, @isSuccess			bit	
	
	IF EXISTS (SELECT * FROM dbo.rew_members WHERE MemberID = @MemberID)
	BEGIN
			
		SELECT		@points_affected = point_balance
		FROM		dbo.rew_members
		WHERE		memberId = @MemberID	
		
		EXEC dbo.rew2_point_activity_main_v2
			 @booking_status_id			= NULL				--smallint		= 0
			,@booking_points_used			= NULL				--int			= null
			,@MemberID				= @MemberID			--int		
			,@storefront_id				= 5				--int			= null
			,@booking_id				= NULL				--int			= null
			,@booking_date				= NULL				--smalldatetime		= null
			,@departure_date			= NULL				--date			= null
			,@booking_value				= NULL				--decimal(19,2)		= null
			,@credit_card_value			= NULL				--decimal(19,2)		= null
			,@promotion_guid			= NULL				--uniqueidentifier	= null
			,@promotion_source_id			= NULL				--int			= null
			,@promotion_expiry_date			= NULL				--date			= null
			,@nPointTime				= NULL				--tinyint		= null
			,@mergeid				= NULL				--int			= null
			,@remarks				= @remarks			--nvarchar		= null
			,@user_id				= @UserID			--uniqueidentifier	= '00000000-0000-0000-0000-000000000000'
			,@point_activity_subtype_id		= @point_activity_subtype_id	--int			= null --For @is_process_booking = 0
			,@points_affected			= @points_affected		--int			= null
			,@affected_date				= @affected_date		--datetime		= null
			,@is_process_booking			= 0				--bit			= 1
			,@run_mode				= 'Normal_except_Expire2'	--varchar(50)		= 'Normal'
			,@message				= @message			OUTPUT	--nvarchar(100)	= null	OUTPUT 
			,@point_activity_id			= @point_activity_id		OUTPUT	--int		= null	OUTPUT
			,@isSuccess				= @isSuccess			OUTPUT	--bit		= null	OUTPUT

		IF @isSuccess = 1
		BEGIN
			UPDATE	dbo.rew_members
			SET	is_point_eligible = 0
				, giftcard_status = @giftcard_status
				, giftcard_level = @giftcard_level
				, rec_modify_by = @UserID
				, rec_modify_when = GETDATE()
				, lastupdated_when = GETDATE()
			WHERE	memberID = @MemberID
		END			


		SELECT	@isSuccess as isSuccess
			, @message as message
			, @point_activity_id as point_activity_id
	END
	ELSE
	BEGIN
		SELECT	0 as isSuccess
			, 'MemberID ' + CAST(@MemberID as varchar) + ' does not exist.' as message
			, NULL AS point_activity_id
	END
		
END	






GO

GRANT EXECUTE ON [dbo].[api_disable_member_eligibility_and_set_points_to_zero_v2] TO [customer_api_user]
GO
