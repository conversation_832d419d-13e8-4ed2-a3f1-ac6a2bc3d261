/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2018-12-18    | Writes an optional customer properties by any user identifier.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[api_customer_set_properties_v1] '1f46ce05-a1fb-4f28-8521-47e443ab6666', '{}', '1f46ce05-a1fb-4f28-8521-47e443ab6666';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[api_customer_set_properties_v1]
    @user_id uniqueidentifier,
    @properties varchar(max),
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    UPDATE dbo.rew_members
    SET    properties = @properties
           , rec_modify_by = @modified_by
           , rec_modify_when = GETDATE()
           , lastupdated_when = GETDATE()
    WHERE  UserId = @user_id;
END
GO

GRANT EXECUTE ON [dbo].[api_customer_set_properties_v1] TO [customer_api_user]
GO
