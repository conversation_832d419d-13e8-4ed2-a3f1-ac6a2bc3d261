/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date       | Comment
----------------------------|------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>at<PERSON>           | 2018-12-20 | Fetch an user identifier by booking id.
-- Andrey S.                | 2019-11-18 | Fetch only member id from ebe_itinerary.
----------------------------|------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[api_customer_find_by_bookingid_v2] 1111;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[api_customer_find_by_bookingid_v2]
    @booking_id int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT      ei.MemberID
    FROM        dbo.ebe_itinerary ei
    INNER JOIN  dbo.ebe_booking AS eb
            ON  ei.itinerary_id = eb.itinerary_id
    WHERE       eb.booking_id = @booking_id;
END
GO

GRANT EXECUTE ON [dbo].[api_customer_find_by_bookingid_v2] TO [customer_api_user]
GO
