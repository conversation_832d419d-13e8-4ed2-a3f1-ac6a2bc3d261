---------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|------------------------------------------------------
-- <PERSON><PERSON><PERSON>.             | 2012-10-31    | delete functionality
-- Kit<PERSON><PERSON>     | 2017-11-28    | Add history_Id as a parameter
-- Thanapol R.              | 2018-06-19    | delete both reward member and auth user
-- Anurag A.                | 2018-12-11    | Merged delete and unblock functionality together. Taken AffectedUserID,ActionedBy, newRecStatus,oldRecStatus, newEmail,CustomerMapping(including contact method value, uri) _del appended/removed_for_unblock and encrypted as inputs instead of manipulating inside proc.
-- Mrun S.                  | 2018-12-21    | remove unused SP param
----------------------------|---------------|------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
exec dbo.backoffice_auth_users_delete_unblock_v6 '00000000-0000-0000-0000-000000000000', 1, '00000000-0000-0000-0000-000000000000', -1 , 1 ,1,  'anurag.agnihotri@agoda.com_del', 9,'<Result><Mappings><ContactMethods><ContactMethod contactType="1" new="anurag.agnihotri@agoda.com_del" old="<EMAIL>"/></ContactMethods><EmailAddress>anurag.agnihotri@agoda.com_del</EmailAddress><UserNames><UserName id="D309993F-1D11-41C5-9D38-D01385850CC4" value="aagnihotri_del" authType="1" uri="http://test1.com_del"></UserName><UserName id="D309993F-1D11-41C5-9D38-D01385850CC4" value="testaagnihotri_del" authType="2" uri="http://test2.com_del"></UserName></UserNames></Mappings></Result>'
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_users_delete_unblock_v6]
(
@AffectedUserID UNIQUEIDENTIFIER,
@memberId int,
@ActionedBy UNIQUEIDENTIFIER,
@newRecStatus int,
@oldRecStatus int,
@isBlockedAction int,
@newEmail varchar(400),
@activityId int,
@CustomerMapping XML
)
AS
  BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	BEGIN TRY

		BEGIN TRANSACTION

			DECLARE @auth_user_in_role TABLE  
			(  
				[UserId] [uniqueidentifier] NOT NULL,
				[RoleId] [uniqueidentifier] NOT NULL,
				[ObjectType] [char](1) NOT NULL,
				[ObjectID] [int] NOT NULL,
				[rec_status] [int] NOT NULL,
				[rec_modified_when] [datetime] NULL,
				[rec_modified_by] [uniqueidentifier] NULL
			)
	
			-- reward members
			UPDATE REW
			SET    rec_status = @newRecStatus,
			       rec_modify_when = Getdate(),
			       rec_modify_by = @ActionedBy,
			       lastupdated_when = Getdate()
			FROM   dbo.rew_members REW
			WHERE  REW.userid = @AffectedUserID
			       AND REW.rec_status = @oldRecStatus
			  -- TO_CHECK_R onlu contact_id join sufficient
			-- reward contacts
			UPDATE REW
			SET    contact_method_value = contactmethods.columndata.value('./@new', 'NVARCHAR(400)'),
			       rec_status = @newRecStatus,
			       rec_modify_when = Getdate(),
			       rec_modify_by = @ActionedBy,
			       lastupdated_when = Getdate()
			FROM   dbo.rew_contacts REW
			INNER JOIN @CustomerMapping.nodes('//Mappings/ContactMethods/ContactMethod') ContactMethods(columndata)
			        ON REW.contact_method_id = contactmethods.columndata.value('./@contactType', 'int')
			WHERE  REW.rec_status = @oldRecStatus
			AND    REW.memberId = @memberId
	    AND    REW.contact_method_value = contactmethods.columndata.value('./@old', 'NVARCHAR(400)');
	
			-- auth
			UPDATE dbo.auth_user_in_role
			SET    rec_status = @newRecStatus,
			       rec_modified_by = @ActionedBy,
			       rec_modified_when = Getdate()
			OUTPUT inserted.userid, inserted.RoleId, inserted.ObjectType, inserted.ObjectID, inserted.rec_status, inserted.rec_modified_when, inserted.rec_modified_by
			INTO	@auth_user_in_role
			WHERE  userid = @AffectedUserID
			       AND rec_status = @oldRecStatus
	
			UPDATE dbo.auth_users
			SET    emailaddress = @newEmail,
			       IsBlocked = (CASE WHEN @isBlockedAction = 1 THEN
			                   (CASE WHEN @newRecStatus = 1 THEN 0 ELSE 1 END)
			                   ELSE IsBlocked END),
			       isdeleted = (CASE WHEN @isBlockedAction = 0 THEN
			                   (CASE WHEN @newRecStatus = 1 THEN 0 ELSE 1 END)
			                   ELSE isdeleted END),
			       rec_status = @newRecStatus,
			       rec_modified_when = Getdate(),
			       rec_modified_by = @ActionedBy
			WHERE  userid = @AffectedUserID
			       AND rec_status = @oldRecStatus
	
			UPDATE USR_MAP
			SET    username = usernames.columndata.value('./@value', 'VARCHAR(400)'),
			       uri = usernames.columndata.value('./@uri', 'VARCHAR(512)'),
			       rec_status = @newRecStatus,
			       rec_modified_when = Getdate(),
			       rec_modified_by = @ActionedBy
			FROM   dbo.auth_user_mapping USR_MAP
			INNER JOIN @CustomerMapping.nodes('//Mappings/UserNames/UserName') UserNames(columndata)
			        ON USR_MAP.authentication_type_id = usernames.columndata.value('./@authTypeId', 'int')
			WHERE  USR_MAP.user_id = @AffectedUserID
			AND    USR_MAP.rec_status = @oldRecStatus;
	
			--update audit data.
			INSERT INTO dbo.auth_assigned_role_audit
			(	
				userid,
			        assigndate,
			        assignedroleid,
			        assigneduserid,
			        assignedobjecttype,
			        assignedobjectid,
			        activityid,
			        rec_status,
			        rec_created_by,
			        rec_created_when
			)
			SELECT @ActionedBy,
			       Getdate(),
			       roleid,
			       userid,
			       objecttype,
			       objectid,
			       @activityId,
			       rec_status,
			       rec_modified_by,
			       rec_modified_when
			FROM   @auth_user_in_role
	
			-- Log into auth_user_mapping_history
			INSERT INTO dbo.auth_user_mapping_history
			            (logtime,
			             user_id,
			             authentication_type_id,
			             activityid,
			             rec_status,
			             rec_created_when,
			             rec_created_by)
			VALUES     (Getdate(),
			            @AffectedUserID,
			            0 --In this features can be many authentication type.
			            ,
			            @activityId --Delete user
			            ,
			            @newRecStatus,
			            Getdate(),
			            @ActionedBy )

		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
			
		DECLARE @err_msg NVARCHAR(2048) = ERROR_MESSAGE()
	
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRANSACTION
		END

		RAISERROR(@err_msg, 16, 1)

	END CATCH
  END

GO

GRANT EXECUTE ON [dbo].[backoffice_auth_users_delete_unblock_v6] TO [customer_api_user]
GO
