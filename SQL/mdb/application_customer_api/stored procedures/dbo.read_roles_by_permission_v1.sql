/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-09-30    | Returns a list of roles for the given permission.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_roles_by_permission_v1] '856C401B-3E32-429A-A8C0-FEB9F4539224';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_roles_by_permission_v1]
    @permission_id uniqueidentifier
AS
BEGIN

    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT RoleId
    FROM   dbo.auth_role_permissions
    WHERE  PermissionId = @permission_id
    AND    rec_status = 1;
    
END
GO

GRANT EXECUTE ON [dbo].[read_roles_by_permission_v1] TO [customer_api_user]
GO
