----------------------------------------------------------------------------------------------------------
-- Author		        | Date		| Comment
----------------------------------------------------------------------------------------------------------
-- Pi<PERSON>t J.		        | 2012-02-09	| To update the data from auth_user_relations to auth_user_in_role
-- György Giczi		    | 2015-04-07	| Formatting to coding standards, added distinct to the select
-- G<PERSON>örgy Giczi		    | 2015-09-28	| Rewrite using merge
-- Atiruj P.		    | 2016-05-11	| Rewrite to use update and insert
-- <PERSON><PERSON><PERSON><PERSON>		    | 2017-02-03	| Track changes
-- <PERSON><PERSON><PERSON>	    | 2017-02-22	| Romoved test code
-- <PERSON><PERSON><PERSON>	    | 2017-05-02	| Changed to ensure created_by is not null
-- Sornram Sangutai	    | 2022-03-10	| Optimized for quicker release transaction on auth_user_in_role in DBPS-6091
-- Varakorn Koschakosai | 2023-02-24    | Add batching logic
----------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_upload_auth_user_relations_finalize_v3]
REVERT;
END_EXEC_TEST
*/
----------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_upload_auth_user_relations_finalize_v3]
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET XACT_ABORT ON

	DECLARE @ObjectType char(1) = 'H'
		, @roleid uniqueidentifier = '78ABBE95-B5DF-4894-839C-BDC4F8B65BB9'

    DECLARE @batch_size int;
    DECLARE @delay_time varchar(8);

    EXEC Agoda_DBA.[dbo].[repl_get_batch_size_by_group_V2] 'mdb_to_cdb'
            , @row_per_batch = @batch_size OUTPUT
            , @delay_sec = @delay_time OUTPUT

	CREATE TABLE #temp_auth_user_relations
	(
		UserId uniqueidentifier NOT NULL
		, roleid uniqueidentifier NOT NULL
		, ObjectType char(1) NOT NULL
		, ObjectId  int NOT NULL
		, rec_status	int NOT NULL
		, rec_created_when	datetime NOT NULL
		, rec_created_by	uniqueidentifier NOT NULL
		, rec_modified_when	datetime NULL
		, rec_modified_by	uniqueidentifier NULL
		, PRIMARY KEY(UserId, RoleId, ObjectType, ObjectID)
	)
	INSERT INTO  #temp_auth_user_relations
	(
			UserId
			, roleid
			, ObjectType
			, ObjectId
			, rec_status
			, rec_created_by
			, rec_created_when
			, rec_modified_by
			, rec_modified_when
	)
	SELECT DISTINCT	ur.UserId
			, @roleid AS roleid
			, ur.ObjectType
			, ur.ObjectId
			, ur.rec_status
			, ur.rec_created_by
			, ur.rec_created_when
			, ur.rec_modified_by
			, ur.rec_modified_when
	FROM		dbo.auth_user_relations AS ur
	WHERE		ur.ObjectType = @ObjectType



	SELECT	SRC.ObjectId as hotel_id
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), SRC.UserId) + ' | role_id: ' + CONVERT(varchar(36), SRC.RoleId) as new_value
			, CASE WHEN SRC.rec_status = 1 THEN 'Added' ELSE 'Deleted' END as remark
			, ISNULL(SRC.rec_modified_by, SRC.rec_created_by) as created_by
			, GETDATE() as created_when
	        , 0 as is_updated
    INTO	#c1
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	#temp_auth_user_relations AS SRC
				ON EXISTS
				(
					SELECT *
					FROM	dbo.auth_user_in_role AS uir
					WHERE	uir.UserId = SRC.userID
					AND	uir.ObjectID = SRC.ObjectID
					AND	uir.roleid = SRC.roleid
					AND	uir.ObjectType = SRC.ObjectType
					AND	uir.rec_status <> SRC.rec_status
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		SRC.objectType = 'H'

    WHILE EXISTS
	(
	  	SELECT * FROM  #c1 WHERE is_updated = 0
	)
    BEGIN
        EXEC Agoda_DBA.[dbo].[repl_get_batch_size_by_group_V2] 'mdb_to_cdb'
                    , @row_per_batch = @batch_size OUTPUT
                    , @delay_sec = @delay_time OUTPUT

        BEGIN TRAN
            INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
            (
                    hotel_id
                    , [type_id]
                    , new_value
                    , remark
                    , created_by
                    , created_when
            )
            SELECT	TOP (@batch_size) hotel_id
                    , [type_id]
                    , new_value
                    , remark
                    , created_by
                    , created_when
            FROM		#c1
            WHERE is_updated = 0
            ORDER BY created_when

            ;WITH CTE AS
            (
                SELECT  TOP (@batch_size) *
                FROM    #c1
                ORDER BY created_when
            )
            UPDATE CTE
            SET	 is_updated = 1

        COMMIT TRAN;
        WAITFOR DELAY @delay_time
    END

	-- UPDATE TGT

	SELECT			TGT.UserId, TGT.RoleId, TGT.ObjectType, TGT.ObjectID
					,src.rec_status
					,src.rec_modified_by
					,src.rec_modified_when
	                , 0 as is_updated
	INTO			#temp_upd
	FROM			#temp_auth_user_relations as SRC
	INNER JOIN		dbo.auth_user_in_role AS TGT
						ON TGT.UserId = SRC.userID
				AND TGT.ObjectID = SRC.ObjectID
				AND TGT.ObjectType = SRC.ObjectType
	WHERE	TGT.rec_status <> SRC.rec_status

    CREATE NONCLUSTERED INDEX IX01_temp_upd__is_updated ON #temp_upd (is_updated)

    WHILE EXISTS
        (
            SELECT * FROM  #temp_upd WHERE is_updated = 0
        )
    BEGIN
        EXEC Agoda_DBA.[dbo].[repl_get_batch_size_by_group_V2] 'mdb_to_cdb'
                    , @row_per_batch = @batch_size OUTPUT
                    , @delay_sec = @delay_time OUTPUT

        select (@batch_size) [UserId],[RoleId],[ObjectType],[ObjectID],rec_status,rec_modified_by,rec_modified_when
        into #temp_upd_auth_user_in_role
        from #temp_upd
        WHERE is_updated = 0
        ORDER BY userID, ObjectID, roleid, ObjectType

        BEGIN TRAN

            UPDATE      TGT
            SET     rec_status = src.rec_status
              , rec_modified_by = src.rec_modified_by
              , rec_modified_when = src.rec_modified_when
                FROM        dbo.auth_user_in_role AS TGT
                        INNER JOIN  #temp_upd_auth_user_in_role src
            ON TGT.UserId = SRC.userID
                AND TGT.ObjectID = SRC.ObjectID
                AND TGT.roleid = SRC.roleid
                AND TGT.ObjectType = SRC.ObjectType

            ;WITH CTE AS
                      (
                          SELECT  TOP (@batch_size) *
                          FROM    #temp_upd
                          ORDER BY userID, ObjectID, roleid, ObjectType
                      )
            UPDATE CTE
            SET  is_updated = 1

        COMMIT TRAN;
        WAITFOR DELAY @delay_time

        drop table #temp_upd_auth_user_in_role
    END

	-- END UPDATE TGT

	SELECT		A.ObjectId  as hotel_id
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), A.UserId) + ' | role_id: ' + CONVERT(varchar(36), A.RoleId) as new_value
			, 'Added' as remark
			, ISNULL(A.rec_modified_by, A.rec_created_by) as created_by
			, GETDATE() as created_when
	        , 0 as is_updated
	INTO	#c2
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	#temp_auth_user_relations AS A
				ON NOT EXISTS
				(
					SELECT *
					FROM	dbo.auth_user_in_role AS B
					WHERE	A.roleid = B.roleid
					AND	A.ObjectType = B.ObjectType
					AND	A.userID = B.UserId
					AND	A.objectID = B.ObjectID
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		A.objectType = 'H'


    WHILE EXISTS
	(
	  	SELECT * FROM  #c2 WHERE is_updated = 0
	)
    BEGIN
        EXEC Agoda_DBA.[dbo].[repl_get_batch_size_by_group_V2] 'mdb_to_cdb'
                    , @row_per_batch = @batch_size OUTPUT
                    , @delay_sec = @delay_time OUTPUT

        BEGIN TRAN
            INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
            (
                    hotel_id
                    , [type_id]
                    , new_value
                    , remark
                    , created_by
                    , created_when
            )
            SELECT	TOP (@batch_size) hotel_id
                    , [type_id]
                    , new_value
                    , remark
                    , created_by
                    , created_when
            FROM		#c2
            WHERE is_updated = 0
            ORDER BY created_when

            ;WITH CTE AS
                (
                SELECT  TOP (@batch_size) *
                FROM    #c2
                ORDER BY created_when
                )
            UPDATE CTE
            SET	 is_updated = 1

        COMMIT TRAN;
        WAITFOR DELAY @delay_time
    END

	-- INSERT TGT

	SELECT	UserId
		, RoleId
		, ObjectType
		, ObjectID
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
	    , 0 as is_updated
	INTO	#temp_ins
	FROM	#temp_auth_user_relations AS A
	WHERE	NOT EXISTS
				(
					SELECT *
					FROM	dbo.auth_user_in_role AS B
					WHERE	A.roleid = B.roleid
					AND	A.ObjectType = B.ObjectType
					AND	A.userID = B.UserId
					AND	A.objectID = B.ObjectID
				)

    CREATE NONCLUSTERED INDEX IX01_temp_ins__is_updated ON #temp_ins(is_updated)

    WHILE EXISTS
	(
	  	SELECT * FROM  #temp_ins WHERE is_updated = 0
	)
    BEGIN

        BEGIN TRAN
            INSERT INTO	dbo.auth_user_in_role
            (
                UserId
                , RoleId
                , ObjectType
                , ObjectID
                , rec_status
                , rec_created_when
                , rec_created_by
                , rec_modified_when
                , rec_modified_by
            )
            SELECT	(@batch_size) UserId
                , RoleId
                , ObjectType
                , ObjectID
                , rec_status
                , rec_created_when
                , rec_created_by
                , rec_modified_when
                , rec_modified_by
            FROM	#temp_ins AS A
            WHERE is_updated = 0
            ORDER BY UserId, ObjectID

            ;WITH CTE AS
            (
                SELECT  TOP (@batch_size) *
                FROM    #temp_ins
                ORDER BY UserId, ObjectID
            )
            UPDATE CTE
            SET	 is_updated = 1

        COMMIT TRAN;
        WAITFOR DELAY @delay_time
    END

	-- END INSERT TGT


	SELECT		TGT.ObjectId  as hotel_id
			, hst.[type_id]
			, 'user_id: ' + CONVERT(varchar(36), TGT.UserId) + ' | role_id: ' + CONVERT(varchar(36), TGT.RoleId) as new_value
			, 'Deleted' as remark
			, ISNULL(rec_modified_by, '00000000-0000-0000-0000-000000000000') as created_by
			, GETDATE() as created_when
	        , 0 as is_updated
	INTO	#c3
	FROM		dbo.ycs4_hotel_sensitive_type AS hst
	INNER JOIN	dbo.auth_user_in_role AS TGT
				ON NOT EXISTS
				(
					SELECT	*
					FROM	#temp_auth_user_relations AS SRC
					WHERE	TGT.userID = SRC.userID
					AND	TGT.objectID = SRC.objectID
				)
	WHERE		hst.source_type = 'auth_user_in_role'
	AND		hst.ref_source_type_id = 1
	AND		TGT.objectType = 'H'
	AND		TGT.RoleId = @roleid

    WHILE EXISTS
	(
	  	SELECT * FROM  #c3 WHERE is_updated = 0
	)
    BEGIN
        BEGIN TRAN

            INSERT INTO	dbo.ycs4_hotel_sensitive_change_tracking
            (
                    hotel_id
                    , [type_id]
                    , new_value
                    , remark
                    , created_by
                    , created_when
            )
            SELECT	TOP (@batch_size) hotel_id
                    , [type_id]
                    , new_value
                    , remark
                    , created_by
                    , created_when
            FROM	#c3
            ORDER BY created_when

            ;WITH CTE AS
            (
                SELECT  TOP (@batch_size) *
                FROM    #c3
                ORDER BY created_when
            )
            UPDATE CTE
            SET	 is_updated = 1

        COMMIT TRAN;
        WAITFOR DELAY @delay_time

    END

	-- DEL TGT


	SELECT		UserId
	            , RoleId
	            , ObjectType
	            , ObjectID
	            , 0 as is_updated
	INTO		#temp_del
	FROM	dbo.auth_user_in_role AS TGT
	WHERE	TGT.RoleId = @roleid
	AND	TGT.objectType = @ObjectType
	AND	NOT EXISTS
	(
		SELECT	*
		FROM	#temp_auth_user_relations AS SRC
		WHERE	TGT.userID = SRC.userID
		AND	TGT.objectID = SRC.objectID
	)

    CREATE NONCLUSTERED INDEX IX01_temp_del__is_updated ON #temp_del (is_updated)

    WHILE EXISTS
        (
            SELECT * FROM  #temp_del WHERE is_updated = 0
        )
    BEGIN
        BEGIN TRAN

            select TOP (@batch_size) userID, ObjectID, roleid, ObjectType
            into #temp_del_auth_user_in_role
            from #temp_del
            WHERE is_updated = 0
            ORDER BY userID, ObjectID, roleid, ObjectType

            DELETE			TGT
                        FROM			dbo.auth_user_in_role AS TGT
                        INNER JOIN		#temp_del_auth_user_in_role SRC
                                        ON	TGT.UserId = src.userID
                                            AND TGT.ObjectID = src.ObjectID
                                            AND TGT.roleid = src.roleid
                                            AND TGT.ObjectType = src.ObjectType

                        ;WITH CTE AS
                                                    (
                                                        SELECT  TOP (@batch_size) *
                                                        FROM    #temp_del
                                                        ORDER BY userID, ObjectID, roleid, ObjectType
                                                    )
            UPDATE CTE
            SET	 is_updated = 1

        COMMIT TRAN;
        WAITFOR DELAY @delay_time

        drop table #temp_del_auth_user_in_role
    END

	--END DEL TGT

	DROP TABLE #temp_auth_user_relations
	DROP TABLE #temp_del
	DROP TABLE #temp_ins
	DROP TABLE #temp_upd
	DROP TABLE #c1
	DROP TABLE #c2
	DROP TABLE #c3


END
GO

GRANT EXECUTE ON [dbo].[backoffice_upload_auth_user_relations_finalize_v3] TO [customer_api_user]
GO