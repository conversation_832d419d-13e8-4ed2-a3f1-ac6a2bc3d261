/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 4k

END_SQL_OBJECT_INFO
*/
-----------------------------------------------------------------------------------------------------------
-- Author                | Date        | Comment
-------------------------|-------------|----------------------------------------------------------
-- <PERSON>          | 2017-05-01  | Initial version
-- Vatulin Yevhen        | 2018-11-12  | Allow to fetch deleted user, optimize member lookup by uuid
-- <PERSON>          | 2018-11-20  | Add manager id
-- <PERSON>          | 2018-12-12  | Optional roles
-- Vatulin Yevhen        | 2018-12-19  | Fetch an optional customer properties
-- Kit<PERSON><PERSON>  | 2019-01-04  | Return department_id and location_id in the second result set
-- Andrew <PERSON>          | 2019-01-07  | Fetch contacts
-- Ahmad Saeed           | 2019-02-18  | Add whitelabel_id
-- Vatulin Yevhen		 | 2019-04-03  | Add origin
-- Varakorn Koschakosai  | 2019-12-17  | Retrieve data from additional info table
-- Weerapong Muangmai    | 2025-02-03  | add fetch wallet data
-------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC  [dbo].[api_rew_member_by_uuid_v11] '02ABDE45-F5E6-4270-9E2D-00EBDAE8E249',1,1
REVERT;
END_EXEC_TEST
*/
--------------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[api_rew_member_by_uuid_v11]
		@uuid uniqueidentifier,
		@override_rec_status BIT = 0,
		@load_roles BIT = 1
AS
	BEGIN
		SET NOCOUNT ON
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
		--1) For MemberDetails and RewardsMemberUserInfoForLogin (combined)
		SELECT		a.membercode as MemberCode
			, a.MemberID
			, a.title
			, a.first_name
			, a.middle_name
			, a.last_name
			, a.suffix
			, a.birth_date
			, a.nationality_id
			, a.language_id
			, ISNULL(a.points_earned, 0) AS points_earned
			, a.points_redeemed
			, ISNULL(a.point_balance, 0) AS point_balance
			, a.points_expire_this_year
			, a.auto_sign_up
			, a.is_elite_status
			, a.elite_activation_date
			, a.is_blacklisted
			, a.elite_expire_date
			, a.deactivation_date
			, a.deactivation_reason
			, a.merged_to
			, a.special_remarks
			, a.is_newsletter
			, a.rec_status
			, a.rec_created_by
			, a.rec_created_when
			, a.rec_modify_by AS rec_modified_by
			, a.rec_modify_when AS rec_modified_when
			, c.country_name
			, a.is_point_eligible
			, a.is_cc_onfile_opt_out
			, a.loyalty_level
			, a.giftcard_status
			, a.giftcard_level
			, a.is_signup_sent
			, a.signup_mail_duedate
			, a.is_one_click_bf_ready
			, a.prefer_partner_loyalty_program_id
			, ISNULL(a.whitelabel_id, 1) as whitelabel_id
			, a.origin
      , a.properties
      , a.UserId as user_id
      , a.wallet_birthdate_hash as wallet_birthdate_hash
      , a.wallet_country_hash as wallet_country_hash
		FROM		   dbo.rew_members as a
			LEFT JOIN	 dbo.geo2_country AS c
				ON  a.nationality_id = c.country_id
		WHERE		   a.UserId = @uuid
								AND		     (a.rec_status = 1 OR @override_rec_status = 1);

    --2) Contacts
    SELECT TOP(100)
	    a.contact_id
			, a.contact_method_id
			, a.MemberID
			, a.organisation_id
			, a.contact_method_value
			, a.contact_method_remark
			, a.rec_status
			, a.rec_created_by
			, a.rec_created_when
			, a.rec_modify_by AS rec_modified_by
			, a.rec_modify_when AS rec_modified_when
			, ISNULL(a.whitelabel_id, 1) AS whitelabel_id
			, a.origin
			, b.contact_method_name
	FROM		dbo.rew_contacts a
	INNER JOIN	dbo.afm_contact_methods b
				ON a.contact_method_id = b.contact_method_id
	INNER JOIN	dbo.rew_members c
				ON a.MemberID = c.MemberID
	WHERE		c.UserId = @uuid
	ORDER BY a.rec_status desc, a.contact_method_id asc, a.rec_created_when desc

		--3) All user mappings and info
            SELECT  a.UserId AS [user_id],
                    a.DisplayName AS display_name,
                    a.EmailAddress AS email_address,
                    a.FailedLoginAttempts as failed_login_attempts,
                    a.IsBlocked as is_blocked,
                    a.Manager as manager_user_id,
                    a.PhoneNo,
                    a.IsTwoFactorEnabled,
                    a.department_id,
                    a.location_id,
                    ISNULL(a.whitelabel_id, 1) AS whitelabel_id,
                    b.uri AS uri,
                    b.authentication_type_id AS authentication_type_id,
                    b.username AS username,
                    b.username AS user_name,
                    b.rec_status,
                    b.rec_created_when,
                    b.rec_created_by,
                    b.rec_modified_by,
                    b.rec_modified_when,
                    b.is_verified,
                    COALESCE(b.password_last_changed, b.rec_modified_when, b.rec_created_when) AS password_last_changed,
                    CASE b.authentication_type_id
                    WHEN 2 THEN b.expired_since
                    ELSE GETDATE()+30 End AS expired_since,
                    a.is_single_task_consumption AS is_single_task_consumption
		FROM		dbo.auth_users a
			INNER JOIN dbo.auth_user_mapping b
				ON a.UserId = b.[user_id]
		WHERE	a.UserId  = @uuid;

    --4) roles
		IF @load_roles = 1
			SELECT		ur.UserID
				, ur.RoleId
				, ur.ObjectType
				, ur.ObjectID
				, ur.skill_code
			FROM	dbo.auth_user_in_role AS ur
			WHERE		ur.UserId = @uuid
							 AND (ur.rec_status = 1 OR @override_rec_status = 1);

		ELSE
			SELECT @uuid as UserId,
						 CAST ('732F908B-2794-4A95-BABD-873EA675BA8E' as uniqueidentifier) as RoleId,
						 'R' as ObjectType,
						 0 as ObjectId,
						 '' as skill_code;

    --5) memberships
		SELECT		 m.program_id
			, m.membership_id
		FROM		   dbo.rew_partner_membership m
			INNER JOIN dbo.rew_members rm
				ON rm.MemberID = m.MemberID
			INNER JOIN dbo.partner_loyalty_point_programs p
				ON p.program_id = m.program_id
		WHERE		   rm.UserId = @uuid
								AND		     p.is_active = 1;

	--6) Additional info
		SELECT  rmai.last_successful_login,
		        rmai.tax_id,
		        rmai.force_logout,
		        rmai.ccpa_opt_out,
		        rmai.ccpa_last_updated_when,
		        rmai.preferred_currency
		FROM    dbo.rew_members_additional_info AS rmai
		    INNER JOIN dbo.rew_members AS rm
		        ON rm.UserId = rmai.user_id
		WHERE   rm.UserId = @uuid
		    AND (rm.rec_status = 1 OR @override_rec_status = 1);
	END
GO

GRANT EXECUTE ON [dbo].[api_rew_member_by_uuid_v11] TO [customer_api_user]
GO
