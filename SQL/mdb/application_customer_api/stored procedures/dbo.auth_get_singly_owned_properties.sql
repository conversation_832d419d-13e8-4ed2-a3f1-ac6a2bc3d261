---------------------------------------------------------------------------------------------------
-- Author               | Date          | Comment
------------------------|---------------|----------------------------------------------------------
-- J <PERSON>       | 26-04-2018    | get properties owned by this user that are NOT
--                                        associated with other users in the table
---------------------------------------------------------------------------------------------------
-- MONKEY_TEST : EXEC [dbo].[auth_get_singly_owned_properties] '55DAC37F-61CE-4E89-B6AF-86355A37969C'
---------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[auth_get_singly_owned_properties]
(
      @userId  UNIQUEIDENTIFIER
)
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

        SELECT ObjectID
        FROM dbo.auth_user_in_role
        WHERE ObjectType = 'H'
        AND ObjectID IN
            (SELECT DISTINCT ObjectID
             FROM dbo.auth_user_in_role
             WHERE ObjectType = 'H'
                AND rec_status = 1
                AND UserId = @userId)
        GROUP BY ObjectID
        HAVING COUNT(DISTINCT UserId) = 1

  END
GO

GRANT EXECUTE ON [dbo].[auth_get_singly_owned_properties] TO [customer_api_user]
GO
