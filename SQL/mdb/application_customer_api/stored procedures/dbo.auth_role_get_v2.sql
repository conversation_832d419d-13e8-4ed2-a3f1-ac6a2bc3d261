
---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Saran AUIR.		| 2011-03-11	| Get the roles related with the user.
-- Sirichai Ch.		| 2012-07-09	| Added Support Out Of Office User, will also get the roles of out of office user for the assigned userid.
------------------------|---------------|----------------------------------------------------------
-- YCS/Rate Module/Lite Rate Control
-- EXEC dbo.auth_role_get_v2 '437EF331-6550-497F-B81F-22088BD17FE0', '5ad1d94a-d0f7-4ba4-b614-8731ba116934'
-- YCS/Rate module/Help , '4149b9d5-a948-407c-8058-43b504cdc466'
-- EXEC dbo.auth_role_get_v2 '437EF331-6550-497F-B81F-22088BD17FE0', '4149b9d5-a948-407c-8058-43b504cdc466'
-- EXEC dbo.auth_role_get_v2 'A0CAA3FE-9DD8-4B36-B90E-C009B4836BC8', '4149b9d5-a948-407c-8058-43b504cdc466'
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_role_get_v2]
	@userId uniqueidentifier,
	@componentId uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

		SELECT		AR.rolename
				, AUIR.objecttype
				, AUIR.objectid
				, PH.hotel_name AS objectname
		FROM		dbo.auth_roles AS AR
		INNER JOIN	dbo.auth_user_in_role AS AUIR
					ON AR.roleid = AUIR.roleid
		INNER JOIN	dbo.auth_role_permissions AS ARP
					ON AR.roleid = ARP.roleid
		INNER JOIN	dbo.auth_permissions AS AP
					ON ARP.permissionid = AP.permissionid
		LEFT JOIN	dbo.product_hotels PH
					ON AUIR.ObjectID = PH.hotel_id
					AND AUIR.ObjectType = 'H'
					AND AUIR.ObjectID <> 0
		WHERE		AR.rec_status = 1
		AND		AUIR.userid = @userId
		AND		AUIR.rec_status = 1
		AND		ARP.rec_status =1
		AND		AP.componentid = @componentId
		AND		AP.rec_status  = 1
	UNION
		SELECT		AR.rolename
				, AUIR.objecttype
				, AUIR.objectid
				, PH.hotel_name AS objectname
		FROM		dbo.auth_roles AR
		INNER JOIN	dbo.auth_user_outofoffice_mapping AS AUOM
					ON AUOM.assignedUserId = @userId	
		INNER JOIN	dbo.auth_user_in_role AS AUIR
					ON AR.roleid = AUIR.roleid
					AND AUIR.userid = AUOM.UserId
		INNER JOIN	dbo.auth_role_permissions AS ARP
					ON AR.roleid = ARP.roleid	
		INNER JOIN	dbo.auth_permissions AS AP
					ON ARP.permissionid = AP.permissionid
		LEFT JOIN	dbo.product_hotels AS PH
					ON AUIR.ObjectID = PH.hotel_id
					AND AUIR.ObjectType = 'H'
					AND AUIR.ObjectID <> 0
		WHERE		AR.rec_status = 1
		AND		AUOM.rec_status = 1
		AND		GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate
		AND		AUIR.out_of_office = 1
		AND		AUIR.rec_status = 1
		AND		ARP.rec_status =1
		AND		AP.componentid = @componentId
		AND		AP.rec_status = 1
END




GO

