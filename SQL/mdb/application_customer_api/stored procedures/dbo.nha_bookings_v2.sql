/*
-----------------------------------------------------------------------------------------------------------
-- Author           | Date       | Comment
--------------------|------------|-------------------------------------------------------------------------
-- <PERSON>     | 15/05/2018 | Get bookings for NHA hotels
-- Andrey S.        | 05/09/2019 | Add WL id filter
--------------------|------------|-------------------------------------------------------------------------
EXEC_TEST
DECLARE @hotelids AS [id_table_type];
INSERT INTO @hotelids (id) (SELECT DISTINCT TOP 100 hotel_id FROM dbo.product_hotels)
EXEC [dbo].[nha_bookings_v2] @hotelids, 1
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[nha_bookings_v2]
    @hotelIds [id_table_type] READONLY,
    @whitelabel_id smallint
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT ebh.hotel_id as h ,eb.workflow_state_id as s ,count(*) as c
    from dbo.ebe_booking_hotel ebh
    inner join dbo.ebe_booking eb on eb.booking_id = ebh.booking_id
    inner join @hotelids hotelIds on hotelIds.id = ebh.hotel_id
    inner join dbo.ebe_booking_summary ebs on ebs.booking_id = ebh.booking_id
    where eb.workflow_state_id in(505,506)
    and ISNULL(ebs.whitelabel_id, 1) = @whitelabel_id
    group by ebh.hotel_id,eb.workflow_state_id
END
GO

GRANT EXECUTE ON [dbo].[nha_bookings_v2] TO [customer_api_user]
GO

