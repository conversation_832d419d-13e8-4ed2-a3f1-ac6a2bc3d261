

---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Saran ARP.		| 2011-01-31	|  Get the component related with the user.
-- Teedanai		| 2011-07-29	| Add OrderId for sorting
-- Sirichai Ch.		| 2012-07-09	| Gets related component for the specific user (for menu)
-- Pakorn W		| 2017-06-21	| remove ObjectID and ObjectType from temp and change distinct to group by statement
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST: exec dbo.auth_components_get_v2  '750E1E53-E400-44E5-86EF-19EC23E2EABE', 'A64A8D89-F1CD-4107-8F37-577AECE7D9F0',1
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_components_get_v2]
	@userId uniqueidentifier,
	@systemId uniqueidentifier,
	@languageId int
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @Permission TABLE
	(
		  PermissionId uniqueidentifier
	)

	-- Get all permissions related with the user.
	;WITH
		Permission AS
		(
				SELECT		AP.PermissionId
				FROM		dbo.auth_permissions AP
				INNER JOIN	dbo.auth_role_permissions ARP
							ON AP.PermissionId = ARP.PermissionId
							AND  ARP.rec_status = 1
				INNER JOIN	dbo.auth_roles AR
							ON ARP.RoleId = AR.RoleId
							AND  AR.rec_status = 1
				INNER JOIN	dbo.auth_user_in_role AUIR
							ON AR.RoleId = AUIR.RoleId
							AND  AUIR.rec_status = 1
							AND  AUIR.UserId = @userId
				WHERE		AP.rec_status = 1
			UNION
				--SUPPORT OUT OF OFFICE
				SELECT		AP.PermissionId
				FROM		dbo.auth_permissions AP
				INNER JOIN	dbo.auth_role_permissions ARP
							ON AP.PermissionId = ARP.PermissionId							
				INNER JOIN	dbo.auth_roles AR
							ON ARP.RoleId = AR.RoleId								
				INNER JOIN	dbo.auth_user_outofoffice_mapping AUOM
							ON AUOM.assignedUserId = @userId
				INNER JOIN	dbo.auth_user_in_role AS AUIR
							ON AR.RoleId = AUIR.RoleId								
							AND  AUIR.UserId = AUOM.UserID								
				WHERE		AP.rec_status = 1
				AND		ARP.rec_status = 1
				AND		AR.rec_status = 1
				AND		AUOM.rec_status = 1
				AND		GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate
				AND		AUIR.out_of_office = 1
				AND		AUIR.rec_status = 1
		)
	INSERT INTO @Permission
	(
		PermissionId
	)
		SELECT	PermissionId
		FROM	Permission AP
		GROUP BY PermissionId

	-- Get the systems
	SELECT		ASY.SystemId
			, ASY.SystemName
			, ASY.Url
			, CASE WHEN ASY.OrderId IS NULL THEN 0 ELSE ASY.OrderId END AS OrderID
			, ASY.OrderId OrderIDwithNull
			, CASE WHEN ASY.SystemId = @systemId THEN 0 ELSE 1 END AS Decode
	FROM		@permission AS AP
	INNER JOIN	dbo.auth_permissions AS ARP
				ON AP.PermissionId = ARP.PermissionId
	INNER JOIN	dbo.auth_components AS AR
				ON ARP.ComponentId = AR.ComponentId				
	INNER JOIN	dbo.auth_subsystems AS AUIR
				ON AR.SubSystemId = AUIR.SubSystemId				
	INNER JOIN	dbo.auth_systems AS ASY
				ON AUIR.SystemId = ASY.SystemId				
	WHERE		AR.rec_status = 1
	AND		AUIR.rec_status = 1
	AND		ASY.rec_status = 1
	GROUP BY 		ASY.SystemId
			, ASY.SystemName
			, ASY.Url
			, CASE WHEN ASY.OrderId IS NULL THEN 0 ELSE ASY.OrderId END 
			, ASY.OrderId 
			, CASE WHEN ASY.SystemId = @systemId THEN 0 ELSE 1 END 
	ORDER BY	Decode, OrderID, ASY.SystemName

	-- Get the components
	SELECT		AUIR.SubSystemId
			, ISNULL(AST.SubSystemName, AUIR.SubSystemName) AS SubSystemName
			, AR.ComponentId
			, ISNULL(ACT.ComponentName, AR.ComponentName) AS ComponentName
			, AR.url
			, ISNULL(AUIR.OrderId,0) AS SubSystemOrderId
			, ISNULL(AR.OrderId,0) AS ComponentOrderId
	FROM		@permission AP
	INNER JOIN	dbo.auth_permissions ARP
				ON AP.PermissionId = ARP.PermissionId
	INNER JOIN	dbo.auth_components AR
				ON ARP.ComponentId = AR.ComponentId
			
	INNER JOIN	dbo.auth_subsystems AUIR
			ON AR.SubSystemId = AUIR.SubSystemId
			
	INNER JOIN	dbo.auth_systems ASY
			ON AUIR.SystemId = ASY.SystemId

	LEFT JOIN	dbo.auth_subsystem_translations AST
				ON AUIR.SubSystemId = AST.SubSystemId
				AND  AST.LanguageId = @languageId
				AND AST.rec_status = 1
	LEFT JOIN	dbo.auth_component_translations ACT
				ON AR.ComponentId = ACT.ComponentId
				AND ACT.LanguageId = @languageId
				AND ACT.rec_status = 1
	WHERE		AR.rec_status = 1
	AND		AUIR.rec_status = 1
	AND		ASY.rec_status = 1
	AND		ASY.systemId = @systemId
	GROUP BY 
		AUIR.SubSystemId
			, ISNULL(AST.SubSystemName, AUIR.SubSystemName)
			, AR.ComponentId
			, ISNULL(ACT.ComponentName, AR.ComponentName)
			, AR.url
			, ISNULL(AUIR.OrderId,0)
			, ISNULL(AR.OrderId,0)
	ORDER BY	SubSystemOrderId, SubSystemName, ComponentOrderId, ComponentName
END



GO

