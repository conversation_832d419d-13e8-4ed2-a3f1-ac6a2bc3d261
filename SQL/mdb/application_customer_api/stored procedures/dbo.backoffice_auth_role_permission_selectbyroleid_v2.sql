-------------------------------------------------------------------------------------------------
-- Author					| Date			| Comment
----------------------------|---------------|----------------------------------------------------
-- Sir<PERSON><PERSON>	| 08/02/2011	| Get all permission for specific roleid
-- Nattwat W.				| 01/03/2019	| Move from BackOffice anf Add whitelabel condition
-------------------------------------------------------------------------------------------------
/*
 EXEC_TEST
 EXECUTE AS LOGIN = 'customer_api_user'
 EXEC [dbo].[backoffice_auth_role_permission_selectbyroleid_v2] 'A65D1B7C-DBF0-45DB-9D44-9A6481EF986A', 1
 REVERT;
 END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[backoffice_auth_role_permission_selectbyroleid_v2]
	@RoleId uniqueidentifier,
	@whitelabel_id SMALLINT
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT arp.RoleId
		  ,arp.PermissionId
		  ,cast(1 as bit) as 'IsUsed'
		  ,ap.PermissionName
		  ,arp.rec_status
		  ,arp.rec_created_when
		  ,arp.rec_created_by
		  ,arp.rec_modified_when
		  ,arp.rec_modified_by
		  ,ISNULL((SELECT emailAddress from dbo.auth_users where UserId = arp.rec_created_by AND ISNULL(whitelabel_id, 1) = @whitelabel_id),'') AS 'EmailCreatedBy'
		  ,ISNULL((SELECT emailAddress from dbo.auth_users where UserId = arp.rec_modified_by AND ISNULL(whitelabel_id, 1) = @whitelabel_id),'') AS 'EmailModifiedBy'
	  FROM dbo.auth_role_permissions arp
	  INNER JOIN dbo.auth_permissions ap ON ap.rec_status > 0 AND ap.PermissionId = arp.PermissionId
	  WHERE arp.rec_status > 0 AND arp.RoleId = @RoleId

END
GO

GRANT EXECUTE ON [dbo].[backoffice_auth_role_permission_selectbyroleid_v2] TO [customer_api_user]
GO
