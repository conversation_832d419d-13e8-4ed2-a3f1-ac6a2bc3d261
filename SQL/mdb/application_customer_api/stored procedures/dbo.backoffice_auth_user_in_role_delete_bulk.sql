/*<Monkey>
      <InstanceType Name="MDB">
      <Database Name="Application_Customer_API" />
      </InstanceType>
      <ScriptSubType>NEW_PROC</ScriptSubType>
      <ObjectName>backoffice_auth_user_in_role_delete_bulk</ObjectName>
      <TestScript>SELECT 1</TestScript>
</Monkey>*/

---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Nasakol P.		| 2018-02-06	| Delete bulk and then audit
------------------------|---------------|----------------------------------------------------------
-- Test : EXEC 
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_user_in_role_delete_bulk]
	@user_role_table [auth_user_in_role_table_type] READONLY
	, @ModifiedBy uniqueidentifier
	, @AuditTable [auth_assigned_role_audit_table_type] READONLY
AS
BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	BEGIN TRY
		BEGIN TRANSACTION;

		CREATE TABLE #OutputTblDelete (
			[UserId] [uniqueidentifier] NULL
			,[RoleId] [uniqueidentifier] NOT NULL
			,[ObjectType] [char](1) NOT NULL
			,[ObjectId] [int] NOT NULL
			)
		
		DELETE	AUIR
		OUTPUT DELETED.UserId, DELETED.RoleId, DELETED.ObjectType, DELETED.ObjectID
			INTO #OutputTblDelete(UserId, RoleId, ObjectType, ObjectID)
		FROM   dbo.auth_user_in_role AS AUIR
		INNER JOIN @user_role_table upload
		       ON	AUIR.UserId = upload.UserId 
					AND	AUIR.RoleId = upload.RoleId 
					AND	AUIR.ObjectType = upload.ObjectType 
					AND	AUIR.ObjectID = upload.ObjectID


		-- Audit deleted rows

		CREATE TABLE #OutputTblInsert (
			[Id] [int] NOT NULL,
			[UserId] [uniqueidentifier] NOT NULL,
			[AssignDate] [datetime] NOT NULL,
			[AssignedRoleId] [uniqueidentifier] NOT NULL,
			[AssignedUserId] [uniqueidentifier] NOT NULL,
			[AssignedObjectType] [char](1) NOT NULL,
			[AssignedObjectId] [int] NOT NULL,
			[ActivityId] [int] NOT NULL,
			[rec_status] [int] NOT NULL,
			[rec_created_by] [uniqueidentifier] NOT NULL,
			[rec_created_when] [datetime] NOT NULL,
			[rec_modified_by] [uniqueidentifier] NULL,
			[rec_modified_when] [datetime] NULL
			)
		
		IF EXISTS (SELECT * FROM @AuditTable)
			INSERT INTO [dbo].[auth_assigned_role_audit] (
				Id
				, [UserId]
				, [AssignDate]
				, [AssignedRoleId]
				, [AssignedUserId]
				, [AssignedObjectType]
				, [AssignedObjectId]
				, [ActivityId]
				, [rec_status]
				, [rec_created_by]
				, [rec_created_when]
				, [rec_modified_by]
				, [rec_modified_when]
				)
			OUTPUT 	INSERTED.Id, INSERTED.UserId, INSERTED.AssignDate, INSERTED.AssignedRoleId, INSERTED.AssignedUserId, INSERTED.AssignedObjectType,
					INSERTED.AssignedObjectId, INSERTED.ActivityId, INSERTED.rec_status, INSERTED.rec_created_by, INSERTED.rec_created_when
				INTO #OutputTblInsert(Id, UserId, AssignDate, AssignedRoleId, AssignedUserId, AssignedObjectType, AssignedObjectId,
					ActivityId, rec_status, rec_created_by, rec_created_when)
			SELECT	
				Id
				, UserId
				, AssignDate
				, AssignedRoleId
				, AssignedUserId
				, AssignedObjectType
				, AssignedObjectId
				, ActivityId
				, rec_status
				, rec_created_by
				, rec_created_when
				, rec_modified_by
				, rec_modified_when
			FROM	@AuditTable
		ELSE
			INSERT INTO [dbo].[auth_assigned_role_audit] (
				[UserId]
				, [AssignDate]
				, [AssignedRoleId]
				, [AssignedUserId]
				, [AssignedObjectType]
				, [AssignedObjectId]
				, [ActivityId]
				, [rec_status]
				, [rec_created_by]
				, [rec_created_when]
				, [rec_modified_by]
				, [rec_modified_when]
				)
			OUTPUT 	INSERTED.Id, INSERTED.UserId, INSERTED.AssignDate, INSERTED.AssignedRoleId, INSERTED.AssignedUserId, INSERTED.AssignedObjectType,
					INSERTED.AssignedObjectId, INSERTED.ActivityId, INSERTED.rec_status, INSERTED.rec_created_by, INSERTED.rec_created_when
				INTO #OutputTblInsert(Id, UserId, AssignDate, AssignedRoleId, AssignedUserId, AssignedObjectType, AssignedObjectId,
					ActivityId, rec_status, rec_created_by, rec_created_when)
			SELECT	@ModifiedBy
				, GETDATE()
				, RoleId
				, UserId
				, ObjectType
				, ObjectID
				, 9
				, 1
				, @ModifiedBy
				, GETDATE()
				, NULL
				, NULL
			FROM	#OutputTblDelete tblDelete
			

		SELECT 
			Id,
			UserId,
			AssignDate,
			AssignedRoleId,
			AssignedUserId,
			AssignedObjectType,
			AssignedObjectId,
			ActivityId,
			rec_status,
			rec_created_by,
			rec_created_when,
			rec_modified_by,
			rec_modified_when 
		FROM #OutputTblInsert

		COMMIT TRANSACTION

	END TRY
	BEGIN CATCH
		DECLARE @err_msg nvarchar(2000) = ERROR_MESSAGE()
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRANSACTION
		END
		RAISERROR(@err_msg, 16, 1)
	END CATCH
END



GO

GRANT EXECUTE ON [dbo].[backoffice_auth_user_in_role_delete_bulk] TO [customer_api_user]
GO
