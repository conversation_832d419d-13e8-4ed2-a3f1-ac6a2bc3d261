/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Varakorn Koschakosai     | 2019-11-15    | Update/insert customer's successful login timestamp
-- Varakorn Koschakosai     | 2020-01-21    | Refactor to upsert bulk of user login time
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
	  DECLARE @user_last_login user_last_login_time_type
	  INSERT INTO @user_last_login (uuid, ts)
	  VALUES ('97f1329c-1c8e-461c-854a-ef12cdd8b41f', GETDATE())
EXEC [dbo].[upsert_success_login_v2] @user_last_login;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[upsert_success_login_v2]
    @user_last_login dbo.user_last_login_time_type READONLY
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    UPDATE     rma
    SET        rma.last_successful_login = ul.ts,
               rma.lastupdated_when = ul.ts
    FROM       dbo.rew_members_additional_info AS rma
    INNER JOIN @user_last_login AS ul
    ON         rma.user_id = ul.uuid

    INSERT INTO dbo.rew_members_additional_info(user_id, last_successful_login, lastupdated_when)
        SELECT ul.uuid, ul.ts, ul.ts FROM @user_last_login AS ul
        WHERE NOT EXISTS(SELECT 1 FROM dbo.rew_members_additional_info AS rma WHERE rma.user_id = ul.uuid)

END
GO

GRANT EXECUTE ON [dbo].[upsert_success_login_v2] TO [customer_api_user]
GO
