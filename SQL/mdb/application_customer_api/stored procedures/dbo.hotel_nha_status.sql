/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- Andrew <PERSON> 		| 09/07/2018	| First version
------------------------------------------------|---------------|------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[hotel_nha_status]
REVERT;
END_EXEC_TEST
------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[hotel_nha_status]
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
  SELECT h.hotel_id as h, h.nha_host_level as l from dbo.product_hotels as h where h.nha_host_level is not null
  END

GO

GRANT EXECUTE ON [dbo].[hotel_nha_status] TO [customer_api_user]
GO
