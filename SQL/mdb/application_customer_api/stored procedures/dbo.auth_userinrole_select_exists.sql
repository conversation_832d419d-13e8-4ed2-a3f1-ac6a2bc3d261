
---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Saran AR.		| 2011-05-25	|  check user is exists in the role.
-- Sirichai Ch.		| 2012-07-10	|  Support Out Of Office features
------------------------|---------------|----------------------------------------------------------
--auth_userinrole_select_exists '9E66EFC2-7349-4AE5-94F7-5570AD40201C', 'YCS RateAllot Hotel User', 'H', 123
--auth_userinrole_select_exists '437EF331-6550-497F-B81F-22088BD17FE0' , 'YCS RateAllot Hotel User', 'H', 71656
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_userinrole_select_exists]
	@userId uniqueidentifier,
	@roleName varchar(50),
	@objectType char(1),
	@objectId int
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT	CAST
		(
			CASE WHEN EXISTS
			(
					SELECT		AUIR.UserId
					FROM		dbo.auth_user_in_role AS AUIR
					INNER JOIN	dbo.auth_roles AS AR
								ON AUIR.roleId = AR.roleId
					WHERE		AUIR.userid = @userId
					AND		AR.roleName = @roleName
					AND		AR.rec_status = 1
					AND 		AUIR.ObjectType = @objectType
					AND 		(
								AUIR.ObjectID = @objectId
								OR AUIR.ObjectID = 0
							)
					AND 		AUIR.rec_status = 1
				UNION
					SELECT		AUIR.UserId
					FROM		dbo.auth_user_in_role AS AUIR
					INNER JOIN	dbo.auth_user_outofoffice_mapping AS AUOM
								ON AUIR.userid = AUOM.UserID
					INNER JOIN	dbo.auth_roles AR
								ON AUIR.roleId = AR.roleId
					WHERE		AUIR.ObjectType = @objectType
					AND		AUOM.AssignedUserId = @userId
					AND		AR.roleName = @roleName
					AND		AR.rec_status = 1
					AND		GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate
					AND 		(
								AUIR.ObjectID = @objectId
								OR AUIR.ObjectID = 0
							)
					AND 		AUIR.rec_status = 1
					AND 		AUIR.out_of_office = 1
			) THEN 1
			ELSE 0 END
			AS BIT
		)
END




GO

