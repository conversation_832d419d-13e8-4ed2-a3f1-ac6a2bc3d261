-------------------------------------------------------------------------------------------------------------------------------------
-- Name           | Date			 | Desc
-------------------------------------------------------------------------------------------------------------------------------------
-- Thirakhom K.   | 2017-12-19 | ALTER PROCEDURE for Delete PII & PCI data from MDB
-- Rati <PERSON>.        | 2018-02-21 | Move to CAPI DB
-- J <PERSON> | 2018-03-05 | Add middlename for rew_members + PhoneNo for auth_users + rew_partner_membership
-- J <PERSON> | 2018-03-14 | Add rev_hotels_v3
-- Rati P         | 2018-03-20 | Performance Tuning review part
-- J Vacheesuthum | 2018-03-21 | rec status -1 consistency & aum replace all username/uri with masking mail
--                                + rew_staff_notes, auth_social_user_info, auth_user_activatedemail_token
-- Natavit R.     | 2018-06-08 | V6, update lastupdated_when column
-- Andrey S.      | 2018-08-16 | v7, set auth_users.rec_status to -1
-- Mrun S.        | 2018-12-21 | Remove Reward role dependency
-- Vatulin Y.     | 2019-09-02 | Clean other PII: properties, nationality_id, birth_date, title
-- Virayut S.     | 2019-09-27 | Remove deleting rew2_member_login_log
-------------------------------------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC  [dbo].[gdpr_customer_info_delete_MDB_v10] 6, '8D3F6DA3-5BC6-498A-88B9-AA2B49387B4F', '<EMAIL>'
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[gdpr_customer_info_delete_MDB_v10]
(
	@MemberID int
	, @requester uniqueidentifier
	, @source_email varchar(100)
)
AS
BEGIN
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @err_msg nvarchar(2000) = ''
		, @err_number int = 0

	DECLARE @masking_email varchar(100)
	SET @masking_email = CONVERT(varchar(100),@MemberID)+'@xxxx.xxx'

	BEGIN TRY
		BEGIN TRANSACTION
		IF EXISTS (SELECT * FROM dbo.rew_members WHERE MemberID = @MemberID)
		BEGIN

			SELECT UserId
			INTO #target
			FROM dbo.rew_members AS rm
			WHERE rm.MemberID = @MemberID

			UPDATE dbo.rew_members
			SET title = ''
			    , first_name = ''
			    , middle_name = '' 
				, last_name = ''
			    , birth_date = NULL
			    , properties = '{}'
				, is_newsletter = 0
			    , nationality_id = 0
				, rec_status = - 1
				, deactivation_date = GETDATE()
				, deactivation_reason = 'Customer request'
				, rec_modify_when = GETDATE()
				, rec_modify_by = @requester
				, lastupdated_when = GETDATE()
			WHERE MemberID = @MemberID

			UPDATE dbo.rew_contacts
			SET contact_method_value = @masking_email
			    , contact_method_remark = ''
				, rec_status = -1
				, rec_modify_when = GETDATE()
				, rec_modify_by = @requester
				, lastupdated_when = GETDATE()
			WHERE MemberID = @MemberID

			UPDATE au
			SET au.EmailAddress = @masking_email
				, au.DisplayName = ''
				, au.rec_modified_when = GETDATE()
				, au.rec_modified_by = @requester
				, au.PhoneNo = ''
				, au.rec_status = -1
			FROM dbo.auth_users AS au
			WHERE au.UserId IN (SELECT UserId FROM #target)

			DELETE aud
			FROM dbo.auth_user_device as aud
			WHERE aud.[User_Id] IN (SELECT UserId FROM #target)

			UPDATE aue
			SET	[username] = @masking_email
			FROM dbo.auth_user_exception AS aue
			WHERE aue.[User_Id] IN (SELECT UserId FROM #target)

			UPDATE	aum
			SET	aum.uri = @masking_email
				, aum.username = @masking_email
				, aum.rec_status = -1
				, aum.rec_modified_when = GETDATE()
				, aum.rec_modified_by = @requester
			FROM dbo.auth_user_mapping AS aum
			WHERE aum.[user_id] IN (SELECT UserId FROM #target)
			
			UPDATE auat
			SET 	token_options = ''
				, rec_modified_by = @requester
				, rec_modified_when = GETDATE()
			FROM dbo.auth_user_activatedemail_token AS auat
			WHERE auat.UserId IN (SELECT UserId FROM #target)

			UPDATE asui
			SET user_info = ''
			    , app_user_id = ''
				, is_active = 0
				, lastupdated_when = GETDATE()
			FROM dbo.auth_social_user_info AS asui
			WHERE asui.[user_id] IN (SELECT UserId FROM #target)

			UPDATE RA
			SET	organisation_id = NULL
				, address_1 = NULL
				, address_2 = NULL
				, postal_code = NULL
				, region = NULL
				, country = NULL
				, state = NULL
				, city = NULL
				, area = NULL
				, rec_status = -1
				, rec_modify_by = @requester
				, rec_modify_when = GETDATE()
				, country_id = 1
				, lastupdated_when = GETDATE()
			FROM dbo.rew_address AS RA
			WHERE MemberID = @MemberID
			
			UPDATE dbo.rew_staff_notes
			SET 	staff_comment = ''
				, rec_modify_by = @requester
				, rec_modify_when = GETDATE()
				, lastupdated_when = GETDATE()
			WHERE MemberID = @MemberID

			DELETE FROM dbo.rew_partner_membership
			WHERE MemberID = @MemberID

            SELECT user_review_id
            INTO     #REVIEW
            FROM dbo.review_user_reviews
            WHERE member_id = @MemberID

            UPDATE rev
            SET member_name = ''
                , review_title = ''
                , review_positives = ''
                , review_negatives = ''
                , review_comments = ''
                , review_tip_text = ''
                , rec_status = -1
                , rec_modify_by = 'CAPIErasure'
                , rec_modify_when = GETDATE()
            FROM dbo.rev_hotels_v3 rev
            WHERE   EXISTS (SELECT * FROM #REVIEW r WHERE rev.user_review_id = r.user_review_id)
            UPDATE rev
            SET member_firstname = ''
                , member_lastname = ''
                , rec_status = -1
                , rec_modified_when = GETDATE()
                , rec_modified_by = @requester
            FROM dbo.review_user_reviews AS rev
            INNER JOIN #REVIEW AS r
                ON rev.user_review_id = r.user_review_id
            UPDATE  ra
            SET ra.review_answer_content = ''
                , ra.rec_modified_when = GETDATE()
                , ra.rec_modified_by = @requester
                , ra.rec_status = -1
            FROM    dbo.review_user_answers ra
            INNER JOIN dbo.review_migration_key_mapping m
                ON ra.review_question_id = m.question_id
            INNER JOIN  dbo.review_migration_key k
                    ON  m.migration_key_id = k.migration_key_id
            WHERE k.migration_key_id BETWEEN 10 and 12
            AND EXISTS (SELECT * FROM #REVIEW r WHERE ra.user_review_id = r.user_review_id)

			SELECT @err_msg AS err_msg, @err_number AS err_number
		END 
		ELSE BEGIN
			SELECT 'There is no MemberID = ' + CONVERT(varchar(10),@MemberID) + ' exists in the dbo.rew_members' AS err_msg
				, 998 AS err_number
		END
		
		COMMIT TRANSACTION

	END TRY
	BEGIN CATCH

		IF XACT_STATE() <> 0
			BEGIN
			    ROLLBACK TRANSACTION
			END

		SET @err_msg = ERROR_MESSAGE()
		SET @err_number = ERROR_NUMBER()
       		RAISERROR(@err_msg, 16, 1)
	END CATCH

END



GO

GRANT EXECUTE ON [dbo].[gdpr_customer_info_delete_MDB_v10] TO [customer_api_user]
GO
