-----------------------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|---------------------------------------------------------------------------------
-- Andrew L.      | 2019-03-14 | Initial version
------------------|------------|---------------------------------------------------------------------------------
-- EXEC_TEST : exec [legacy_properties_mid_v1] 3
-----------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[legacy_properties_mid_v1]
@memberId int
AS
BEGIN
  SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT * from dbo.rew_address as ra WHERE ra.MemberID = @memberId
  SELECT rt.traveler_type_id from dbo.rew_member_traveler_type rt WHERE rt.MemberID = @memberId

  SELECT	a.preference_id AS preference_id
      , a.MemberId as member_id
      , a.city_id
      , a.rec_status as record_status
      , a.rec_created_by as record_created_by
      , a.rec_created_when as record_created_when
      , a.rec_modified_by AS record_modified_by
      , a.rec_modified_when AS record_modified_when
      , b.country_id
      , b.city_name
      , c.country_name
      , cast(0 as bit) as is_new
    FROM	dbo.rew_member_preference a
      INNER JOIN dbo.geo2_city b
        ON a.city_id = b.city_id
      INNER JOIN dbo.geo2_country c
        ON b.country_id = c.country_id
    WHERE	a.MemberID = @memberId
END

GO

GRANT EXECUTE ON [dbo].[legacy_properties_mid_v1] TO [customer_api_user]
GO
