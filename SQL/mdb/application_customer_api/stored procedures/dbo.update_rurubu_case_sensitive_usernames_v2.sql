/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- <PERSON><PERSON><PERSON>         | 2022-23-05    | Update case sensitive username for existing Rurubu users
-- Weerapong Muangmai       | 2022-06-21    | Add Authenication Type id
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[update_rurubu_case_sensitive_usernames_v2] '1f46ce05-a1fb-4f28-8521-47e443ab6533', 'AgodaTest', 11;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[update_rurubu_case_sensitive_usernames_v2]
    @user_id uniqueidentifier,
    @username_to_be_updated nvarchar(100),
    @authentication_type_id INT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    UPDATE      dbo.auth_user_mapping
    SET         username = @username_to_be_updated,
                rec_modified_by	= '9b7dde37-d0fe-44fa-8c5e-fe61277c44b1',
                rec_modified_when = GETDATE()
    WHERE       user_id = @user_id
    AND         authentication_type_id = @authentication_type_id
    AND         whitelabel_id = 4

END
GO

GRANT EXECUTE ON [dbo].[update_rurubu_case_sensitive_usernames_v2] TO [customer_api_user]
GO
