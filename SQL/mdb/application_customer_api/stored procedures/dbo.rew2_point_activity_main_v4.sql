-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wich<PERSON>	| 2011-12-??	|
--				|		| @run_mode ('Normal','FIFO_Only','Add_Activity_Only','Expire_Only','Update_Point_Only')
-- Sirilak			| 2012-01-17	| Change @config_redeem_point_per_dollar from int to decimal(19,2)
-- Wich<PERSON> <PERSON>.			| 2012-01-23	| Add parameter @booking_status_id in rew2_point_activity_process_non_booking SP
-- Chate C.			| 1013-02-07	| Refactoring code format and add logic to handle booking order than 2 years.
-- Chate C.			| 2013-08-06	| Adding more parameters.
-- Chate C.			| 2014-01-21	| Add "EXEC [dbo].[rew2_initial_offset_flag] @MemberID"
-- Preutsaji L.			| 2014-01-29	| Apply New FIFO Logic V2
-- Preutsaji L.			| 2014-04-11	| Apply New FIFO Logic V2 for expiration process
-- Peter Szabo			| 2014-09-04	| TFS#21258: Created v2 version for migrating sp to Application_Rewards
-- Shotirose P.			| 2017-08-02	| Insert test script and remove RAISERROR
-- Natavit R.       | 2017-03-23     | V3, return output as a result set
-- Natavit R.   | 2018-06-08    | V4, call rew2_point_activity_update_rew_members_v2
--------------------------------|---------------|----------------------------------------------------------
-- EXEC_TEST : EXEC rew2_point_activity_main_v4 @MemberID=1
--------------------------------|---------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[rew2_point_activity_main_v4] (
   @booking_status_id			SMALLINT		= 0
  ,@booking_points_used			INT			= null
  ,@MemberID				INT
  ,@storefront_id				INT			= null
  ,@booking_id				INT			= null

  ,@booking_date				SMALLDATETIME		= null
  ,@departure_date			DATE			= null
  ,@booking_value				DECIMAL(19,2)		= null
  ,@credit_card_value			DECIMAL(19,2)		= null
  ,@promotion_guid			UNIQUEIDENTIFIER	= null

  ,@promotion_source_id			INT			= null
  ,@promotion_expiry_date			DATE			= null
  ,@nPointTime				TINYINT			= null
  ,@mergeid				INT			= null
  ,@remarks				NVARCHAR(500)		= null

  ,@user_id				UNIQUEIDENTIFIER	= '00000000-0000-0000-0000-000000000000'
  ,@point_activity_subtype_id		INT			= null --For @is_process_booking = 0
  ,@points_affected			INT			= null
  ,@affected_date				DATETIME		= null
  ,@is_process_booking			BIT			= 1

  ,@run_mode				VARCHAR(50)		= 'Normal'
  ,@message				NVARCHAR(100)		= null	OUTPUT
  ,@point_activity_id			INT			= null	OUTPUT
  ,@isSuccess				BIT			= null	OUTPUT

  -- Additional Parameters
  ,@refund_value           		DECIMAL(18,2)		= null
  ,@is_user_logged_in      		BIT			= 1		-- used by 15431
  ,@Is_BNPL_booking        		BIT			= null
  ,@payment_model          		INT			= null
  ,@affiliate_model        		INT			= null
  ,@discount_savings       		DECIMAL(18,2)		= null
  ,@promotion_code         		VARCHAR(20)		= null
  ,@booking_date_until     		SMALLDATETIME		= null

)
AS
  BEGIN
    SET XACT_ABORT ON
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    DECLARE  @process_id				INT
    ,@is_expired				BIT 		= 0
    ,@is_points_affected_change		BIT 		= 0
    ,@error_number				INT 		= 0
    ,@error_message				NVARCHAR(2048)	= ''
    ,@error_sp_name				VARCHAR(2048)	= ''
    ,@error_step_id				SMALLINT 	= 0
    ,@step_id				SMALLINT 	= 0
    ,@return				SMALLINT 	= 0
    ,@is_run_expire				BIT		= 0
    ,@is_run_expire2			BIT		= 0
    ,@is_run_add_activity			BIT		= 0
    ,@is_run_FIFO				BIT		= 0
    ,@is_run_update_point			BIT		= 0
    ,@points_available			INT		= 0
    ,@config_rate_premium			DECIMAL (19,2)
    ,@config_rate_elite			DECIMAL (19,2)
    ,@config_redeem_point_per_dollar	DECIMAL (19,2)
    ,@config_point_pending_period		TINYINT
    ,@config_annual_expiry_period		TINYINT
    ,@custom_departure_date			DATETIME	= NULL



    ---------------------------------------------------------------------------------------------------------------
    -- START AUDIT PROCESS.
    ---------------------------------------------------------------------------------------------------------------
    INSERT INTO dbo.rew2_process_audit
    (
      process_start
      ,process_status
      ,rec_status
      ,rec_created_when
      ,rec_created_by
      ,booking_status_id
      ,booking_points_used
      ,MemberID
      ,storefront_id
      ,booking_id
      ,booking_date
      ,departure_date
      ,booking_value
      ,credit_card_value
      ,promotion_guid
      ,promotion_source_id
      ,promotion_expiry_date
      ,nPointTime
      ,mergeid
      ,remarks
      ,[user_id]
      ,point_activity_subtype_id
      ,points_affected
      ,affected_date
      ,is_process_booking
      ,run_mode
      ,[message]
      ,point_activity_id
      ,isSuccess
      ,is_user_logged_in
    )

    VALUES
      (
        GETDATE()
        ,'processing'
        ,1
        ,GETDATE()
        ,@user_id
        ,@booking_status_id
        ,@booking_points_used
        ,@MemberID
        ,@storefront_id
        ,@booking_id
        ,@booking_date
        ,@departure_date
        ,@booking_value
        ,@credit_card_value
        ,@promotion_guid
        ,@promotion_source_id
        ,@promotion_expiry_date
        ,@nPointTime
        ,@mergeid
        ,@remarks
        ,@user_id
        ,@point_activity_subtype_id
        ,@points_affected
        ,@affected_date
        ,@is_process_booking
        ,@run_mode
        ,@message
        ,@point_activity_id
        ,@isSuccess
        ,@is_user_logged_in
      )

    SET @process_id = SCOPE_IDENTITY()




    BEGIN TRY


    BEGIN TRANSACTION

    ---------------------------------------------------------------------------------------------------------------
    -- RETURN SUCCESS FOR SUBTYPE = 10
    ---------------------------------------------------------------------------------------------------------------
    SET	@step_id = 10
    IF	    @booking_status_id NOT IN (10)	-->> 10 = Technical Problem
            OR  @booking_status_id is null		-->> For Non-Booking Action
      BEGIN


        /*
        || 2014-01-22 (TFS18440)
        || Added by Chate C.
        */
        SET @step_id = 15
        SET @step_id = 175
        EXEC @return = [dbo].[rew2_initial_offset_flag]
            @MemberID
            ,@error_number				= @error_number			OUTPUT	--int				OUTPUT
            ,@error_message				= @error_message		OUTPUT	--nvarchar(2048)		OUTPUT
            ,@error_sp_name				= @error_sp_name		OUTPUT	--varchar(100)			OUTPUT
            ,@error_step_id				= @error_step_id		OUTPUT	--smallint			OUTPUT

        --IF @return = -1 RAISERROR (@error_message,11,1);


        ---------------------------------------------------------------------------------------------------------------
        -- VALIDATE MEMBER ID
        ---------------------------------------------------------------------------------------------------------------
        --SET	@step_id = 20
        --IF @MemberID IS NULL
        --BEGIN
        --	SELECT	@error_message = 'Member ID is null.'
        --		,@isSuccess = -1
        --	RAISERROR (@error_message,11,1);
        --END
        /*
              SET	@step_id = 30
              IF NOT EXISTS (SELECT *
                  FROM	dbo.rew_members  (nolock)
                  WHERE	MemberID = @MemberID
                  AND	rec_status	= 1)
              BEGIN
                SELECT	@error_message = 'Member ID = ['+CONVERT(VARCHAR(40),@MemberID)+'] is not found!!!.'
                  ,@isSuccess = -1
                RAISERROR (@error_message,11,1);
              END
        */
        -------------------------------------------------------------------------------------------------------------------
        -- GET DATA FROM AFM_CONFIGURATION
        -------------------------------------------------------------------------------------------------------------------
        SET	@step_id = 40
        SELECT	@config_rate_premium			= SUM(CASE configuration_key WHEN 'PPPDOL' THEN CONVERT(decimal(19,2),configuration_value) ELSE 0 END)	,
          @config_rate_elite			= SUM(CASE configuration_key WHEN 'EPPDOL' THEN CONVERT(decimal(19,2),configuration_value) ELSE 0 END) ,
          @config_redeem_point_per_dollar		= SUM(CASE configuration_key WHEN 'RPPDOL' THEN CONVERT(decimal(19,2),configuration_value) ELSE 0 END),
          @config_point_pending_period		= SUM(CASE configuration_key WHEN 'POPEPE' THEN CONVERT(tinyint,configuration_value) ELSE 0 END),
          @config_annual_expiry_period		= SUM(CASE configuration_key WHEN 'ANEXPE' THEN CONVERT(tinyint,configuration_value) ELSE 0 END)

        FROM	dbo.afm_configuration
        WHERE	configuration_group_id = 3
               AND	configuration_key IN
                    (
                      'PPPDOL'
                      ,'EPPDOL'
                      ,'RPPDOL'
                      ,'POPEPE'
                      ,'ANEXPE'
                    )
               AND	rec_status = 1

        ---------------------------------------------------------------------------------------------------------------
        -- SET RUNNING STEP FROM @run_mode
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 50
        IF  @run_mode = 'Normal'
          SELECT	@is_run_expire = 1
            ,@is_run_add_activity = 1
            ,@is_run_expire2 = 1
            ,@is_run_FIFO = 1
            ,@is_run_update_point = 1

        ELSE IF @run_mode = 'Expire_Only'
          SELECT  @is_run_expire = 1
            ,@is_run_add_activity = 0
            ,@is_run_expire2 = 0
            ,@is_run_FIFO = 0
            ,@is_run_update_point = 1

        ELSE IF @run_mode = 'Add_Activity_Only'
          SELECT  @is_run_expire = 0
            ,@is_run_add_activity = 1
            ,@is_run_expire2 = 0
            ,@is_run_FIFO = 0
            ,@is_run_update_point = 1

        ELSE IF @run_mode = 'FIFO_Only'
          SELECT @is_run_expire = 0
            ,@is_run_add_activity = 0
            ,@is_run_expire2 = 0
            ,@is_run_FIFO = 1
            ,@is_run_update_point = 1

        ELSE IF @run_mode = 'Update_Point_Only'
          SELECT @is_run_expire = 0
            ,@is_run_add_activity = 0
            ,@is_run_expire2 = 0
            ,@is_run_FIFO = 0
            ,@is_run_update_point = 1

        ELSE IF @run_mode = 'Expire__FIFO'
          SELECT @is_run_expire = 1
            ,@is_run_add_activity = 0
            ,@is_run_expire2 = 0
            ,@is_run_FIFO = 1
            ,@is_run_update_point = 1

        ELSE IF @run_mode = 'Normal_except_Expire2'
          SELECT @is_run_expire = 1
            ,@is_run_add_activity = 1
            ,@is_run_expire2 = 0
            ,@is_run_FIFO = 1
            ,@is_run_update_point = 1

        --ELSE
        --BEGIN
        --	SELECT	@error_message = '@run_mode is not correct!!!.'
        --		,@isSuccess = -1
        --		RAISERROR (@error_message,11,1)
        --END

        ---------------------------------------------------------------------------------------------------------------
        --  Chate C. 2013-02-07
        --  Validate a given booking if it is order than 2 calendar years.(Expired booking)
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 55
        IF @booking_id IS NOT NULL
          BEGIN
            SELECT	@custom_departure_date = DATEADD(DAY , @config_point_pending_period , b.booking_date_until )
            FROM	dbo.ebe_booking as b
            WHERE	b.booking_id = @booking_id

            --IF @custom_departure_date IS NOT NULL
            --	AND DATEPART(YEAR,GETDATE()) - DATEPART(YEAR,@custom_departure_date) >= 2
            --BEGIN
            --	SET @error_message = 'Booking_ID "' + CONVERT(VARCHAR,@booking_id) + '" has already been expired.'
            --	SET @isSuccess = -1
            --	RAISERROR (@error_message,11,1);
            --END

          END


        ---------------------------------------------------------------------------------------------------------------
        -- RUN EXPIRE PROCESS.
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 60 -- Run expire point process
        IF	@is_run_expire = 1
          BEGIN
            ---------------------------------------------------------------------------------------------------------------
            --  Preutsaji L. 2014-04-11
            --  Apply FIFO V2 for expiration process
            ---------------------------------------------------------------------------------------------------------------
            EXEC @return = dbo.rew2_point_activity_expired_v2
                @MemberID			= @MemberID				,
                @process_id			= @process_id				,
                @config_annual_expiry_period	= @config_annual_expiry_period		,
                @points_available		= @points_available		OUTPUT	,
                @is_expired			= @is_expired			OUTPUT	,
                @error_number			= @error_number			OUTPUT	,
                @error_message			= @error_message		OUTPUT	,
                @error_sp_name			= @error_sp_name		OUTPUT	,
                @error_step_id			= @error_step_id		OUTPUT	,
                @user_id			= @user_id
            --IF	@return = -1
            --	RAISERROR (@error_message,11,1)
          END

        ---------------------------------------------------------------------------------------------------------------
        -- ADD / MODIFY REW2_POINT_ACTIVITY.
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 70
        IF	@is_run_add_activity = 1
          BEGIN
            IF @is_process_booking = 1
              BEGIN
                SET	@step_id = 71
                EXEC	@return = dbo.rew2_point_activity_process_booking
                    @process_id				=  @process_id
                    ,@booking_status_id			=  @booking_status_id
                    ,@booking_points_used			=  @booking_points_used
                    ,@points_available			=  @points_available
                    ,@MemberID				=  @MemberID
                    ,@storefront_id				=  @storefront_id
                    ,@booking_id				=  @booking_id
                    ,@booking_date				=  @booking_date
                    ,@departure_date			=  @departure_date
                    ,@booking_value				=  @booking_value
                    ,@credit_card_value			=  @credit_card_value
                    ,@promotion_guid			=  @promotion_guid
                    ,@promotion_source_id			=  @promotion_source_id
                    ,@nPointTime				=  @nPointTime
                    ,@mergeid				=  @mergeid
                    ,@remarks				=  @remarks
                    ,@user_id				=  @user_id
                    ,@error_number				=  @error_number			OUTPUT
                    ,@error_message				=  @error_message			OUTPUT
                    ,@error_sp_name				=  @error_sp_name			OUTPUT
                    ,@error_step_id				=  @error_step_id			OUTPUT
                    ,@message				=  @message				OUTPUT
                    ,@point_activity_id			=  @point_activity_id			OUTPUT
                    ,@is_points_affected_change		=  @is_points_affected_change		OUTPUT
                    ,@config_rate_premium			=  @config_rate_premium
                    ,@config_rate_elite			=  @config_rate_elite
                    ,@config_redeem_point_per_dollar	=  @config_redeem_point_per_dollar
                    ,@config_point_pending_period		=  @config_point_pending_period
                    ,@config_annual_expiry_period		=  @config_annual_expiry_period
                    ,@is_user_logged_in			=  @is_user_logged_in -- Added by Chate C. 2013-08-06 (15431)
                --,@is_user_logged_in			=  1 -- Switch off logic (all bookings get points)




                --IF @return = -1 RAISERROR (@error_message,11,1);
              END
            ELSE --IF @is_process_booking = 1
              BEGIN
                SET	@step_id = 75
                EXEC @return = dbo.rew2_point_activity_process_non_booking
                    @process_id				= @process_id					--int
                    ,@booking_status_id			= @booking_status_id				--smallint
                    ,@point_activity_subtype_id		= @point_activity_subtype_id			--int
                    ,@points_affected			= @points_affected				--int			= null
                    ,@MemberID				= @MemberID					--int
                    ,@storefront_id				= @storefront_id				--int
                    ,@booking_id				= @booking_id					--int
                    ,@affected_date				= @affected_date				--datetime		= null
                    ,@promotion_guid			= @promotion_guid				--int			= null
                    ,@promotion_source_id			= @promotion_source_id				--int			= null
                    ,@mergeid				= @mergeid					--int			= null
                    ,@remarks				= @remarks					--nvarchar		= null
                    ,@user_id				= @user_id					--uniqueidentifier	= '00000000-0000-0000-0000-000000000000'
                    ,@config_annual_expiry_period		= @config_annual_expiry_period			--tinyint
                    ,@promotion_expiry_date			= @promotion_expiry_date			--date			= NULL
                    ,@error_number				= @error_number				OUTPUT	--int			OUTPUT
                    ,@error_message				= @message				OUTPUT	--nvarchar(2048)	OUTPUT
                    ,@error_sp_name				= @error_sp_name			OUTPUT	--varchar(2048)		OUTPUT
                    ,@error_step_id				= @error_step_id			OUTPUT	--varchar(1024)		OUTPUT
                    ,@point_activity_id			= @point_activity_id			OUTPUT	--bigint		OUTPUT
                    ,@is_points_affected_change		= @is_points_affected_change			--bit
                    ,@is_user_logged_in			=  @is_user_logged_in -- Added by Chate C. 2013-08-06 (15431)
                --,@is_user_logged_in			=  1 -- Switch off logic (all bookings get points)

                --SET @error_message = @message
                --IF @return = -1 RAISERROR (@error_message,11,1);
              END
          END
        ---------------------------------------------------------------------------------------------------------------
        -- RUN EXPIRE2 PROCESS.
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 80 -- Run expire point process
        IF	@is_run_expire2 = 1
          BEGIN
            EXEC @return = dbo.rew2_point_activity_expired_v2
                @MemberID			= @MemberID				,
                @process_id			= @process_id				,
                @config_annual_expiry_period	= @config_annual_expiry_period		,
                @points_available		= @points_available		OUTPUT	,
                @is_expired			= @is_expired			OUTPUT	,
                @error_number			= @error_number			OUTPUT	,
                @error_message			= @error_message		OUTPUT	,
                @error_sp_name			= @error_sp_name		OUTPUT	,
                @error_step_id			= @error_step_id		OUTPUT	,
                @user_id			= @user_id
            --IF @return = -1 RAISERROR (@error_message,11,1)
          END
        ---------------------------------------------------------------------------------------------------------------
        -- RUN FIFO PROCESS.
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 90
        IF	@is_run_FIFO = 1
            OR @is_points_affected_change = 1
          BEGIN
            EXEC	@return = dbo.rew2_point_activity_FIFO_V2
                @MemberID			= @MemberID				,
                @process_id			= @process_id				,
                @is_points_affected_change	= @is_points_affected_change	OUTPUT	,
                @error_number			= @error_number			OUTPUT	,
                @error_message			= @error_message		OUTPUT	,
                @error_sp_name			= @error_sp_name		OUTPUT	,
                @error_step_id			= @step_id			OUTPUT	,
                @user_id			= @user_id
            --IF @return = -1 RAISERROR (@error_message,11,1);
          END

        ---------------------------------------------------------------------------------------------------------------
        -- UPDATE POINT IN  REW_MEMBERS.
        ---------------------------------------------------------------------------------------------------------------
        SET	@step_id = 100
        IF	@is_run_update_point = 1
            OR @is_expired = 1
            OR @is_points_affected_change = 1
          EXEC @return = dbo.rew2_point_activity_update_rew_members_v2
              @MemberID			= @MemberID				,
              @error_number			= @error_number			OUTPUT	,
              @error_message			= @error_message		OUTPUT	,
              @error_sp_name			= @error_sp_name		OUTPUT	,
              @error_step_id			= @error_step_id		OUTPUT	,
              @user_id			= @user_id
        --IF @return = -1 RAISERROR (@error_message,11,1);


      END -- IF @booking_status_id NOT IN (10)

    ---------------------------------------------------------------------------------------------------------------
    -- UPDATE STATUS = "succeed" IN AUDIT PROCESS.
    ---------------------------------------------------------------------------------------------------------------
    SET	@step_id = 110
    UPDATE	dbo.rew2_process_audit
    SET	process_finish		= GETDATE()	,
      process_status		= CASE WHEN process_status = 'warning' THEN process_status ELSE 'succeed' END	,
      rec_modified_when	= GETDATE()	,
      rec_modified_by		= @user_id
    WHERE	process_id = @process_id


    IF @@TRANCOUNT > 0 COMMIT

    SELECT	@isSuccess		= 1
      ,@message		= ISNULL(@message,'')
      ,@point_activity_id	= ISNULL(@point_activity_id,0)

    SELECT
      @isSuccess         AS is_success,
      @message           AS error_message,
      @point_activity_id AS point_activity_id

    PRINT	'Succeed......'
    RETURN	0

    END TRY

    BEGIN CATCH
    PRINT	'@return  = '+CONVERT(VARCHAR(20),@return)
    IF	@return = 0
      BEGIN
        SELECT 	@error_number		= ERROR_NUMBER()	,
          @error_message		= ERROR_MESSAGE()	,
          @error_sp_name		= OBJECT_NAME(@@PROCID)+'('+(CONVERT(VARCHAR(10),@step_id))+')'	,
          @error_step_id		= @step_id
      END
    ELSE
      BEGIN
        SELECT  @error_sp_name		= OBJECT_NAME(@@PROCID)+'('+(CONVERT(VARCHAR(10),@step_id))+') >> '+@error_sp_name

      END

    IF @@TRANCOUNT > 0 ROLLBACK

    ---------------------------------------------------------------------------------------------------------------
    -- UPDATE STATUS = "failed" IN AUDIT PROCESS.
    ---------------------------------------------------------------------------------------------------------------
    UPDATE	dbo.rew2_process_audit
    SET	process_finish		= GETDATE()		,
      process_status		= 'failed'		,
      error_number		= @error_number		,
      error_message		= @error_message	,
      error_sp_name		= @error_sp_name	,
      error_step_id		= @error_step_id	,
      rec_modified_when	= GETDATE()		,
      rec_modified_by		= @user_id
    WHERE	process_id = @process_id



    PRINT 'Failed........'
    PRINT 'Error_number  : '+CONVERT(VARCHAR(20),@error_number)
    PRINT 'Error_message : '+CONVERT(NVARCHAR(2048),@error_message)
    PRINT 'Error_sp_name : '+CONVERT(VARCHAR(200),@error_sp_name)
    PRINT 'Error_Step_id : '+CONVERT(NVARCHAR(2048),@error_step_id)

    SELECT	@isSuccess		= 0
      ,@message		= ISNULL(@error_message,'')
      ,@point_activity_id	= ISNULL(@point_activity_id,0)
    PRINT '@message	     : '+CONVERT(VARCHAR(20),@message)
    RETURN -1
    END CATCH

  END


go

                                                                         
