---------------------------------------------------------------------------------------------------
-- Author			| Date			| Comment
--------------------|---------------|----------------------------------------------------------
-- Supacheep A.		| 2018-03-06	| Get Member Detail By PhoneNumber
--------------------|---------------|----------------------------------------------------------
-- Test : EXEC_TEST
--
-- EXECUTE AS LOGIN = 'customer_api_user'
-- exec [dbo].[backoffice_get_member_detail_by_phonenumber] '668754487'
-- REVERT;
--
-- END_EXEC_TEST
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_get_member_detail_by_phonenumber]
	@phoneNumber varchar(50)
AS
BEGIN
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT 
		AU.UserId,
		AU.DisplayName,
		AU.EmailAddress,
		AU.IsBlocked,
		AU.IsDeleted,
		AU.FailedLoginAttempts,
		AU.PhoneNo,
		AU.IsTwoFactorEnabled,
		RM.MemberID,
		RM.title,
		RM.first_name,
		RM.last_name,
		RM.birth_date,
		RM.nationality_id,
		RM.organisation_id,
		RM.language_id,
		RM.is_newsletter,
		RM.rec_status
	FROM dbo.auth_user_mapping MP
		INNER JOIN dbo.rew_members RM on RM.UserId = MP.user_id
		INNER JOIN dbo.auth_users AU on AU.UserId = MP.user_id
	WHERE MP.authentication_type_id = 7
  and MP.username = @phoneNumber
END

GO

GRANT EXECUTE ON [dbo].[backoffice_get_member_detail_by_phonenumber] TO [customer_api_user]
GO
