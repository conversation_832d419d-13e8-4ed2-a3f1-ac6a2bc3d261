/*
---------------------------------------------------------------------------------------------------
-- Author     | Date       | Comment
--------------|------------|-----------------------------------------------------------------------
-- <PERSON><PERSON>.  | 2018-05-09 | Add onlyActive param, return only active records by default
-- <PERSON><PERSON>.  | 2018-04-26 | Get user_in_role for list of objects
-- Sumeet S.  | 2019-02-15 | Get is_fraudulent for list of objects
-- Virayut S. | 2019-07-16 | Add wildcard(*) to not filter by objectType
---------------------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN ='customer_api_user'
DECLARE @objectIds AS [id_table_type];
INSERT INTO @objectIds (id) VALUES (2837296),(2837296),(5435902)
EXEC dbo.auth_user_in_role_get_by_objects_v4 @objectIds, 'H'
REVERT;
END_EXEC_TEST
------------------------|---------------|----------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[auth_user_in_role_get_by_objects_v4]
    @objectIds  [dbo].[ID_TABLE_TYPE] READONLY,
    @objectType CHAR(1) = 'H',
    @roleId     UNIQUEIDENTIFIER = NULL,
    @onlyActive BIT = 1
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
      au.ObjectID AS ObjectId,
      au.ObjectType,
      au.UserId,
      au.RoleId,
      au_role.RoleName,
      au.rec_created_when,
      au.rec_created_by,
      au.rec_modified_when,
      au.rec_modified_by,
      au.rec_status,
      coalesce(au.is_fraudulent, 0) as is_fraudulent
    FROM dbo.auth_user_in_role AS au
      LEFT JOIN dbo.auth_roles AS au_role ON au_role.RoleId = au.RoleId
    WHERE au.ObjectID IN (SELECT id FROM @objectIds)
      AND (@roleId IS NULL OR au.roleid = @roleId)
      AND (@objectType = '*' OR au.objecttype = @objectType)
      AND (@onlyActive = 0 OR au.rec_status = 1)
  END
GO

GRANT EXECUTE ON [dbo].[auth_user_in_role_get_by_objects_v4] TO [customer_api_user]
GO
