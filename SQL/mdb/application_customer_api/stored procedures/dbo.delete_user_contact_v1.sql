/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-06-06    | Performs soft delete for the given unverified user contact.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[delete_user_contact_v1] 1, '00000000-0000-0000-0000-000000000000';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[delete_user_contact_v1]
    @contact_id int,
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    UPDATE dbo.rew_contacts
    SET    rec_status = -1
           , rec_modify_by = @modified_by
           , rec_modify_when = GETDATE()
           , lastupdated_when = GETDATE()
    WHERE  contact_id = @contact_id;
END
GO

GRANT EXECUTE ON [dbo].[delete_user_contact_v1] TO [customer_api_user]
GO
