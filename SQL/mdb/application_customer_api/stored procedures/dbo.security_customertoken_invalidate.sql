---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- GyÃ¶rgy <PERSON>i		| 2016-05-19	| Invalidates a customer token
------------------------|---------------|----------------------------------------------------------
-- Test : EXEC dbo.security_customertoken_invalidate
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_customertoken_invalidate]
	@user_id uniqueidentifier
	, @device_id uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DELETE
	FROM	dbo.auth_user_authenticationtoken
	WHERE	[user_id] = @user_id
	AND	device_id = @device_id
END




GO

