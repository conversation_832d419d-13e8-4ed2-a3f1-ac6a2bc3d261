
/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- Andrew <PERSON> 		| 15/05/2018	| Get bookings for members
------------------------------------------------|---------------|------------------------------------------
MONKEY_TEST

DECLARE @memberIds AS [id_table_type];
INSERT INTO @memberIds (id)
(SELECT DISTINCT TOP 100 MemberID
FROM dbo.rew_members)
EXEC [dbo].[bookings_for_members] @memberIds

END_MONKEY_TEST
------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[bookings_for_members]
@memberIds [id_table_type] READONLY
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

  SELECT ei.memberid as m ,ebh.hotel_id as h ,count(memberid) as c
  from dbo.ebe_itinerary ei inner join dbo.ebe_booking eb on eb.itinerary_id = ei.itinerary_id
  inner join dbo.ebe_booking_hotel ebh on ebh.booking_id = eb.booking_id
  inner join @memberids memberIds on ei.memberid = memberIds.id
  group by ei.memberid,ebh.hotel_id
END

GO

GRANT EXECUTE ON [dbo].[bookings_for_members] TO [customer_api_user]
GO
