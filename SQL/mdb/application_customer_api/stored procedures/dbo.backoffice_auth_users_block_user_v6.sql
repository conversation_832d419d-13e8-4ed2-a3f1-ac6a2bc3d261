/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2018-07-18    | Performs a soft delete of user account in order to properly block the account
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_users_block_user_v6] '********-0000-0000-0000-************', '********-0000-0000-0000-************'
REVERT;
END_EXEC_TEST

-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[backoffice_auth_users_block_user_v6]
    @user_id uniqueidentifier,
    @modified_by uniqueidentifier,
    @history_id bigint = NULL
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
	  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    -- Define max email length without deletion suffix
    DECLARE @del_suffix varchar(10) = '_del' + FORMAT(GETDATE(), 'yyMMdd');
    DECLARE @max_email_len int = COL_LENGTH('Agoda_Core.dbo.auth_users', 'EmailAddress') - 10;
    IF @max_email_len IS NULL OR @max_email_len < 1
    BEGIN
        THROW 50001, 'Unable to detect a length limit of email address', 1;
    END;

    BEGIN TRANSACTION;
        -- SET can only assign a scalar value, so it will protect us from multiple accounts!
        DECLARE @member_id int = (SELECT memberId FROM dbo.rew_members WHERE UserId = @user_id);

        -- Delete user account
	      UPDATE dbo.auth_users
	      SET    IsBlocked = 1
	             , rec_status = -1
	             , rec_modified_when = GETDATE()
	             , rec_modified_by = @modified_by
	             , EmailAddress = CASE
				            WHEN LEN(EmailAddress) > @max_email_len THEN SUBSTRING(EmailAddress, 1, @max_email_len) + @del_suffix
				            ELSE EmailAddress + @del_suffix END
        WHERE  UserId = @user_id;

        UPDATE dbo.auth_user_in_role
        SET    rec_status = -1
               , rec_modified_when = GETDATE()
               , rec_modified_by = @modified_by
        WHERE  UserId = @user_id;

        UPDATE dbo.auth_user_mapping
        SET    rec_status = -1
               , rec_modified_when = GETDATE()
               , rec_modified_by = @modified_by
               , username = CASE
				            WHEN LEN(username) > @max_email_len THEN SUBSTRING(username, 1, @max_email_len) + @del_suffix
				            ELSE username + @del_suffix END
				       , uri = CASE
			              WHEN LEN(username) > @max_email_len THEN REPLACE(uri, username, SUBSTRING(username, 1, @max_email_len) + @del_suffix)
			              ELSE REPLACE(uri, username, username + @del_suffix) END
        WHERE  user_id = @user_id;

        -- Delete rewards member
        UPDATE dbo.rew_members
        SET    rec_status = -1
               , rec_modify_by = @modified_by
               , rec_modify_when = GETDATE()
               , lastupdated_when = GETDATE()
        WHERE  MemberID = @member_id;

        -- Delete rewards contact
        UPDATE dbo.rew_contacts
        SET    rec_status = -1
               , rec_modify_by = @modified_by
               , rec_modify_when = GETDATE()
               , lastupdated_when = GETDATE()
               , contact_method_value = CASE
                    WHEN LEN(contact_method_value) > @max_email_len THEN SUBSTRING(contact_method_value, 1, @max_email_len) + @del_suffix
                    ELSE contact_method_value + @del_suffix END
        WHERE  MemberID = @member_id;

        -- Insert audit record
        IF @history_id IS NOT NULL
			  BEGIN
			      SET IDENTITY_INSERT agoda_core.dbo.auth_user_mapping_history ON;
            INSERT INTO dbo.auth_user_mapping_history
            (
                        history_id
                        , logtime
                        , user_id
                        , authentication_type_id
                        , ActivityId
                        , rec_status
                        , rec_created_when
                        , rec_created_by
            )
            VALUES
            (
                        @history_id
                        , GETDATE()
                        , @user_id
                        , 0
                        , 33 -- Block user
                        , 1
                        , GETDATE()
                        , @modified_by
            );
			      SET IDENTITY_INSERT agoda_core.dbo.auth_user_mapping_history OFF;
			  END
			  ELSE
			  BEGIN
            INSERT INTO dbo.auth_user_mapping_history
            (
                        logtime
                        , user_id
                        , authentication_type_id
                        , ActivityId
                        , rec_status
                        , rec_created_when
                        , rec_created_by
            )
            VALUES
            (
                        GETDATE()
                        , @user_id
                        , 0
                        , 33 -- Block user
                        , 1
                        , GETDATE()
                        , @modified_by
            );
            SET @history_id = SCOPE_IDENTITY();
        END;
    COMMIT TRANSACTION;

    SELECT @history_id AS history_id;
END

GO

GRANT EXECUTE ON [dbo].[backoffice_auth_users_block_user_v6] TO [customer_api_user]
GO
