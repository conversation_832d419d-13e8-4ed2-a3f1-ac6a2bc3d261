/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
-------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|----------------------------------------------------------------
-- Tanawat Kaochalongkeang  | 2017-11-30    | Update auth user in role
-- Abhishek P.              | 2025-06-26    | Added validation for ObjectType 'K' - only one ObjectId per UserId
----------------------------|---------------|----------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_user_in_role_insert_v3] 'D4906E22-0807-4E90-A80F-00000006DB3E',
    'A855ECC7-47BA-4752-88EC-6A66226D71BF', 'H', 62937, 1, 'DF6B04C8-8188-4AB4-8DD9-C70ACD90E616'
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_user_in_role_insert_v3]
(
    @UserId uniqueidentifier
    , @RoleId uniqueidentifier
    , @ObjectType char(1)
    , @ObjectId int
    , @rec_status int
    , @rec_created_by uniqueidentifier
)
AS
BEGIN

  SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    -- Validate that for ObjectType 'K', each UserId can only have one ObjectId
    IF @ObjectType = 'K'
BEGIN
        IF EXISTS (
            SELECT 1
            FROM dbo.auth_user_in_role t
            WHERE t.UserId = @UserId
              AND t.ObjectType = 'K'
              AND t.ObjectID <> @ObjectId
              AND t.rec_status = 1
        )
BEGIN
            THROW 50000, 'A UserId can only have one ObjectId for ObjectType = ''K''.', 1;
            RETURN;
END
END

    IF (
        EXISTS
        (
            SELECT  *
            FROM    dbo.auth_user_in_role
            WHERE   UserId = @UserId
            AND     RoleID = @RoleId
            AND     ObjectId = @ObjectId
            AND     ObjectType = @ObjectType
        )
    )
BEGIN
UPDATE  dbo.auth_user_in_role
SET     rec_status = @rec_status
  , rec_modified_by = @rec_created_by
  , rec_modified_when = GETDATE()
WHERE   UserId = @UserId
  AND     RoleID = @RoleId
  AND     ObjectId = @ObjectId
  AND     ObjectType = @ObjectType
END
ELSE
BEGIN
INSERT INTO dbo.auth_user_in_role
(
  UserId
, RoleId
, ObjectType
, ObjectId
, rec_status
, rec_created_when
, rec_created_by
, rec_modified_when
, rec_modified_by
)
VALUES
    (
      @UserId
    , @RoleId
    , @ObjectType
    , @ObjectId
    , @rec_status
    , GETDATE()
    , @rec_created_by
    , NULL
    , NULL
    )
END
END
GO

GRANT EXECUTE ON [dbo].[backoffice_auth_user_in_role_insert_v3] TO [customer_api_user]
GO
