
-------------------------------------------------------------------------------------------
-- Author		| Date			| Comments
----------------|---------------|----------------------------------------------------------
-- Thanapol R.	| 2018-06-18	| Insert user activities.
-------------------------------------------------------------------------------------------
-- exec dbo.api_user_mapping_history_insert 123456, 20, <some uuid>
/*
EXEC_TEST

EXECUTE AS LOGIN = 'customer_api_user'
exec dbo.api_user_mapping_history_insert 123456, 20, '4E8B68CA-0C21-40B2-8756-B92AF2A0990B'
REVERT;

END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[api_user_mapping_history_insert]
    @MemberId                   int                 -- customer member id
    , @ActivityId               int                -- see dbo.auth_activity
    , @RecCreatedBy             uniqueidentifier    -- who create this record
    , @AuthenticationTypeId     int = 0             -- optional. only for login activity. see dbo.auth_authentication_type
AS
BEGIN
    SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    BEGIN TRY

        BEGIN TRANSACTION

        DECLARE @UserId uniqueidentifier
            , @Now DATETIME
            , @HistoryId bigint

        SET @Now = GETDATE()

        -- get user_id from member_id
        SELECT  @UserId = userid
        FROM    dbo.rew_members
        WHERE   memberid = @MemberId

        -- insert activity
        IF @UserId IS NOT NULL
        BEGIN
            INSERT INTO dbo.auth_user_mapping_history (
                logtime
                , user_id
                , authentication_type_id
                , ActivityId
                , rec_status
                , rec_created_when
                , rec_created_by
            ) VALUES (
                @Now
                , @UserId
                , @AuthenticationTypeId
                , @ActivityId
                , 1
                , @Now
                , @RecCreatedBy
            )
            SET @HistoryId = SCOPE_IDENTITY()
        END
        ELSE
        BEGIN
            SET @HistoryId = -1
        END

        COMMIT TRANSACTION

    END TRY
    BEGIN CATCH
        DECLARE @err_msg nvarchar(2048) = ERROR_MESSAGE()

        IF XACT_STATE() <> 0
        BEGIN
            ROLLBACK TRANSACTION
        END

        RAISERROR(@err_msg, 16, 1)
    END CATCH

    SELECT @HistoryId AS history_id
END

GO

GRANT EXECUTE ON [dbo].[api_user_mapping_history_insert] TO [customer_api_user]
GO
