----------------------------|---------------|----------------------------------------------------------
-- Author      | Date       | Comment
----------------------------|---------------|----------------------------------------------------------
-- Gyr<PERSON>i | 2016-07-28	| Inserts into auth_user_mapping
-- kankit      | 2017-03-16 | Removing Salt as it is nit used any mode, adding rec_status and is_verified
-- Andrey S.   | 2018-10-07 | v2, extend PII columns size WFAPI-2152
----------------------------|---------------|----------------------------------------------------------
-- Test : EXEC dbo.security_usermapping_insert_v2
----------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[security_usermapping_insert_v2]
	@user_id uniqueidentifier
	, @authentication_type_id tinyint
	, @uri varchar(512)
	, @username varchar(400)
	, @rec_created_by uniqueidentifier
	, @rec_status int = 1
	, @is_verified bit = 0
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	IF EXISTS
	(
		SELECT	*
		FROM	dbo.auth_user_mapping AS um
		WHERE	um.[user_id] = @user_id
		AND	um.authentication_type_id = @authentication_type_id
	)
	BEGIN
		UPDATE	dbo.auth_user_mapping
		SET	uri = @uri
			, username = @username
			, rec_status = @rec_status
			, rec_modified_by = @rec_created_by
			, rec_modified_when = GETDATE()
			, expired_since = DATEADD(day, 90, GETDATE())
			, password_last_changed = GETDATE()
		WHERE	[user_id] = @user_id
		AND	authentication_type_id = @authentication_type_id
	END
	ELSE
	BEGIN
		INSERT INTO dbo.auth_user_mapping
		(
			[user_id]
			, authentication_type_id
			, uri
			, username
			, rec_status
			, rec_created_when
			, rec_created_by
			, expired_since
			, password_last_changed
			,is_verified
		)
		VALUES
		(
			@user_id
			, @authentication_type_id
			, @uri
			, @username
			, @rec_status
			, GETDATE()
			, @rec_created_by
			, DATEADD(day, 90, GETDATE())
			, GETDATE()
			, @is_verified
		)
	END
END

GO

GRANT EXECUTE ON [dbo].[security_usermapping_insert_v2] TO [customer_api_user]
GO
