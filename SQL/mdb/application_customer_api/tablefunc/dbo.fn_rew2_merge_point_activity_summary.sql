




-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Saran B.			| 2012-08-14	|
-- Chate <PERSON>.			| 2012-10-12	| Refactoring code format. 
-----------------------------------------------------------------------------------------------------------
--select * from dbo.[fn_rew2_merge_point_activity_summary](1008862)
-----------------------------------------------------------------------------------------------------------
CREATE FUNCTION  [dbo].[fn_rew2_merge_point_activity_summary] (
	@MergeId int
)	
RETURNS TABLE
AS
	RETURN 
		(
			SELECT point_activity_id
				,MemberID
				,point_activity_subtype_id
				,storefront_id
				,points_pending
				,points_affected
				,points_available
				,affected_date
				,expiry_date
				,booking_id
				,booking_date
				,departure_date
				,booking_cancelled_date
				,booking_value
				,credit_card_value
				,Redeem_value_USD
				,Refund_value_USD
				,promotion_point_activity_id_ref
				,'' remarks
				,rec_status
				,rec_created_when
			FROM dbo.fn_rew2_merge_point_activity_summary_RELATED_BOOKING(@MergeId)

			UNION ALL

			SELECT point_activity_id
				,MemberID
				,point_activity_subtype_id
				,storefront_id
				,points_pending
				,points_affected
				,points_available
				,affected_date
				,expiry_date
				,booking_id
				,booking_date
				,departure_date
				,booking_cancelled_date
				,booking_value
				,credit_card_value
				,Redeem_value_USD
				,Refund_value_USD
				,promotion_point_activity_id_ref
				,remarks
				,rec_status
				,rec_created_when
			FROM dbo.fn_rew2_merge_point_activity_summary_NON_BOOKING(@MergeId)

		)







GO

GRANT SELECT ON [dbo].[fn_rew2_merge_point_activity_summary] TO [customer_api_user]
GO
