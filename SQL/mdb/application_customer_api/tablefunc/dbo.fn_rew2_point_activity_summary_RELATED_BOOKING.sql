
-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Saran B.			| 2012-08-14	| 
-- Chate <PERSON>.			| 2012-10-12	| Refactoring code format.
-- Thirakhom K.			| 2017-08-23	| Remove unnecessary join condition
-----------------------------------------------------------------------------------------------------------
--MONKEY_TEST select * from dbo.fn_rew2_point_activity_summary_RELATED_BOOKING(2781990)
-----------------------------------------------------------------------------------------------------------
CREATE FUNCTION  [dbo].[fn_rew2_point_activity_summary_RELATED_BOOKING] (
	@MemberID int
)	
RETURNS TABLE
AS
	RETURN 
		(	
			SELECT	MAX(B.point_activity_id)											AS point_activity_id
				,A.MemberID													AS MemberID
				, CASE  
					WHEN  A.point_activity_subtype_id = 11	THEN 100
					WHEN  A.point_activity_subtype_id = 101	THEN 10
					WHEN  A.point_activity_subtype_id = 102	THEN 12
					WHEN  A.point_activity_subtype_id = 13	THEN 103
					ELSE A.point_activity_subtype_id 
				  END														AS point_activity_subtype_id 
				,A.storefront_id												AS storefront_id
				,SUM(A.points_pending)												AS points_pending 
				,SUM(A.points_affected)												AS points_affected 
				,SUM(A.points_available)											AS points_available 
				,MAX(A.affected_date)												AS affected_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.expiry_date		ELSE NULL END)		AS expiry_date
				,A.booking_id													AS booking_id
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.booking_date		ELSE NULL END)		AS booking_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.departure_date		ELSE NULL END)		AS departure_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.booking_cancelled_date	ELSE NULL END)		AS booking_cancelled_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.booking_value		ELSE 0 END)		AS booking_value
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.credit_card_value	ELSE 0 END)		AS credit_card_value
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.Redeem_value_USD	ELSE 0 END)		AS Redeem_value_USD
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.Refund_value_USD	ELSE 0 END)		AS Refund_value_USD
				,0														AS promotion_point_activity_id_ref
				,1														AS rec_status
				,MAX(	CASE 
						WHEN A.point_activity_id >= B.point_activity_id 
							THEN	CASE 
									--WHEN A.point_activity_subtype_id = 10 THEN DATEADD(SS,3,ISNULL(A.rec_modified_when,A.rec_created_when))
									WHEN A.point_activity_subtype_id = 10 THEN DATEADD(SS,3,A.rec_created_when)
									ELSE A.rec_created_when	
								END 
					ELSE A.rec_created_when END
				)										AS rec_created_when
			FROM dbo.fn_rew2_point_activity_summary_BOOKING(@MemberID) AS B
			INNER JOIN dbo.rew2_point_activity AS A
				ON A.booking_id = B.booking_id
					--AND A.MemberID = B.MemberId --- Put the comment by Thirakhom K. 2017-08-23
			INNER JOIN dbo.rew2_point_activity_subtypes S
				ON S.point_activity_subtype_id = A.point_activity_subtype_id
			WHERE A.MemberID = @MemberID
				AND S.point_activity_type_id = 1
				AND B.row_num = 1
				AND A.rec_status = 1
				AND S.rec_status = 1
			GROUP BY A.MemberID
				, CASE 
					WHEN A.point_activity_subtype_id = 11
						THEN 100
					WHEN A.point_activity_subtype_id = 101
						THEN 10
					WHEN A.point_activity_subtype_id = 102
						THEN 12
					WHEN A.point_activity_subtype_id = 13
						THEN 103
					ELSE A.point_activity_subtype_id
					END
				, A.storefront_id
				, A.booking_id

		)
	
/*	
		(	SELECT	MIN(A.point_activity_id)		AS point_activity_id
				,A.MemberID				AS MemberID
				, CASE A.point_activity_subtype_id 
					WHEN 11		THEN 100
					WHEN 101	THEN 10
					WHEN 102	THEN 12
					ELSE A.point_activity_subtype_id 
				  END					AS point_activity_subtype_id 
				,A.storefront_id			AS storefront_id
				,SUM(A.points_pending)			AS points_pending 
				,SUM(A.points_affected)			AS points_affected 
				,SUM(A.points_available)		AS points_available 
				,MAX(B.affected_date)			AS affected_date
				,MAX(B.expiry_date)			AS expiry_date
				,A.booking_id				AS booking_id
				,MAX(B.booking_date)			AS booking_date
				,MAX(B.departure_date)			AS departure_date
				,MAX(B.booking_cancelled_date)		AS booking_cancelled_date
				,MAX(B.booking_value)			AS booking_value
				,MAX(B.credit_card_value)		AS credit_card_value
				,MAX(B.Redeem_value_USD)		AS Redeem_value_USD
				,MAX(B.Refund_value_USD)		AS Refund_value_USD
				,0					AS promotion_point_activity_id_ref				
				,1					AS rec_status
				,MAX(B.rec_created_when)		AS rec_created_when
			FROM	dbo.fn_rew2_point_activity_summary_BOOKING(@MemberID) AS B
				INNER JOIN rew2_point_activity A
					ON	A.booking_id	= B.booking_id	
					AND	A.MemberID	= B.MemberId
					AND	A.rec_status	= 1
				INNER JOIN rew2_point_activity_subtypes S
					ON	S.point_activity_subtype_id	= A.point_activity_subtype_id 
					AND	S.rec_status			= 1
			WHERE	A.MemberID			= @MemberID		
			--AND	S.point_activity_type_id	IN (1,2)	-- 1:Point For Booking , 2:Points For Review
			AND	S.point_activity_type_id	= 1	-- 1:Point For Booking 
			AND	B.row_num			= 1
			GROUP BY	A.MemberID, 
					CASE A.point_activity_subtype_id 
						WHEN 11		THEN 100
						WHEN 101	THEN 10
						WHEN 102	THEN 12
					ELSE A.point_activity_subtype_id 
					END 
					, A.storefront_id,A.booking_id
		)
*/		

GO

GRANT SELECT ON [dbo].[fn_rew2_point_activity_summary_RELATED_BOOKING] TO [customer_api_user]
GO
