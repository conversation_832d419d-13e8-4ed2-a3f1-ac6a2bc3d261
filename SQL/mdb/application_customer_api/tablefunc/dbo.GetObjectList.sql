-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE FUNCTION [dbo].[GetObjectList]
(
	@objectType char(1),
	@objectId	int
)
RETURNS 
@ObjectList TABLE 
(
	-- Add the column definitions for the TABLE variable here
	ID int, 
	name nvarchar(100)
)
AS
BEGIN
		IF @objectType = 'H'
		INSERT INTO @ObjectList (ID,name)
		SELECT hotel_id as ID, hotel_name as name 
		FROM product_hotels 
		WHERE hotel_id=@objectId 
		AND rec_status=1
	ELSE IF @objectType = 'L'
		INSERT INTO @ObjectList (ID,name)
		SELECT language_id as ID, language_name as name
		FROM afm_language 
		WHERE language_id=@objectId 
		AND rec_status=1		
	ELSE IF @objectType = 'C'
		INSERT INTO @ObjectList (ID,name)
		SELECT city_id as ID, city_name as name
		FROM geo2_city 
		WHERE city_id=@objectId 
		AND rec_status=1
	ELSE IF @objectType = '0'	
		INSERT INTO @ObjectList (ID,name)	
		SELECT @objectId as ID, '' as name
	ELSE
		INSERT INTO @ObjectList (ID,name)	
		SELECT @objectId as ID, '' as name		
	
	RETURN 
END



GO

