-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Wichai <PERSON>	| 2012-03-21	| Added the second query since "UNION ALL"
-- Chate C.			| 2012-10-12	| Refactoring code format. 
-- Thirakhom K.			| 2017-08-23	| Remove unnecessary join condition. This caused performance drop 
-----------------------------------------------------------------------------------------------------------
--MONKEY_TEST select * from dbo.fn_rew2_point_activity_summary_BOOKING(2781990)
-----------------------------------------------------------------------------------------------------------
CREATE FUNCTION  [dbo].[fn_rew2_point_activity_summary_BOOKING] (
	@MemberID int
)	
RETURNS TABLE
AS

	RETURN 
	(
		SELECT ROW_NUMBER() OVER 
			(
				PARTITION BY	A.MemberID,A.booking_id 
				ORDER BY	A.point_activity_id DESC
			) AS row_num
			,A.point_activity_id
			,A.MemberID
			,A.point_activity_subtype_id
			,A.storefront_id
			,A.points_pending
			,A.points_affected
			,A.points_available
			,A.affected_date
			,A.expiry_date
			,A.booking_id
			,A.booking_date
			,A.departure_date
			,A.booking_cancelled_date
			,A.booking_value
			,A.credit_card_value
			,A.Redeem_value_USD
			,A.Refund_value_USD
			,A.promotion_point_activity_id_ref
			,A.rec_status
			,A.rec_created_when
		FROM	dbo.rew2_point_activity AS A
		WHERE	A.point_activity_subtype_id = 10
		AND	A.MemberID = @MemberID
		AND	A.rec_status = 1
				
		
		UNION ALL
		
		SELECT ROW_NUMBER() OVER 
			(
				PARTITION BY A.MemberID	,A.booking_id 
				ORDER BY A.point_activity_id DESC
			) AS row_num
			,A.point_activity_id
			,A.MemberID
			,A.point_activity_subtype_id
			,A.storefront_id
			,A.points_pending
			,A.points_affected
			,A.points_available
			,A.affected_date
			,A.expiry_date
			,A.booking_id
			,A.booking_date
			,A.departure_date
			,A.booking_cancelled_date
			,A.booking_value
			,A.credit_card_value
			,A.Redeem_value_USD
			,A.Refund_value_USD
			,A.promotion_point_activity_id_ref
			,A.rec_status
			,A.rec_created_when
		FROM	dbo.rew2_point_activity AS A
		WHERE	A.point_activity_subtype_id = 100
			AND A.MemberID = @MemberID
			AND A.rec_status = 1
			AND NOT EXISTS 
			(
				SELECT	*
				FROM	dbo.rew2_point_activity AS B
				WHERE	B.point_activity_subtype_id = 10
				--AND	B.MemberID = A.MemberID  ---- Put the comment by Thirakhom K. 2017-08-23
				AND	B.Booking_ID = A.booking_ID
			)
	)

GO

GRANT SELECT ON [dbo].[fn_rew2_point_activity_summary_BOOKING] TO [customer_api_user]
GO
