
CREATE FUNCTION [dbo].[SPLIT_TO_INT]
(
	@str_in VARCHAR(max)
	, @separator VARCHAR(4)
)
RETURNS @strtable TABLE (val int)
AS
BEGIN
	DECLARE	@Occurrences INT
		, @Counter INT
		, @tmpStr VARCHAR(max)
		,@real_len INT  
  
	select @real_len = len(@str_in)  

	SET @Counter = 0
	IF SUBSTRING(@str_in,LEN(@str_in),1) <> @separator 
	SET @str_in = @str_in + @separator

	SET @Occurrences = (DATALENGTH(REPLACE(@str_in,@separator,@separator+'#')) - DATALENGTH(@str_in))/ DATALENGTH(@separator)
	SET @tmpStr = @str_in

	WHILE @Counter <= @Occurrences 
	BEGIN
		SET @Counter = @Counter + 1
		INSERT INTO @strtable
		SELECT CAST(SUBSTRING(@tmpStr,1,CHARINDEX(@separator,@tmpStr)-1) AS INT)

		SET @tmpStr = SUBSTRING(@tmpStr,CHARINDEX(@separator,@tmpStr)+1,@real_len)

		IF DATALENGTH(@tmpStr) = 0
		BEGIN
			BREAK
		END
	END
	RETURN
END




GO

GRANT SELECT ON [dbo].[SPLIT_TO_INT] TO [customer_api_user]
GO
