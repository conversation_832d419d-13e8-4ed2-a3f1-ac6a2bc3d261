
---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Po<PERSON><PERSON> Wiangwang	| ??/??/????	| 
-- Sirichai Ch.		| 26/11/2012	| Add option to check all status of rec_status
------------------------|---------------|----------------------------------------------------------
-- test : SELECT * FROM dbo.GetObjectList_v3( 'H', 71656, 0)
-- test : SELECT * FROM dbo.GetObjectList_v3( 'H', 71656, 1)
------------------------|---------------|----------------------------------------------------------

CREATE FUNCTION [dbo].[GetObjectList_v3]
(
	@objectType char(1),
	@objectId	int,
	@CheckAllStatus bit  =  0
)
RETURNS 
@ObjectList TABLE 
(
	-- Add the column definitions for the TABLE variable here
	ID int, 
	name nvarchar(100)
)
AS
BEGIN
	
	IF @objectType  =  'H'
	BEGIN
		INSERT INTO @ObjectList (ID,name)
		SELECT hotel_id AS ID
			, hotel_name AS name 
		FROM dbo.product_hotels (NOLOCK)
		WHERE hotel_id = @objectId 
		AND (rec_status = 1 OR @CheckAllStatus  =  1)
	END
	ELSE IF @objectType ='F'
	BEGIN
		INSERT	INTO @ObjectList(ID, name)
		SELECT	affliate_id AS ID
			, company_name AS name
		FROM	dbo.afl_affiliate
		WHERE	affliate_id = @objectId
		AND (rec_status = 1 OR @CheckAllStatus  =  1)
	END
	ELSE IF @objectType  =  'L'
	BEGIN
		INSERT INTO @ObjectList (ID,name)
		SELECT language_id AS ID, language_name AS name
		FROM dbo.afm_language (NOLOCK)
		WHERE language_id = @objectId 
		AND (rec_status = 1 OR @CheckAllStatus  =  1)		
	END
	ELSE IF @objectType  =  'C'
	BEGIN	
		INSERT INTO @ObjectList (ID,name)
		SELECT city_id AS ID, city_name AS name
		FROM dbo.geo2_city (NOLOCK)
		WHERE city_id = @objectId 
		AND (rec_status = 1 OR @CheckAllStatus  =  1)
	END
	ELSE IF @objectType  =  '0'	
	BEGIN	
		INSERT INTO @ObjectList (ID,name)	
		SELECT @objectId AS ID, '' AS name
	END
	ELSE
	BEGIN
		INSERT INTO @ObjectList (ID,name)	
		SELECT @objectId AS ID, '' AS name	
	END	
	
	RETURN 
END



GO

