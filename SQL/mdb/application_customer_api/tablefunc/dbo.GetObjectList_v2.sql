

---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- E<PERSON><PERSON>		| 2011-11-17	| Create ObjectTable from different Object Types
-- Nicolas <PERSON>	| 2011-12-29	| Refactor for coding standards
------------------------|---------------|----------------------------------------------------------
-- Test : ???
------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[GetObjectList_v2]
(
	@objectType char(1)
	, @objectId int
)
RETURNS @ObjectList TABLE
	(
		ID int
		, name nvarchar(100)
	)
AS
BEGIN
	IF @objectType ='H'
	BEGIN
		INSERT	INTO @ObjectList(ID, name)
		SELECT	hotel_id AS ID
			, hotel_name AS name
		FROM	dbo.product_hotels
		WHERE	hotel_id = @objectId
		AND	rec_status <> -1
	END
	ELSE IF @objectType ='F'
	BEGIN
		INSERT	INTO @ObjectList(ID, name)
		SELECT	affliate_id AS ID
			, company_name AS name
		FROM	dbo.afl_affiliate
		WHERE	affliate_id = @objectId
		AND	rec_status = 1
	END
	ELSE IF @objectType ='L'
	BEGIN
		INSERT	INTO @ObjectList(ID, name)
		SELECT	language_id AS ID
			, language_name AS name
		FROM	dbo.afm_language
		WHERE	language_id = @objectId
		AND	rec_status = 1
	END
	ELSE IF @objectType ='C'
	BEGIN
		INSERT	INTO @ObjectList(ID, name)
		SELECT	city_id AS ID
			, city_name AS name
		FROM	dbo.geo2_city
		WHERE	city_id = @objectId
		AND	rec_status = 1
	END
	ELSE IF @objectType ='0'
	BEGIN
		INSERT INTO @ObjectList(ID, name)
		SELECT	@objectId AS ID
			, '' AS name
	END
	ELSE
	BEGIN
		INSERT	INTO @ObjectList(ID, name)
		SELECT	@objectId AS ID
			, '' AS name
	END
	RETURN
END




GO

