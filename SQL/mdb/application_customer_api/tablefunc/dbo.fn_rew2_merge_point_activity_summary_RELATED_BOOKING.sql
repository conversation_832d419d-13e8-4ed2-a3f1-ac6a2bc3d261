



-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Saran B.			| 2012-08-14	| 
-- Chate <PERSON>.			| 2012-10-12	| Refactoring code format. 
-----------------------------------------------------------------------------------------------------------
--select * from dbo.[fn_rew2_merge_point_activity_summary_RELATED_BOOKING](1008862)
-----------------------------------------------------------------------------------------------------------
CREATE FUNCTION  [dbo].[fn_rew2_merge_point_activity_summary_RELATED_BOOKING] (
	@MergeId int
)	
RETURNS TABLE
AS
	RETURN 
		(	
			SELECT	MAX(B.point_activity_id)											AS point_activity_id
				,A.MemberID													AS MemberID
				, CASE  
					WHEN  A.point_activity_subtype_id = 11	THEN 100
					WHEN  A.point_activity_subtype_id = 101	THEN 10
					WHEN  A.point_activity_subtype_id = 102	THEN 12
					WHEN  A.point_activity_subtype_id = 13	THEN 103
					ELSE A.point_activity_subtype_id 
				  END														AS point_activity_subtype_id 
				,A.storefront_id												AS storefront_id
				,SUM(A.points_pending)												AS points_pending 
				,SUM(A.points_affected)												AS points_affected 
				,SUM(A.points_available)											AS points_available 
				,MAX(A.affected_date)												AS affected_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.expiry_date		ELSE NULL END)		AS expiry_date
				,A.booking_id													AS booking_id
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.booking_date		ELSE NULL END)		AS booking_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.departure_date		ELSE NULL END)		AS departure_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.booking_cancelled_date	ELSE NULL END)		AS booking_cancelled_date
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.booking_value		ELSE 0 END)		AS booking_value
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.credit_card_value	ELSE 0 END)		AS credit_card_value
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.Redeem_value_USD	ELSE 0 END)		AS Redeem_value_USD
				,MAX(CASE WHEN A.point_activity_id >= B.point_activity_id THEN A.Refund_value_USD	ELSE 0 END)		AS Refund_value_USD
				,0														AS promotion_point_activity_id_ref
				,1														AS rec_status
				,MAX(	CASE 
						WHEN A.point_activity_id >= B.point_activity_id 
							THEN	CASE 
									--WHEN A.point_activity_subtype_id = 10 THEN DATEADD(SS,3,ISNULL(A.rec_modified_when,A.rec_created_when))
									WHEN A.point_activity_subtype_id = 10 THEN DATEADD(SS,3,A.rec_created_when)
									ELSE A.rec_created_when	
								END 
					ELSE A.rec_created_when END
				)										AS rec_created_when
			FROM	dbo.fn_rew2_merge_point_activity_summary_BOOKING(@MergeId) AS B
				INNER JOIN rew2_merge_point_activity AS A
					ON	A.booking_id	= B.booking_id	
					AND	A.MemberID	= B.MemberId
					
				INNER JOIN dbo.rew2_point_activity_subtypes S
					ON	S.point_activity_subtype_id	= A.point_activity_subtype_id 
					
			WHERE	A.MergeId = @MergeId
			AND	S.point_activity_type_id	= 1
			AND	B.row_num			= 1
			AND	A.rec_status			= 1
			AND	S.rec_status			= 1
			
			GROUP BY	A.MemberID, 
					CASE  
						WHEN  A.point_activity_subtype_id = 11	THEN 100
						WHEN  A.point_activity_subtype_id = 101	THEN 10
						WHEN  A.point_activity_subtype_id = 102	THEN 12
						WHEN  A.point_activity_subtype_id = 13	THEN 103
						ELSE A.point_activity_subtype_id 
					END					
					, A.storefront_id,A.booking_id	
		)
	






GO

GRANT SELECT ON [dbo].[fn_rew2_merge_point_activity_summary_RELATED_BOOKING] TO [customer_api_user]
GO
