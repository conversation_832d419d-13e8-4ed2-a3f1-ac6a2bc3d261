

-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Saran B.			| 2012-08-14	| 
-- Chate <PERSON>.			| 2012-10-12	| Refactoring code format. 
-----------------------------------------------------------------------------------------------------------

/*
select * from dbo.fn_rew2_point_activity_summary_NON_BOOKING(5000016)
*/
CREATE FUNCTION  [dbo].[fn_rew2_point_activity_summary_NON_BOOKING] (
	@MemberID int
)	
RETURNS TABLE
AS
	RETURN 
	
		(
			SELECT	MAX(A.point_activity_id)								AS point_activity_id
				,A.MemberID										AS MemberID
				,A.point_activity_subtype_id								AS point_activity_subtype_id
				--, CASE  
				--	WHEN  A.point_activity_subtype_id = 502	THEN 43
				--	WHEN  A.point_activity_subtype_id = 42	THEN 43
				--	ELSE A.point_activity_subtype_id 
				--  END											AS point_activity_subtype_id 
				,MAX(A.storefront_id)									AS storefront_id
				,SUM(A.points_pending)									AS points_pending 
				,SUM(A.points_affected)									AS points_affected 
				,SUM(A.points_available)								AS points_available 
				,A.affected_date									AS affected_date
				,MAX(A.expiry_date)									AS expiry_date
				,A.booking_id										AS booking_id
				,MAX(A.booking_date)									AS booking_date
				,MAX(A.departure_date	)								AS departure_date
				,MAX(A.booking_cancelled_date	)							AS booking_cancelled_date
				,MAX(A.booking_value	)								AS booking_value
				,MAX(A.credit_card_value)								AS credit_card_value
				,MAX(A.Redeem_value_USD	)								AS Redeem_value_USD
				,MAX(A.Refund_value_USD	)								AS Refund_value_USD
				,0											AS promotion_point_activity_id_ref
				,MAX(CASE WHEN A.point_activity_subtype_id IN (42,502) THEN '' ELSE remarks END)	AS remarks	
				,1											AS rec_status
				,MAX(A.rec_created_when	)								AS rec_created_when			
			FROM	dbo.rew2_point_activity A
				INNER JOIN dbo.rew2_point_activity_subtypes ST 
					ON ST.point_activity_subtype_id = A.point_activity_subtype_id
			WHERE	MemberID			= @MemberID
			AND	ST.point_activity_type_id	> 1
			AND	ST.rec_status			= 1			
			AND	A.rec_status			= 1
			GROUP BY 
				A.MemberID, 
				A.point_activity_subtype_id,
				--CASE  
				--	WHEN  A.point_activity_subtype_id = 502	THEN 43
				--	WHEN  A.point_activity_subtype_id = 42	THEN 43
				--	ELSE A.point_activity_subtype_id 
				--END					
				A.booking_id,A.affected_date	
			
		)	
	



GO

GRANT SELECT ON [dbo].[fn_rew2_point_activity_summary_NON_BOOKING] TO [customer_api_user]
GO
