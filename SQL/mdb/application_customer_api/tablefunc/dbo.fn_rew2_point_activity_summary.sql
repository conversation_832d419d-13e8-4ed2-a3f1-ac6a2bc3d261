-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Saran B.			| 2012-08-14	| 
-- Chate C.			| 2012-10-12	| Refactoring code format. 
-- Chate C.			| 2014-02-03	| Apply new logic from FIFO
-- Thirakhom K.			| 2017-08-23	| Check EXISTS from @MemberID parameter instead
-----------------------------------------------------------------------------------------------------------
-- MONKEY_TEST SELECT * FROM dbo.fn_rew2_point_activity_summary(2781990)
-----------------------------------------------------------------------------------------------------------
CREATE FUNCTION  [dbo].[fn_rew2_point_activity_summary] (
	@MemberID int
)	
RETURNS TABLE
AS
	RETURN 
		(
			SELECT	  A.point_activity_id
				, A.MemberID
				, A.point_activity_subtype_id
				, A.storefront_id
				, A.points_pending
				, A.points_affected
				, A.points_available
				, A.affected_date
				, A.[expiry_date]
				, A.booking_id
				, A.booking_date
				, A.departure_date
				, A.booking_cancelled_date
				, A.booking_value
				, A.credit_card_value
				, A.Redeem_value_USD
				, A.Refund_value_USD
				, 0 AS promotion_point_activity_id_ref
				,CASE	WHEN S.point_activity_type_id = 1 THEN '' -- Booking
					WHEN A.point_activity_subtype_id IN (42,502) THEN '' -- Non-Booking (Includes "Else" )
					ELSE A.remarks END AS remarks
				, A.rec_status
				, A.rec_created_when
			FROM	dbo.rew2_point_activity AS A
				INNER JOIN dbo.rew2_point_activity_subtypes S 
					ON S.point_activity_subtype_id = A.point_activity_subtype_id
			WHERE	A.MemberID = @MemberID  
			AND	A.is_visible = 1
			AND	A.rec_status = 1

			UNION ALL
			
			SELECT  point_activity_id
				,MemberID
				,point_activity_subtype_id
				,storefront_id
				,points_pending
				,points_affected
				,points_available
				,affected_date
				,expiry_date
				,booking_id
				,booking_date
				,departure_date
				,booking_cancelled_date
				,booking_value
				,credit_card_value
				,Redeem_value_USD
				,Refund_value_USD
				,promotion_point_activity_id_ref
				,'' remarks
				,rec_status
				,rec_created_when
			FROM	dbo.fn_rew2_point_activity_summary_RELATED_BOOKING(@MemberID) AS A
			WHERE	EXISTS
				(
					SELECT	*
					FROM	dbo.rew2_point_activity AS B
					WHERE	B.MemberID = @MemberID
					AND	B.is_offset IS NULL
					AND	B.is_visible IS NULL
				)

			UNION ALL

			SELECT	point_activity_id
				,MemberID
				,point_activity_subtype_id
				,storefront_id
				,points_pending
				,points_affected
				,points_available
				,affected_date
				,expiry_date
				,booking_id
				,booking_date
				,departure_date
				,booking_cancelled_date
				,booking_value
				,credit_card_value
				,Redeem_value_USD
				,Refund_value_USD
				,promotion_point_activity_id_ref
				,remarks
				,rec_status
				,rec_created_when
			FROM	dbo.fn_rew2_point_activity_summary_NON_BOOKING(@MemberID) AS A
			WHERE	EXISTS
				(
					SELECT	*
					FROM	dbo.rew2_point_activity AS B
					WHERE	B.MemberID = @MemberID
					AND	B.is_offset IS NULL
					AND	B.is_visible IS NULL
				)

		)

GO

GRANT SELECT ON [dbo].[fn_rew2_point_activity_summary] TO [customer_api_user]
GO
