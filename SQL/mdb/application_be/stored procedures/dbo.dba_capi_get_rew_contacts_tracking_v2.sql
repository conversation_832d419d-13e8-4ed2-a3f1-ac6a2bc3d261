------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Jantakarn A.		| 2017-09-19	| Create SP for select gap data by using latest id
-- Pravit T.		| 2017-10-16	| GET Batch Size Value.
-- Siravitch Ch.	| 2017-12-21	| add checking datetime clause
-- Jantakarn A.		| 2018-05-18	| v2: add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
--MONKEY_TEST EXEC [dbo].[dba_capi_get_rew_contacts_tracking_v2] @max_id = -2147483648
-------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[dba_capi_get_rew_contacts_tracking_v2]
	@max_id int 

AS
BEGIN 

	SET NOCOUNT ON 
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 

	DECLARE @top_rows int 
		, @config_time int
		
	SELECT @top_rows =  top_rows
	FROM Agoda_dba.dbo.dba_dev_job_schedule_controler
	WHERE job_desc = '[DBDev] - Update_gap_data_for_migration_customer_api - Table Name'

	SELECT	@config_time = mins_config_time
	FROM	Agoda_Working.dbo.capi_data_migration_mdc_config_time
	WHERE	sp_name = 'dba_capi_get_'

	SELECT TOP (@top_rows) 	 tracking_id
			  ,action_type
			  ,action_date
			  ,contact_id
			  ,contact_method_id
			  ,MemberID
			  ,organisation_id
			  ,contact_method_value
			  ,contact_method_remark
			  ,rec_status
			  ,rec_created_by
			  ,rec_created_when
			  ,rec_modify_by
			  ,rec_modify_when
			  ,is_valid
			  ,lastupdated_when
	FROM	Agoda_logging.dbo.rew_contacts_tracking
	WHERE	tracking_id >= @max_id
	AND	action_date <= DATEADD(MINUTE,@config_time,GETDATE())
	ORDER BY tracking_id ASC


END 

GO

