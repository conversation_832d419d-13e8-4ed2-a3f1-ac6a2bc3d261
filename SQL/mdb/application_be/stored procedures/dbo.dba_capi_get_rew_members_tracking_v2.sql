------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Siravitch Ch.	| 2017-09-15	| Create SP for select gap data by using latest id
-- Pravit T.		| 2017-10-16	| GET Batch Size Value.
-- Siravitch Ch.	| 2017-11-01	| Add new userid column.
-- Siravitch Ch.	| 2017-12-21	| add checking datetime clause.
-- Sir<PERSON>tch Ch.	| 2018-01-04	| update where clause.
-- <PERSON><PERSON><PERSON>n A.		| 2018-05-18	| v2: add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
--MONKEY TEST EXEC [dbo].[dba_capi_get_rew_members_tracking_v2] @max_id = -2147483648
-------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[dba_capi_get_rew_members_tracking_v2]
	@max_id int 

AS
BEGIN 

	SET NOCOUNT ON 
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 

	DECLARE @top_rows int 
		, @config_time int
			
	SELECT @top_rows =  top_rows
	FROM Agoda_dba.dbo.dba_dev_job_schedule_controler
	WHERE job_desc = '[DBDev] - Update_gap_data_for_migration_customer_api - rew_members'

	SELECT	@config_time = mins_config_time
	FROM	Agoda_Working.dbo.capi_data_migration_mdc_config_time
	WHERE	sp_name = 'dba_capi_get_'

	SELECT TOP (@top_rows) 	tracking_id
			,action_type
			,action_date
			,MemberID
			,MemberCode
			,Password
			,member_rating
			,title
			,first_name
			,middle_name
			,last_name
			,suffix
			,birth_date
			,nationality_id
			,organisation_id
			,language_id
			,points_earned
			,points_redeemed
			,point_balance
			,points_expire_this_year
			,auto_sign_up
			,is_elite_status
			,elite_activation_date
			,is_blacklisted
			,elite_expire_date
			,deactivation_date
			,deactivation_reason
			,merged_to
			,special_remarks
			,is_newsletter
			,rec_status
			,rec_created_by
			,rec_created_when
			,rec_modify_by
			,rec_modify_when
			,signup_mail_duedate
			,is_signup_sent
			,is_upgraded
			,is_point_eligible
			,is_cc_onfile_opt_out
			,loyalty_level
			,giftcard_status
			,giftcard_level
			,is_one_click_bf_ready
			,prefer_partner_loyalty_program_id
			,UserId
			,lastupdated_when
	FROM	Agoda_Logging.dbo.rew_members_tracking
	WHERE	tracking_id >= @max_id
	AND	action_date <= DATEADD(MINUTE,@config_time,GETDATE())
	ORDER BY tracking_id ASC



END 


GO

