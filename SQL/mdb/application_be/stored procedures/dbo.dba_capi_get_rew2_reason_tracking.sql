
------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-04-03	| Create SP for select gap data by using latest id
------------------------|---------------|------------------------------------------------------------------------
--EXEC [dbo].[dba_capi_get_rew2_reason_tracking] @max_id = -2147483648
-------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[dba_capi_get_rew2_reason_tracking]
	@max_id INT 
AS
BEGIN 

	SET NOCOUNT ON 
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 
	
	DECLARE @top_rows int
		, @config_time int
	
	SELECT	@top_rows =  top_rows
	FROM	Agoda_dba.dbo.dba_dev_job_schedule_controler
	WHERE	job_desc = '[DBDev] - Update_gap_data_for_migration_customer_api - Table Name'

	SELECT	@config_time = mins_config_time
	FROM	Agoda_Working.dbo.capi_data_migration_mdc_config_time
	WHERE	sp_name = 'dba_capi_get_'

	SELECT TOP (@top_rows) tracking_id
			,action_type
			,action_date
			,[reason_id]
			,[reason_type_id]
			,[name]
			,[rec_status]
			,[rec_created_when]
			,[rec_created_by]
			,[rec_modified_when]
			,[rec_modified_by]
	FROM	Agoda_logging.dbo.rew2_reason_tracking
	WHERE	tracking_id >= @max_id
	AND	action_date <= DATEADD(MINUTE,@config_time,GETDATE())
	ORDER By tracking_id ASC



END 
GO

