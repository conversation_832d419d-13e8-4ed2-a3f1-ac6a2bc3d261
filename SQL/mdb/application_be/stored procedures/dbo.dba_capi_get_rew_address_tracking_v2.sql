------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Sumeth W.		| 2018-01-10	| Create SP for select gap data by using latest id
-- Sumeth W.		| 2018-01-11	| add config time
-- Jantakarn A.		| 2018-05-18	| v2: add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
-- MONKEY_TEST : EXEC [dbo].[dba_capi_get_rew_address_tracking_v2] @max_id = -2147483648
-------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[dba_capi_get_rew_address_tracking_v2]
	@max_id int 

AS
BEGIN 

	SET NOCOUNT ON 
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 

	DECLARE @top_rows int
		, @config_time int
		
	SELECT @top_rows =  top_rows
	FROM Agoda_dba.dbo.dba_dev_job_schedule_controler
	WHERE job_desc = '[DBDev] - Update_gap_data_for_migration_customer_api - Table Name'

	SELECT	@config_time = mins_config_time
	FROM	Agoda_Working.dbo.capi_data_migration_mdc_config_time
	WHERE	sp_name = 'dba_capi_get_'

	SELECT TOP (@top_rows) 	 tracking_id
			, action_type
			, action_date
			, address_id
			, address_type_id
			, MemberID
			, organisation_id
			, address_1
			, address_2
			, postal_code
			, region
			, country
			, state
			, city
			, area
			, rec_status
			, rec_created_by
			, rec_created_when
			, rec_modify_by
			, rec_modify_when
			, country_id
			, lastupdated_when
	FROM	Agoda_logging.dbo.rew_address_tracking
	WHERE	tracking_id >= @max_id
	AND	action_date <= DATEADD(MINUTE,@config_time,GETDATE())
	ORDER BY tracking_id ASC


END 

GO

