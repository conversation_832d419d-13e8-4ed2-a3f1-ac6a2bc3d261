---------------------------------------------------------------------------------------------------
-- Author	| Date		| Comment
----------------|---------------|------------------------------------------------------------------
-- Jantakarn A.	| 2018-03-19	| To populate data to member_id column
----------------|---------------|------------------------------------------------------------------
--  EXEC [dbo].[hotel_review_snippets_cleanup_V1] @batch_size = 5000
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[hotel_review_snippets_cleanup_V1]
		 @batch_size int = 500
		,@delay_time varchar(15) = '00:00:03'


AS
BEGIN
		SET XACT_ABORT ON
		SET NOCOUNT ON 
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 

		
		SELECT  hrs.review_snippet_id
			,rh.member_id
		INTO	#Temp_hotel_review_snippets
		FROM	[dbo].[hotel_review_snippets] hrs
		INNER JOIN	[dbo].rev_hotels_v3 rh
			ON hrs.hotel_review_id = rh.hotel_review_id
		WHERE	hrs.provider_id  = 332


		BEGIN TRY
		WHILE EXISTS (SELECT * FROM #Temp_hotel_review_snippets)
		BEGIN
			BEGIN TRANSACTION

			;WITH CTE_UPDATE
				AS (
					SELECT TOP (@batch_size) review_snippet_id
						,member_id
					FROM		#Temp_hotel_review_snippets
					ORDER BY	review_snippet_id ASC

				)

			UPDATE A
			SET	A.member_id = B.member_id
				,A.[lastupdated_when] = GETDATE() --update_by?
			FROM dbo.hotel_review_snippets AS A
			INNER JOIN CTE_UPDATE AS B
				ON A.review_snippet_id = B.review_snippet_id



			;WITH CTE_UPDATE AS
			(
				SELECT TOP (@batch_size) review_snippet_id
						,member_id
					FROM		#Temp_hotel_review_snippets
					ORDER BY	review_snippet_id ASC
			)
			DELETE FROM CTE_UPDATE


			COMMIT TRANSACTION

					WAITFOR DELAY @delay_time
		END

		END TRY

		BEGIN CATCH
		DECLARE @err_msg_u NVARCHAR(2048) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRANSACTION
		END

		RAISERROR (
			@err_msg_u
			, 16
			, 1
		)
		END CATCH

		DROP TABLE #Temp_hotel_review_snippets
END 
