---------------------------------------------------------------------------------------------------
-- Author	| Date		| Comment
----------------|---------------|------------------------------------------------------------------
-- RATI P.	| 2017-04-19	| Created
-- Jantakarn A.	| 2018-03-19	| V2: Add new col (member_id)
----------------|---------------|------------------------------------------------------------------
--  EXEC [dbo].[hotel_review_snippets_create_working_tables_V2]
---------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[hotel_review_snippets_create_working_tables_V2]
AS
BEGIN

		SET NOCOUNT ON
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED


		IF EXISTS (SELECT 1 FROM Agoda_dataupload.dbo.sysobjects WHERE name='RPL_hotel_review_snippets_generate' AND type='U')
		BEGIN
			DROP TABLE Agoda_dataupload.dbo.RPL_hotel_review_snippets_generate
		END

		CREATE TABLE Agoda_dataupload.dbo.RPL_hotel_review_snippets_generate
		(
			hotel_review_id bigint NOT NULL 
			,hotel_id int NOT NULL
			,language_id int NOT NULL 
			,review_date datetime NOT NULL 
			,review_snippet nvarchar(150) NOT NULL
			,member_id int NOT NULL
			,member_name nvarchar(200)
			,nationality_id INT NOT NULL
			,provider_id INT NOT NULL
			,data_date datetime NOT NULL
			,action_type tinyint NULL

		)

		CREATE NONCLUSTERED INDEX [IX01_RPL_hotel_review_snippets_generate]
		ON Agoda_dataupload.dbo.RPL_hotel_review_snippets_generate (hotel_review_id,action_type)

		IF EXISTS (SELECT 1 FROM Agoda_dataupload.dbo.sysobjects WHERE name='RPL_hotel_review_snippets_delete' AND type='U')
		BEGIN
			DROP TABLE Agoda_dataupload.dbo.RPL_hotel_review_snippets_delete
		END

		CREATE TABLE Agoda_dataupload.dbo.RPL_hotel_review_snippets_delete
		(
			hotel_review_id bigint NOT NULL 

		)

		CREATE NONCLUSTERED INDEX [IX01_RPL_hotel_review_snippets_delete]
		ON Agoda_dataupload.dbo.RPL_hotel_review_snippets_delete (hotel_review_id)


END 
