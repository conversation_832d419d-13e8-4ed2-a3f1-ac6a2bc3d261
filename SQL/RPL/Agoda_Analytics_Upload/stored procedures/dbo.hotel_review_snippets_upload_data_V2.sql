---------------------------------------------------------------------------------------------------
-- Author	| Date		| Comment
----------------|---------------|------------------------------------------------------------------
-- RATI P.	| 2017-04-19	| Created
-- Jantakarn A.	| 2018-03-19	| V2: Add new col (member_id)
----------------|---------------|------------------------------------------------------------------
-- EXEC [dbo].[hotel_review_snippets_upload_data_V2] 
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[hotel_review_snippets_upload_data_V2]
		 @batch_size int = 500
		,@delay_time varchar(15) = '00:00:03'
AS
BEGIN

	SET NOCOUNT ON 
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 


	WHILE EXISTS (SELECT * FROM  Agoda_dataupload.dbo.RPL_hotel_review_snippets_generate)
	BEGIN

			BEGIN TRY
			BEGIN TRANSACTION

				;WITH CTE_INSERT 
				AS (
					SELECT TOP (@batch_size)
							hotel_review_id 
							,hotel_id 
							,language_id 
							,review_date 
							,review_snippet
							,member_id 
							,member_name 
							,nationality_id 
							,provider_id 
							,data_date 
							,action_type 
					FROM	Agoda_dataupload.dbo.RPL_hotel_review_snippets_generate
					ORDER BY hotel_id,hotel_review_id

				)

					INSERT INTO dbo.hotel_review_snippets
					   (
							hotel_id
						   ,language_id
						   ,member_id
						   ,member_name
						   ,nationality_id
						   ,review_snippet
						   ,review_date
						   ,provider_id
						   ,hotel_review_id
						   ,lastupdated_by
						   ,lastupdated_when
					   )

					SELECT hotel_id
						   ,language_id
						   ,member_id
						   ,member_name
						   ,nationality_id
						   ,review_snippet
						   ,review_date
						   ,provider_id
						   ,hotel_review_id
						   ,'14820F31-5F5D-483F-9296-BA80839B3F29' AS lastupdated_by
						   ,data_date AS lastupdated_when
					FROM	CTE_INSERT

					;WITH CTE_INSERT 
				AS (
					SELECT TOP (@batch_size)
							hotel_review_id 
							,hotel_id 
					FROM	Agoda_dataupload.dbo.RPL_hotel_review_snippets_generate
					ORDER BY hotel_id,hotel_review_id

				)
				DELETE FROM CTE_INSERT


			COMMIT TRANSACTION
			WAITFOR DELAY	@delay_time
			END TRY

			BEGIN CATCH
			DECLARE @err_msg_3 NVARCHAR(2048) = ERROR_MESSAGE()

			IF XACT_STATE() <> 0 
			BEGIN
				ROLLBACK TRANSACTION
			END

			RAISERROR(
					@err_msg_3
					,16
					,1
				)
			END CATCH
	END 
END
