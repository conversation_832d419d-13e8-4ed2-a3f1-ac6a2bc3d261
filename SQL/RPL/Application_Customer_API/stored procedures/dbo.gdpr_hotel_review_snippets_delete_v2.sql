-------------------------------------------------------------------------------------------------------------------------------------
-- Name			| Date		| Desc
-------------------------------------------------------------------------------------------------------------------------------------
--J Vache<PERSON><PERSON>| 2018-03-27| RPL server delete hotel_review_snippets
-------------------------------------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
exec dbo.gdpr_hotel_review_snippets_delete_v2 34195601
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[gdpr_hotel_review_snippets_delete_v2]
(
	@MemberID int
)
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DELETE dbo.hotel_review_snippets
	WHERE member_id = @MemberID

	DELETE dbo.content_hotel_review_snippets_from_nlp
	WHERE member_id = @MemberID

END


GO

GRANT EXECUTE ON [dbo].[gdpr_hotel_review_snippets_delete_v2] TO [customer_api_user]
GO
