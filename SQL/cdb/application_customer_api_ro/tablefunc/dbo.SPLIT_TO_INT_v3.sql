---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Arik F.		| 2017-02-15	| New version of split_to_int
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST SELECT * FROM dbo.[SPLIT_TO_INT_v3]('1,2,3', ',')
------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[SPLIT_TO_INT_v3]
		(
			@str_in		VARCHAR(8000) 
			, @separator	VARCHAR(4) -- actually supports only char(1) delimiter (as current function does)
		)
		RETURNS TABLE WITH SCHEMABINDING AS
		RETURN
			--===== "Inline" CTE Driven "Tally Table" produces values from 0 up to 10,000...
			WITH E1(N) AS 
			(
				SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL 
				SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL 
				SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1
				--10E+1 or 10 rows
			)  
			, E2(N) AS 
			(	
				SELECT	1
				FROM	E1 AS a
					, E1 AS b
				--10E+2 or 100 rows
			)
			, E4(N) AS 
			(
				SELECT	1
				FROM	E2 AS a
					, E2 AS b
				--10E+4 or 10,000 rows max
			) 
			, E16(N) AS 
			(
				SELECT	1 
				FROM	E4 AS a
					, E4 AS b
				--10E+16 or 100,000,000 rows max
			)
			, cteTally(N) AS 
			(
				--==== This provides the "base" CTE and limits the number of rows right up front
				-- for both a performance gain and prevention of accidental "overruns"
				SELECT TOP (ISNULL(DATALENGTH(@str_in),0)) ROW_NUMBER() 
				OVER	(ORDER BY (SELECT NULL)) 
				FROM	E16
			)
			, cteStart(N1) AS 
			(
				--==== This returns N+1 (starting position of each "element" just once for each delimiter)
				SELECT	1 

				UNION	ALL

				SELECT	t.N+1 
				FROM	cteTally AS t 
				WHERE	SUBSTRING(@str_in,t.N,1) = @separator
			)
			, cteLen(N1,L1) AS
			(
				--==== Return start and length (for use in substring)
				SELECT	s.N1,
					ISNULL(NULLIF(CHARINDEX(@separator,@str_in,s.N1),0)-s.N1,8000)
				FROM	cteStart AS s
			)

			--===== Do the actual split. The ISNULL/NULLIF combo handles the length for the final element when no delimiter is found.
			SELECT	VAL  = CONVERT(INT,SUBSTRING(@str_in, l.N1, l.L1))
			FROM	cteLen AS l



GO

GRANT SELECT ON [dbo].[SPLIT_TO_INT_v3] TO [customer_api_user]
GO
