
--------------------------|-------------|----------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------
-- <PERSON>        | 2021-10-01  | -
--------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
  DECLARE @userId uniqueidentifier = '45bf6875-2703-414e-b17c-0db5bdf7132f'
  DECLARE @objectType char = 'H';

  DECLARE @objectIds dbo.int_id_list;
  INSERT INTO @objectIds (id) values (6597191), (7090935), (6718717), (0);

  EXEC dbo.assigned_roles_select_by_objecttype_and_objectids_v4 @userId, @objectType, @objectIds
REVERT
END_EXEC_TEST
*/

CREATE PROCEDURE [dbo].[assigned_roles_select_by_objecttype_and_objectids_v4]
	@userId uniqueidentifier,
    @objectType char,
    @objectIds dbo.int_id_list READONLY,
    @recStatus int = 1
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT	id as objectId
    INTO	#Input_objectIds
    FROM	@objectIds

    SELECT DISTINCT AssignedRoles.AssignedRoleId, AuthRoles.RoleName, UserInRole.ObjectType, ObjectType.ObjectName, UserInRole.ObjectId
    FROM dbo.auth_role_assigned_roles AssignedRoles
         INNER JOIN dbo.auth_roles AuthRoles
            ON AssignedRoles.AssignedRoleId = AuthRoles.roleId AND AuthRoles.objecttype IS NOT NULL
         INNER JOIN dbo.auth_user_in_role UserInRole
            ON (
                AssignedRoles.roleId = UserInRole.roleid
                AND UserInRole.objectId IN (SELECT * FROM @objectIds)
                AND UserInRole.rec_status = @recStatus
                AND (@objectType IS NULL OR (UserInRole.objectType = @objectType))
                AND UserInRole.userId = @userId
            )
         INNER JOIN dbo.auth_object_type ObjectType
            ON UserInRole.objectType = ObjectType.objectType
END

GO

GRANT EXECUTE ON [dbo].[assigned_roles_select_by_objecttype_and_objectids_v4] TO [customer_api_user]
GO
