/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-08-20    | Returns a list of roles for the given user.
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-09-15    | Reduce tempdb contention by avoiding table variables.
----------------------------|---------------|--------------------------------------------------------------------------
-- Weerapong Mua            | 2020-06-01    | Add condition to read roles
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_customer_roles_v3] '3BDD0247-7381-405D-8263-0342796257A9', 20000;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_customer_roles_v3]
    @user_id uniqueidentifier,
    @limit_no_of_roles INT = -1
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @no_of_roles INT;

    SELECT  @no_of_roles = COUNT(1)
    FROM     dbo.auth_user_in_role r1
    WHERE    UserId = @user_id
    AND      rec_status = 1
    AND      ObjectType <> 'R'
    OPTION (OPTIMIZE FOR (@user_id UNKNOWN));

    IF (@limit_no_of_roles = -1) OR (@no_of_roles < @limit_no_of_roles)
    BEGIN
        -- TODO: Use STRING_AGG when it's available: https://docs.microsoft.com/en-us/sql/t-sql/functions/string-agg-transact-sql?view=sql-server-2017
        SELECT   RoleId AS role_id
                 , ObjectType AS ref_type
                 , STUFF((SELECT CONCAT(', ', r2.ObjectID)
                          FROM dbo.auth_user_in_role r2
                          WHERE r2.UserId = @user_id AND r2.RoleId = r1.RoleId AND r2.ObjectType = r1.ObjectType FOR XML PATH ('')), 1, 2, '') AS [references]
        FROM     dbo.auth_user_in_role r1
        WHERE    UserId = @user_id
        AND      rec_status = 1
        AND      ObjectType <> 'R'
        GROUP BY RoleId, ObjectType
        OPTION (OPTIMIZE FOR (@user_id UNKNOWN));
    END
END
GO

GRANT EXECUTE ON [dbo].[read_customer_roles_v3] TO [customer_api_user]
GO
