---------------------------------------------------------------------------------------------------
-- Author		            | Date		      | Comment
----------------------------|-----------------|----------------------------------------------------------
-- Si<PERSON> 			    | 2020-09-01	  |  Fetch city name from city_id and language_id
----------------------------|-----------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @city_ids AS [id_table_type];
INSERT INTO @city_ids (id) VALUES (9395)
EXEC [dbo].[get_city_name_v1] @city_ids, 22
REVERT;
END_EXEC_TEST
*/
----------------------------|-----------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[get_city_name_v1]
  @city_ids id_table_type READONLY,
  @language_id int
AS
BEGIN

    SET NOCOUNT ON 
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
 
    SELECT
        c.id as city_id,
        ISNULL(gcl.city_name, gc.city_name) AS city_name
    FROM   @city_ids as c
        INNER JOIN dbo.geo2_city as gc ON c.id = gc.city_id
        LEFT JOIN dbo.geo2_city_language AS gcl ON gc.city_id = gcl.city_id AND gcl.language_id = @language_id
END
GO

GRANT EXECUTE ON [dbo].[get_city_name_v1] TO [customer_api_user]
GO
