---------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------------
-- <PERSON><PERSON>.              | 2018-05-07  | Add onlyActive option
-- <PERSON><PERSON>.              | 2018-05-03  | Get users by Hotel
-- Kittis<PERSON> Wongkraphan   | 2018-11-19  | Return Manager and is_single_task_consumption in the result
---------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
  DECLARE @objectIds AS [id_table_type];
  INSERT INTO @objectIds (id) (SELECT DISTINCT TOP 10 [ObjectID] FROM dbo.[auth_user_in_role] where ObjectType = 'H' and ObjectID > 1)
  EXEC dbo.security_get_users_by_objects_v3 @objectIds, 'H'
REVERT
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_get_users_by_objects_v3]
  @objectIds  [dbo].[ID_TABLE_TYPE] READONLY,
  @objectType CHAR(1) = 'H',
  @roleId     UNIQUEIDENTIFIER = NULL,
  @onlyActive BIT = 1
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
      u.UserId,
      u.EmailAddress,
      u.DisplayName,
      u.Manager,
      u.is_single_task_consumption,
      u.rec_status,
      u.rec_created_when,
      u.rec_created_by,
      u.rec_modified_when,
      u.rec_modified_by,
      au.ObjectID as ObjectId,
      au.ObjectType
    FROM dbo.auth_user_in_role AS au
      INNER JOIN dbo.auth_users AS u ON u.UserId = au.UserId AND (@onlyActive = 0 OR u.rec_status = 1)
    WHERE au.ObjectID IN (SELECT id FROM @objectIds)
      AND (@roleId IS NULL OR au.roleid = @roleId)
      AND au.objecttype = @objectType
      AND (@onlyActive = 0 OR au.rec_status = 1)
  END
GO

GRANT EXECUTE ON [dbo].[security_get_users_by_objects_v3] TO [customer_api_user]
GO
