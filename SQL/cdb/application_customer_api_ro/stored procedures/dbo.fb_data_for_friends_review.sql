---------------------------------------------------------------------------------------------------
-- Author		| Date			| Comment
------------------------|-----------------------|----------------------------------------------------------
-- Andrew <PERSON> 	| 2017-11-22		|  First version.
-- Rati P.		| 2018-01-26		|  Return existing hotel in product_hotels
------------------------|-----------------------|----------------------------------------------------------
-- MONKEY_TEST EXEC dbo.fb_data_for_friends_review '13702250,5440176'
------------------------|-----------------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[fb_data_for_friends_review]
  @memberIds varchar(max)
AS
BEGIN
	SET NOCOUNT ON 
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 

	DECLARE  @temp_member TABLE
	(
		memberid int NOT NULL
	)
	INSERT INTO @temp_member(memberid)
	SELECT VAL FROM dbo.[SPLIT_TO_INT_v3](@memberIds ,',')

	 SELECT		rev.hotel_review_id
				 ,h.hotel_id
				 ,rev.member_id as memberid 
				 ,city.city_id 
				 ,rev.rating_value_for_money
				 ,rev.rating_location
				 ,rev.rating_staff_performance
				 ,rev.rating_hotel_conditions_cleanliness
				 ,rev.rating_room_comfort
				 ,rev.rating_food_dining
				 ,rev.rating_overall
				 ,rev.rating_average
				 ,rev.rating_facility
				 ,rev.review_comments
				 ,rev.checkin_date
				 ,rev.checkout_date
	 FROM		dbo.rev_hotels_v3 rev
	 INNER JOIN	@temp_member mem
					ON rev.member_id = mem.memberid
	 INNER JOIN	dbo.product_hotels h
					ON rev.hotel_id = h.hotel_id
	 INNER JOIN	dbo.geo2_city city
					ON h.city_id = city.city_id
	WHERE		rev.provider_id =332
	AND			rev.rec_status = 1

END




GO

GRANT EXECUTE ON [dbo].[fb_data_for_friends_review] TO [customer_api_user]
GO
