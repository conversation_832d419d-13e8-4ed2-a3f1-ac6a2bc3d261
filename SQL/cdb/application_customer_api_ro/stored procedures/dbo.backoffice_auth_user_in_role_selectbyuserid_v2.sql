---------------------------------------------------------------------------------------------------
-- Author		            | Date		    | Comment
--------------------------------|------------------------------------------------------------------
-- Pong<PERSON> Wiangwang		| 2011-23-02	| Get selected Authentication user in Role by userid
-- Mrun S               | 2019-27-02  | Rename to backoffice_auth_user_in_role_selectbyuserid_v2
-------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_user_in_role_selectbyuserid_v2] '56003888-4FF1-4F78-93DA-ED7E1742B1EC'
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_auth_user_in_role_selectbyuserid_v2]
	@UserId uniqueidentifier
AS

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED  
	SELECT auir.[UserId]
		  ,aum.UserName
		  ,au.EmailAddress
		  ,auir.[RoleId]
		  ,ar.RoleName
		  ,auir.[ObjectType]
		  ,aot.ObjectName
		  ,[ObjectID]
		  ,auir.[rec_status]
		  ,auir.[rec_created_when]
		  ,auir.[rec_created_by]
		  ,auir.[rec_modified_when]
		  ,auir.[rec_modified_by]
		  ,ISNULL((SELECT emailAddress from dbo.auth_users  where UserId = auir.rec_created_by),'') AS 'EmailCreatedBy'
		  ,ISNULL((SELECT emailAddress from dbo.auth_users  where UserId = auir.rec_modified_by),'') AS 'EmailModifiedBy'
	  FROM dbo.auth_user_in_role  auir
	  INNER JOIN dbo.auth_roles  ar  ON auir.RoleId = ar.RoleId AND ar.rec_status > 0
	  INNER JOIN dbo.auth_user_mapping aum ON aum.User_id = auir.UserId AND aum.rec_status > 0
	  INNER JOIN dbo.auth_object_type  aot ON auir.ObjectType = aot.ObjectType  AND aot.rec_status > 0
	  INNER JOIN dbo.auth_users  au ON au.userid = auir.userid-- AND au.rec_status > 0
	  WHERE auir.UserId = @UserId AND auir.rec_status > 0
	  ORDER BY aum.UserName,ar.RoleName,auir.[ObjectType],[ObjectID]
	 
GO

GRANT EXECUTE ON [dbo].[backoffice_auth_user_in_role_selectbyuserid_v2] TO [customer_api_user]
GO
