/*
----------------------------------------------------------------------------------------------
-- Author                 | Date       | Comment
--------------------------|------------|------------------------------------------------------
-- Patipat Duangchalomnin	| 2018-02-01 | HOSTW-597 : Init Sp
-- <PERSON>orn Weecharungsan 	| 2018-02-14 | IM-2713 : Add more fields to response
-- Pakorn Weecharungsan 	| 2018-02-15 | IM-2713 : Add role name to response
-- <PERSON>orn Weecharungsan 	| 2018-02-15 | IM-2713 : Remove join auth_users
-- <PERSON><PERSON>      | 2018-04-02 | WFAPI-1673 : Add ignore rec_status option
-- <PERSON><PERSON><PERSON>            | 2019-02-15 | FRAUD-2896 : return is_fraudulent flag info
----------------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN ='customer_api_user'
EXEC [dbo].[api_get_authorized_user_v4] 2846025, 'H', NULL, 0
REVERT;
END_EXEC_TEST
----------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_get_authorized_user_v4]
    @objectId       INT,
    @objectType     CHAR = 'H',
    @roleId         UNIQUEIDENTIFIER = NULL,
    @allResStatuses BIT = 0
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
      au.UserId,
      au.RoleId,
      au.ObjectType,
      au_role.RoleName,
      au.rec_created_when,
      au.rec_created_by,
      au.rec_modified_when,
      au.rec_modified_by,
      au.rec_status,
      coalesce(au.is_fraudulent, 0) as is_fraudulent
    FROM dbo.auth_user_in_role AS au
      INNER JOIN dbo.auth_roles AS au_role
        ON au_role.RoleId = au.RoleId
           AND (@allResStatuses = 1 OR au_role.rec_status = 1)
    WHERE au.ObjectID = @objectId
          AND (@roleId IS NULL OR au.roleid = @roleId)
          AND (@allResStatuses = 1 OR au.rec_status = 1)
          AND au.objecttype = @objectType
  END
GO

GRANT EXECUTE ON [dbo].[api_get_authorized_user_v4] TO [customer_api_user]
GO
