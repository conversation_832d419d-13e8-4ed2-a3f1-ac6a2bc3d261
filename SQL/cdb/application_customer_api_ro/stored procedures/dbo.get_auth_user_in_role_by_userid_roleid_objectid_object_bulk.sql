---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
---------------------------------------------------------------------------------------------------
-- <PERSON><PERSON><PERSON>      | 2019-12-20  | To fetch all users with rec_status 0 with is_fradulent = 1
---------------------------------------------------------------------------------------------------
/*
EXEC_TEST
	EXECUTE AS LOGIN = 'customer_api_user'
	  DECLARE @user_role_table auth_user_in_role_table_type
	  INSERT INTO @user_role_table (UserId, RoleId, ObjectType, ObjectId)
	  VALUES ('2bb76b22-3c29-4451-b3d2-24ad0d0888aa', 'a7047b2c-58d7-4053-a056-8fcb948b436a', 0, 0), ('af7a0145-6d3f-410f-ae8b-b97b97b011d2', 'a7047b2c-58d7-4053-a056-8fcb948b436a', 0, 0)
	  
	  EXEC dbo.get_auth_user_in_role_by_userid_roleid_objectid_object_bulk @user_role_table
	
	REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[get_auth_user_in_role_by_userid_roleid_objectid_object_bulk]
	@user_role_table [auth_user_in_role_table_type] READONLY
AS
BEGIN
  SET NOCOUNT ON;
  SET XACT_ABORT ON;
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

  SELECT	au.ObjectID AS ObjectId,
      		au.ObjectType,
		au.UserId AS UserID,
		au.RoleId,
		au.rec_created_when,
		au.rec_created_by,
		au.rec_modified_when,
		au.rec_modified_by,
		au.rec_status,
		COALESCE(au.is_fraudulent, 0) AS is_fraudulent
  FROM    	@user_role_table AS u
  INNER JOIN  	dbo.auth_user_in_role AS au
        ON    	au.UserId = u.UserId
        AND   	au.RoleId = u.RoleId
        AND   	au.ObjectType = u.ObjectType
        AND   	au.ObjectID = u.ObjectId
  WHERE   	au.ObjectType = 'H'
  AND     	au.is_fraudulent = 1
  AND     	au.rec_status = 0

END
GO

GRANT EXECUTE ON [dbo].[get_auth_user_in_role_by_userid_roleid_objectid_object_bulk] TO [customer_api_user]
GO
