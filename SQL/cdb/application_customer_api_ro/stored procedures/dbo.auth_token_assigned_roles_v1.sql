---------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|-------------------------------------------------------------------
-- Andrey S.      | 2020-04-02 | migrate from existing raw query
---------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[auth_token_assigned_roles_v1]
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_token_assigned_roles_v1]
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT ar.RoleId, ar.AssignedRoleId
	FROM dbo.auth_role_assigned_roles ar
END
GO

GRANT EXECUTE ON [dbo].[auth_token_assigned_roles_v1] TO [customer_api_user]
GO
