------------------------------------------------------------------------------------------------
-- Author	            | Date       | Comment
------------------------|------------|----------------------------------------------------------
-- <PERSON><PERSON><PERSON><PERSON> 	| 2018-05-30 | Create stored procedure
-- <PERSON><PERSON>.            | 2018-10-07 | v1, extend PII columns size WFAPI-2152
-- Mrun S.              | 2018-15-11 | add encrypted query params
-- Ahmad <PERSON>.             | 2019-04-09 | add whitelabel id
-- Vatulin Y.           | 2019-09-02 | optimize by limiting a size of result set
------------------------|------------|----------------------------------------------------------
/*
EXEC_TEST

EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.capi_get_user_contacts_by_contact_value_v3 1, '<EMAIL>','?8VAxpu8ZdTV7nia5LA9RjA==', 1
REVERT;

END_EXEC_TEST
*/
------------------------|------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[capi_get_user_contacts_by_contact_value_v3]
(
    @contact_method_id int = null
    , @contact_value nvarchar(400) = null
    , @encrypted_contact_value nvarchar(400) = null
    , @whitelabel_id int
) AS

BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT	TOP(1) contact_id
            , contact_method_id
            , MemberID as 'member_id'
            , contact_method_value
            , contact_method_remark
            , rec_status
            , ISNULL(rec_modify_when, rec_created_when) as 'last_update_when'
            , is_valid
            , ISNULL(whitelabel_id, 1)
    FROM	dbo.rew_contacts
    WHERE	(contact_method_value = @contact_value OR contact_method_value = @encrypted_contact_value)
    AND		contact_method_id = @contact_method_id
    AND		rec_status = 1
    AND     ISNULL(whitelabel_id, 1) = @whitelabel_id
    AND     MemberId = 0
    UNION ALL
    SELECT	TOP(1) contact_id
            , contact_method_id
            , MemberID as 'member_id'
            , contact_method_value
            , contact_method_remark
            , rec_status
            , ISNULL(rec_modify_when, rec_created_when) as 'last_update_when'
            , is_valid
            , ISNULL(whitelabel_id, 1)
    FROM	dbo.rew_contacts
    WHERE	(contact_method_value = @contact_value OR contact_method_value = @encrypted_contact_value)
    AND		contact_method_id = @contact_method_id
    AND		rec_status = 1
    AND     ISNULL(whitelabel_id, 1) = @whitelabel_id
    AND     MemberId <> 0;
END
GO

GRANT EXECUTE ON [dbo].[capi_get_user_contacts_by_contact_value_v3] TO customer_api_user
GO
