-----------------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
-----------------------------------------------------------------------------------------------------------
-- <PERSON> 	| 15/05/2018	| Get reviews for NHA hosts hotels
-- Thanavard <PERSON>rdlob 	| 17/12/2019    | Return avg rating_staff_performance - NHAWEB-1328 Host side panel - average host scores
-----------------------------------------------------------------------------------------------------------
/* 
EXEC_TEST
	EXECUTE AS LOGIN = 'customer_api_user'
	DECLARE @hotelids AS [id_table_type];
	INSERT INTO @hotelids (id) (SELECT DISTINCT TOP 100 hotel_id FROM dbo.product_hotels)
	EXEC [dbo].[host_reviews_v2] @hotelids
	REVERT;
END_EXEC_TEST
*/
-----------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[host_reviews_v2]
	@hotelIds [id_table_type] READONLY
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    	SELECT 		hotel_id AS h
			, COUNT(hotel_id) AS c
			, AVG(rating_average) AS a
			, AVG(rating_staff_performance) * 2.0 AS avg_staff_score
    	FROM 		dbo.rev_hotels_v3 
	INNER JOIN 	@hotelids hotelIds 
		ON 	hotelIds.id = rev_hotels_v3.hotel_id
    	WHERE 		rec_status = 1 
	GROUP BY 	hotel_id
END
GO

GRANT EXECUTE ON [dbo].[host_reviews_v2] TO [customer_api_user]
GO
