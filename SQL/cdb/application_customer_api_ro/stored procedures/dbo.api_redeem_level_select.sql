
/*
---------------------------------------------------------------------------------------------------
-- Author		| Date		    | Comment
--------------- |---------------|----------------------------------------------------------
-- Saran B.		| 2012-08-08	| select the redeem level.
----------------|---------------|----------------------------------------------------------
-- MONKEY_TEST: Exec [api_redeem_level_select] 0
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_redeem_level_select] 
	@is_elite BIT
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	IF (@is_elite = 1)
		SELECT redeem_level_id
			,redeemed_points
			,award_value
			,is_elite_level
			,is_premier_level
			,rec_status
			,rec_created_by
			,rec_created_when
			,rec_modify_by AS rec_modified_by
			,rec_modify_when AS rec_modified_when
		FROM dbo.rew_redeem_levels
		WHERE is_elite_level = 1 
		AND rec_status = 1
		ORDER BY redeemed_points
	ELSE
		SELECT redeem_level_id
			,redeemed_points
			,award_value
			,is_elite_level
			,is_premier_level
			,rec_status
			,rec_created_by
			,rec_created_when
			,rec_modify_by AS rec_modified_by
			,rec_modify_when AS rec_modified_when
		FROM dbo.rew_redeem_levels
		WHERE is_premier_level = 1 
		AND rec_status = 1
		ORDER BY redeemed_points
		
END



GO

GRANT EXECUTE ON [dbo].[api_redeem_level_select] TO [customer_api_user]
GO
