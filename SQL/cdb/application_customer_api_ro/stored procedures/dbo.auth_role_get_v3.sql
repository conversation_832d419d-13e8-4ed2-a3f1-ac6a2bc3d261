---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Saran AUIR.		| 2011-03-11	| Get the roles related with the user.
-- Sirichai Ch.		| 2012-07-09	| Added Support Out Of Office User, will also get the roles of out of office user for the assigned userid.
-- Siravitch C.		| 2019-08-06	| Performance Tuning for solving High CPU and Timeout
------------------------|---------------|----------------------------------------------------------
-- YCS/Rate Module/Lite Rate Control
-- EXEC dbo.auth_role_get_v3 '437EF331-6550-497F-B81F-22088BD17FE0', '5ad1d94a-d0f7-4ba4-b614-8731ba116934'
-- YCS/Rate module/Help , '4149b9d5-a948-407c-8058-43b504cdc466'
-- EXEC dbo.auth_role_get_v3 '437EF331-6550-497F-B81F-22088BD17FE0', '4149b9d5-a948-407c-8058-43b504cdc466'
-- EXEC_TEST : EXEC dbo.auth_role_get_v3 'A0CAA3FE-9DD8-4B36-B90E-C009B4836BC8', '4149b9d5-a948-407c-8058-43b504cdc466'
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_role_get_v3]
	@userId uniqueidentifier,
	@componentId uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	DECLARE @roles AS TABLE (
		RoleId uniqueidentifier
		, RoleName varchar(50)
	)

	INSERT INTO @roles (
		RoleId
		, RoleName
	)
	SELECT	AR.roleid
		, AR.rolename
	FROM	dbo.auth_permissions AS AP
	INNER JOIN	dbo.auth_role_permissions AS ARP
		ON ARP.permissionid = AP.permissionid
	INNER JOIN	dbo.auth_roles AR
		ON AR.roleid = ARP.roleid
	WHERE	AP.componentid = @componentId
	AND	AP.rec_status = 1
	AND	ARP.rec_status = 1
	AND	AR.rec_status = 1

	;WITH CTE
	AS
	(
		SELECT	AUIR.objectid
			, AUIR.objecttype
			, AR.rolename
		FROM	@roles	AR
		INNER JOIN	dbo.auth_user_in_role AS AUIR
			ON	AR.roleid = AUIR.roleid
		WHERE	AUIR.userid = @userId
		AND	AUIR.rec_status = 1
		UNION
		SELECT	AUIR.objectid
			, AUIR.objecttype
			, AR.rolename
		FROM	dbo.auth_user_outofoffice_mapping AS AUOM
		INNER JOIN	dbo.auth_user_in_role AS AUIR
			ON	AUIR.userid = AUOM.UserId
		INNER JOIN	@roles	AR
			ON	AR.roleid = AUIR.roleid
		WHERE	AUOM.assignedUserId = @userId
		AND	AUOM.rec_status = 1
		AND	GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate
	)

	SELECT	AUIR.rolename
		, AUIR.objecttype
		, AUIR.objectid
		, PH.hotel_name AS objectname
	FROM	CTE	AUIR
	LEFT JOIN	dbo.product_hotels AS PH
		ON AUIR.ObjectID = PH.hotel_id
		AND AUIR.ObjectType = 'H'
		AND AUIR.ObjectID <> 0


END
GO

GRANT EXECUTE ON [dbo].[auth_role_get_v3] TO [customer_api_user]
GO
