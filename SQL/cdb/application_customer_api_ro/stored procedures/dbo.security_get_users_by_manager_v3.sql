---------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------------
-- Kit<PERSON><PERSON>   | 2018-11-19  | Initial stored procedure
-- Kit<PERSON><PERSON>   | 2018-11-30  | Search all users who are under the manager
-- Kittis<PERSON>   } 2018-12-04  | Use if condition instead of where condition
---------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[security_get_users_by_manager_v3] 'A1925598-30DC-408D-A1A4-6AC036881BFC'
REVERT
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_get_users_by_manager_v3]
  @manager UNIQUEIDENTIFIER,
  @onlyActive BIT = 1
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    
    CREATE TABLE #SUBORDINATE_LIST_INFO
    (
      UserId uniqueidentifier
    )
    
    ;WITH
      SUBORDINATE_LIST (UserId) AS
      (
          SELECT  UserId
          FROM  dbo.auth_users
          WHERE  Manager = @manager
          AND  Manager <> UserId
        UNION ALL
          SELECT    A.UserId
          FROM    dbo.auth_users AS a
          INNER JOIN  SUBORDINATE_LIST AS SL
                ON A.Manager = SL.UserId
      )
    INSERT  INTO #SUBORDINATE_LIST_INFO
    (
      UserId
    )
    SELECT  UserId
    FROM  SUBORDINATE_LIST OPTION (QUERYTRACEON 9481)

    IF (@onlyActive = 0)
    BEGIN
      SELECT
        au.UserId,
        EmailAddress,
        DisplayName,
        Manager,
        is_single_task_consumption,
        rec_status,
        rec_created_when,
        rec_created_by,
        rec_modified_when,
        rec_modified_by,
        department_id,
        location_id
      FROM dbo.auth_users au
      INNER JOIN  #SUBORDINATE_LIST_INFO sli
            ON    au.UserId = sli.UserId
    END
    ELSE
    BEGIN
      SELECT
        au.UserId,
        EmailAddress,
        DisplayName,
        Manager,
        is_single_task_consumption,
        rec_status,
        rec_created_when,
        rec_created_by,
        rec_modified_when,
        rec_modified_by,
        department_id,
        location_id
      FROM dbo.auth_users au
      INNER JOIN  #SUBORDINATE_LIST_INFO sli
            ON    au.UserId = sli.UserId
      WHERE  rec_status = 1
    END

    DROP TABLE #SUBORDINATE_LIST_INFO
  END
GO

GRANT EXECUTE ON [dbo].[security_get_users_by_manager_v3] TO [customer_api_user]
GO
