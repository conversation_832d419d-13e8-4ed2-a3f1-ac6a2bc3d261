
---------------------------------------------------------------------------------------------------
-- Author       | Date          | Comment
----------------|---------------|----------------------------------------------------------
-- A.<PERSON> | 2017-12-18    | Fetch new hotels by date or minimal hotel_id.
----------------|---------------|----------------------------------------------------------
-- MONKEY_TEST: EXEC capi_lexis_sync_new_hotels null, '2017-12-15'
-- MONKEY_TEST: EXEC capi_lexis_sync_new_hotels 6, null
----------------|---------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[capi_lexis_sync_new_hotels]
	  @hotel_id int
	, @date     date
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF @hotel_id IS NOT NULL AND @hotel_id > 0
	BEGIN
		SELECT	hotel_id, rec_created_when
		FROM	dbo.product_hotels
		WHERE	hotel_id > @hotel_id
		ORDER BY hotel_id ASC
	END
	ELSE IF @date IS NOT NULL
	BEGIN
		SELECT	hotel_id, rec_created_when
		FROM	dbo.product_hotels
		WHERE	rec_created_when >= @date 
		AND	rec_created_when < DATEADD(DAY, +1, @date)
		ORDER BY hotel_id ASC
	END
END


GO

GRANT EXECUTE ON [dbo].[capi_lexis_sync_new_hotels] TO [customer_api_user]
GO
