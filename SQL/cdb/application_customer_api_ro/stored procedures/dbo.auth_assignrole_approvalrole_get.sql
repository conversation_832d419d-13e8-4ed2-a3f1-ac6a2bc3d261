
--------------------------|-------------|----------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------
-- <PERSON><PERSON><PERSON> Chayswangwon<PERSON> | 2011-10-18  | Gets the approval role list by user id
-- <PERSON>        | 2011-11-23  | Refactor for coding standards
-- Sirichai               | 2012-01-25  | get only rec_status  = 1 data
-- Kit<PERSON><PERSON>   | 2018-05-21  | Copy from MDB
--------------------------|-------------|----------------------------------------------------------
-- EXEC_TEST : EXEC dbo.auth_assignrole_approvalrole_get '45bf6875-2703-414e-b17c-0db5bdf7132f'
--------------------------|-------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_assignrole_approvalrole_get]
  @UserId uniqueidentifier
AS
BEGIN
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

  SELECT  DISTINCT RAR.ApprovalRoleId
          , R2.RoleName as ApprovalRoleName
          , RAR.AssignedRoleId as RoleId
          , R.RoleName
          , R.ObjectType
  FROM  dbo.auth_role_assigned_roles AS RAR
  INNER JOIN  dbo.auth_user_in_role AS UIR
        ON RAR.RoleId = UIR.RoleId
        AND RAR.rec_status = 1
        AND UIR.rec_status = 1
  INNER JOIN  dbo.auth_roles AS R
        ON R.RoleId = RAR.AssignedRoleId
        AND R.rec_status = 1
  INNER JOIN  dbo.auth_roles AS R2
        ON R2.RoleId = RAR.ApprovalRoleid
        AND R2.rec_status = 1
  WHERE UIR.UserId = @UserId
  ORDER BY  RAR.ApprovalRoleId
END
GO

GRANT EXECUTE ON [dbo].[auth_assignrole_approvalrole_get] TO [customer_api_user]
GO
