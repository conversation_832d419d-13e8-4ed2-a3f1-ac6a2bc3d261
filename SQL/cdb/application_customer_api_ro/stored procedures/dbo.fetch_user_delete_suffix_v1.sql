/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Varakorn Koschakosai     | 2020-03-04    | Fetch the proper suffix for user deletion
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @suffixList as delete_suffix_list;
INSERT INTO @suffixList VALUES ('_?hHvOaOk0XNKwwg3UC4TmcnhheJGUffBJNpsYluh1LsE=_', '_del'), ('_?hHvOaOk0XNKwwg3UC4Tmcs1AK/DH1tgkU+n7SxcaWi8=_', '_del1')
EXEC [dbo].[fetch_user_delete_suffix_v1] @suffixList;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[fetch_user_delete_suffix_v1]
    @suffixList dbo.delete_suffix_list READONLY

AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    DECLARE @text varchar(400)

    SELECT * INTO #suffixTracking FROM @suffixList

    WHILE EXISTS (select * from #suffixTracking)
    BEGIN
        SELECT top 1 @text = encryptText from #suffixTracking
        IF NOT exists (select 1 from dbo.auth_user_mapping where username = @text and authentication_type_id = 2)
            BREAK
        DELETE FROM #suffixTracking WHERE encryptText = @text
    END

    SELECT top 1 deleteSuffix from #suffixTracking
    DROP TABLE #suffixTracking
END
GO

GRANT EXECUTE ON [dbo].[fetch_user_delete_suffix_v1] TO [customer_api_user]
GO



