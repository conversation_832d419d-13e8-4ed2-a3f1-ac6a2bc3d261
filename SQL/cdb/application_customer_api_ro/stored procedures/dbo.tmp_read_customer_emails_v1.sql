/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-10-16    | Delete it as soon as possible.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[tmp_read_customer_emails_v1] DEFAULT;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[tmp_read_customer_emails_v1]
    @member_ids dbo.id_table_type READONLY
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT     rm.MemberId AS member_id
               , au.EmailAddress AS email
    FROM       dbo.rew_members rm
    INNER JOIN @member_ids m
            ON m.id = rm.MemberId
    INNER JOIN dbo.auth_users au
            ON au.UserId = rm.UserId
    WHERE      rm.rec_status = 1;
END
GO

GRANT EXECUTE ON [dbo].[tmp_read_customer_emails_v1] TO [customer_api_user]
GO
