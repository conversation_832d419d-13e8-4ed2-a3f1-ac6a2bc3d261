-------------------------------------------------------------------------------------------------------------------------------------
-- Name			| Date			| Desc
-------------------------------------------------------------------------------------------------------------------------------------
--J <PERSON>ache<PERSON>um| 2018-03-09     | for other teams to check whether user is blocked (for gdpr suspension)
-------------------------------------------------------------------------------------------------------------------------------------
-- Test : EXEC dbo.auth_user_check_is_blocked_v2 13280390 
-------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_user_check_is_blocked_v2]
(
	@MemberID int
)
AS
BEGIN
	
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
 
	SELECT 		au.isBlocked
	FROM 		dbo.auth_users au
	INNER JOIN 	dbo.rew_members rm
			ON au.UserId = rm.UserId
	WHERE 		rm.MemberID = @MemberID
END

GO

GRANT EXECUTE ON [dbo].[auth_user_check_is_blocked_v2] TO [customer_api_user]
GO
