---------------------------------------------------------------------------------------------------
-- Author		                | Date		      | Comment
----------------------------|-----------------|----------------------------------------------------------
-- Andrew <PERSON> 			      | 2017-11-22	  |  First version.
----------------------------|-----------------|----------------------------------------------------------
-- MONKEY_TEST EXEC dbo.get_cms_by_id 1,20725,'A'
----------------------------|-----------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[get_cms_by_id]
  @language_id int,
  @cms_id int,
  @cms_version varchar
AS
BEGIN

    SET NOCOUNT ON 
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
 
    SELECT cms_data 
    FROM   dbo.cms_data 
    WHERE   language_id = @language_id
    AND     cms_item_id = @cms_id
    AND     cms_data_version = @cms_version
END



GO

GRANT EXECUTE ON [dbo].[get_cms_by_id] TO [customer_api_user]
GO
