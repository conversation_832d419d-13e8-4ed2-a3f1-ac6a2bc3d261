---------------------------------------------------------------------------------------------------
-- Author         | Date       | Comment
------------------|------------|-------------------------------------------------------------------
-- Natavit R.     | 2018-07-23 | From rew_contacts_check_exist_primary_email_Duplicate
-- <PERSON><PERSON>.      | 2018-10-07 | v2, extend PII columns size WFAPI-2152
-- Mrun S.        | 2018-15-11 | add encrypted query params
------------------|------------|-------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
exec dbo.capi_rew_contacts_check_duplicate_email_contact_method_v2 1, 1, 1, '<EMAIL>', '<EMAIL>'
REVERT;
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[capi_rew_contacts_check_duplicate_email_contact_method_v2]
(
	@member_id int
	,@contact_id int
	,@contact_method_id int
	,@contact_method_value nvarchar(400)
	,@encrypted_contact_method_value nvarchar(400)
)
AS
BEGIN

  SET NOCOUNT ON
  SET XACT_ABORT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

declare @PrimaryID int
declare @Alternate int
  DECLARE @message nvarchar(50) = ''
--dbo.afm_contact_methods
--1 = Primary E-mail
--8 = Alternate E-Mail
set @PrimaryID = 1
set @Alternate = 8
-- Modified by Apichart @ 20090227: Add check contact_method_id
if (@contact_method_id not in (@PrimaryID,@Alternate)) return;
set @contact_method_value = ltrim(rtrim(@contact_method_value))

if exists
	(	select contact_id from dbo.rew_contacts
		where (contact_method_value = @contact_method_value OR contact_method_value = @encrypted_contact_method_value)
		and contact_method_id in (@PrimaryID,@Alternate)
		and memberid <> @member_id
		and rec_status = 1 -->have to set this flag?
	)
	set @message ='Email value is duplicate to other reward member.'
else
begin
	if (@contact_id=0)
	-- Case add new
	begin
		if (@contact_method_id = @PrimaryID)
			begin
				if exists
					(	select contact_id from dbo.rew_contacts
						where contact_method_id = @PrimaryID
						and memberid = @member_id
						and rec_status = 1 -->have to set this flag?
					)
				set @message ='This member already has Primary Email Value.'
			end
		else
			begin
				if exists
					(	select contact_id from dbo.rew_contacts
						--where contact_method_value = @contact_method_value
						where contact_method_id = @Alternate
						and memberid = @member_id
						and rec_status = 1 -->have to set this flag?
					)
				set @message ='This member already has Alternate email value.'
			end
	end
	-- Case Update (just add contact_id <> @contact_id)
	else
	begin
		if (@contact_method_id = @PrimaryID)
		begin
			if exists
				(	select contact_id from dbo.rew_contacts
					where contact_method_id = @PrimaryID
					and memberid = @member_id
					and rec_status = 1 -->have to set this flag?
					and contact_id <> @contact_id
				)
			set @message ='This member already have Primary Email Value.'
		end
		else
		begin
			if exists
				(	select contact_id from dbo.rew_contacts
					where (contact_method_value = @contact_method_value OR contact_method_value = @encrypted_contact_method_value)
					and contact_method_id = @Alternate
					and memberid = @member_id
					and rec_status = 1 -->have to set this flag?
					and contact_id <> @contact_id
				)
			set @message ='This Alternative Email Value already exists.'
		end
	end
end

  SELECT @message as message

END
GO

GRANT EXECUTE ON [dbo].[capi_rew_contacts_check_duplicate_email_contact_method_v2] TO [customer_api_user]
GO
