
-----------------------------------------------------------------------------------------------------------
-- Author			| Date		| Comment
-----------------------------------------------------------------------------------------------------------
-- <PERSON> 		| 15/05/2018	| Get finance for NHA hosts hotels
-- Thitisorn     		| 17/01/2019	| Add payment method
-----------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @hotelids AS [id_table_type];
INSERT INTO @hotelids (id) VALUES (1),(2)
EXEC [dbo].[host_finance_v2] @hotelids
REVERT;
END_EXEC_TEST
*/
-----------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[host_finance_v2]
@hotelIds [id_table_type] READONLY
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
  
  	SELECT 		hotel_id AS h
			, f.has_active_bank_account AS a
			, f.hotel_payment_method_id AS p
  	FROM 		dbo.finance_hotel f 
	INNER JOIN 	@hotelids hotelIds 
		ON 	hotelIds.id = f.hotel_id
END
GO

GRANT EXECUTE ON [dbo].[host_finance_v2] TO [customer_api_user]
GO
