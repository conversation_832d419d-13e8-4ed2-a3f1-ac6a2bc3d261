---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- E<PERSON><PERSON>            | 2011-11-18  | Used for downloading the auth_user_relations
-- <PERSON>      | 2011-12-28  | Refactor for coding standards
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.backoffice_download_auth_user_relations_current_relations 'H', '6FA9D0AC-1F2A-41E6-B0FE-011143B2AB8D'
REVERT
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_download_auth_user_relations_current_relations]
    @ObjectType char(1),
    @UserId uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    IF @ObjectType = 'H'
    BEGIN
        SELECT      m.username AS UserName
                , r.ObjectType
                , r.ObjectId
                , h.Hotel_name AS RelationName
                , ci.city_name AS City
                , co.country_name AS Country
        FROM        dbo.auth_user_relations AS r
        INNER JOIN  dbo.auth_user_mapping AS m
                    ON r.UserId = m.user_id
        INNER JOIN  dbo.product_hotels AS h
                    ON r.ObjectId = h.Hotel_Id
        INNER JOIN  dbo.geo2_city AS ci
                    ON ci.city_id = h.city_id
        INNER JOIN  dbo.geo2_country AS co
                    ON co.country_id = h.country_id
        WHERE       r.UserId = @UserId
        AND     r.ObjectType = @ObjectType
        AND     m.authentication_type_id = 1
        ORDER BY    r.ObjectId
    END
    IF @ObjectType = 'F'
    BEGIN
        SELECT      m.username AS UserName
                , r.ObjectType
                , r.ObjectId
                , a.first_name AS RelationName
                , a.company_name AS CompanyName
        FROM        dbo.auth_user_relations AS r
        INNER JOIN  dbo.auth_user_mapping AS m
                    ON r.UserId = m.user_id
                    AND m.authentication_type_id = 1
        INNER JOIN  dbo.afl_affiliate AS a
                    ON r.ObjectId = a.affliate_id
        WHERE       r.UserId = @UserId
        AND     r.ObjectType = @ObjectType
        ORDER BY    r.ObjectId
    END
END
GO

GRANT EXECUTE ON [dbo].[backoffice_download_auth_user_relations_current_relations] TO [customer_api_user]
GO
