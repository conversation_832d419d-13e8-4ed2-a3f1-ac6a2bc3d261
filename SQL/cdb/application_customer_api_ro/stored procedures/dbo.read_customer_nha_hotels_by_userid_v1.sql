/*
SQL_OBJECT_INFO

estimated_execution_count: 1k
estimated_execution_time_unit: minute
sp_timeout_configuration: 5s

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Seth <PERSON>p          | 2025-07-14    | Returns a list of all active NHA hotels for the given user.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_customer_nha_hotels_by_userid_v1] '3E1FB199-2631-4F7C-9782-064AB038C74D';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_customer_nha_hotels_by_userid_v1]
    @user_id uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    -- Get all properties for that user using INNER JOIN to ensure we only get valid properties
    SELECT DISTINCT h.master_hotel_id AS hotel_id, u.userid as userid, h.booking_count as booking_count
    FROM dbo.host_properties_v2 h
    INNER JOIN dbo.rew_members u ON h.nha_host_member_id = u.memberid
    WHERE u.userid = @user_id
    AND u.rec_status = 1
    AND h.master_hotel_id IS NOT NULL;

END
GO

GRANT EXECUTE ON [dbo].[read_customer_nha_hotels_by_userid_v1] TO [customer_api_user]
GO
