-------------------------------------------------------------------------------------------------
-- Author                      | Date        | Comment
------------------------------|-------------|----------------------------------------------------
-- <PERSON><PERSON><PERSON> Tantiwanichapan   | 2011-01-25  | Get All Authentication System
-- Kit<PERSON><PERSON>       | 2019-03-13  | Support whitelabel ID
------------------------------|-------------|----------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.backoffice_auth_system_all_v3 1
REVERT
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_auth_system_all_v3]
  @whitelabelId   SMALLINT 
AS
BEGIN
  SET NOCOUNT ON
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

  SELECT  S.SystemID
          , S.SystemName
          , ISNULL(S.URL,'') AS 'URL'
          , S.OrderId
          , S.rec_status
          , S.rec_created_when
          , S.rec_created_by
          , S.rec_modified_when
          , S.rec_modified_by
          , ISNULL(
            ( SELECT EmailAddress 
              FROM dbo.auth_users 
              WHERE UserID = S.rec_created_by 
              AND ISNULL(whitelabel_id, 1) = @whitelabelId
            ), '') AS 'EmailCreatedBy'
          , ISNULL(
            ( SELECT EmailAddress 
              FROM dbo.auth_users 
              WHERE UserID = S.rec_modified_by
              ANd ISNULL(whitelabel_id, 1) = @whitelabelId
            ), '') AS 'EmailModifiedBy'
  FROM dbo.auth_systems  S
  WHERE S.rec_status > 0
  ORDER BY S.OrderId, S.SystemName
END
GO

GRANT EXECUTE ON [dbo].[backoffice_auth_system_all_v3] TO [customer_api_user]
GO
