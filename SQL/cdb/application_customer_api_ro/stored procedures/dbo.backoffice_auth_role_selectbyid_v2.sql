---------------------------------------------------------------------------------------------------
-- Author		              | Date		    | Comment
---------------------------------------------------------------------------------------------------------
-- Sir<PERSON><PERSON>wang<PERSON> | 02/01/2011	| Get All Authentication Role by Roleid
-- <PERSON><PERSON><PERSON> Wiangwang	    | 10/16/2012	| Created V2 - Added object type
-- Mrun S                 | 2019-02-25  | Rename to backoffice_auth_role_selectbyid_v2
-------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[backoffice_auth_role_selectbyid_v2] '07327098-87F1-4DD5-9DF3-00944BA8D0EB'
REVERT;
END_EXEC_TEST
*/
-------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[backoffice_auth_role_selectbyid_v2]
	@RoleId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT RoleId
		, RoleName
		, ISNULL(ObjectType, '') AS ObjectType
		, rec_status
		, rec_created_when
		, rec_created_by
		, rec_modified_when
		, rec_modified_by
		, ISNULL((
				SELECT emailAddress
				FROM dbo.auth_users
				WHERE UserId = ar.rec_created_by
				), '') AS 'EmailCreatedBy'
		, ISNULL((
				SELECT emailAddress
				FROM dbo.auth_users
				WHERE UserId = ar.rec_modified_by
				), '') AS 'EmailModifiedBy'
	FROM dbo.auth_roles ar
	WHERE RoleId = @RoleId
		AND rec_status > 0
	ORDER BY RoleName
END

GO

GRANT EXECUTE ON [dbo].[backoffice_auth_role_selectbyid_v2] TO [customer_api_user]
GO