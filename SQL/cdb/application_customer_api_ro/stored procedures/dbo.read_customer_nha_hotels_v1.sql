/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-08-20    | Returns a list of active NHA hotels for the given user.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_customer_nha_hotels_v1] '856C401B-3E32-429A-A8C0-FEB9F4539224';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_customer_nha_hotels_v1]
    @user_id uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT     DISTINCT r.ObjectID AS hotel_id
    FROM       dbo.auth_user_in_role r
    INNER JOIN dbo.product_hotels h
            ON h.hotel_id = r.ObjectID
    WHERE      r.UserId = @user_id
    AND        r.rec_status = 1
    AND        r.ObjectType = 'H'
    AND        h.is_non_hotel_accommodation_mode = 1
    AND        h.rec_status = 1
    AND        r.RoleId IN ('86a9719d-cab0-43ff-bcc1-b3a490375257', 'a2c5af8b-90e9-47d6-a75d-b6e0805e9ce3')
    OPTION (OPTIMIZE FOR (@user_id UNKNOWN));
END
GO

GRANT EXECUTE ON [dbo].[read_customer_nha_hotels_v1] TO [customer_api_user]
GO