/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Adi <PERSON>           | 2021-04-05    | Returns true if Customer with this memberId exists
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[fetch_customer_exists_by_member_id_v1] 123;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[fetch_customer_exists_by_member_id_v1]
    @member_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @customer_exists BIT = 0

    IF EXISTS ( SELECT 1 FROM dbo.rew_members WHERE MemberId = @member_id AND rec_status != -1 )
    BEGIN
           SET @customer_exists = 1
    END
    ELSE
    BEGIN
           SET @customer_exists = 0
    END

    SELECT @customer_exists as customer_exists
END
GO

GRANT EXECUTE ON [dbo].[fetch_customer_exists_by_member_id_v1] TO [customer_api_user]
GO
