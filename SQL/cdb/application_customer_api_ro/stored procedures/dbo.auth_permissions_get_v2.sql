


-------------------------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|--------------------------------------------------------------------------
-- Saran B.		| 2011-01-31	| Get the permissions related with the user.
-- Sirichai Ch.		| 2012-07-13	| Support Out of office user.
-- Sirichai Ch.		| 2012-09-03	| Added PermissionName to output (Requestor: FINANCE TEAM)
-- Ni<PERSON>porn S.		| 2012-09-06	| Refactor Code to improve performance
------------------------|---------------|--------------------------------------------------------------------------
-- EXEC dbo.auth_permissions_get_v2 '56003888-4FF1-4F78-93DA-ED7E1742B1EC', '9A7CEDD4-A46B-4715-BAB7-0A5A4101AFAC'
-- YCS/Rate Module/Lite Rate Control
-- EXEC dbo.auth_permissions_get_v2 '437EF331-6550-497F-B81F-22088BD17FE0', '5ad1d94a-d0f7-4ba4-b614-8731ba116934'
-- YCS/Rate module/Help , '4149b9d5-a948-407c-8058-43b504cdc466'
-- MONKEY_TEST EXEC dbo.auth_permissions_get_v2 '437EF331-6550-497F-B81F-22088BD17FE0', '4149b9d5-a948-407c-8058-43b504cdc466'
------------------------|---------------|--------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[auth_permissions_get_v2]
	@userId uniqueidentifier,
	@componentId uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	-- Get all permissions related with the user.
	;WITH
		Permission AS
		(
				SELECT		AP.PermissionId
						, AP.PermissionName
						, AUIR.ObjectID
						, AUIR.ObjectType
				FROM		dbo.auth_permissions AP
				INNER JOIN	dbo.auth_role_permissions ARP
							ON AP.PermissionId = ARP.PermissionId
				INNER JOIN	dbo.auth_roles AR
							ON ARP.RoleId = AR.RoleId
				INNER JOIN	dbo.auth_user_in_role AUIR
							ON AR.RoleId = AUIR.RoleId
				WHERE		AP.rec_status = 1
				AND		AP.componentId = @componentId
				AND		AUIR.rec_status = 1
				AND		AUIR.UserId = @userId
				AND		ARP.rec_status = 1
				AND		AR.rec_status = 1
			UNION --SUPPORT OUT OF OFFICE USER.
				SELECT		AP.PermissionId
						, AP.PermissionName
						, AUIR.ObjectID
						, AUIR.ObjectType
				FROM		dbo.auth_permissions AS AP
				INNER JOIN	dbo.auth_role_permissions AS ARP
							ON AP.PermissionId = ARP.PermissionId
				INNER JOIN	dbo.auth_roles AS AR
							ON ARP.RoleId = AR.RoleId
				INNER JOIN	dbo.auth_user_in_role AUIR
							ON AR.RoleId = AUIR.RoleId
				INNER JOIN	dbo.auth_user_outofoffice_mapping AS AUOM
							ON AUIR.UserId = AUOM.UserID
				WHERE		AP.rec_status = 1
				AND		AP.componentId = @componentId
				AND		ARP.rec_status = 1
				AND		AR.rec_status = 1
				AND		AUOM.rec_status = 1
				AND		GETDATE() BETWEEN AUOM.StartDate AND AUOM.EndDate
				AND		AUIR.out_of_office = 1
				AND		AUIR.rec_status = 1
				AND		AUOM.assignedUserId = @userId
			UNION
				SELECT		AP.PermissionId
						, AP.PermissionName
						, ARP.ObjectID
						, ARP.ObjectType
				FROM		dbo.auth_permissions AP
				INNER JOIN	dbo.auth_user_permission ARP
							ON AP.PermissionId = ARP.PermissionId
				WHERE		AP.rec_status = 1
				AND		AP.componentId = @componentId
				AND		ARP.rec_status = 1
				AND		ARP.DenyRule = 0
				AND		ARP.UserId = @userId
		)
	-- Remove the denied permissions.
		SELECT	AP.PermissionId
			, AP.PermissionName
			, AP.ObjectID
			, AP.ObjectType
		FROM	Permission AP
		WHERE	NOT EXISTS
			(
				SELECT	*
				FROM	dbo.auth_user_permission  ARP
				WHERE   AP.ObjectType = ARP.ObjectType
				AND	AP.PermissionId = ARP.PermissionId
				AND	ARP.UserId = @userId
				AND	ARP.ObjectId = 0
				AND	ARP.DenyRule  = 1
				AND	ARP.rec_status = 1
			)
		EXCEPT
			SELECT	AUP.PermissionId
				, AP.PermissionName
				, AUP.ObjectID
				, AUP.ObjectType
			FROM	dbo.auth_user_permission AUP
			INNER JOIN dbo.auth_permissions AP
					ON AUP.permissionid = AP.permissionid
			WHERE	AUP.rec_status = 1
			AND	AUP.DenyRule = 1
			AND	AUP.UserId = @userId
			AND	AP.rec_status = 1
END






GO

GRANT EXECUTE ON [dbo].[auth_permissions_get_v2] TO [customer_api_user]
GO
