
/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- <PERSON> 		| 15/05/2018	| Get finance for NHA hosts hotels
------------------------------------------------|---------------|------------------------------------------

MONKEY_TEST

DECLARE @hotelids AS [id_table_type];
INSERT INTO @hotelids (id) (SELECT DISTINCT TOP 100 hotel_id FROM dbo.product_hotels)
EXEC [dbo].[host_finance] @hotelids

END_MONKEY_TEST
------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[host_finance]
@hotelIds [id_table_type] READONLY
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
  SELECT hotel_id AS h, f.has_active_bank_account as a
  FROM dbo.finance_hotel f INNER JOIN @hotelids hotelIds ON hotelIds.id = f.hotel_id
END

GO

GRANT EXECUTE ON [dbo].[host_finance] TO [customer_api_user]
GO
