
/*
---------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------
-- Saran B.               | 2012-08-09  | selects merged member.
-- Saran B.               | 2012-12-11  | Modified the points column.
-- Saran B.               | 2013-01-03  | Modified the points column.
-- Kittis<PERSON>   | 2018-04-17  | Copy to CDB
-- Dawanit S.             | 2019-05-29  | Separated name column to first_name and last_name
--------------------------|-------------|----------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.api_merged_member_select_v2 3
REVERT;
END_EXEC_TEST
--------------------------|-------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[api_merged_member_select_v2]
	@memberId INT
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	SELECT a.mergeId
		,b.MemberID
		,b.MemberCode
		,b.first_name AS first_name
		,b.last_name AS last_name
		,a.MemberId2
		,(
			SELECT TOP 1 contact_method_value
			FROM dbo.rew_contacts
			WHERE MemberID = b.MemberID
				AND contact_method_id = 1
			ORDER BY ISNULL(rec_modify_when, '19990101')
			) AS Email
		,(
			SELECT ISNULL(CAST(count(distinct c.booking_id) AS VARCHAR(10)) + '|' + CAST(SUM(c.points_pending * d.point_activity_value) + SUM(c.points_affected * d.point_activity_value) AS VARCHAR(10)), '0|0')
			FROM dbo.rew2_merge_point_activity c INNER JOIN dbo.rew2_point_activity_subtypes d 
						ON c.point_activity_subtype_id = d.point_activity_subtype_id AND d.rec_status = 1
			WHERE c.MergeId = a.MergeId
				AND c.rec_status = 1
			) AS points
		,isnull(a.approvedate, MergeTimeStamp) AS approvedate
	FROM dbo.rew_merge_members a INNER JOIN dbo.rew_members b 
				ON a.MemberId1 = b.MemberID
	WHERE a.MemberId2 = @memberId
		AND a.MergeStatusId = 2
END
GO

GRANT EXECUTE ON [dbo].[api_merged_member_select_v2] TO [customer_api_user]
GO
