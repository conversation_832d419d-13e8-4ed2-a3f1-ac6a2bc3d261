---------------------------------------------------------------------------------------------------
-- Author				| Date			| Comment
------------------------|---------------|----------------------------------------------------------
-- Nasakol P.			| 28/03/2018	| Access PII of user
-- Vatulin Y.     | 09/11/2018  | Remove facebook data
-- Varakorn K.    | 13/05/2019  | Remove full name concatenation & adjust to search by user id
------------------------|---------------|----------------------------------------------------------
-- EXEC_TEST
-- EXECUTE AS LOGIN = 'customer_api_user'
-- EXEC [dbo].[backoffice_PII_access_v3] '05F64249-EE8B-4428-AB1F-112AE0E3FDE0';
-- REVERT;
-- END_EXEC_TEST
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[backoffice_PII_access_v3]
	@user_id uniqueidentifier
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	SELECT AU.UserId
	       , AU.DisplayName
	       , AU.EmailAddress
	       , AU.PhoneNo
	       , RM.MemberID
	       , ISNULL(RM.title, '') as title
 	       , ISNULL(RM.first_name, '') as first_name
 	       , ISNULL(RM.middle_name, '') as middle_name
 	       , ISNULL(RM.last_name, '') as last_name
	       , RM.birth_date
	       , RM.nationality_id
	       , RC.contact_method_id
	       , RC.contact_method_value
	       , RA.address_type_id
	       , ISNULL(RA.address_1, '') as address_1
	       , ISNULL(RA.address_2, '') as address_2
	       , ISNULL(RA.city, '') as city
			   , ISNULL(RA.state, '') as state
			   , ISNULL(RA.region, '') as region
			   , ISNULL(RA.country, '') as country
			   , ISNULL(RA.postal_code, '') as postal_code
	FROM dbo.auth_users AU
		INNER JOIN dbo.rew_members RM on AU.userId = RM.UserId
		LEFT JOIN dbo.rew_contacts RC on RC.MemberID = RM.MemberID
		LEFT JOIN dbo.rew_address RA on RA.MemberID = RM.MemberID
	WHERE AU.UserId = @user_id AND
	      RC.contact_method_id <> 11 -- mobile device is not PII
	ORDER BY RM.MemberId;
END


GO

GRANT EXECUTE ON [dbo].[backoffice_PII_access_v3] TO [customer_api_user]
GO
