/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1k

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Ayu Shrivastava          | 2025-04-15    | Returns a list of active NHA hotels for the given user.
-- Ayu <PERSON>va          | 2025-04-21    | Returns a list of active NHA (sNHA and mNHA) hotels for the given user.
-- Ayu Shri<PERSON>tava          | 2025-05-21    | Returns a user id for the given property id.
-- Ayu Shrivastava          | 2025-06-17    | Returns booking count given property id.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_customer_nha_hotels_v6] 63430447
EXEC [dbo].[read_customer_nha_hotels_v6] 63089331
EXEC [dbo].[read_customer_nha_hotels_v6] 52733284
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_customer_nha_hotels_v6]
    @property_id int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    -- First get the user ID from the property ID
    DECLARE @user_id uniqueidentifier
    SELECT @user_id = u.userid
    FROM dbo.host_properties_v2 h
    LEFT JOIN dbo.rew_members u ON h.nha_host_member_id = u.memberid
    WHERE h.master_hotel_id = @property_id
    AND u.rec_status = 1;
    -- If no user found, return empty result
    IF @user_id IS NULL
    BEGIN
        RETURN;
    END
    -- Get all properties for that user using INNER JOIN to ensure we only get valid properties
    SELECT DISTINCT h.master_hotel_id AS hotel_id, u.userid as userid, h.booking_count as booking_count
    FROM dbo.host_properties_v2 h
    INNER JOIN dbo.rew_members u ON h.nha_host_member_id = u.memberid
    WHERE u.userid = @user_id
    AND u.rec_status = 1
    AND h.master_hotel_id IS NOT NULL;

END
GO

GRANT EXECUTE ON [dbo].[read_customer_nha_hotels_v6] TO [customer_api_user]
GO
