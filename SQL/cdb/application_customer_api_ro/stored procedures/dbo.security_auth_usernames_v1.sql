---------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
--------------------------|-------------|----------------------------------------------------------------
-- <PERSON><PERSON><PERSON>        | 2019-07-12  | Return username and authentication type
---------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
  DECLARE @UserIds uuid_table_type
  INSERT INTO @UserIds (uuid)
  VALUES ('CC9E7952-745F-4A90-808F-532B599E422C'), ('B7FB0322-3ADC-4C6E-945B-532B59E5154B')
  EXEC [dbo].[security_auth_usernames_v1] @UserIds
REVERT
END_EXEC_TEST
*/
---------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_auth_usernames_v1]
   @users [dbo].[uuid_table_type] READONLY,
   @onlyActive BIT = 1
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
      
      SELECT  [user_id],
              username,
              authentication_type_id
      FROM    [dbo].[auth_user_mapping] AS a
      WHERE   EXISTS  (
                            SELECT  uuid
                            FROM    @users AS b
							              WHERE	a.[user_id] = b.uuid
                      )
      AND   ( 
              @onlyActive = 0 
              OR rec_status = 1
            )
END
GO

GRANT execute ON [dbo].[security_auth_usernames_v1] TO [customer_api_user]
GO
