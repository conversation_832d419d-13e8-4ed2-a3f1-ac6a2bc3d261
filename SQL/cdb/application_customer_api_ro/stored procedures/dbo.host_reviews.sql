
/*
-----------------------------------------------------------------------------------------------------------
-- Author						| Date			| Comment
------------------------------------------------|---------------|------------------------------------------
-- <PERSON> 		| 15/05/2018	| Get reviews for NHA hosts hotels
------------------------------------------------|---------------|------------------------------------------

MONKEY_TEST

DECLARE @hotelids AS [id_table_type];
INSERT INTO @hotelids (id) (SELECT DISTINCT TOP 100 hotel_id FROM dbo.product_hotels)
EXEC [dbo].[host_reviews] @hotelids

END_MONKEY_TEST
------------------------------------------------|---------------|------------------------------------------
*/

CREATE PROCEDURE [dbo].[host_reviews]
@hotelIds [id_table_type] READONLY
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
  SELECT hotel_id AS h ,COUNT(hotel_id) AS c ,AVG(rating_average) AS a
  FROM rev_hotels_v3 INNER JOIN @hotelids hotelIds ON hotelIds.id = rev_hotels_v3.hotel_id
  WHERE rec_status = 1 GROUP BY hotel_id

END

GO

GRANT EXECUTE ON [dbo].[host_reviews] TO [customer_api_user]
GO
