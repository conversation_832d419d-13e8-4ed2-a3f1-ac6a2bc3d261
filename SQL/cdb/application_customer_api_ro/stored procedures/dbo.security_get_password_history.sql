---------------------------------------------------------------------------------------------------
-- Author				| Date			| Comment
------------------------|---------------|----------------------------------------------------------
-- Yumashish S.			| 2017-21-02	| Created
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST EXEC [dbo].[security_get_password_history] 'A93FF85D-0A32-4E42-BC60-2080F559E1AE'
-- Test : EXEC [dbo].[security_get_password_history] 'A93FF85D-0A32-4E42-BC60-2080F559E1AE', 5
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[security_get_password_history]
	@userId uniqueidentifier,
	@limit int = 10
AS
BEGIN
	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT TOP (@limit) 
			password_history_id
			,[user_id]
			,pwhash
			,salt
			,rec_created_by
			,rec_created_when
	FROM	dbo.auth_password_history
	WHERE	[user_id] = @userId
	ORDER BY rec_created_when DESC
END



GO

GRANT EXECUTE ON [dbo].[security_get_password_history] TO [customer_api_user]
GO
