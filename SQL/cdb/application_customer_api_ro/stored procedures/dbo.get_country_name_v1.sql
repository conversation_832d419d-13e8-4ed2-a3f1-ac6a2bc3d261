---------------------------------------------------------------------------------------------------
-- Author		            | Date		      | Comment
----------------------------|-----------------|----------------------------------------------------------
-- Si<PERSON> 			    | 2020-09-01	  |  Fetch country name from country_id and language_id
----------------------------|-----------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @country_ids AS [id_table_type];
INSERT INTO @country_ids (id) VALUES (106)
EXEC [dbo].[get_country_name_v1] @country_ids, 22
REVERT;
END_EXEC_TEST
*/
----------------------------|-----------------|----------------------------------------------------------

CREATE PROCEDURE [dbo].[get_country_name_v1]
  @country_ids id_table_type READONLY,
  @language_id int
AS
BEGIN

    SET NOCOUNT ON 
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
 
    SELECT
        c.id as country_id,
        ISNULL(gcl.country_name, gc.country_name) AS country_name
    FROM  @country_ids as c
        INNER JOIN dbo.geo2_country as gc ON gc.country_id = c.id
        LEFT JOIN dbo.geo2_country_language AS gcl ON gc.country_id = gcl.country_id AND gcl.language_id = @language_id
END
GO

GRANT EXECUTE ON [dbo].[get_country_name_v1] TO [customer_api_user]
GO
