/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-07-07    | Returns city id for the given hotel id.
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-10-03    | Filter active/in-active hotels.
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @hotel_ids dbo.id_table_type
INSERT INTO @hotel_ids
SELECT TOP(500) hotel_id
FROM	dbo.product_hotels
WHERE	rec_status = 1
ORDER BY hotel_id DESC
EXEC dbo.get_hotel_cities_v2 @hotel_ids;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[get_hotel_cities_v2]
    @hotel_ids dbo.id_table_type READONLY,
    @rec_status int = 1
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT     ph.hotel_id
               , ph.city_id
    FROM       dbo.product_hotels ph
    INNER JOIN @hotel_ids ids
            ON ph.hotel_id = ids.id
    WHERE      ph.rec_status = @rec_status;
END
GO

GRANT EXECUTE ON [dbo].[get_hotel_cities_v2] TO [customer_api_user]
GO
