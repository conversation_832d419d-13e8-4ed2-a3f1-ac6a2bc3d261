

---------------------------------------------------------------------------------------------------
-- Author               | Date          | Comment
------------------------|---------------|----------------------------------------------------------
-- Dash Fu				| 2017-02-08    | Get only one record that either user_id matched or app_user_id matched.
------------------------|---------------|----------------------------------------------------------
-- MONKEY_TEST EXEC [security_social_user_info_select_one] '5D3C547F-5D8A-4630-B1C0-1286E03670CC', 'o6_bmasdasdsad6_2sgVt7hMZOPfL', 200
---------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[security_social_user_info_select_one]
	@user_id uniqueidentifier,
	@app_user_id VARCHAR(50),
	@app_id SMALLINT
AS
BEGIN

    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
			
    SELECT TOP 1 user_info_id
		, [user_id]
		, app_user_id
		, app_id
		, user_info
    FROM   	dbo.auth_social_user_info tb
    WHERE  	tb.app_id = @app_id 
    AND 	is_active = 1
    AND       (tb.[user_id] = @user_id OR tb.app_user_id = @app_user_id)

END




GO

GRANT EXECUTE ON [dbo].[security_social_user_info_select_one] TO [customer_api_user]
GO
