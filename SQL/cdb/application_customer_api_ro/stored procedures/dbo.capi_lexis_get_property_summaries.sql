
---------------------------------------------------------------------------------------------------
-- Author       | Date          | Comment
----------------|---------------|----------------------------------------------------------
-- A.<PERSON> | 2017-12-18    | Get hotel's fields for TPRM screening.
----------------|---------------|----------------------------------------------------------
-- manual testing with existing unprocessed hotels in capi_lexis_properties table:
-- DECLARE @table AS CAPI_LEXIS_PROPERTY_IDS_TABLE_TYPE;
-- INSERT INTO @table (hotel_id) EXEC dbo.capi_lexis_properties_to_screen
-- EXEC dbo.capi_lexis_get_property_summaries @table
---------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[capi_lexis_get_property_summaries]
    @properties [dbo].[capi_lexis_property_ids_table_type] READONLY
AS
  BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT
	  ph.hotel_id
	  , ph.hotel_name
	  , ph.local_name
	  , ph.hotel_formerly_name
	  , country.country_name
	  , city.city_name
	  , addr.postal_code as addr_postal_code
	  , addr.address_i as addr_address_i
	  , addr.address_ii as addr_address_ii
	  , addr.country as addr_country
	  , addr.city as addr_city
	FROM dbo.product_hotels AS ph
	LEFT JOIN dbo.geo2_country AS country 
		ON ph.country_id = country.country_id
	LEFT JOIN dbo.geo2_city AS city 
		ON ph.city_id = city.city_id
	LEFT JOIN dbo.product_address AS addr 
		ON ph.hotel_id = addr.product_id
	WHERE ph.hotel_id IN (SELECT hotel_id FROM @properties)
  END



GO

GRANT EXECUTE ON [dbo].[capi_lexis_get_property_summaries] TO [customer_api_user]
GO
