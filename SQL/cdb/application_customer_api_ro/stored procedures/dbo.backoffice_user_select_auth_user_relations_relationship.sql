---------------------------------------------------------------------------------------------------
-- Author               | Date        | Comment
------------------------|-------------|----------------------------------------------------------
-- E<PERSON><PERSON>            | 2011-10-18  | Select relationship which user assigned to
-- <PERSON>      | 2011-12-28  | Refactor for coding standards
------------------------|-------------|----------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.backoffice_user_select_auth_user_relations_relationship 1070, 'H'
REVERT
END_EXEC_TEST
*/
CREATE PROCEDURE [dbo].[backoffice_user_select_auth_user_relations_relationship]
    @ObjectId int,
    @ObjectType char(1)
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT      r.ObjectId AS ObjectId
            , m.username AS UserName
    FROM        dbo.auth_user_relations AS r
    INNER JOIN  dbo.auth_user_mapping AS m
                ON r.UserId = m.user_id
    WHERE       r.ObjectId = @ObjectId
    AND     r.ObjectType = @ObjectType
    AND     m.authentication_type_id = 1
END
GO

GRANT EXECUTE ON [dbo].[backoffice_user_select_auth_user_relations_relationship] TO [customer_api_user]
GO
