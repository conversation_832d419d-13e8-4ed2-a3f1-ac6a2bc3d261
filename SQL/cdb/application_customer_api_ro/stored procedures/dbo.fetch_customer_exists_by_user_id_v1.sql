/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Adi <PERSON>           | 2021-04-05    | Returns true if Customer with this userId exists
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @user_id uniqueidentifier = NEWID();
EXEC [dbo].[fetch_customer_exists_by_user_id_v1] @user_id;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[fetch_customer_exists_by_user_id_v1]
    @user_id uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @customer_exists BIT = 0

    IF EXISTS ( SELECT 1 FROM dbo.auth_users WHERE UserId = @user_id AND rec_status != -1 )
    BEGIN
           SET @customer_exists = 1
    END
    ELSE
    BEGIN
           SET @customer_exists = 0
    END

    SELECT @customer_exists as customer_exists
END
GO

GRANT EXECUTE ON [dbo].[fetch_customer_exists_by_user_id_v1] TO [customer_api_user]
GO
