-------------------------------------------------------------------------------------------
-- Author               | Date          | Comment
------------------------|---------------|--------------------------------------------------
-- Nimish <PERSON>     | 2020-01-16    | Initial stored procedure
------------------------|---------------|--------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [jtb].[capi_get_rurubu_user_by_email_v1] 'tfdhtHl10aN3ghsjg2x2E1+icQaaaZqWeK+07QzHM3OK3ejiLCpF46sIHiwUIa/R9Xq4cWj8Vmzpz/KJzF9vCA=='
REVERT;
END_EXEC_TEST
*/
------------------------|---------------|--------------------------------------------------
CREATE PROCEDURE [jtb].[capi_get_rurubu_user_by_email_v1]
    @email VARCHAR(200)
AS
BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET XACT_ABORT ON
    DECLARE @error_message VARCHAR(4000)

    BEGIN TRY
        BEGIN TRANSACTION

            DECLARE @memberNumber INT = -1

            -- Find memberNumber from Auth table
            SELECT TOP 1 @memberNumber = rp.memberNumber
            FROM [JTB].RurubuProfile AS rp
            WHERE rp.mailAddress = @email
            ORDER BY rp.updateDate DESC

            -- Personal Profile
            ;WITH CTE_rp AS (
                SELECT
                    rp.id
                     , rp.memberNumber
                     , rp.lastNameKana
                     , rp.firstNameKana
                     , rp.lastName
                     , rp.firstName
                     , rp.lastNameEng
                     , rp.middleNameEng
                     , rp.firstNameEng
                     , rp.mailAddress
                     , rp.portableMailAddress
                     , rp.analysisNo
                     , rp.itemNote
                     , rp.point
                     , rp.updateDate
                     , ROW_NUMBER() OVER (
                    PARTITION BY rp.memberNumber
                    ORDER BY rp.updateDate DESC
                    ) AS row_num
                FROM [JTB].RurubuProfile AS rp
                WHERE rp.memberNumber = @memberNumber
            )

            -- Personal Auth
            , CTE_ra AS (
                SELECT
                    ra.id
                    , ra.memberNumber
                    , ra.userId
                    , ra.password
                    , ra.userStatus
                    , ra.updateDate
                    , ROW_NUMBER() OVER (
                        PARTITION BY ra.memberNumber
                        ORDER BY ra.updateDate DESC
                    ) AS row_num
                FROM [JTB].RurubuAuth AS ra
                WHERE ra.memberNumber = @memberNumber
            )

            -- Personal Salt
            , CTE_rs AS (
                SELECT
                    rs.id
                    , rs.memberNumber
                    , rs.salt
                    , rs.updateDate
                    , ROW_NUMBER() OVER (
                        PARTITION BY rs.memberNumber
                        ORDER BY rs.updateDate DESC
                    ) AS row_num
                FROM [JTB].RurubuSalt AS rs
                WHERE rs.memberNumber = @memberNumber
            )

            SELECT TOP 1
                -- Personal Profile
                CASE
                  WHEN rp.id IS NULL THEN -1
                  ELSE rp.id
                END AS profileId
                , rp.memberNumber
                , rp.lastNameKana
                , rp.firstNameKana
                , rp.lastName
                , rp.firstName
                , rp.lastNameEng
                , rp.middleNameEng
                , rp.firstNameEng
                , rp.mailAddress
                , rp.portableMailAddress
                , rp.analysisNo
                , rp.itemNote
                , rp.point
                , rp.updateDate AS profileUpdateDate
                -- Personal Auth
                , CASE
                    WHEN ra.id IS NULL THEN -1
                    ELSE ra.id
                  END AS authId
                , ra.userId
                , ra.password
                , ra.userStatus
                , ra.updateDate AS authUpdateDate
                -- Personal Salt
                , CASE
                    WHEN rs.id IS NULL THEN -1
                    ELSE rs.id
                  END AS saltId
                , rs.salt
                , rs.updateDate AS saltUpdateDate
            FROM CTE_rp AS rp
            LEFT JOIN CTE_ra AS ra
                   ON rp.memberNumber = ra.memberNumber
                  AND ra.row_num = 1
            LEFT JOIN CTE_rs AS rs
                   ON rp.memberNumber = rs.memberNumber
                  AND rs.row_num = 1
            WHERE rp.row_num = 1

        COMMIT
    END TRY

    BEGIN CATCH
        ROLLBACK TRANSACTION
        PRINT 'Cannot get Rurubu user with email ' + @email;
        THROW
    END CATCH
END
GO

GRANT EXECUTE ON [jtb].[capi_get_rurubu_user_by_email_v1] TO [customer_api_user]
GO