/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Varakorn Koschakosai     | 2020-09-24    | Read RM audit log
-- Varakorn Koschakosai     | 2020-10-07    | Remove whitelabel_id from condition
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[read_rm_audit_log_v2] 'd37dff06-e6ad-4143-85e5-334bd7c3ec91', 100
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[read_rm_audit_log_v2]
    @user_id uniqueidentifier,
    @limit int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT TOP (@limit)
        audit_logs_id,
        user_id,
        whitelabel_id,
        event_name,
        old_value,
        new_value,
        property_name,
        rec_created_by,
        rec_created_when
    FROM dbo.audit_logs_rm
    WHERE user_id = @user_id
END
GO

GRANT EXECUTE ON [dbo].[read_rm_audit_log_v2] TO [customer_api_user]
GO
