----------------------------------------------------------------------------------------------------------
-- Author	| Date		    | Comment
----------------------------------------------------------------------------------------------------------
-- Wipoo S.	| 2022-11-16	| Update deactivate id to new member id
----------------------------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC dbo.update_deactivate_to_new_member_id_v1
@ActiveId = 12944605
, @DeActiveId = 12944609
, @Approver = '803A2231-D04C-4B84-98EE-33A5B366415F'
REVERT;
END_EXEC_TEST
*/
----------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[update_deactivate_to_new_member_id_v1]
	@ActiveId		INT,
	@DeActiveId		INT,
	@Approver		uniqueidentifier
AS
BEGIN

  SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    -------------------------------------------------------------------------------------
	-- Replace Deactivate ID to New memberID in EBE_itinerary Table
	-------------------------------------------------------------------------------------
    UPDATE	dbo.ebe_itinerary
    SET	MemberId = @ActiveId,
       rec_modify_when = GETDATE(),
       rec_modify_by	= @Approver
    WHERE MemberId = @DeactiveId

END
GO

GRANT EXECUTE ON [dbo].[update_deactivate_to_new_member_id_v1] TO [customer_api_user]
GO
