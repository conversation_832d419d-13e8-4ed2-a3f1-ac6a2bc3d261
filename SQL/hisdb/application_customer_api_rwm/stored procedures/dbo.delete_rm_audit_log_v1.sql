/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Virayut S.               | 2020-11-06    | Delete audit log by userId and whiteLabelId
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[delete_rm_audit_log_v1] 'd37dff06-e6ad-4143-85e5-334bd7c3ec91', 18, 1000
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[delete_rm_audit_log_v1]
    @user_id uniqueidentifier,
    @whitelabel_id int,
    @batch_size int
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @row int = 1
    DECLARE @audit_ids TABLE (id uniqueidentifier)

    INSERT INTO @audit_ids (id)
    SELECT audit_logs_id
    FROM dbo.audit_logs_rm
    WHERE
        user_id = @user_id
    AND
        whitelabel_id = @whitelabel_id

    WHILE (@row > 0)
    BEGIN

        DELETE TOP(@batch_size) AL FROM dbo.audit_logs_rm AL
        INNER JOIN @audit_ids AI ON AI.id = AL.audit_logs_id

        SET @row = @@ROWCOUNT
    END

END
GO

GRANT EXECUTE ON [dbo].[delete_rm_audit_log_v1] TO [customer_api_user]
GO
