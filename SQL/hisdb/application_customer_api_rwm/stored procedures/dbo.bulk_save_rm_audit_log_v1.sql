/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Virayut S.               | 2020-11-03    | Bulk insert rm audit log
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @audits dbo.save_rm_audit_log_table_type
INSERT INTO @audits
(
    audit_logs_id,
    user_id,
    whitelabel_id,
    event_name,
    old_value,
    new_value,
    property_name,
    rec_created_by,
    rec_created_when
) VALUES
(
    'd37dff06-e6ad-4143-85e5-334bd7c3ec91',
    'f0d89550-8fe3-4eeb-9f2c-bc7279667e3b',
    14,
    'insert',
    'old name',
    'new name',
    'firstname',
    'b0caaa0d-2bcb-4178-9d99-b634267ae82a',
    GETDATE()
)
EXEC [dbo].[bulk_save_rm_audit_log_v1] @audits, 1000
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[bulk_save_rm_audit_log_v1]
    @audits dbo.save_rm_audit_log_table_type READONLY
    , @batch_size int
    , @delay_time varchar(8) = '00:00:05'
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    BEGIN TRY

    IF OBJECT_ID(N'Agoda_Dataupload.dbo.audit_log_migrate','U') IS NULL
    BEGIN
        CREATE TABLE Agoda_Dataupload.dbo.audit_log_migrate
        (
            [audit_logs_id]     uniqueidentifier NOT NULL
            ,[user_id]	        uniqueidentifier NOT NULL
            ,[whitelabel_id]    smallint NOT NULL
            ,[event_name]       varchar(10) NOT NULL
            ,[old_value]        varchar(255) NOT NULL
            ,[new_value]        varchar(255) NOT NULL
            ,[property_name]    varchar(100) NOT NULL
            ,[rec_created_by]   uniqueidentifier NOT NULL
            ,[rec_created_when] datetime NOT NULL
            ,[process_status]   smallint NOT NULL
        ) ON [PRIMARY]

        ALTER TABLE Agoda_Dataupload.dbo.audit_log_migrate ADD  DEFAULT ((0)) FOR [process_status]

        CREATE CLUSTERED INDEX [IX01_audit_log_migrate__audit_logs_id] ON Agoda_Dataupload.dbo.audit_log_migrate
        (
            [audit_logs_id] ASC
        ) ON [PRIMARY]

        CREATE NONCLUSTERED INDEX [IX02_audit_log_migrate__process_status] ON Agoda_Dataupload.dbo.audit_log_migrate
        (
            [process_status] ASC
        ) ON [PRIMARY]
    END

    INSERT INTO Agoda_Dataupload.dbo.audit_log_migrate
    (
        audit_logs_id
        , user_id
        , whitelabel_id
        , event_name
        , old_value
        , new_value
        , property_name
        , rec_created_by
        , rec_created_when
    )
    SELECT
        AU.audit_logs_id
        , AU.user_id
        , AU.whitelabel_id
        , AU.event_name
        , AU.old_value
        , AU.new_value
        , AU.property_name
        , AU.rec_created_by
        , AU.rec_created_when
    FROM @audits AU
    WHERE NOT EXISTS (
        SELECT 	*
        FROM 	Agoda_Dataupload.dbo.audit_log_migrate ALM
        WHERE 	AU.audit_logs_id = ALM.audit_logs_id
        AND   	process_status IN (0,1)
    )

    WHILE EXISTS (SELECT * FROM Agoda_Dataupload.dbo.audit_log_migrate WHERE process_status = 0)
    BEGIN
        BEGIN TRAN

        SELECT TOP(@batch_size)
            audit_logs_id
            , user_id
            , whitelabel_id
            , event_name
            , old_value
            , new_value
            , property_name
            , rec_created_by
            , rec_created_when
        INTO #temp
        FROM Agoda_Dataupload.dbo.audit_log_migrate
        WHERE process_status = 0
        ORDER BY audit_logs_id

        INSERT INTO dbo.audit_logs_rm
        (
            audit_logs_id
            , user_id
            , whitelabel_id
            , event_name
            , old_value
            , new_value
            , property_name
            , rec_created_by
            , rec_created_when
         )
        SELECT
            audit_logs_id
            , user_id
            , whitelabel_id
            , event_name
            , old_value
            , new_value
            , property_name
            , rec_created_by
            , rec_created_when
        FROM #temp b
        WHERE NOT EXISTS (
            SELECT * FROM dbo.audit_logs_rm AL WHERE AL.audit_logs_id = b.audit_logs_id
        )

        IF @@ROWCOUNT > @batch_size
        BEGIN
            IF @@TRANCOUNT <> 0
            BEGIN
                ROLLBACK TRANSACTION
            END

            DECLARE @err_msg nvarchar(2000) = 'Affected rows exceed the permitted row counts allowed of ' + CONVERT(nvarchar(10),@batch_size) + ' !'
            ;THROW 51000, @err_msg, 1;
        END

        UPDATE a
        SET process_status = 1
        FROM Agoda_Dataupload.dbo.audit_log_migrate AS a
        INNER JOIN #temp AS b
        ON a.audit_logs_id = b.audit_logs_id

        DROP TABLE #temp

        COMMIT TRAN;
        WAITFOR DELAY @delay_time
    END
    END TRY
    BEGIN CATCH
        DECLARE @error_message  VARCHAR(4000)
        SET @error_message = ERROR_MESSAGE();
        IF @@TRANCOUNT <> 0
        BEGIN
            ROLLBACK TRANSACTION
        END
        ;THROW 51000, @error_message, 1;-- Error number, Message text, State
    END CATCH

END
GO

GRANT EXECUTE ON [dbo].[bulk_save_rm_audit_log_v1] TO [customer_api_user]
GO
