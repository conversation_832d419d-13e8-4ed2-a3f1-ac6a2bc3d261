----------------------------------------------------------------------------------------------------------
-- Author                 | Date        | Comment
----------------------------------------------------------------------------------------------------------
-- Kumar <PERSON>            | 2017-07-20  | Create SP
-- Rati P.                | 2017-10-05  | Fix performance : change join order
-- Kittis<PERSON>   | 2018-03-13  | Make up fields name
-- Andre<PERSON> S.              | 2018-04-03  | Add TOP 20000 clause (WFAPI-1684)
----------------------------------------------------------------------------------------------------------

/*
EXEC_TEST
    EXECUTE AS LOGIN = 'customer_api_user'
    EXEC dbo.api_member_booking_select_mmb_v4 12489570
    REVERT;
END_EXEC_TEST
 */
----------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[api_member_booking_select_mmb_v4]
    @member_Id INT
AS
BEGIN
    SET XACT_ABORT ON
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

SELECT TOP 20000 EB.booking_id
            , EB.booking_date
     , EBH.checkin_date AS arrive_Date
     , EBH.checkout_date AS depart_Date
     , EBR.room_type_name
     , 0 AS credit_card_value
     , 'USD' AS currencyCode
     , EB.cancellation_policy_code
     , EB.cancellation_policy
     , EBH.hotel_id
     , PH.hotel_name
     , PH.city_id
     , @member_Id AS member_Id
     , EBH.no_of_rooms
FROM    dbo.ebe_itinerary AS EI
            INNER JOIN  dbo.ebe_booking AS EB
                        ON EI.itinerary_id = EB.itinerary_id
            INNER JOIN  dbo.ebe_booking_hotel AS EBH
                        ON EB.booking_id = EBH.booking_id
            INNER JOIN  dbo.ebe_booking_hotel_room AS EBR
                        ON EB.booking_id = EBR.booking_id
            INNER JOIN  dbo.product_hotels AS PH
                        ON EBH.hotel_id = PH.hotel_id
WHERE   EBR.room_no = 1
  AND     EBR.rec_status = 1
  AND     NOT(EB.workflow_state_id = 171)
  AND     EI.MemberId = @member_Id
END
GO

GRANT EXECUTE ON [dbo].[api_member_booking_select_mmb_v4] TO [customer_api_user]
GO
