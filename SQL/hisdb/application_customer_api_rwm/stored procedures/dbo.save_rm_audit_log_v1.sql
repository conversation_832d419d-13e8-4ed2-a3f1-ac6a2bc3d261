/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Varakorn Koschakosai     | 2020-09-24    | Insert rm audit log
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @rec_created_when DATETIME
SET @rec_created_when = GETDATE()
EXEC [dbo].[save_rm_audit_log_v1] 'd37dff06-e6ad-4143-85e5-334bd7c3ec91', 'f0d89550-8fe3-4eeb-9f2c-bc7279667e3b', 14,
    'insert', 'old name', 'new name', 'firstname', 'b0caaa0d-2bcb-4178-9d99-b634267ae82a', @rec_created_when
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[save_rm_audit_log_v1]
    @audit_logs_id      uniqueidentifier,
    @user_id            uniqueidentifier,
    @whitelabel_id      smallint,
    @event_name         varchar(10),
	@old_value          varchar(255),
	@new_value          varchar(255),
	@property_name      varchar(100),
	@rec_created_by     uniqueidentifier,
	@rec_created_when   datetime
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    INSERT INTO dbo.audit_logs_rm
    (
        audit_logs_id,
        user_id,
        whitelabel_id,
        event_name,
        old_value,
        new_value,
        property_name,
        rec_created_by,
        rec_created_when
     )
    VALUES
    (
        @audit_logs_id,
        @user_id,
        @whitelabel_id,
        @event_name,
        @old_value,
        @new_value,
        @property_name,
        @rec_created_by,
        @rec_created_when
    );

END
GO

GRANT EXECUTE ON [dbo].[save_rm_audit_log_v1] TO [customer_api_user]
GO
