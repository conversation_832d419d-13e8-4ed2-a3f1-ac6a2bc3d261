CREATE TYPE dbo.save_rm_audit_log_table_type AS TABLE
(
    audit_logs_id      uniqueidentifier,
    user_id            uniqueidentifier,
    whitelabel_id      smallint,
    event_name         varchar(10),
	old_value          varchar(255),
	new_value          varchar(255),
	property_name      varchar(100),
	rec_created_by     uniqueidentifier,
	rec_created_when   datetime
);
GRANT EXECUTE ON TYPE ::[dbo].[save_rm_audit_log_table_type] TO [customer_api_user]
