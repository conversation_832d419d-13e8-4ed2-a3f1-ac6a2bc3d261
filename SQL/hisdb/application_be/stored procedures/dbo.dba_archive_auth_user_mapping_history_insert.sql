-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-03-05	| create sp for move data from upload to agoda_archive
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_archive_auth_user_mapping_history_insert]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_archive_auth_user_mapping_history_insert]
AS
BEGIN

	
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	BEGIN TRANSACTION

			INSERT INTO [agoda_archive].[dbo].[auth_user_mapping_history] (
				[logtime]
				,[user_id]
				,[authentication_type_id]
				,[ActivityId]
				,[rec_status]
				,[rec_created_when]
				,[rec_created_by]
				,[rec_modified_when]
				,[rec_modified_by]
				,[history_id] )
			SELECT	[logtime]
				,[user_id]
				,[authentication_type_id]
				,[ActivityId]
				,[rec_status]
				,[rec_created_when]
				,[rec_created_by]
				,[rec_modified_when]
				,[rec_modified_by]
				,[history_id]
			FROM	[agoda_dataupload].[dbo].[auth_user_mapping_history_archive] A
			WHERE	NOT EXISTS (
						SELECT	[history_id]
						FROM	[agoda_archive].[dbo].[auth_user_mapping_history] B
						WHERE	A.[history_id] = B.[history_id]
						)
			ORDER BY	[history_id] ASC

			TRUNCATE TABLE [agoda_dataupload].[dbo].[auth_user_mapping_history_archive]

	COMMIT TRANSACTION
END
