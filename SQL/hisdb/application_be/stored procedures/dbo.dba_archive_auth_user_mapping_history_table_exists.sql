
-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-09-05	| create sp for check auth_user_mapping_history_archive table in dataupload
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_archive_auth_user_mapping_history_table_exists]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_archive_auth_user_mapping_history_table_exists]
AS
BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF NOT EXISTS (SELECT * FROM agoda_dataupload.sys.objects WHERE type ='U' AND name = 'auth_user_mapping_history_archive')
	BEGIN
 	
		CREATE TABLE [agoda_dataupload].[dbo].[auth_user_mapping_history_archive](
			[logtime] [datetime] NOT NULL,
			[user_id] [uniqueidentifier] NOT NULL,
			[authentication_type_id] [tinyint] NOT NULL,
			[ActivityId] [int] NOT NULL,
			[rec_status] [int] NOT NULL,
			[rec_created_when] [datetime] NOT NULL,
			[rec_created_by] [uniqueidentifier] NOT NULL,
			[rec_modified_when] [datetime] NULL,
			[rec_modified_by] [uniqueidentifier] NULL,
			[history_id] [bigint] NOT NULL,
		 CONSTRAINT [PK_auth_user_mapping_history_archive] PRIMARY KEY CLUSTERED 
		(
			[history_id] ASC
		)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
		) ON [PRIMARY]
	
	END

END
