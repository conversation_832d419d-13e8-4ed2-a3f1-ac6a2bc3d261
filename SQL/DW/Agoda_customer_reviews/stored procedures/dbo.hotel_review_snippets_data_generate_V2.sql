---------------------------------------------------------------------------------------------------
-- Author		| Date			| Comment
------------------------|-----------------------|------------------------------------------------------------------
-- RATI P.		| 2017-04-19	        | Created
-- Jantakarn A.		| 2017-08-15	        | Add more logic of hotel provider 3038 to handle limit text <= 150 characters
-- Jantakarn A.		| 2018-03-19	        | V2: Add new column (member_id)
------------------------|-----------------------|------------------------------------------------------------------
-- TEST : EXEC [dbo].[hotel_review_snippets_data_generate_V2]
---------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[hotel_review_snippets_data_generate_V2]
	@provider_list varchar(100)  = '332,3038'
	,@topN tinyint = 3


AS
BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @review_date_back_date datetime
	SET @review_date_back_date = DATEADD(day,-365,CONVERT(DATE,GETDATE()))

	/*
	Main Logic
	Agoda Reviews (332)
		LEN(review_comments) <= 150
		AND LEN(review_comments) >= 20
		AND profanity_score = 0
		AND review_date >= GETDATE() - 365
		AND rating_overall > 7
		AND rec_status = 1

	Provider Reviews (3038)
			rating_average > 8
		AND	rec_status = 1
		AND	review_date >= DATEADD(DAY,-365,GETDATE() )
		AND review_positives IS NOT NULL
	*/


	IF EXISTS (SELECT 1 FROM Agoda_dataupload.dbo.sysobjects WHERE name='DW_hotel_review_snippets_generate' AND type='U')
	BEGIN
		DROP TABLE Agoda_dataupload.dbo.DW_hotel_review_snippets_generate
	END

	CREATE TABLE Agoda_dataupload.dbo.DW_hotel_review_snippets_generate
	(
		hotel_review_id bigint NOT NULL 
		,hotel_id int NOT NULL
		,language_id int NOT NULL 
		,review_date datetime NOT NULL 
		,review_snippet nvarchar(150) NOT NULL
		,member_id int NULL
		,member_name nvarchar(200)
		,nationality_id INT NOT NULL
		,provider_id INT NOT NULL
		,data_date datetime NOT NULL
		,action_type tinyint NULL

	)

	IF EXISTS (SELECT 1 FROM Agoda_dataupload.dbo.sysobjects WHERE name='DW_hotel_review_snippets_delete' AND type='U')
	BEGIN
		DROP TABLE Agoda_dataupload.dbo.DW_hotel_review_snippets_delete
	END

	CREATE TABLE Agoda_dataupload.dbo.DW_hotel_review_snippets_delete
	(
		hotel_review_id bigint NOT NULL 

	)



	/************************************************************* Agoda Reviews (332) *********************************************************************/

	IF  EXISTS (SELECT * FROM  dbo.SPLIT_TO_INT_v2 (@provider_list,',') WHERE VAL = 332)
	BEGIN

			SELECT hotel_review_id
					,hotel_id
					,original_language
					,review_date
					,review_comments
					,rev.member_id
					,rev.member_name
					,country_id
					,provider_id
			INTO	#TEMP_agoda
			FROM	dbo.rev_hotels_v3 rev
			WHERE	rev.provider_id = 332 
			AND		rev.profanity_score = 0
			AND		rev.rating_overall > 7
			AND		rev.rec_status = 1
			AND		rev.review_date >= @review_date_back_date
			AND		LEN(review_comments) <= 150
			AND		LEN(review_comments) >= 20
	

			;WITH CTE_ROW_NUM
			AS (

				SELECT hotel_review_id
						,hotel_id
						,original_language
						,review_date
						,review_comments
						,member_id
						,member_name
						,country_id
						,provider_id
						,ROW_NUMBER() OVER(PARTITION BY hotel_id ,original_language ORDER BY review_date DESC) ROW_Num
				FROM #TEMP_agoda
				WHERE original_language > 0
				AND   country_id > 0
				AND	  member_name IS NOT NULL
				AND	  member_name <> ''			

			)

			INSERT INTO  Agoda_dataupload.dbo.DW_hotel_review_snippets_generate
			(

				hotel_review_id 
				,hotel_id 
				,language_id 
				,review_date 
				,review_snippet 
				,member_id
				,member_name
				,nationality_id 
				,provider_id 
				,data_date

			)
			SELECT  A.hotel_review_id
						,A.hotel_id
						,A.original_language
						,A.review_date
						,A.review_comments
						,A.member_id
						,ISNULL(mem.first_name,A.member_name) AS  member_name
						,A.country_id
						,A.provider_id
						,GETDATE()
			FROM	CTE_ROW_NUM A
			LEFT JOIN	dbo.rew_members mem
						ON A.member_id = mem.memberid
						AND rec_status = 1
			WHERE ROW_Num <= @topN	

			DROP TABLE #TEMP_agoda

	END


	/************************************************************* Provider Reviews (3038)*********************************************************************/


	IF  EXISTS (SELECT * FROM  dbo.SPLIT_TO_INT_v2 (@provider_list,',') WHERE VAL <> 332)
	BEGIN 
			SELECT hotel_review_id
					,hotel_id
					,original_language
					,review_date
					,review_positives
					,rev.member_id
					,rev.member_name
					,country_id
					,provider_id
			INTO	#TEMP_provider
			FROM	dbo.rev_hotels_v3 rev
			INNER JOIN dbo.review_hotel_snippet_config rc
						ON rev.original_language = rc.language_id
			WHERE	rev.provider_id IN (SELECT DISTINCT VAL FROM  dbo.SPLIT_TO_INT_v2 (@provider_list,',') WHERE VAL <> 332) 
			AND		rev.rating_average > 8
			AND		rev.rec_status = 1
			AND		rev.review_date >= @review_date_back_date
			AND		LEN(rev.review_positives) <= 150
			


			;WITH CTE_ROW_NUM
			AS (

				SELECT hotel_review_id
						,hotel_id
						,original_language
						,review_date
						,review_positives
						,member_id
						,member_name
						,country_id
						,provider_id
						,ROW_NUMBER() OVER(PARTITION BY provider_id,hotel_id ,original_language ORDER BY review_date DESC) ROW_Num
				FROM #TEMP_provider
				WHERE original_language > 0
				AND   country_id > 0
				AND	  member_name IS NOT NULL
				AND	  member_name <> ''			

			)
		
			INSERT INTO  Agoda_dataupload.dbo.DW_hotel_review_snippets_generate
			(

				hotel_review_id 
				,hotel_id 
				,language_id 
				,review_date 
				,review_snippet 
				,member_id
				,member_name
				,nationality_id 
				,provider_id 
				,data_date

			)
			SELECT  A.hotel_review_id
						,A.hotel_id
						,A.original_language
						,A.review_date
						,SUBSTRING(A.review_positives,1,150) 
						,A.member_id
						,A.member_name 
						,A.country_id
						,A.provider_id
						,GETDATE()
			FROM	CTE_ROW_NUM A
			WHERE ROW_Num <= @topN


			DROP TABLE #TEMP_provider

	END  


	/*--------- Validate data with source table ----------------------

	source table		:  Agoda_dataupload.dbo.DW_hotel_review_snippets_generate
	destination table	:  Agoda_Analytics_Upload.dbo.hotel_review_snippets

	*/

	
		-- Insert 
		UPDATE Agoda_dataupload.dbo.DW_hotel_review_snippets_generate
		SET   action_type = 1
		FROM  Agoda_dataupload.dbo.DW_hotel_review_snippets_generate s
		LEFT  JOIN Agoda_Analytics_Upload.dbo.hotel_review_snippets d
					ON s.hotel_review_id = d.hotel_review_id
		WHERE	d.hotel_review_id IS NULL

		-- Delete 
		INSERT INTO Agoda_dataupload.dbo.DW_hotel_review_snippets_delete (hotel_review_id)
		SELECT  d.hotel_review_id
		FROM  Agoda_Analytics_Upload.dbo.hotel_review_snippets d
		LEFT  JOIN Agoda_dataupload.dbo.DW_hotel_review_snippets_generate s
					ON s.hotel_review_id = d.hotel_review_id
		WHERE	s.hotel_review_id IS NULL




	------ Result --------
	-- Select count(*) FROM Agoda_dataupload.dbo.DW_hotel_review_snippets_generate (nolock) where action_type = 1
	-- Select * FROM Agoda_dataupload.dbo.DW_hotel_review_snippets_delete (nolock) 

END
