
---------------------------------------------------------------------------------------------------
-- Author		    | Date		    | Comment
----------------|-------------|----------------------------------------------------------
-- Natavit R.   | 2018-06-26  | WFAPI-1805 support phone login, remove hash out of the uri
----------------|-------------|----------------------------------------------------------
/*
EXEC_TEST

EXECUTE AS LOGIN = 'customer_api_user'
SELECT dbo.get_type_and_username_by_uri('agoda://username/hash')
REVERT;

END_EXEC_TEST
*/
------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[get_type_and_username_by_uri]
(
	@uri varchar(max)
)
RETURNS varchar(max)
AS
BEGIN
	DECLARE	@new_uri varchar(max) = @uri

	IF (LEFT(@new_uri, 8) = 'agoda://' OR LEFT(@new_uri, 8) = 'phone://')
    BEGIN
      SELECT @new_uri = SUBSTRING(@new_uri, 0, CHARINDEX('/', @new_uri, 9))
    END

	RETURN	@new_uri
END


GO

GRANT EXECUTE ON [dbo].[get_type_and_username_by_uri] TO [customer_api_user]
GO

