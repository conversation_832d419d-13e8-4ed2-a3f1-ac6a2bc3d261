
---------------------------------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|----------------------------------------------------------
-- Balazs Dombai	| 2014-07-28	|  Return the "username" part of the given security uri.
------------------------|---------------|----------------------------------------------------------
-- SELECT dbo.get_username_by_uri('agoda://username/hash')
------------------------|---------------|----------------------------------------------------------
CREATE FUNCTION [dbo].[get_username_by_uri]
(
	@uri varchar(max)
)
RETURNS varchar(max)
AS
BEGIN
	DECLARE	@userName varchar(max) = @uri

	IF	LEFT(@userName, 8) = 'agoda://' --here we have an uri; get username
	BEGIN
		SELECT @userName = SUBSTRING(@userName, 9, CHARINDEX('/', @userName, 9) - 9)
	END

	RETURN	@userName
END



GO

