
-------------------------------------------------------------------------------------------
-- Author			| Date			| Comments
--------------------|---------------|----------------------------------------------------------
-- Kanokphol T.		| 2017-02-08	| Created.
-- Payut M.			| 2017-02-20	| Update the logic to be more unique.
-------------------------------------------------------------------------------------------
/*
select dbo.generate_favorite_shared_hash(1, RAND())
select dbo.generate_favorite_shared_hash(2, RAND())
select dbo.generate_favorite_shared_hash(3, RAND())
select dbo.generate_favorite_shared_hash(4, RAND())
select dbo.generate_favorite_shared_hash(142524, RAND())
select dbo.generate_favorite_shared_hash(100000000000000000, RAND())
select dbo.generate_favorite_shared_hash(500000000000000000, RAND())
select dbo.generate_favorite_shared_hash(900000000000000000, RAND())
select dbo.generate_favorite_shared_hash(1000000000000000000, RAND())
*/
-------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[generate_favorite_shared_hash]
(
	@GroupId BIGINT,
	@Random FLOAT -- this should be RAND()
)
RETURNS VARCHAR(256)
AS
BEGIN
				   
	DECLARE @Prefix VARCHAR(3)
		, @GroupIdText VARCHAR(20)
		, @Date DATETIME
		, @TimeString VARCHAR(12)
		, @CompletedString VARCHAR(256)

	-- random 2 digits to differentiate the hash
	SET @Prefix = CAST((@Random * 100) AS INT)

	-- at least 18 digits, prefix with 0 if needed
	SET @GroupIdText = @GroupId

	IF (LEN(@GroupIdText) < 18)
		SET @GroupIdText = REPLICATE('0', 18 - LEN(@GroupIdText)) + @GroupIdText

	-- get only time HHmmss
	SET @Date = GETDATE()
	SET @TimeString = REPLACE(CONVERT(VARCHAR, @Date, 108), ':', '')

	SET @CompletedString = @Prefix + @GroupIdText + @TimeString

	DECLARE @Base62Return VARCHAR(256)

	SET @Base62Return = RTRIM(dbo.Base62Encode(@CompletedString))

	RETURN @Base62Return

END

GO

GRANT EXECUTE ON [dbo].[generate_favorite_shared_hash] TO [customer_api_user]
GO
