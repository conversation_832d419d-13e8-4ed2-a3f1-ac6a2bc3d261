-------------------------------------------------------------------------------------------
-- Author			| Date			| Comments
--------------------|---------------|----------------------------------------------------------
-- Sumeth W.		| 2017-02-08	| Created.
-------------------------------------------------------------------------------------------
/*
SELECT rtrim(dbo.Base62Encode('12301170207105902')) AS MyResult
*/
-------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[Base62Encode](@input [decimal](36, 0))
RETURNS [char](50) WITH EXECUTE AS CALLER
AS 
BEGIN

DECLARE @v_modulo INT
	, @v_temp_int decimal(38) = @input
	, @v_temp_val VARCHAR(256) = ''
	, @v_temp_char VARCHAR(1)


DECLARE @c_base62_digits VARCHAR(62) = '0123456789aAbBcCdDeEfFgGhHiIjJkKlLmMnNoOpPqQrRsStTuUvVwWxXyYzZ'; 
   
	IF ( @input = 0 )
		BEGIN
			SET @v_temp_val = '0';  
		END    
        
	WHILE ( @v_temp_int <> 0 )
		BEGIN
			SET @v_modulo = @v_temp_int % 62;  
			SET @v_temp_char = substring( @c_base62_digits, @v_modulo + 1, 1 );  
			SET @v_temp_val = @v_temp_char + @v_temp_val;   
			SET @v_temp_int = floor(@v_temp_int / 62);  
		END
    
RETURN @v_temp_val;  

END


--SELECT rtrim(dbo.Base62Encode('12301170207105902')) AS MyResult
--'xF2ZZ75Wd'

GO

GRANT EXECUTE ON [dbo].[Base62Encode] TO [customer_api_user]
GO
