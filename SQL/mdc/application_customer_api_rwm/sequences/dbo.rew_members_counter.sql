DECLARE @SERVER_PREFIX VARCHAR(2) = UPPER(LEFT(@@SERVERNAME, 2));

IF (@SERVER_PREFIX = 'HK')
BEGIN
    CREATE SEQUENCE [dbo].[rew_members_counter]
        AS [int]
        START WITH   6550000
        INCREMENT BY 1
        MINVALUE     1
        MAXVALUE     2147483600;
END
ELSE IF (@SERVER_PREFIX = 'SG')
BEGIN
    CREATE SEQUENCE [dbo].[rew_members_counter]
        AS [int]
        START WITH   4900000
        INCREMENT BY 1
        MINVALUE     1
        MAXVALUE     2147483600;
END
ELSE IF (@SERVER_PREFIX = 'SH')
BEGIN
    CREATE SEQUENCE [dbo].[rew_members_counter]
        AS [int]
        START WITH   1
        INCREMENT BY 1
        MINVALUE     1
        MAXVALUE     2147483600;
END
ELSE IF (@SERVER_PREFIX = 'AM')
BEGIN
        CREATE SEQUENCE [dbo].[rew_members_counter]
            AS [int]
            START WITH   1700000
            INCREMENT BY 1
            MINVALUE     1
            MAXVALUE     2147483600;
END
ELSE IF (@SERVER_PREFIX = 'AS')
BEGIN
        CREATE SEQUENCE [dbo].[rew_members_counter]
            AS [int]
            START WITH   1100000
            INCREMENT BY 1
            MINVALUE     1
            MAXVALUE     2147483600;
END
ELSE
BEGIN
    CREATE SEQUENCE [dbo].[rew_members_counter]
        AS [int]
        START WITH   1100000
        INCREMENT BY 1
        MINVALUE     1
        MAXVALUE     2147483600;
END
GO

GRANT UPDATE ON [dbo].[rew_members_counter] TO [customer_api_user]
GO
