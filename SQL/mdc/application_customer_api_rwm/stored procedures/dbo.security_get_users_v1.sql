---------------------------------------------------------------------------------------------------
-- Author     | Date       | Comment
--------------|------------|-----------------------------------------------------------------------
-- Andrey S.  | 2018-04-24 | Created
---------------------------------------------------------------------------------------------------
-- Test : EXEC [dbo].[security_get_users_v1] (SELECT TOP 2 UserId FROM dbo.auth_users)
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE dbo.security_get_users_v1
    @users [dbo].[uuid_table_type] READONLY
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
      UserId,
      EmailAddress,
      DisplayName,
      rec_status,
      rec_created_when,
      rec_created_by,
      rec_modified_when,
      rec_modified_by
    FROM dbo.auth_users
    WHERE UserId IN ( SELECT uuid FROM @users )
  END
GO

GRANT EXECUTE ON dbo.security_get_users_v1 TO [customer_api_user]
GO
