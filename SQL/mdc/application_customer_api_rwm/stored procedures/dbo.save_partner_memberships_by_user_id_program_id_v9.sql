/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-03-11    | Creates or Updates the record in the partner_memberships by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();
DECLARE @program_id_value int = CAST(RAND() * 2147483647 AS int);

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('program_id',CONVERT(VARBINARY(8000), CAST(@program_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@program_id_value AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('membership_id',0x, 0x)
    , ('membership_id_hash',0x, 0x)
    , ('is_preferred',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[save_partner_memberships_by_user_id_program_id_v9] @sp_request_id, @user_id_value, @program_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_partner_memberships_by_user_id_program_id_v9]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @program_id int
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_program_id int = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_membership_id_hash binary(16) = NULL;
    DECLARE @new_membership_id varbinary(8000) = NULL;
    DECLARE @new_is_preferred bit = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_partner_memberships_by_user_id_program_id_v9
         @sp_request_id
         , @user_id
         , @program_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_id = @new_user_id OUT
         , @new_program_id = @new_program_id OUT
         , @new_origin = @new_origin OUT
         , @new_membership_id = @new_membership_id OUT
         , @new_is_preferred = @new_is_preferred OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_program_id = MAX(IIF([key] = 'program_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_membership_id = MAX(IIF([key] = 'membership_id',[value], NULL))
             , @new_membership_id_hash = MAX(IIF([key] = 'membership_id_hash', [value], NULL))
             , @new_is_preferred = MAX(IIF([key] = 'is_preferred', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.partner_memberships
        (
                    user_id
                    , program_id
                    , origin
                    , membership_id
                    , membership_id_hash
                    , is_preferred
                    , rec_created_when
                    , server
                    , rec_status
        )
        OUTPUT      INSERTED.user_id
                    , INSERTED.program_id
                    , INSERTED.origin
                    , INSERTED.membership_id
                    , INSERTED.is_preferred
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
                    , INSERTED.server
        VALUES
        (
                    @new_user_id
                    , @new_program_id
                    , @new_origin
                    , @new_membership_id
                    , @new_membership_id_hash
                    , @new_is_preferred
                    , @new_rec_created_when
                    , @new_server
                    , @new_rec_status
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_id AS user_id
               , @new_program_id AS program_id
               , @new_origin AS origin
               , @new_membership_id AS membership_id
               , @new_is_preferred AS is_preferred
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_partner_memberships_by_user_id_program_id_v9] TO [customer_api_user]
GO
