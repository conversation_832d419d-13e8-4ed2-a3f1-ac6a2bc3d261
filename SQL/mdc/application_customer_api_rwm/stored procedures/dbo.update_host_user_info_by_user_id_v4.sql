/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 500

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2025-07-03    | Performs an update of record in the host_user_info by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('display_name',0x, 0x)
    , ('display_name_hash',0x, 0x)
    , ('country_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('state_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('city_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('user_description',0x, 0x)
    , ('user_description_hash',0x, 0x)
    , ('avg_response_rate',CONVERT(VARBINARY(8000), CAST(0.0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0.0 AS VARCHAR(8000))))
    , ('avg_response_time',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('photo_url',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('lastupdated_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('contact_person_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('effective_level',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('speak_languages',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('gender',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('tprm_host_type',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('tprm_questionnaire_status',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('tprm_questionnaire_changed_date',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('user_profile_type',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[update_host_user_info_by_user_id_v4] @sp_request_id, @user_id_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_host_user_info_by_user_id_v4]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_display_name varbinary(8000) = NULL OUTPUT
    , @new_country_id int = NULL OUTPUT
    , @new_state_id int = NULL OUTPUT
    , @new_city_id int = NULL OUTPUT
    , @new_user_description varbinary(8000) = NULL OUTPUT
    , @new_avg_response_rate decimal(38,35) = NULL OUTPUT
    , @new_avg_response_time int = NULL OUTPUT
    , @new_photo_url varchar(8000) = NULL OUTPUT
    , @new_lastupdated_by uniqueidentifier = NULL OUTPUT
    , @new_contact_person_id int = NULL OUTPUT
    , @new_effective_level tinyint = NULL OUTPUT
    , @new_speak_languages varchar(8000) = NULL OUTPUT
    , @new_gender varchar(8000) = NULL OUTPUT
    , @new_tprm_host_type tinyint = NULL OUTPUT
    , @new_tprm_questionnaire_status tinyint = NULL OUTPUT
    , @new_tprm_questionnaire_changed_date datetime = NULL OUTPUT
    , @new_user_profile_type tinyint = NULL OUTPUT
    , @new_origin char(2) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_display_name_hash bit = 0;
    DECLARE @new_display_name_hash binary(16) = NULL;
    DECLARE @flag_country_id bit = 0;
    DECLARE @flag_state_id bit = 0;
    DECLARE @flag_city_id bit = 0;
    DECLARE @flag_user_description_hash bit = 0;
    DECLARE @new_user_description_hash binary(16) = NULL;
    DECLARE @flag_avg_response_rate bit = 0;
    DECLARE @flag_avg_response_time bit = 0;
    DECLARE @flag_photo_url bit = 0;
    DECLARE @flag_lastupdated_by bit = 0;
    DECLARE @flag_contact_person_id bit = 0;
    DECLARE @flag_effective_level bit = 0;
    DECLARE @flag_speak_languages bit = 0;
    DECLARE @flag_gender bit = 0;
    DECLARE @flag_tprm_host_type bit = 0;
    DECLARE @flag_tprm_questionnaire_status bit = 0;
    DECLARE @flag_tprm_questionnaire_changed_date bit = 0;
    DECLARE @flag_user_profile_type bit = 0;
    DECLARE @flag_origin bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_server bit = 0;
    DECLARE @flag_rec_status bit = 0;
    SELECT   @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_display_name_hash = MAX(IIF([key] = 'display_name_hash', 1, 0))
             , @new_display_name = MAX(IIF([key] = 'display_name', [value], NULL))
             , @new_display_name_hash = MAX(IIF([key] = 'display_name_hash', [value], NULL))
             , @flag_country_id = MAX(IIF([key] = 'country_id', 1, 0))
             , @new_country_id = MAX(IIF([key] = 'country_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_state_id = MAX(IIF([key] = 'state_id', 1, 0))
             , @new_state_id = MAX(IIF([key] = 'state_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_city_id = MAX(IIF([key] = 'city_id', 1, 0))
             , @new_city_id = MAX(IIF([key] = 'city_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_user_description_hash = MAX(IIF([key] = 'user_description_hash', 1, 0))
             , @new_user_description = MAX(IIF([key] = 'user_description', [value], NULL))
             , @new_user_description_hash = MAX(IIF([key] = 'user_description_hash', [value], NULL))
             , @flag_avg_response_rate = MAX(IIF([key] = 'avg_response_rate', 1, 0))
             , @new_avg_response_rate = MAX(IIF([key] = 'avg_response_rate', CONVERT(decimal(38,35), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_avg_response_time = MAX(IIF([key] = 'avg_response_time', 1, 0))
             , @new_avg_response_time = MAX(IIF([key] = 'avg_response_time', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_photo_url = MAX(IIF([key] = 'photo_url', 1, 0))
             , @new_photo_url = MAX(IIF([key] = 'photo_url', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_lastupdated_by = MAX(IIF([key] = 'lastupdated_by', 1, 0))
             , @new_lastupdated_by = MAX(IIF([key] = 'lastupdated_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_contact_person_id = MAX(IIF([key] = 'contact_person_id', 1, 0))
             , @new_contact_person_id = MAX(IIF([key] = 'contact_person_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_effective_level = MAX(IIF([key] = 'effective_level', 1, 0))
             , @new_effective_level = MAX(IIF([key] = 'effective_level', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_speak_languages = MAX(IIF([key] = 'speak_languages', 1, 0))
             , @new_speak_languages = MAX(IIF([key] = 'speak_languages', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_gender = MAX(IIF([key] = 'gender', 1, 0))
             , @new_gender = MAX(IIF([key] = 'gender', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_tprm_host_type = MAX(IIF([key] = 'tprm_host_type', 1, 0))
             , @new_tprm_host_type = MAX(IIF([key] = 'tprm_host_type', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_tprm_questionnaire_status = MAX(IIF([key] = 'tprm_questionnaire_status', 1, 0))
             , @new_tprm_questionnaire_status = MAX(IIF([key] = 'tprm_questionnaire_status', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_tprm_questionnaire_changed_date = MAX(IIF([key] = 'tprm_questionnaire_changed_date', 1, 0))
             , @new_tprm_questionnaire_changed_date = MAX(IIF([key] = 'tprm_questionnaire_changed_date', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_user_profile_type = MAX(IIF([key] = 'user_profile_type', 1, 0))
             , @new_user_profile_type = MAX(IIF([key] = 'user_profile_type', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_origin = MAX(IIF([key] = 'origin', 1, 0))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_user_id = tbl.user_id = IIF(@flag_user_id = 1, @new_user_id, tbl.user_id)
               , @new_display_name = tbl.display_name = IIF(@flag_display_name_hash = 1, @new_display_name, tbl.display_name)
               , @new_display_name_hash = tbl.display_name_hash = CASE
                   WHEN @flag_display_name_hash = 0 THEN tbl.display_name_hash
                   WHEN @has_stale_protection = 0 THEN @new_display_name_hash
                   WHEN @new_display_name_hash = tbl.display_name_hash OR (@new_display_name_hash IS NULL AND tbl.display_name_hash IS NULL) THEN tbl.display_name_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'display_name_hash' AND (([value]) = tbl.display_name_hash OR ([value] IS NULL AND tbl.display_name_hash IS NULL))) THEN @new_display_name_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'display_name_hash' AS [column], display_name_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.display_name_hash, NULL)
               END
               , @new_country_id = tbl.country_id = CASE
                   WHEN @flag_country_id = 0 THEN tbl.country_id
                   WHEN @has_stale_protection = 0 THEN @new_country_id
                   WHEN @new_country_id = tbl.country_id OR (@new_country_id IS NULL AND tbl.country_id IS NULL) THEN tbl.country_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'country_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.country_id OR ([value] IS NULL AND tbl.country_id IS NULL))) THEN @new_country_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'country_id' AS [column], CAST(country_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.country_id, NULL)
               END
               , @new_state_id = tbl.state_id = CASE
                   WHEN @flag_state_id = 0 THEN tbl.state_id
                   WHEN @has_stale_protection = 0 THEN @new_state_id
                   WHEN @new_state_id = tbl.state_id OR (@new_state_id IS NULL AND tbl.state_id IS NULL) THEN tbl.state_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'state_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.state_id OR ([value] IS NULL AND tbl.state_id IS NULL))) THEN @new_state_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'state_id' AS [column], CAST(state_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.state_id, NULL)
               END
               , @new_city_id = tbl.city_id = CASE
                   WHEN @flag_city_id = 0 THEN tbl.city_id
                   WHEN @has_stale_protection = 0 THEN @new_city_id
                   WHEN @new_city_id = tbl.city_id OR (@new_city_id IS NULL AND tbl.city_id IS NULL) THEN tbl.city_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'city_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.city_id OR ([value] IS NULL AND tbl.city_id IS NULL))) THEN @new_city_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'city_id' AS [column], CAST(city_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.city_id, NULL)
               END
               , @new_user_description = tbl.user_description = IIF(@flag_user_description_hash = 1, @new_user_description, tbl.user_description)
               , @new_user_description_hash = tbl.user_description_hash = CASE
                   WHEN @flag_user_description_hash = 0 THEN tbl.user_description_hash
                   WHEN @has_stale_protection = 0 THEN @new_user_description_hash
                   WHEN @new_user_description_hash = tbl.user_description_hash OR (@new_user_description_hash IS NULL AND tbl.user_description_hash IS NULL) THEN tbl.user_description_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'user_description_hash' AND (([value]) = tbl.user_description_hash OR ([value] IS NULL AND tbl.user_description_hash IS NULL))) THEN @new_user_description_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'user_description_hash' AS [column], user_description_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.user_description_hash, NULL)
               END
               , @new_avg_response_rate = tbl.avg_response_rate = CASE
                   WHEN @flag_avg_response_rate = 0 THEN tbl.avg_response_rate
                   WHEN @has_stale_protection = 0 THEN @new_avg_response_rate
                   WHEN @new_avg_response_rate = tbl.avg_response_rate OR (@new_avg_response_rate IS NULL AND tbl.avg_response_rate IS NULL) THEN tbl.avg_response_rate
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'avg_response_rate' AND (CONVERT(decimal(38,35), CAST(([value]) AS VARCHAR(8000))) = tbl.avg_response_rate OR ([value] IS NULL AND tbl.avg_response_rate IS NULL))) THEN @new_avg_response_rate
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'avg_response_rate' AS [column], CAST(avg_response_rate AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.avg_response_rate, NULL)
               END
               , @new_avg_response_time = tbl.avg_response_time = CASE
                   WHEN @flag_avg_response_time = 0 THEN tbl.avg_response_time
                   WHEN @has_stale_protection = 0 THEN @new_avg_response_time
                   WHEN @new_avg_response_time = tbl.avg_response_time OR (@new_avg_response_time IS NULL AND tbl.avg_response_time IS NULL) THEN tbl.avg_response_time
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'avg_response_time' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.avg_response_time OR ([value] IS NULL AND tbl.avg_response_time IS NULL))) THEN @new_avg_response_time
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'avg_response_time' AS [column], CAST(avg_response_time AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.avg_response_time, NULL)
               END
               , @new_photo_url = tbl.photo_url = CASE
                   WHEN @flag_photo_url = 0 THEN tbl.photo_url
                   WHEN @has_stale_protection = 0 THEN @new_photo_url
                   WHEN @new_photo_url = tbl.photo_url OR (@new_photo_url IS NULL AND tbl.photo_url IS NULL) THEN tbl.photo_url
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'photo_url' AND (CAST(([value]) AS varchar(8000)) = tbl.photo_url OR ([value] IS NULL AND tbl.photo_url IS NULL))) THEN @new_photo_url
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'photo_url' AS [column], CAST(photo_url AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.photo_url, NULL)
               END
               , @new_lastupdated_by = tbl.lastupdated_by = CASE
                   WHEN @flag_lastupdated_by = 0 THEN tbl.lastupdated_by
                   WHEN @has_stale_protection = 0 THEN @new_lastupdated_by
                   WHEN @new_lastupdated_by = tbl.lastupdated_by OR (@new_lastupdated_by IS NULL AND tbl.lastupdated_by IS NULL) THEN tbl.lastupdated_by
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'lastupdated_by' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.lastupdated_by OR ([value] IS NULL AND tbl.lastupdated_by IS NULL))) THEN @new_lastupdated_by
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'lastupdated_by' AS [column], CAST(lastupdated_by AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.lastupdated_by, NULL)
               END
               , @new_contact_person_id = tbl.contact_person_id = CASE
                   WHEN @flag_contact_person_id = 0 THEN tbl.contact_person_id
                   WHEN @has_stale_protection = 0 THEN @new_contact_person_id
                   WHEN @new_contact_person_id = tbl.contact_person_id OR (@new_contact_person_id IS NULL AND tbl.contact_person_id IS NULL) THEN tbl.contact_person_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'contact_person_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.contact_person_id OR ([value] IS NULL AND tbl.contact_person_id IS NULL))) THEN @new_contact_person_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'contact_person_id' AS [column], CAST(contact_person_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.contact_person_id, NULL)
               END
               , @new_effective_level = tbl.effective_level = CASE
                   WHEN @flag_effective_level = 0 THEN tbl.effective_level
                   WHEN @has_stale_protection = 0 THEN @new_effective_level
                   WHEN @new_effective_level = tbl.effective_level OR (@new_effective_level IS NULL AND tbl.effective_level IS NULL) THEN tbl.effective_level
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'effective_level' AND (CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))) = tbl.effective_level OR ([value] IS NULL AND tbl.effective_level IS NULL))) THEN @new_effective_level
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'effective_level' AS [column], CAST(effective_level AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.effective_level, NULL)
               END
               , @new_speak_languages = tbl.speak_languages = CASE
                   WHEN @flag_speak_languages = 0 THEN tbl.speak_languages
                   WHEN @has_stale_protection = 0 THEN @new_speak_languages
                   WHEN @new_speak_languages = tbl.speak_languages OR (@new_speak_languages IS NULL AND tbl.speak_languages IS NULL) THEN tbl.speak_languages
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'speak_languages' AND (CAST(([value]) AS varchar(8000)) = tbl.speak_languages OR ([value] IS NULL AND tbl.speak_languages IS NULL))) THEN @new_speak_languages
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'speak_languages' AS [column], CAST(speak_languages AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.speak_languages, NULL)
               END
               , @new_gender = tbl.gender = CASE
                   WHEN @flag_gender = 0 THEN tbl.gender
                   WHEN @has_stale_protection = 0 THEN @new_gender
                   WHEN @new_gender = tbl.gender OR (@new_gender IS NULL AND tbl.gender IS NULL) THEN tbl.gender
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'gender' AND (CAST(([value]) AS varchar(8000)) = tbl.gender OR ([value] IS NULL AND tbl.gender IS NULL))) THEN @new_gender
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'gender' AS [column], CAST(gender AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.gender, NULL)
               END
               , @new_tprm_host_type = tbl.tprm_host_type = CASE
                   WHEN @flag_tprm_host_type = 0 THEN tbl.tprm_host_type
                   WHEN @has_stale_protection = 0 THEN @new_tprm_host_type
                   WHEN @new_tprm_host_type = tbl.tprm_host_type OR (@new_tprm_host_type IS NULL AND tbl.tprm_host_type IS NULL) THEN tbl.tprm_host_type
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'tprm_host_type' AND (CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))) = tbl.tprm_host_type OR ([value] IS NULL AND tbl.tprm_host_type IS NULL))) THEN @new_tprm_host_type
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'tprm_host_type' AS [column], CAST(tprm_host_type AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.tprm_host_type, NULL)
               END
               , @new_tprm_questionnaire_status = tbl.tprm_questionnaire_status = CASE
                   WHEN @flag_tprm_questionnaire_status = 0 THEN tbl.tprm_questionnaire_status
                   WHEN @has_stale_protection = 0 THEN @new_tprm_questionnaire_status
                   WHEN @new_tprm_questionnaire_status = tbl.tprm_questionnaire_status OR (@new_tprm_questionnaire_status IS NULL AND tbl.tprm_questionnaire_status IS NULL) THEN tbl.tprm_questionnaire_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'tprm_questionnaire_status' AND (CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))) = tbl.tprm_questionnaire_status OR ([value] IS NULL AND tbl.tprm_questionnaire_status IS NULL))) THEN @new_tprm_questionnaire_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'tprm_questionnaire_status' AS [column], CAST(tprm_questionnaire_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.tprm_questionnaire_status, NULL)
               END
               , @new_tprm_questionnaire_changed_date = tbl.tprm_questionnaire_changed_date = CASE
                   WHEN @flag_tprm_questionnaire_changed_date = 0 THEN tbl.tprm_questionnaire_changed_date
                   WHEN @has_stale_protection = 0 THEN @new_tprm_questionnaire_changed_date
                   WHEN @new_tprm_questionnaire_changed_date = tbl.tprm_questionnaire_changed_date OR (@new_tprm_questionnaire_changed_date IS NULL AND tbl.tprm_questionnaire_changed_date IS NULL) THEN tbl.tprm_questionnaire_changed_date
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'tprm_questionnaire_changed_date' AND (CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))) = tbl.tprm_questionnaire_changed_date OR ([value] IS NULL AND tbl.tprm_questionnaire_changed_date IS NULL))) THEN @new_tprm_questionnaire_changed_date
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'tprm_questionnaire_changed_date' AS [column], CAST(tprm_questionnaire_changed_date AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.tprm_questionnaire_changed_date, NULL)
               END
               , @new_user_profile_type = tbl.user_profile_type = CASE
                   WHEN @flag_user_profile_type = 0 THEN tbl.user_profile_type
                   WHEN @has_stale_protection = 0 THEN @new_user_profile_type
                   WHEN @new_user_profile_type = tbl.user_profile_type OR (@new_user_profile_type IS NULL AND tbl.user_profile_type IS NULL) THEN tbl.user_profile_type
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'user_profile_type' AND (CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))) = tbl.user_profile_type OR ([value] IS NULL AND tbl.user_profile_type IS NULL))) THEN @new_user_profile_type
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'user_profile_type' AS [column], CAST(user_profile_type AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.user_profile_type, NULL)
               END
               , @new_origin = tbl.origin = CASE
                   WHEN @flag_origin = 0 THEN tbl.origin
                   WHEN @has_stale_protection = 0 THEN @new_origin
                   WHEN @new_origin = tbl.origin OR (@new_origin IS NULL AND tbl.origin IS NULL) THEN tbl.origin
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'origin' AND (CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))) = tbl.origin OR ([value] IS NULL AND tbl.origin IS NULL))) THEN @new_origin
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'origin' AS [column], CAST(origin AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.origin, NULL)
               END
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when
        FROM   dbo.host_user_info tbl
        WHERE  tbl.user_id = @user_id;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(max);
        SELECT @expected_value = [value_long] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_host_user_info_by_user_id_v4] TO [customer_api_user]
GO
