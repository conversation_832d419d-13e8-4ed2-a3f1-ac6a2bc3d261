/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-10-27    | Creates or Updates the record in the customers_hash by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();
DECLARE @member_id_value int = CAST(RAND() * 2147483647 AS int);
DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('member_id',CONVERT(VARBINARY(8000), CAST(@member_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@member_id_value AS VARCHAR(8000))))
    , ('whitelabel_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('first_name_hash',0x, 0x)
    , ('last_name_hash',0x, 0x)
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_created_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))));

EXEC [dbo].[save_customers_hash_by_user_id_v1] @sp_request_id, @user_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customers_hash_by_user_id_v1]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_member_id int = NULL;
    DECLARE @new_whitelabel_id smallint = NULL;
    DECLARE @new_first_name_hash binary(16) = NULL;
    DECLARE @new_last_name_hash binary(16) = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_created_by uniqueidentifier = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_rec_modified_by uniqueidentifier = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_customers_hash_by_user_id_v1
         @sp_request_id
         , @user_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_id = @new_user_id OUT
         , @new_member_id = @new_member_id OUT
         , @new_whitelabel_id = @new_whitelabel_id OUT
         , @new_first_name_hash = @new_first_name_hash OUT
         , @new_last_name_hash = @new_last_name_hash OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_created_by = @new_rec_created_by OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_rec_modified_by = @new_rec_modified_by OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_member_id = MAX(IIF([key] = 'member_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_first_name_hash = MAX(IIF([key] = 'first_name_hash', ([value]), NULL))
             , @new_last_name_hash = MAX(IIF([key] = 'last_name_hash', ([value]), NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_by = MAX(IIF([key] = 'rec_created_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_modified_by = MAX(IIF([key] = 'rec_modified_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.customers_hash
        (
                    user_id
                    , member_id
                    , whitelabel_id
                    , first_name_hash
                    , last_name_hash
                    , origin
                    , rec_status
                    , rec_created_when
                    , rec_created_by
                    , rec_modified_when
                    , rec_modified_by
        )
        OUTPUT      INSERTED.user_id
                    , INSERTED.member_id
                    , INSERTED.whitelabel_id
                    , INSERTED.first_name_hash
                    , INSERTED.last_name_hash
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_created_by
                    , INSERTED.rec_modified_when
                    , INSERTED.rec_modified_by
        VALUES
        (
                    @new_user_id
                    , @new_member_id
                    , @new_whitelabel_id
                    , @new_first_name_hash
                    , @new_last_name_hash
                    , @new_origin
                    , @new_rec_status
                    , @new_rec_created_when
                    , @new_rec_created_by
                    , @new_rec_modified_when
                    , @new_rec_modified_by
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_id AS user_id
               , @new_member_id AS member_id
               , @new_whitelabel_id AS whitelabel_id
               , @new_first_name_hash AS first_name_hash
               , @new_last_name_hash AS last_name_hash
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_created_by AS rec_created_by
               , @new_rec_modified_when AS rec_modified_when
               , @new_rec_modified_by AS rec_modified_by;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_customers_hash_by_user_id_v1] TO [customer_api_user]
GO
