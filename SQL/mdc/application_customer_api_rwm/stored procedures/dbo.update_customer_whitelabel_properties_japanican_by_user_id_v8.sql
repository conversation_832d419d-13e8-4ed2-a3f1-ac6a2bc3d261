/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2020-11-20    | Performs an update of record in the customer_whitelabel_properties_japanican by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_map;
DECLARE @attributes dbo.string_map;
INSERT INTO @attributes ([key], [value]) VALUES
    ('user_id', CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('external_member_id', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('nationality_id', 0x)
    , ('nationality_id_hash', 0x)
    , ('birth_date', 0x)
    , ('birth_date_hash', 0x)
    , ('telephone_number', 0x)
    , ('telephone_number_hash', 0x)
    , ('contact_number', 0x)
    , ('contact_number_hash', 0x)
    , ('age', 0x)
    , ('age_hash', 0x)
    , ('gender', 0x)
    , ('gender_hash', 0x)
    , ('phone2', 0x)
    , ('phone2_hash', 0x)
    , ('zip_code', 0x)
    , ('zip_code_hash', 0x)
    , ('address1', 0x)
    , ('address1_hash', 0x)
    , ('address2', 0x)
    , ('address2_hash', 0x)
    , ('address3', 0x)
    , ('address3_hash', 0x)
    , ('postal_code', 0x)
    , ('postal_code_hash', 0x)
    , ('city', 0x)
    , ('city_hash', 0x)
    , ('area', 0x)
    , ('area_hash', 0x)
    , ('region', 0x)
    , ('region_hash', 0x)
    , ('state', 0x)
    , ('state_hash', 0x)
    , ('country', 0x)
    , ('country_hash', 0x)
    , ('alternative_email', 0x)
    , ('alternative_email_hash', 0x)
    , ('subscription', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('origin', CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status', CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[update_customer_whitelabel_properties_japanican_by_user_id_v8] @sp_request_id, @user_id_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_customer_whitelabel_properties_japanican_by_user_id_v8]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_map READONLY
    , @attributes dbo.string_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_external_member_id varchar(8000) = NULL OUTPUT
    , @new_nationality_id varbinary(8000) = NULL OUTPUT
    , @new_birth_date varbinary(8000) = NULL OUTPUT
    , @new_telephone_number varbinary(8000) = NULL OUTPUT
    , @new_contact_number varbinary(8000) = NULL OUTPUT
    , @new_age varbinary(8000) = NULL OUTPUT
    , @new_gender varbinary(8000) = NULL OUTPUT
    , @new_phone2 varbinary(8000) = NULL OUTPUT
    , @new_zip_code varbinary(8000) = NULL OUTPUT
    , @new_address1 varbinary(8000) = NULL OUTPUT
    , @new_address2 varbinary(8000) = NULL OUTPUT
    , @new_address3 varbinary(8000) = NULL OUTPUT
    , @new_postal_code varbinary(8000) = NULL OUTPUT
    , @new_city varbinary(8000) = NULL OUTPUT
    , @new_area varbinary(8000) = NULL OUTPUT
    , @new_region varbinary(8000) = NULL OUTPUT
    , @new_state varbinary(8000) = NULL OUTPUT
    , @new_country varbinary(8000) = NULL OUTPUT
    , @new_alternative_email varbinary(8000) = NULL OUTPUT
    , @new_subscription varchar(8000) = NULL OUTPUT
    , @new_origin char(2) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_external_member_id bit = 0;
    DECLARE @flag_nationality_id_hash bit = 0;
    DECLARE @new_nationality_id_hash binary(16) = NULL;
    DECLARE @flag_birth_date_hash bit = 0;
    DECLARE @new_birth_date_hash binary(16) = NULL;
    DECLARE @flag_telephone_number_hash bit = 0;
    DECLARE @new_telephone_number_hash binary(16) = NULL;
    DECLARE @flag_contact_number_hash bit = 0;
    DECLARE @new_contact_number_hash binary(16) = NULL;
    DECLARE @flag_age_hash bit = 0;
    DECLARE @new_age_hash binary(16) = NULL;
    DECLARE @flag_gender_hash bit = 0;
    DECLARE @new_gender_hash binary(16) = NULL;
    DECLARE @flag_phone2_hash bit = 0;
    DECLARE @new_phone2_hash binary(16) = NULL;
    DECLARE @flag_zip_code_hash bit = 0;
    DECLARE @new_zip_code_hash binary(16) = NULL;
    DECLARE @flag_address1_hash bit = 0;
    DECLARE @new_address1_hash binary(16) = NULL;
    DECLARE @flag_address2_hash bit = 0;
    DECLARE @new_address2_hash binary(16) = NULL;
    DECLARE @flag_address3_hash bit = 0;
    DECLARE @new_address3_hash binary(16) = NULL;
    DECLARE @flag_postal_code_hash bit = 0;
    DECLARE @new_postal_code_hash binary(16) = NULL;
    DECLARE @flag_city_hash bit = 0;
    DECLARE @new_city_hash binary(16) = NULL;
    DECLARE @flag_area_hash bit = 0;
    DECLARE @new_area_hash binary(16) = NULL;
    DECLARE @flag_region_hash bit = 0;
    DECLARE @new_region_hash binary(16) = NULL;
    DECLARE @flag_state_hash bit = 0;
    DECLARE @new_state_hash binary(16) = NULL;
    DECLARE @flag_country_hash bit = 0;
    DECLARE @new_country_hash binary(16) = NULL;
    DECLARE @flag_alternative_email_hash bit = 0;
    DECLARE @new_alternative_email_hash binary(16) = NULL;
    DECLARE @flag_subscription bit = 0;
    DECLARE @flag_origin bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_server bit = 0;
    DECLARE @flag_rec_status bit = 0;
    SELECT   @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_external_member_id = MAX(IIF([key] = 'external_member_id', 1, 0))
             , @new_external_member_id = MAX(IIF([key] = 'external_member_id', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_nationality_id_hash = MAX(IIF([key] = 'nationality_id_hash', 1, 0))
             , @new_nationality_id = MAX(IIF([key] = 'nationality_id', [value], NULL))
             , @new_nationality_id_hash = MAX(IIF([key] = 'nationality_id_hash', [value], NULL))
             , @flag_birth_date_hash = MAX(IIF([key] = 'birth_date_hash', 1, 0))
             , @new_birth_date = MAX(IIF([key] = 'birth_date', [value], NULL))
             , @new_birth_date_hash = MAX(IIF([key] = 'birth_date_hash', [value], NULL))
             , @flag_telephone_number_hash = MAX(IIF([key] = 'telephone_number_hash', 1, 0))
             , @new_telephone_number = MAX(IIF([key] = 'telephone_number', [value], NULL))
             , @new_telephone_number_hash = MAX(IIF([key] = 'telephone_number_hash', [value], NULL))
             , @flag_contact_number_hash = MAX(IIF([key] = 'contact_number_hash', 1, 0))
             , @new_contact_number = MAX(IIF([key] = 'contact_number', [value], NULL))
             , @new_contact_number_hash = MAX(IIF([key] = 'contact_number_hash', [value], NULL))
             , @flag_age_hash = MAX(IIF([key] = 'age_hash', 1, 0))
             , @new_age = MAX(IIF([key] = 'age', [value], NULL))
             , @new_age_hash = MAX(IIF([key] = 'age_hash', [value], NULL))
             , @flag_gender_hash = MAX(IIF([key] = 'gender_hash', 1, 0))
             , @new_gender = MAX(IIF([key] = 'gender', [value], NULL))
             , @new_gender_hash = MAX(IIF([key] = 'gender_hash', [value], NULL))
             , @flag_phone2_hash = MAX(IIF([key] = 'phone2_hash', 1, 0))
             , @new_phone2 = MAX(IIF([key] = 'phone2', [value], NULL))
             , @new_phone2_hash = MAX(IIF([key] = 'phone2_hash', [value], NULL))
             , @flag_zip_code_hash = MAX(IIF([key] = 'zip_code_hash', 1, 0))
             , @new_zip_code = MAX(IIF([key] = 'zip_code', [value], NULL))
             , @new_zip_code_hash = MAX(IIF([key] = 'zip_code_hash', [value], NULL))
             , @flag_address1_hash = MAX(IIF([key] = 'address1_hash', 1, 0))
             , @new_address1 = MAX(IIF([key] = 'address1', [value], NULL))
             , @new_address1_hash = MAX(IIF([key] = 'address1_hash', [value], NULL))
             , @flag_address2_hash = MAX(IIF([key] = 'address2_hash', 1, 0))
             , @new_address2 = MAX(IIF([key] = 'address2', [value], NULL))
             , @new_address2_hash = MAX(IIF([key] = 'address2_hash', [value], NULL))
             , @flag_address3_hash = MAX(IIF([key] = 'address3_hash', 1, 0))
             , @new_address3 = MAX(IIF([key] = 'address3', [value], NULL))
             , @new_address3_hash = MAX(IIF([key] = 'address3_hash', [value], NULL))
             , @flag_postal_code_hash = MAX(IIF([key] = 'postal_code_hash', 1, 0))
             , @new_postal_code = MAX(IIF([key] = 'postal_code', [value], NULL))
             , @new_postal_code_hash = MAX(IIF([key] = 'postal_code_hash', [value], NULL))
             , @flag_city_hash = MAX(IIF([key] = 'city_hash', 1, 0))
             , @new_city = MAX(IIF([key] = 'city', [value], NULL))
             , @new_city_hash = MAX(IIF([key] = 'city_hash', [value], NULL))
             , @flag_area_hash = MAX(IIF([key] = 'area_hash', 1, 0))
             , @new_area = MAX(IIF([key] = 'area', [value], NULL))
             , @new_area_hash = MAX(IIF([key] = 'area_hash', [value], NULL))
             , @flag_region_hash = MAX(IIF([key] = 'region_hash', 1, 0))
             , @new_region = MAX(IIF([key] = 'region', [value], NULL))
             , @new_region_hash = MAX(IIF([key] = 'region_hash', [value], NULL))
             , @flag_state_hash = MAX(IIF([key] = 'state_hash', 1, 0))
             , @new_state = MAX(IIF([key] = 'state', [value], NULL))
             , @new_state_hash = MAX(IIF([key] = 'state_hash', [value], NULL))
             , @flag_country_hash = MAX(IIF([key] = 'country_hash', 1, 0))
             , @new_country = MAX(IIF([key] = 'country', [value], NULL))
             , @new_country_hash = MAX(IIF([key] = 'country_hash', [value], NULL))
             , @flag_alternative_email_hash = MAX(IIF([key] = 'alternative_email_hash', 1, 0))
             , @new_alternative_email = MAX(IIF([key] = 'alternative_email', [value], NULL))
             , @new_alternative_email_hash = MAX(IIF([key] = 'alternative_email_hash', [value], NULL))
             , @flag_subscription = MAX(IIF([key] = 'subscription', 1, 0))
             , @new_subscription = MAX(IIF([key] = 'subscription', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_origin = MAX(IIF([key] = 'origin', 1, 0))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_user_id = tbl.user_id = IIF(@flag_user_id = 1, @new_user_id, tbl.user_id)
               , @new_external_member_id = tbl.external_member_id = CASE
                   WHEN @flag_external_member_id = 0 THEN tbl.external_member_id
                   WHEN @has_stale_protection = 0 THEN @new_external_member_id
                   WHEN @new_external_member_id = tbl.external_member_id OR (@new_external_member_id IS NULL AND tbl.external_member_id IS NULL) THEN tbl.external_member_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'external_member_id' AND (CAST(([value]) AS varchar(8000)) = tbl.external_member_id OR ([value] IS NULL AND tbl.external_member_id IS NULL))) THEN @new_external_member_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'external_member_id' AS [column], CAST(external_member_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.external_member_id, NULL)
               END
               , @new_nationality_id = tbl.nationality_id = IIF(@flag_nationality_id_hash = 1, @new_nationality_id, tbl.nationality_id)
               , @new_nationality_id_hash = tbl.nationality_id_hash = CASE
                   WHEN @flag_nationality_id_hash = 0 THEN tbl.nationality_id_hash
                   WHEN @has_stale_protection = 0 THEN @new_nationality_id_hash
                   WHEN @new_nationality_id_hash = tbl.nationality_id_hash OR (@new_nationality_id_hash IS NULL AND tbl.nationality_id_hash IS NULL) THEN tbl.nationality_id_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'nationality_id_hash' AND (([value]) = tbl.nationality_id_hash OR ([value] IS NULL AND tbl.nationality_id_hash IS NULL))) THEN @new_nationality_id_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'nationality_id_hash' AS [column], nationality_id_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.nationality_id_hash, NULL)
               END
               , @new_birth_date = tbl.birth_date = IIF(@flag_birth_date_hash = 1, @new_birth_date, tbl.birth_date)
               , @new_birth_date_hash = tbl.birth_date_hash = CASE
                   WHEN @flag_birth_date_hash = 0 THEN tbl.birth_date_hash
                   WHEN @has_stale_protection = 0 THEN @new_birth_date_hash
                   WHEN @new_birth_date_hash = tbl.birth_date_hash OR (@new_birth_date_hash IS NULL AND tbl.birth_date_hash IS NULL) THEN tbl.birth_date_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'birth_date_hash' AND (([value]) = tbl.birth_date_hash OR ([value] IS NULL AND tbl.birth_date_hash IS NULL))) THEN @new_birth_date_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'birth_date_hash' AS [column], birth_date_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.birth_date_hash, NULL)
               END
               , @new_telephone_number = tbl.telephone_number = IIF(@flag_telephone_number_hash = 1, @new_telephone_number, tbl.telephone_number)
               , @new_telephone_number_hash = tbl.telephone_number_hash = CASE
                   WHEN @flag_telephone_number_hash = 0 THEN tbl.telephone_number_hash
                   WHEN @has_stale_protection = 0 THEN @new_telephone_number_hash
                   WHEN @new_telephone_number_hash = tbl.telephone_number_hash OR (@new_telephone_number_hash IS NULL AND tbl.telephone_number_hash IS NULL) THEN tbl.telephone_number_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'telephone_number_hash' AND (([value]) = tbl.telephone_number_hash OR ([value] IS NULL AND tbl.telephone_number_hash IS NULL))) THEN @new_telephone_number_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'telephone_number_hash' AS [column], telephone_number_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.telephone_number_hash, NULL)
               END
               , @new_contact_number = tbl.contact_number = IIF(@flag_contact_number_hash = 1, @new_contact_number, tbl.contact_number)
               , @new_contact_number_hash = tbl.contact_number_hash = CASE
                   WHEN @flag_contact_number_hash = 0 THEN tbl.contact_number_hash
                   WHEN @has_stale_protection = 0 THEN @new_contact_number_hash
                   WHEN @new_contact_number_hash = tbl.contact_number_hash OR (@new_contact_number_hash IS NULL AND tbl.contact_number_hash IS NULL) THEN tbl.contact_number_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'contact_number_hash' AND (([value]) = tbl.contact_number_hash OR ([value] IS NULL AND tbl.contact_number_hash IS NULL))) THEN @new_contact_number_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'contact_number_hash' AS [column], contact_number_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.contact_number_hash, NULL)
               END
               , @new_age = tbl.age = IIF(@flag_age_hash = 1, @new_age, tbl.age)
               , @new_age_hash = tbl.age_hash = CASE
                   WHEN @flag_age_hash = 0 THEN tbl.age_hash
                   WHEN @has_stale_protection = 0 THEN @new_age_hash
                   WHEN @new_age_hash = tbl.age_hash OR (@new_age_hash IS NULL AND tbl.age_hash IS NULL) THEN tbl.age_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'age_hash' AND (([value]) = tbl.age_hash OR ([value] IS NULL AND tbl.age_hash IS NULL))) THEN @new_age_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'age_hash' AS [column], age_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.age_hash, NULL)
               END
               , @new_gender = tbl.gender = IIF(@flag_gender_hash = 1, @new_gender, tbl.gender)
               , @new_gender_hash = tbl.gender_hash = CASE
                   WHEN @flag_gender_hash = 0 THEN tbl.gender_hash
                   WHEN @has_stale_protection = 0 THEN @new_gender_hash
                   WHEN @new_gender_hash = tbl.gender_hash OR (@new_gender_hash IS NULL AND tbl.gender_hash IS NULL) THEN tbl.gender_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'gender_hash' AND (([value]) = tbl.gender_hash OR ([value] IS NULL AND tbl.gender_hash IS NULL))) THEN @new_gender_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'gender_hash' AS [column], gender_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.gender_hash, NULL)
               END
               , @new_phone2 = tbl.phone2 = IIF(@flag_phone2_hash = 1, @new_phone2, tbl.phone2)
               , @new_phone2_hash = tbl.phone2_hash = CASE
                   WHEN @flag_phone2_hash = 0 THEN tbl.phone2_hash
                   WHEN @has_stale_protection = 0 THEN @new_phone2_hash
                   WHEN @new_phone2_hash = tbl.phone2_hash OR (@new_phone2_hash IS NULL AND tbl.phone2_hash IS NULL) THEN tbl.phone2_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'phone2_hash' AND (([value]) = tbl.phone2_hash OR ([value] IS NULL AND tbl.phone2_hash IS NULL))) THEN @new_phone2_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'phone2_hash' AS [column], phone2_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.phone2_hash, NULL)
               END
               , @new_zip_code = tbl.zip_code = IIF(@flag_zip_code_hash = 1, @new_zip_code, tbl.zip_code)
               , @new_zip_code_hash = tbl.zip_code_hash = CASE
                   WHEN @flag_zip_code_hash = 0 THEN tbl.zip_code_hash
                   WHEN @has_stale_protection = 0 THEN @new_zip_code_hash
                   WHEN @new_zip_code_hash = tbl.zip_code_hash OR (@new_zip_code_hash IS NULL AND tbl.zip_code_hash IS NULL) THEN tbl.zip_code_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'zip_code_hash' AND (([value]) = tbl.zip_code_hash OR ([value] IS NULL AND tbl.zip_code_hash IS NULL))) THEN @new_zip_code_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'zip_code_hash' AS [column], zip_code_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.zip_code_hash, NULL)
               END
               , @new_address1 = tbl.address1 = IIF(@flag_address1_hash = 1, @new_address1, tbl.address1)
               , @new_address1_hash = tbl.address1_hash = CASE
                   WHEN @flag_address1_hash = 0 THEN tbl.address1_hash
                   WHEN @has_stale_protection = 0 THEN @new_address1_hash
                   WHEN @new_address1_hash = tbl.address1_hash OR (@new_address1_hash IS NULL AND tbl.address1_hash IS NULL) THEN tbl.address1_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'address1_hash' AND (([value]) = tbl.address1_hash OR ([value] IS NULL AND tbl.address1_hash IS NULL))) THEN @new_address1_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'address1_hash' AS [column], address1_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.address1_hash, NULL)
               END
               , @new_address2 = tbl.address2 = IIF(@flag_address2_hash = 1, @new_address2, tbl.address2)
               , @new_address2_hash = tbl.address2_hash = CASE
                   WHEN @flag_address2_hash = 0 THEN tbl.address2_hash
                   WHEN @has_stale_protection = 0 THEN @new_address2_hash
                   WHEN @new_address2_hash = tbl.address2_hash OR (@new_address2_hash IS NULL AND tbl.address2_hash IS NULL) THEN tbl.address2_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'address2_hash' AND (([value]) = tbl.address2_hash OR ([value] IS NULL AND tbl.address2_hash IS NULL))) THEN @new_address2_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'address2_hash' AS [column], address2_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.address2_hash, NULL)
               END
               , @new_address3 = tbl.address3 = IIF(@flag_address3_hash = 1, @new_address3, tbl.address3)
               , @new_address3_hash = tbl.address3_hash = CASE
                   WHEN @flag_address3_hash = 0 THEN tbl.address3_hash
                   WHEN @has_stale_protection = 0 THEN @new_address3_hash
                   WHEN @new_address3_hash = tbl.address3_hash OR (@new_address3_hash IS NULL AND tbl.address3_hash IS NULL) THEN tbl.address3_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'address3_hash' AND (([value]) = tbl.address3_hash OR ([value] IS NULL AND tbl.address3_hash IS NULL))) THEN @new_address3_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'address3_hash' AS [column], address3_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.address3_hash, NULL)
               END
               , @new_postal_code = tbl.postal_code = IIF(@flag_postal_code_hash = 1, @new_postal_code, tbl.postal_code)
               , @new_postal_code_hash = tbl.postal_code_hash = CASE
                   WHEN @flag_postal_code_hash = 0 THEN tbl.postal_code_hash
                   WHEN @has_stale_protection = 0 THEN @new_postal_code_hash
                   WHEN @new_postal_code_hash = tbl.postal_code_hash OR (@new_postal_code_hash IS NULL AND tbl.postal_code_hash IS NULL) THEN tbl.postal_code_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'postal_code_hash' AND (([value]) = tbl.postal_code_hash OR ([value] IS NULL AND tbl.postal_code_hash IS NULL))) THEN @new_postal_code_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'postal_code_hash' AS [column], postal_code_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.postal_code_hash, NULL)
               END
               , @new_city = tbl.city = IIF(@flag_city_hash = 1, @new_city, tbl.city)
               , @new_city_hash = tbl.city_hash = CASE
                   WHEN @flag_city_hash = 0 THEN tbl.city_hash
                   WHEN @has_stale_protection = 0 THEN @new_city_hash
                   WHEN @new_city_hash = tbl.city_hash OR (@new_city_hash IS NULL AND tbl.city_hash IS NULL) THEN tbl.city_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'city_hash' AND (([value]) = tbl.city_hash OR ([value] IS NULL AND tbl.city_hash IS NULL))) THEN @new_city_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'city_hash' AS [column], city_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.city_hash, NULL)
               END
               , @new_area = tbl.area = IIF(@flag_area_hash = 1, @new_area, tbl.area)
               , @new_area_hash = tbl.area_hash = CASE
                   WHEN @flag_area_hash = 0 THEN tbl.area_hash
                   WHEN @has_stale_protection = 0 THEN @new_area_hash
                   WHEN @new_area_hash = tbl.area_hash OR (@new_area_hash IS NULL AND tbl.area_hash IS NULL) THEN tbl.area_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'area_hash' AND (([value]) = tbl.area_hash OR ([value] IS NULL AND tbl.area_hash IS NULL))) THEN @new_area_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'area_hash' AS [column], area_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.area_hash, NULL)
               END
               , @new_region = tbl.region = IIF(@flag_region_hash = 1, @new_region, tbl.region)
               , @new_region_hash = tbl.region_hash = CASE
                   WHEN @flag_region_hash = 0 THEN tbl.region_hash
                   WHEN @has_stale_protection = 0 THEN @new_region_hash
                   WHEN @new_region_hash = tbl.region_hash OR (@new_region_hash IS NULL AND tbl.region_hash IS NULL) THEN tbl.region_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'region_hash' AND (([value]) = tbl.region_hash OR ([value] IS NULL AND tbl.region_hash IS NULL))) THEN @new_region_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'region_hash' AS [column], region_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.region_hash, NULL)
               END
               , @new_state = tbl.state = IIF(@flag_state_hash = 1, @new_state, tbl.state)
               , @new_state_hash = tbl.state_hash = CASE
                   WHEN @flag_state_hash = 0 THEN tbl.state_hash
                   WHEN @has_stale_protection = 0 THEN @new_state_hash
                   WHEN @new_state_hash = tbl.state_hash OR (@new_state_hash IS NULL AND tbl.state_hash IS NULL) THEN tbl.state_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'state_hash' AND (([value]) = tbl.state_hash OR ([value] IS NULL AND tbl.state_hash IS NULL))) THEN @new_state_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'state_hash' AS [column], state_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.state_hash, NULL)
               END
               , @new_country = tbl.country = IIF(@flag_country_hash = 1, @new_country, tbl.country)
               , @new_country_hash = tbl.country_hash = CASE
                   WHEN @flag_country_hash = 0 THEN tbl.country_hash
                   WHEN @has_stale_protection = 0 THEN @new_country_hash
                   WHEN @new_country_hash = tbl.country_hash OR (@new_country_hash IS NULL AND tbl.country_hash IS NULL) THEN tbl.country_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'country_hash' AND (([value]) = tbl.country_hash OR ([value] IS NULL AND tbl.country_hash IS NULL))) THEN @new_country_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'country_hash' AS [column], country_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.country_hash, NULL)
               END
               , @new_alternative_email = tbl.alternative_email = IIF(@flag_alternative_email_hash = 1, @new_alternative_email, tbl.alternative_email)
               , @new_alternative_email_hash = tbl.alternative_email_hash = CASE
                   WHEN @flag_alternative_email_hash = 0 THEN tbl.alternative_email_hash
                   WHEN @has_stale_protection = 0 THEN @new_alternative_email_hash
                   WHEN @new_alternative_email_hash = tbl.alternative_email_hash OR (@new_alternative_email_hash IS NULL AND tbl.alternative_email_hash IS NULL) THEN tbl.alternative_email_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'alternative_email_hash' AND (([value]) = tbl.alternative_email_hash OR ([value] IS NULL AND tbl.alternative_email_hash IS NULL))) THEN @new_alternative_email_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'alternative_email_hash' AS [column], alternative_email_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.alternative_email_hash, NULL)
               END
               , @new_subscription = tbl.subscription = CASE
                   WHEN @flag_subscription = 0 THEN tbl.subscription
                   WHEN @has_stale_protection = 0 THEN @new_subscription
                   WHEN @new_subscription = tbl.subscription OR (@new_subscription IS NULL AND tbl.subscription IS NULL) THEN tbl.subscription
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'subscription' AND (CAST(([value]) AS varchar(8000)) = tbl.subscription OR ([value] IS NULL AND tbl.subscription IS NULL))) THEN @new_subscription
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'subscription' AS [column], CAST(subscription AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.subscription, NULL)
               END
               , @new_origin = tbl.origin = CASE
                   WHEN @flag_origin = 0 THEN tbl.origin
                   WHEN @has_stale_protection = 0 THEN @new_origin
                   WHEN @new_origin = tbl.origin OR (@new_origin IS NULL AND tbl.origin IS NULL) THEN tbl.origin
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'origin' AND (CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))) = tbl.origin OR ([value] IS NULL AND tbl.origin IS NULL))) THEN @new_origin
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'origin' AS [column], CAST(origin AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.origin, NULL)
               END
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when
        FROM   dbo.customer_whitelabel_properties_japanican tbl
        WHERE  tbl.user_id = @user_id;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(8000);
        SELECT @expected_value = [value] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_customer_whitelabel_properties_japanican_by_user_id_v8] TO [customer_api_user]
GO
