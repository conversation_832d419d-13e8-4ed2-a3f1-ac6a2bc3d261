/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-03-18    | Creates or Updates the record in the host_user_info by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('display_name',0x, 0x)
    , ('display_name_hash',0x, 0x)
    , ('country_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('state_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('city_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('user_description',0x, 0x)
    , ('user_description_hash',0x, 0x)
    , ('avg_response_rate',CONVERT(VARBINARY(8000), CAST(0.0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0.0 AS VARCHAR(8000))))
    , ('avg_response_time',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('photo_url',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('lastupdated_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('contact_person_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('effective_level',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('speak_languages',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('gender',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('tprm_host_type',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('tprm_questionnaire_status',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('tprm_questionnaire_changed_date',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[save_host_user_info_by_user_id_v3] @sp_request_id, @user_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_host_user_info_by_user_id_v3]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_display_name_hash binary(16) = NULL;
    DECLARE @new_display_name varbinary(8000) = NULL;
    DECLARE @new_country_id int = NULL;
    DECLARE @new_state_id int = NULL;
    DECLARE @new_city_id int = NULL;
    DECLARE @new_user_description_hash binary(16) = NULL;
    DECLARE @new_user_description varbinary(8000) = NULL;
    DECLARE @new_avg_response_rate decimal(38,35) = NULL;
    DECLARE @new_avg_response_time int = NULL;
    DECLARE @new_photo_url varchar(8000) = NULL;
    DECLARE @new_lastupdated_by uniqueidentifier = NULL;
    DECLARE @new_contact_person_id int = NULL;
    DECLARE @new_effective_level tinyint = NULL;
    DECLARE @new_speak_languages varchar(8000) = NULL;
    DECLARE @new_gender varchar(8000) = NULL;
    DECLARE @new_tprm_host_type tinyint = NULL;
    DECLARE @new_tprm_questionnaire_status tinyint = NULL;
    DECLARE @new_tprm_questionnaire_changed_date datetime = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_host_user_info_by_user_id_v3
         @sp_request_id
         , @user_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_id = @new_user_id OUT
         , @new_display_name = @new_display_name OUT
         , @new_country_id = @new_country_id OUT
         , @new_state_id = @new_state_id OUT
         , @new_city_id = @new_city_id OUT
         , @new_user_description = @new_user_description OUT
         , @new_avg_response_rate = @new_avg_response_rate OUT
         , @new_avg_response_time = @new_avg_response_time OUT
         , @new_photo_url = @new_photo_url OUT
         , @new_lastupdated_by = @new_lastupdated_by OUT
         , @new_contact_person_id = @new_contact_person_id OUT
         , @new_effective_level = @new_effective_level OUT
         , @new_speak_languages = @new_speak_languages OUT
         , @new_gender = @new_gender OUT
         , @new_tprm_host_type = @new_tprm_host_type OUT
         , @new_tprm_questionnaire_status = @new_tprm_questionnaire_status OUT
         , @new_tprm_questionnaire_changed_date = @new_tprm_questionnaire_changed_date OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_display_name = MAX(IIF([key] = 'display_name', [value], NULL))
             , @new_display_name_hash = MAX(IIF([key] = 'display_name_hash', [value], NULL))
             , @new_country_id = MAX(IIF([key] = 'country_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_state_id = MAX(IIF([key] = 'state_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_city_id = MAX(IIF([key] = 'city_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_user_description = MAX(IIF([key] = 'user_description', [value], NULL))
             , @new_user_description_hash = MAX(IIF([key] = 'user_description_hash', [value], NULL))
             , @new_avg_response_rate = MAX(IIF([key] = 'avg_response_rate', CONVERT(decimal(38,35), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_avg_response_time = MAX(IIF([key] = 'avg_response_time', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_photo_url = MAX(IIF([key] = 'photo_url', CAST(([value]) AS varchar(8000)), NULL))
             , @new_lastupdated_by = MAX(IIF([key] = 'lastupdated_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_contact_person_id = MAX(IIF([key] = 'contact_person_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_effective_level = MAX(IIF([key] = 'effective_level', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_speak_languages = MAX(IIF([key] = 'speak_languages', CAST(([value]) AS varchar(8000)), NULL))
             , @new_gender = MAX(IIF([key] = 'gender', CAST(([value]) AS varchar(8000)), NULL))
             , @new_tprm_host_type = MAX(IIF([key] = 'tprm_host_type', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_tprm_questionnaire_status = MAX(IIF([key] = 'tprm_questionnaire_status', CONVERT(tinyint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_tprm_questionnaire_changed_date = MAX(IIF([key] = 'tprm_questionnaire_changed_date', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.host_user_info
        (
                    user_id
                    , display_name
                    , display_name_hash
                    , country_id
                    , state_id
                    , city_id
                    , user_description
                    , user_description_hash
                    , avg_response_rate
                    , avg_response_time
                    , photo_url
                    , lastupdated_by
                    , contact_person_id
                    , effective_level
                    , speak_languages
                    , gender
                    , tprm_host_type
                    , tprm_questionnaire_status
                    , tprm_questionnaire_changed_date
                    , origin
                    , rec_created_when
                    , server
                    , rec_status
        )
        OUTPUT      INSERTED.user_id
                    , INSERTED.display_name
                    , INSERTED.country_id
                    , INSERTED.state_id
                    , INSERTED.city_id
                    , INSERTED.user_description
                    , INSERTED.avg_response_rate
                    , INSERTED.avg_response_time
                    , INSERTED.photo_url
                    , INSERTED.lastupdated_by
                    , INSERTED.contact_person_id
                    , INSERTED.effective_level
                    , INSERTED.speak_languages
                    , INSERTED.gender
                    , INSERTED.tprm_host_type
                    , INSERTED.tprm_questionnaire_status
                    , INSERTED.tprm_questionnaire_changed_date
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
                    , INSERTED.server
        VALUES
        (
                    @new_user_id
                    , @new_display_name
                    , @new_display_name_hash
                    , @new_country_id
                    , @new_state_id
                    , @new_city_id
                    , @new_user_description
                    , @new_user_description_hash
                    , @new_avg_response_rate
                    , @new_avg_response_time
                    , @new_photo_url
                    , @new_lastupdated_by
                    , @new_contact_person_id
                    , @new_effective_level
                    , @new_speak_languages
                    , @new_gender
                    , @new_tprm_host_type
                    , @new_tprm_questionnaire_status
                    , @new_tprm_questionnaire_changed_date
                    , @new_origin
                    , @new_rec_created_when
                    , @new_server
                    , @new_rec_status
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_id AS user_id
               , @new_display_name AS display_name
               , @new_country_id AS country_id
               , @new_state_id AS state_id
               , @new_city_id AS city_id
               , @new_user_description AS user_description
               , @new_avg_response_rate AS avg_response_rate
               , @new_avg_response_time AS avg_response_time
               , @new_photo_url AS photo_url
               , @new_lastupdated_by AS lastupdated_by
               , @new_contact_person_id AS contact_person_id
               , @new_effective_level AS effective_level
               , @new_speak_languages AS speak_languages
               , @new_gender AS gender
               , @new_tprm_host_type AS tprm_host_type
               , @new_tprm_questionnaire_status AS tprm_questionnaire_status
               , @new_tprm_questionnaire_changed_date AS tprm_questionnaire_changed_date
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_host_user_info_by_user_id_v3] TO [customer_api_user]
GO
