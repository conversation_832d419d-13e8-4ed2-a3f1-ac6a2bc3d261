/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Varakorn Koschakosai     | 2019-12-03    | Update/insert customer's successful login timestamp
-- Varakorn Koschakosai     | 2019-12-12    | Fix the concurrency upsert issue
-- Varakorn Koschakosai     | 2020-01-24    | Refactor to upsert bulk of user login time
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
	  DECLARE @user_last_login user_last_login_time_type
	  INSERT INTO @user_last_login (uuid, ts)
	  VALUES ('97f1329c-1c8e-461c-854a-ef12cdd8b41f', GETDATE())
EXEC [dbo].[upsert_success_login_v3] @user_last_login;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[upsert_success_login_v3]
    @user_last_login dbo.user_last_login_time_type READONLY
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    UPDATE     ca
    SET        ca.last_successful_login = ul.ts,
               ca.lastupdated_when = ul.ts
    FROM       dbo.customer_additional_info AS ca
    INNER JOIN @user_last_login AS ul
    ON         ca.user_id = ul.uuid

    INSERT INTO dbo.customer_additional_info(user_id, last_successful_login, lastupdated_when)
        SELECT ul.uuid, ul.ts, ul.ts FROM @user_last_login AS ul
        WHERE NOT EXISTS(SELECT 1 FROM dbo.customer_additional_info AS ca WHERE ca.user_id = ul.uuid)

END
GO

GRANT EXECUTE ON [dbo].[upsert_success_login_v3] TO [customer_api_user]
GO

