/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-11-04    | Creates or Updates the record in the customer_verified_contacts_hash by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @contact_type_value smallint = CAST(RAND() * 32767 AS smallint);
DECLARE @contact_value_hash_value varbinary(8000) = CRYPT_GEN_RANDOM(8);
DECLARE @whitelabel_id_value smallint = CAST(RAND() * 32767 AS smallint);

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('contact_type',CONVERT(VARBINARY(8000), CAST(@contact_type_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@contact_type_value AS VARCHAR(8000))))
    , ('contact_value_hash',CONVERT(VARBINARY(8000), CAST(@contact_value_hash_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@contact_value_hash_value AS VARCHAR(8000))))
    , ('whitelabel_id',CONVERT(VARBINARY(8000), CAST(@whitelabel_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@whitelabel_id_value AS VARCHAR(8000))))
    , ('user_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))));

EXEC [dbo].[save_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1] @sp_request_id, @contact_type_value, @contact_value_hash_value, @whitelabel_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1]
    @sp_request_id uniqueidentifier
    , @contact_type smallint
    , @contact_value_hash binary(16)
    , @whitelabel_id smallint
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_contact_type smallint = NULL;
    DECLARE @new_contact_value_hash binary(16) = NULL;
    DECLARE @new_whitelabel_id smallint = NULL;
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1
         @sp_request_id
         , @contact_type
         , @contact_value_hash
         , @whitelabel_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_contact_type = @new_contact_type OUT
         , @new_contact_value_hash = @new_contact_value_hash OUT
         , @new_whitelabel_id = @new_whitelabel_id OUT
         , @new_user_id = @new_user_id OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_contact_type = MAX(IIF([key] = 'contact_type', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_contact_value_hash = MAX(IIF([key] = 'contact_value_hash', ([value]), NULL))
             , @new_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.customer_verified_contacts_hash
        (
                    contact_type
                    , contact_value_hash
                    , whitelabel_id
                    , user_id
                    , origin
                    , rec_status
                    , rec_created_when
                    , rec_modified_when
        )
        OUTPUT      INSERTED.contact_type
                    , INSERTED.contact_value_hash
                    , INSERTED.whitelabel_id
                    , INSERTED.user_id
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
        VALUES
        (
                    @new_contact_type
                    , @new_contact_value_hash
                    , @new_whitelabel_id
                    , @new_user_id
                    , @new_origin
                    , @new_rec_status
                    , @new_rec_created_when
                    , @new_rec_modified_when
        );
    END
    ELSE
    BEGIN
        SELECT @new_contact_type AS contact_type
               , @new_contact_value_hash AS contact_value_hash
               , @new_whitelabel_id AS whitelabel_id
               , @new_user_id AS user_id
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1] TO [customer_api_user]
GO
