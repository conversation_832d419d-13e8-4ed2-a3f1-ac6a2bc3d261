/*
-----------------------------------------------------------------------------------------
-- Author			| Date          |Comment
-----------------------------------------------------------------------------------------
-- Pan <PERSON> | 2019-10-27    | Upsert user devices
-- <PERSON>    | 2019-11-29    | Adds functionality to receive last login time from params
-----------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[user_devices_upsert_v4] 'f56011a4-b109-478a-92b2-805c9f82d5f2', 'e1fcb365-17a1-497b-9226-dd66ba431d97'
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[user_devices_upsert_v4]
	@user_id uniqueidentifier,
	@device_id uniqueidentifier,
	@when_last_login datetime = null
AS
BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    
	IF EXISTS
	(
		SELECT	*
		FROM	dbo.user_devices
		WHERE	user_id = @user_id
		AND	device_id = @device_id
	)
	BEGIN
		UPDATE	dbo.user_devices
		SET	rec_status = 1
			, rec_modified_by = @user_id
			, rec_modified_when = (CASE WHEN @when_last_login is NULL THEN GETDATE() ELSE @when_last_login END)
		WHERE	user_id = @user_id
		AND	device_id = @device_id
	END
	ELSE
	BEGIN
		INSERT INTO dbo.user_devices
		(user_id, device_id, rec_status, rec_created_by, rec_created_when, rec_modified_by, rec_modified_when)
		VALUES (@user_id, @device_id, 1, @user_id, GETDATE(), NULL, NULL)
	END

	SELECT	user_id
		,device_id
		,rec_status
		,rec_created_by
		,rec_created_when
		,rec_modified_by
		,rec_modified_when
	FROM	dbo.user_devices
	WHERE	user_id = @user_id
	AND	device_id = @device_id
END
GO

GRANT EXECUTE ON [dbo].[user_devices_upsert_v4] TO [customer_api_user]
GO
