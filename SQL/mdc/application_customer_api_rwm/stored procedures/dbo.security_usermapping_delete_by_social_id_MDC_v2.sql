

--------------------------------|---------------|----------------------------------------------------------
-- Author			| Date		| Comment
--------------------------------|---------------|----------------------------------------------------------
-- Yumashish Subba		| 2016-10-210	| Deletes auth_user_mapping if the auth_type_id and username is matched
-- Rati				| 2017-10-11	| support MDC
-- Siravitch Ch.		| 2018-01-26	| add xact_abort on
-- Andrew <PERSON> .		| 2018-10-15	| Add encrypted parameter
--------------------------------|---------------|----------------------------------------------------------
--	Test helper
--	SELECT TOP 100 * from dbo.auth_user_mapping;
--
--	SELECT TOP 100 P.user_id, W.authentication_type_id, P.username AS 'email_un', W.username AS 'social_un', P.rec_status AS 'email_rs', W.rec_status AS 'social_rs'
--  FROM dbo.auth_user_mapping AS P
--  JOIN (SELECT TOP 1000 * from dbo.auth_user_mapping where authentication_type_id <> 2 order by authentication_type_id desc) AS W 
--  ON W.user_id = P.user_id
--  WHERE P.authentication_type_id = 2
--
-- Below is and example of format only
-- 
--			SET	rec_status = -1
--				, rec_modified_by = @rec_modified_by
--				, rec_modified_when = GETDATE()
-- EXEC_TEST
-- EXECUTE AS LOGIN = 'customer_api_user'
-- EXEC dbo.security_usermapping_delete_by_social_id_MDC_v2 '8AA77514-959F-4E89-A91F-FFCF0BE45601','username', 'username', 4,'E6D73D17-6305-481E-A6B0-9E4FECB4904E', NULL
-- REVERT;
-- END_EXEC_TEST
--------------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[security_usermapping_delete_by_social_id_MDC_v2]
	@user_id uniqueidentifier
	, @username varchar(255)
	, @encrypted varchar(255)
	, @authentication_type_id tinyint
	, @rec_modified_by uniqueidentifier
	-- MDC
	,@history_id bigint = NULL
AS
BEGIN
  SET NOCOUNT ON
	SET XACT_ABORT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	BEGIN TRY
		BEGIN TRAN
			DECLARE @deletedRC INT = 0
			DELETE FROM	dbo.auth_user_mapping
			WHERE		([user_id] = @user_id
			OR		([username] = @username AND [user_id] <> @user_id)
			OR		([username] = @encrypted AND [user_id] <> @user_id))
			AND		[authentication_type_id] = @authentication_type_id
			AND		rec_status = 1

			SET @deletedRC = @@ROWCOUNT
			IF (@deletedRC > 0)
			BEGIN

			IF @history_id IS NULL 
			SET @history_id= NEXT VALUE FOR Agoda_Customer_API_RW.[dbo].[auth_user_mapping_history_seq]

				INSERT INTO dbo.auth_user_mapping_history
				( 	logtime
					, user_id
					, authentication_type_id
					, ActivityId
					, rec_status
					, rec_created_when
					, rec_created_by
					,history_id
				)	
				VALUES(	
					GETDATE()
					,@user_id
					,@authentication_type_id
					,12 --Remove User mapping (API)
					,1
					,GETDATE()
					,@user_id
					,@history_id
					)
				-- MDC supported 
				--SELECT @history_id as history_id
			END
			SELECT @deletedRC AS RESULT, @history_id as history_id
		COMMIT TRAN			
	END TRY
	BEGIN CATCH
		DECLARE @err_msg NVARCHAR(2048) = ERROR_MESSAGE()
		IF XACT_STATE() <> 0
		BEGIN
			ROLLBACK TRAN 	
		END
	--	RAISERROR(@err_msg, 16, 1)
	END CATCH
END





GO

GRANT EXECUTE ON [dbo].[security_usermapping_delete_by_social_id_MDC_v2] TO [customer_api_user]
GO
