/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen <PERSON>at<PERSON>           | 2019-03-15    | Performs the upsert of encrypted data with metadata into the given table.
-- Yevhen <PERSON>atulin           | 2019-06-11    | Save the whitelabel id and the hash for unverified phone number.
-- Yevhen <PERSON>           | 2019-06-27    | Fix concurrency issues by re-ordering write operations.
-- Yev<PERSON>           | 2019-07-16    | Reduce number of writes to customer_auth_mappings.
-- Yev<PERSON>           | 2019-08-28    | Add email hash for genius integration.
-- Yevhen Vatulin           | 2020-08-26    | Use hard-timeout
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @now datetime = GETDATE();
EXEC [dbo].[save_customer_v19] 1000, '00000000-0000-0000-0000-000000000001', 1, DEFAULT, 'abcde==', 0x, 0x, 0x, 'XX', 1, '00000000-0000-0000-0000-000000000000', 1, @now, @now;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customer_v19]
    @timeout_in_ms bigint,
    @user_id uniqueidentifier,
    @member_id int,
    @auth_mappings dbo.uuid_table_type READONLY,
    @encrypted_customer varchar(max),
    @fingerprint binary(16),
    @phone_hash binary(16),
    @email_hash binary(64),
    @origin char(2),
    @whitelabel_id smallint,
    @modified_by uniqueidentifier,
    @status smallint,
    @last_updated datetime,
    @rec_created_when datetime
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @set_timeout nvarchar(1000) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
    exec sp_executesql @set_timeout;

    -- Ignore duplicate writes
    IF EXISTS(SELECT id FROM dbo.customers WHERE id = @user_id AND fingerprint = @fingerprint)
    BEGIN
        RETURN;
    END;

    BEGIN TRY
        BEGIN TRANSACTION;
        DECLARE @new_mappings varchar(max) =
            (SELECT uuid AS mapping FROM @auth_mappings ORDER BY uuid FOR XML PATH(''), TYPE).value('.', 'VARCHAR(MAX)');
        DECLARE @existing_mappings varchar(max) =
            (SELECT mapping FROM dbo.customer_auth_mappings WHERE user_id = @user_id AND rec_status = 1 ORDER BY mapping FOR XML PATH(''), TYPE).value('.', 'VARCHAR(MAX)');

        IF @status = 1 AND (@existing_mappings IS NULL OR @new_mappings <> @existing_mappings)
        BEGIN
            -- For associate operations we must to remove existing mappings!
            UPDATE dbo.customer_auth_mappings
            SET    rec_status = -1
                   , rec_modified_by = @modified_by
                   , rec_modified_when = @last_updated
            WHERE  user_id = @user_id;

            DELETE      customer_mapping
            FROM        dbo.customer_auth_mappings AS customer_mapping
            INNER JOIN  @auth_mappings AS auth_mapping
            ON          customer_mapping.mapping = auth_mapping.uuid
            WHERE       customer_mapping.rec_status <> 1
            OR          customer_mapping.user_id = @user_id;

            INSERT INTO dbo.customer_auth_mappings
            (
                        mapping
                        , user_id
                        , rec_status
                        , rec_created_by
                        , rec_created_when
            )
            SELECT      uuid
                        , @user_id
                        , @status
                        , @modified_by
                        , @last_updated
            FROM        @auth_mappings;

            UPDATE dbo.genius
            SET    email_hash = @email_hash
                   , lastupdated_when = @last_updated
            WHERE  member_id = @member_id;

            IF @@ROWCOUNT = 0
            BEGIN
                INSERT INTO dbo.genius
                (
                            member_id
                            , email_hash
                            , lastupdated_when
                )
                VALUES
                (
                            @member_id
                            , @email_hash
                            , @last_updated
                );
            END;
        END

        IF @status <> 1
        BEGIN
            UPDATE dbo.customer_auth_mappings
            SET    rec_status = @status
                   , rec_modified_by = @modified_by
                   , rec_modified_when = @last_updated
            WHERE  user_id = @user_id;
        END;

        UPDATE dbo.customers
        SET    [value] = @encrypted_customer
               , fingerprint = @fingerprint
               , phone_hash = @phone_hash
               , rec_status = @status
               , rec_modified_by = @modified_by
               , rec_modified_when = @last_updated
        WHERE  id = @user_id;

        IF @@ROWCOUNT = 0
        BEGIN
            INSERT INTO dbo.customers
            (
                        id
                        , member_id
                        , [value]
                        , fingerprint
                        , phone_hash
                        , country_code
                        , whitelabel_id
                        , rec_status
                        , rec_created_by
                        , rec_created_when
            )
            VALUES
            (
                        @user_id
                        , @member_id
                        , @encrypted_customer
                        , @fingerprint
                        , @phone_hash
                        , @origin
                        , @whitelabel_id
                        , @status
                        , @modified_by
                        , @rec_created_when
            );
        END;
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        DECLARE @error_number int = ERROR_NUMBER();
        DECLARE @error_msg varchar(max) = ERROR_MESSAGE();

        IF @@TRANCOUNT > 0
        BEGIN
            ROLLBACK TRANSACTION;
        END;

        DECLARE @existing_fingerprint binary(16) = (SELECT fingerprint FROM dbo.customers WHERE id = @user_id);
        IF (@error_number = 2627 OR @error_number = 1205) AND @@NESTLEVEL < 5  -- Violates Primary Key, or Transaction was deadlocked
        BEGIN
            EXEC [dbo].[save_customer_v19] @timeout_in_ms, @user_id, @member_id, @auth_mappings, @encrypted_customer, @fingerprint,
                                           @phone_hash, @email_hash, @origin, @whitelabel_id, @modified_by,
                                           @status, @last_updated, @rec_created_when;
        END
        ELSE
        BEGIN
            DECLARE @extended_error_msg varchar(max) = CONCAT(
                'Error Code: ', @error_number, CHAR(10),
                'Error Message: ', @error_msg, CHAR(10),
                'Call Level: ', @@NESTLEVEL, CHAR(10),
                'Existing Fingerprint: ', (SELECT @existing_fingerprint FOR XML PATH(''), BINARY BASE64), CHAR(10),
                'New Fingerprint: ', (SELECT @fingerprint FOR XML PATH(''), BINARY BASE64)
            );
            THROW 50001, @extended_error_msg, 1;
        END;
    END CATCH;
END
GO

GRANT EXECUTE ON [dbo].[save_customer_v19] TO [customer_api_user]
GO
