/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2024-11-20    | Creates or Updates the record in the user_permitted_ip_range by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_permitted_ip_range_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_permitted_ip_range_id',CONVERT(VARBINARY(8000), CAST(@user_permitted_ip_range_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_permitted_ip_range_id_value AS VARCHAR(8000))))
    , ('user_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('start_ip_address',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('end_ip_address',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[save_user_permitted_ip_range_by_user_permitted_ip_range_id_v1] @sp_request_id, @user_permitted_ip_range_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_user_permitted_ip_range_by_user_permitted_ip_range_id_v1]
    @sp_request_id uniqueidentifier
    , @user_permitted_ip_range_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_permitted_ip_range_id uniqueidentifier = NULL;
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_start_ip_address varchar(15) = NULL;
    DECLARE @new_end_ip_address varchar(15) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_user_permitted_ip_range_by_user_permitted_ip_range_id_v1
         @sp_request_id
         , @user_permitted_ip_range_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_permitted_ip_range_id = @new_user_permitted_ip_range_id OUT
         , @new_user_id = @new_user_id OUT
         , @new_start_ip_address = @new_start_ip_address OUT
         , @new_end_ip_address = @new_end_ip_address OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_permitted_ip_range_id = MAX(IIF([key] = 'user_permitted_ip_range_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_start_ip_address = MAX(IIF([key] = 'start_ip_address', CAST(([value]) AS varchar(15)), NULL))
             , @new_end_ip_address = MAX(IIF([key] = 'end_ip_address', CAST(([value]) AS varchar(15)), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.user_permitted_ip_range
        (
                    user_permitted_ip_range_id
                    , user_id
                    , start_ip_address
                    , end_ip_address
                    , rec_created_when
                    , server
                    , rec_status
        )
        OUTPUT      INSERTED.user_permitted_ip_range_id
                    , INSERTED.user_id
                    , INSERTED.start_ip_address
                    , INSERTED.end_ip_address
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
                    , INSERTED.server
        VALUES
        (
                    @new_user_permitted_ip_range_id
                    , @new_user_id
                    , @new_start_ip_address
                    , @new_end_ip_address
                    , @new_rec_created_when
                    , @new_server
                    , @new_rec_status
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_permitted_ip_range_id AS user_permitted_ip_range_id
               , @new_user_id AS user_id
               , @new_start_ip_address AS start_ip_address
               , @new_end_ip_address AS end_ip_address
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_user_permitted_ip_range_by_user_permitted_ip_range_id_v1] TO [customer_api_user]
GO
