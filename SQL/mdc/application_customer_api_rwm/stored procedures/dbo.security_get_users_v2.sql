---------------------------------------------------------------------------------------------------
-- Author     | Date       | Comment
--------------|------------|-----------------------------------------------------------------------
-- <PERSON><PERSON>.  | 2018-05-10 | Add onlyActive param, by default returns only active records
-- <PERSON><PERSON>.  | 2018-04-24 | Bulk version of get auth_user by UUID
---------------------------------------------------------------------------------------------------
-- Test : EXEC [dbo].[security_get_users_v2] (SELECT TOP 2 UserId FROM dbo.auth_users)
------------------------|---------------|----------------------------------------------------------
CREATE PROCEDURE [dbo].[security_get_users_v2]
    @users [dbo].[uuid_table_type] READONLY,
    @onlyActive BIT = 1
AS
  BEGIN
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    SELECT
      UserId,
      EmailAddress,
      DisplayName,
      rec_status,
      rec_created_when,
      rec_created_by,
      rec_modified_when,
      rec_modified_by
    FROM dbo.auth_users
    WHERE UserId IN ( SELECT uuid FROM @users ) AND ( @onlyActive = 0 OR rec_status = 1 )
  END
GO

GRANT EXECUTE ON [dbo].[security_get_users_v2] TO [customer_api_user]
GO
