/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Weerapong Mua            | 2021-09-23    | init sp from save_customer_v19
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @now datetime = GETDATE();
EXEC [dbo].[save_email_genius_v1] 1, 0x, @now;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_email_genius_v1]
    @member_id int,
    @email_hash binary(64),
    @last_updated datetime
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    BEGIN TRANSACTION;

    BEGIN
        UPDATE dbo.genius
        SET    email_hash = @email_hash
               , lastupdated_when = @last_updated
        WHERE  member_id = @member_id;

        IF @@ROWCOUNT = 0
        BEGIN
            INSERT INTO dbo.genius
            (
                member_id
                , email_hash
                , lastupdated_when
            )
            VALUES
            (
                @member_id
                , @email_hash
                , @last_updated
            );
        END;
    END

    COMMIT TRANSACTION;
END
GO

GRANT EXECUTE ON [dbo].[save_email_genius_v1] TO [customer_api_user]
GO
