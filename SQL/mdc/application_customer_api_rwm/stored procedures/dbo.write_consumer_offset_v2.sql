/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2020-11-13    | Writes Kafka consumer offset for the given topic and partition.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
EXEC [dbo].[write_consumer_offset_v2] @sp_request_id, 'topic', 1, 2, 3;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[write_consumer_offset_v2]
    @sp_request_id uniqueidentifier
    , @topic varchar(255)
    , @partition int
    , @offset bigint
    , @timestamp bigint
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    UPDATE dbo.consumer_offsets
    SET    [offset] = @offset
           , [timestamp] = @timestamp
    WHERE  [topic] = @topic
    AND    [partition] = @partition;

    IF @@ROWCOUNT = 0
    BEGIN
        INSERT INTO dbo.consumer_offsets
        (
                [topic]
                , [partition]
                , [offset]
                , [timestamp]
        )
        VALUES
        (
                 @topic
                 , @partition
                 , @offset
                 , @timestamp
        );
    END;
END
GO

GRANT EXECUTE ON [dbo].[write_consumer_offset_v2] TO [customer_api_user]
GO
