/*
-----------------------------------------------------------------------------------------
-- Author		| Date          |Comment
-----------------------------------------------------------------------------------------
-- Pan Utamaphethai 	| 2019-10-1     | Select user devices
-----------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[user_devices_fetch_v1] 'f56011a4-b109-478a-92b2-805c9f82d5f2'
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[user_devices_fetch_v1]
	@user_id uniqueidentifier,
	@device_id uniqueidentifier = NULL,
	@rec_status bit = 1

AS
BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    
		SELECT	user_id
			,device_id
			,rec_status
			,rec_created_by
			,rec_created_when
			,rec_modified_by
			,rec_modified_when
		FROM	dbo.user_devices
		WHERE	user_id = @user_id
		AND	(@device_id IS NULL OR device_id = @device_id)
		AND	rec_status = @rec_status
END
GO

GRANT EXECUTE ON [dbo].[user_devices_fetch_v1] TO [customer_api_user]
GO
