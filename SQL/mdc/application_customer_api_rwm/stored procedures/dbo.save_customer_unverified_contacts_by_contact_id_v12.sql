/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-07-13    | Creates or Updates the record in the customer_unverified_contacts by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @contact_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('contact_id',CONVERT(VARBINARY(8000), CAST(@contact_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@contact_id_value AS VARCHAR(8000))))
    , ('user_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('contact_type',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('contact_value',0x, 0x)
    , ('contact_value_hash',0x, 0x)
    , ('remark',0x, 0x)
    , ('remark_hash',0x, 0x)
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[save_customer_unverified_contacts_by_contact_id_v12] @sp_request_id, @contact_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customer_unverified_contacts_by_contact_id_v12]
    @sp_request_id uniqueidentifier
    , @contact_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_contact_id uniqueidentifier = NULL;
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_contact_type smallint = NULL;
    DECLARE @new_contact_value_hash binary(16) = NULL;
    DECLARE @new_contact_value varbinary(8000) = NULL;
    DECLARE @new_remark_hash binary(16) = NULL;
    DECLARE @new_remark varbinary(8000) = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_customer_unverified_contacts_by_contact_id_v15
         @sp_request_id
         , @contact_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_contact_id = @new_contact_id OUT
         , @new_user_id = @new_user_id OUT
         , @new_contact_type = @new_contact_type OUT
         , @new_contact_value = @new_contact_value OUT
         , @new_remark = @new_remark OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_contact_id = MAX(IIF([key] = 'contact_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_contact_type = MAX(IIF([key] = 'contact_type', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_contact_value = MAX(IIF([key] = 'contact_value', [value], NULL))
             , @new_contact_value_hash = MAX(IIF([key] = 'contact_value_hash', [value], NULL))
             , @new_remark = MAX(IIF([key] = 'remark', [value], NULL))
             , @new_remark_hash = MAX(IIF([key] = 'remark_hash', [value], NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.customer_unverified_contacts
        (
                    contact_id
                    , user_id
                    , contact_type
                    , contact_value
                    , contact_value_hash
                    , remark
                    , remark_hash
                    , origin
                    , rec_status
                    , rec_created_when
                    , rec_modified_when
                    , server
        )
        OUTPUT      INSERTED.contact_id
                    , INSERTED.user_id
                    , INSERTED.contact_type
                    , INSERTED.contact_value
                    , INSERTED.remark
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
                    , INSERTED.server
        VALUES
        (
                    @new_contact_id
                    , @new_user_id
                    , @new_contact_type
                    , @new_contact_value
                    , @new_contact_value_hash
                    , @new_remark
                    , @new_remark_hash
                    , @new_origin
                    , @new_rec_status
                    , @new_rec_created_when
                    , @new_rec_modified_when
                    , @new_server
        );
    END
    ELSE
    BEGIN
        SELECT @new_contact_id AS contact_id
               , @new_user_id AS user_id
               , @new_contact_type AS contact_type
               , @new_contact_value AS contact_value
               , @new_remark AS remark
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_customer_unverified_contacts_by_contact_id_v12] TO [customer_api_user]
GO
