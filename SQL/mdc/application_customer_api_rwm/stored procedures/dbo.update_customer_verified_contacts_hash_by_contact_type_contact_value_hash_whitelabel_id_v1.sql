/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-11-04    | Performs an update of record in the customer_verified_contacts_hash by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @contact_type_value smallint = CAST(RAND() * 32767 AS smallint);
DECLARE @contact_value_hash_value binary(16) = CRYPT_GEN_RANDOM(16);
DECLARE @whitelabel_id_value smallint = CAST(RAND() * 32767 AS smallint);

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('contact_type',CONVERT(VARBINARY(8000), CAST(@contact_type_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@contact_type_value AS VARCHAR(8000))))
    , ('contact_value_hash',0x, 0x)
    , ('whitelabel_id',CONVERT(VARBINARY(8000), CAST(@whitelabel_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@whitelabel_id_value AS VARCHAR(8000))))
    , ('user_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))));

EXEC [dbo].[update_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1] @sp_request_id, @contact_type_value, @contact_value_hash_value, @whitelabel_id_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1]
    @sp_request_id uniqueidentifier
    , @contact_type smallint
    , @contact_value_hash binary(16)
    , @whitelabel_id smallint
    , @relations dbo.string_long_map READONLY
    , @attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_contact_type smallint = NULL OUTPUT
    , @new_contact_value_hash binary(16) = NULL OUTPUT
    , @new_whitelabel_id smallint = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_origin char(2) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_contact_type bit = 0;
    DECLARE @flag_contact_value_hash bit = 0;
    DECLARE @flag_whitelabel_id bit = 0;
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_origin bit = 0;
    DECLARE @flag_rec_status bit = 0;
    DECLARE @flag_rec_created_when bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    SELECT   @flag_contact_type = MAX(IIF([key] = 'contact_type', 1, 0))
             , @new_contact_type = MAX(IIF([key] = 'contact_type', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_contact_value_hash = MAX(IIF([key] = 'contact_value_hash', 1, 0))
             , @new_contact_value_hash = MAX(IIF([key] = 'contact_value_hash', ([value]), NULL))
             , @flag_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', 1, 0))
             , @new_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_origin = MAX(IIF([key] = 'origin', 1, 0))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_created_when = MAX(IIF([key] = 'rec_created_when', 1, 0))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_contact_type = tbl.contact_type = IIF(@flag_contact_type = 1, @new_contact_type, tbl.contact_type)
               , @new_contact_value_hash = tbl.contact_value_hash = IIF(@flag_contact_value_hash = 1, @new_contact_value_hash, tbl.contact_value_hash)
               , @new_whitelabel_id = tbl.whitelabel_id = IIF(@flag_whitelabel_id = 1, @new_whitelabel_id, tbl.whitelabel_id)
               , @new_user_id = tbl.user_id = CASE
                   WHEN @flag_user_id = 0 THEN tbl.user_id
                   WHEN @has_stale_protection = 0 THEN @new_user_id
                   WHEN @new_user_id = tbl.user_id OR (@new_user_id IS NULL AND tbl.user_id IS NULL) THEN tbl.user_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'user_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.user_id OR ([value] IS NULL AND tbl.user_id IS NULL))) THEN @new_user_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'user_id' AS [column], CAST(user_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.user_id, NULL)
               END
               , @new_origin = tbl.origin = CASE
                   WHEN @flag_origin = 0 THEN tbl.origin
                   WHEN @has_stale_protection = 0 THEN @new_origin
                   WHEN @new_origin = tbl.origin OR (@new_origin IS NULL AND tbl.origin IS NULL) THEN tbl.origin
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'origin' AND (CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))) = tbl.origin OR ([value] IS NULL AND tbl.origin IS NULL))) THEN @new_origin
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'origin' AS [column], CAST(origin AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.origin, NULL)
               END
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when = IIF(@flag_rec_created_when = 1, @new_rec_created_when, tbl.rec_created_when)
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
        FROM   dbo.customer_verified_contacts_hash tbl
        WHERE  tbl.contact_type = @contact_type
        AND        tbl.contact_value_hash = @contact_value_hash
        AND        tbl.whitelabel_id = @whitelabel_id;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(max);
        SELECT @expected_value = [value_long] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_customer_verified_contacts_hash_by_contact_type_contact_value_hash_whitelabel_id_v1] TO [customer_api_user]
GO
