/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 1

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2024-11-20    | Performs an update of record in the user_permitted_ip_range by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_permitted_ip_range_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_permitted_ip_range_id',CONVERT(VARBINARY(8000), CAST(@user_permitted_ip_range_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_permitted_ip_range_id_value AS VARCHAR(8000))))
    , ('user_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('start_ip_address',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('end_ip_address',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[update_user_permitted_ip_range_by_user_permitted_ip_range_id_v1] @sp_request_id, @user_permitted_ip_range_id_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_user_permitted_ip_range_by_user_permitted_ip_range_id_v1]
    @sp_request_id uniqueidentifier
    , @user_permitted_ip_range_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_user_permitted_ip_range_id uniqueidentifier = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_start_ip_address varchar(15) = NULL OUTPUT
    , @new_end_ip_address varchar(15) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_user_permitted_ip_range_id bit = 0;
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_start_ip_address bit = 0;
    DECLARE @flag_end_ip_address bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_server bit = 0;
    DECLARE @flag_rec_status bit = 0;
    SELECT   @flag_user_permitted_ip_range_id = MAX(IIF([key] = 'user_permitted_ip_range_id', 1, 0))
             , @new_user_permitted_ip_range_id = MAX(IIF([key] = 'user_permitted_ip_range_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_start_ip_address = MAX(IIF([key] = 'start_ip_address', 1, 0))
             , @new_start_ip_address = MAX(IIF([key] = 'start_ip_address', CAST(([value]) AS varchar(15)), NULL))
             , @flag_end_ip_address = MAX(IIF([key] = 'end_ip_address', 1, 0))
             , @new_end_ip_address = MAX(IIF([key] = 'end_ip_address', CAST(([value]) AS varchar(15)), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_user_permitted_ip_range_id = tbl.user_permitted_ip_range_id = IIF(@flag_user_permitted_ip_range_id = 1, @new_user_permitted_ip_range_id, tbl.user_permitted_ip_range_id)
               , @new_user_id = tbl.user_id = CASE
                   WHEN @flag_user_id = 0 THEN tbl.user_id
                   WHEN @has_stale_protection = 0 THEN @new_user_id
                   WHEN @new_user_id = tbl.user_id OR (@new_user_id IS NULL AND tbl.user_id IS NULL) THEN tbl.user_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'user_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.user_id OR ([value] IS NULL AND tbl.user_id IS NULL))) THEN @new_user_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'user_id' AS [column], CAST(user_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.user_id, NULL)
               END
               , @new_start_ip_address = tbl.start_ip_address = CASE
                   WHEN @flag_start_ip_address = 0 THEN tbl.start_ip_address
                   WHEN @has_stale_protection = 0 THEN @new_start_ip_address
                   WHEN @new_start_ip_address = tbl.start_ip_address OR (@new_start_ip_address IS NULL AND tbl.start_ip_address IS NULL) THEN tbl.start_ip_address
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'start_ip_address' AND (CAST(([value]) AS varchar(8000)) = tbl.start_ip_address OR ([value] IS NULL AND tbl.start_ip_address IS NULL))) THEN @new_start_ip_address
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'start_ip_address' AS [column], CAST(start_ip_address AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.start_ip_address, NULL)
               END
               , @new_end_ip_address = tbl.end_ip_address = CASE
                   WHEN @flag_end_ip_address = 0 THEN tbl.end_ip_address
                   WHEN @has_stale_protection = 0 THEN @new_end_ip_address
                   WHEN @new_end_ip_address = tbl.end_ip_address OR (@new_end_ip_address IS NULL AND tbl.end_ip_address IS NULL) THEN tbl.end_ip_address
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'end_ip_address' AND (CAST(([value]) AS varchar(8000)) = tbl.end_ip_address OR ([value] IS NULL AND tbl.end_ip_address IS NULL))) THEN @new_end_ip_address
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'end_ip_address' AS [column], CAST(end_ip_address AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.end_ip_address, NULL)
               END
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when
        FROM   dbo.user_permitted_ip_range tbl
        WHERE  tbl.user_permitted_ip_range_id = @user_permitted_ip_range_id;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(max);
        SELECT @expected_value = [value_long] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_user_permitted_ip_range_by_user_permitted_ip_range_id_v1] TO [customer_api_user]
GO
