/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Anurag <PERSON>         | 2019-12-19    | Update/insert user's additional information from key value pair as xml
-- Anurag <PERSON>         | 2020-03-02    | Refactored not to update ccpa_last_updated_when for repeated ccpa_opt_out update
-- Yev<PERSON>           | 2020-12-05    | CCPA logic is controlled by application
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @now datetime = GETDATE();
DECLARE @ccpa_last_updated varbinary = CONVERT(VARBINARY,GETDATE());
EXEC [dbo].[upsert_additional_info_v3] '97f1329c-1c8e-461c-854a-ef12cdd8b41f', @now,'false',@ccpa_last_updated,'1234',@now;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[upsert_additional_info_v3]
    @user_id uniqueidentifier,
    @now datetime,
    @ccpa_opt_out bit =NULL,
    @ccpa_last_updated_when varbinary(8000) = NULL,
    @tax_id varchar(max) = NULL,
    @last_successful_login datetime = NULL

AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    BEGIN TRY
        IF EXISTS(SELECT user_id FROM dbo.customer_additional_info WHERE user_id = @user_id)
        BEGIN
            UPDATE addInfo
            SET ccpa_opt_out = ISNULL(@ccpa_opt_out,addInfo.ccpa_opt_out),
            col1 = ISNULL(NULLIF(@ccpa_last_updated_when, 0x), addInfo.col1),
            last_successful_login = ISNULL(@last_successful_login,addInfo.last_successful_login),
            tax_id = ISNULL(@tax_id,addInfo.tax_id),
            lastupdated_when = @now
            FROM dbo.customer_additional_info AS addInfo
            WHERE addInfo.user_id=@user_id
        END
        ELSE
        BEGIN
            INSERT INTO dbo.customer_additional_info
            (
                user_id,ccpa_opt_out,col1,last_successful_login,tax_id,lastupdated_when
            )
            SELECT
            @user_id,
            @ccpa_opt_out,
            @ccpa_last_updated_when,
            @last_successful_login,
            @tax_id,
            @now
        END;
        END TRY
    BEGIN CATCH
        -- Ignore duplicate key errors only
        IF ERROR_NUMBER() NOT IN (2601, 2627) THROW;
        EXEC dbo.upsert_additional_info_v3 @user_id,@now,@ccpa_opt_out,@ccpa_last_updated_when,@tax_id,@last_successful_login
    END CATCH;
END;
GO
GRANT EXECUTE ON [dbo].[upsert_additional_info_v3] TO [customer_api_user]
GO