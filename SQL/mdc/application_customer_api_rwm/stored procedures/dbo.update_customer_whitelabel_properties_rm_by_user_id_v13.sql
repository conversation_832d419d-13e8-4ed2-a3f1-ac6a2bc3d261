/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-03-10    | Performs an update of record in the customer_whitelabel_properties_rm by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('add_company_profile_to_receipt',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('company',0x, 0x)
    , ('company_hash',0x, 0x)
    , ('time_zone_rm',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('default_currency_id',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('default_reward_program_id',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('default_reward_account_id',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('referrer_id',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('site_id',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('idp_user_id',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('type_rm',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('fraud_status',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('gaming_status',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('is_news_letter',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('referral_signup_reward_uuid',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('date_pushed_to_salesforce',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('promotion_signup_UUID',CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[update_customer_whitelabel_properties_rm_by_user_id_v13] @sp_request_id, @user_id_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_customer_whitelabel_properties_rm_by_user_id_v13]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_add_company_profile_to_receipt bit = NULL OUTPUT
    , @new_company varbinary(8000) = NULL OUTPUT
    , @new_time_zone_rm varchar(8000) = NULL OUTPUT
    , @new_default_currency_id varchar(8000) = NULL OUTPUT
    , @new_default_reward_program_id uniqueidentifier = NULL OUTPUT
    , @new_default_reward_account_id uniqueidentifier = NULL OUTPUT
    , @new_referrer_id uniqueidentifier = NULL OUTPUT
    , @new_site_id uniqueidentifier = NULL OUTPUT
    , @new_idp_user_id uniqueidentifier = NULL OUTPUT
    , @new_type_rm int = NULL OUTPUT
    , @new_fraud_status int = NULL OUTPUT
    , @new_gaming_status int = NULL OUTPUT
    , @new_is_news_letter bit = NULL OUTPUT
    , @new_referral_signup_reward_uuid uniqueidentifier = NULL OUTPUT
    , @new_date_pushed_to_salesforce datetime = NULL OUTPUT
    , @new_promotion_signup_UUID uniqueidentifier = NULL OUTPUT
    , @new_origin char(2) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_add_company_profile_to_receipt bit = 0;
    DECLARE @flag_company_hash bit = 0;
    DECLARE @new_company_hash binary(16) = NULL;
    DECLARE @flag_time_zone_rm bit = 0;
    DECLARE @flag_default_currency_id bit = 0;
    DECLARE @flag_default_reward_program_id bit = 0;
    DECLARE @flag_default_reward_account_id bit = 0;
    DECLARE @flag_referrer_id bit = 0;
    DECLARE @flag_site_id bit = 0;
    DECLARE @flag_idp_user_id bit = 0;
    DECLARE @flag_type_rm bit = 0;
    DECLARE @flag_fraud_status bit = 0;
    DECLARE @flag_gaming_status bit = 0;
    DECLARE @flag_is_news_letter bit = 0;
    DECLARE @flag_referral_signup_reward_uuid bit = 0;
    DECLARE @flag_date_pushed_to_salesforce bit = 0;
    DECLARE @flag_promotion_signup_UUID bit = 0;
    DECLARE @flag_origin bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_server bit = 0;
    DECLARE @flag_rec_status bit = 0;
    SELECT   @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_add_company_profile_to_receipt = MAX(IIF([key] = 'add_company_profile_to_receipt', 1, 0))
             , @new_add_company_profile_to_receipt = MAX(IIF([key] = 'add_company_profile_to_receipt', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_company_hash = MAX(IIF([key] = 'company_hash', 1, 0))
             , @new_company = MAX(IIF([key] = 'company', [value], NULL))
             , @new_company_hash = MAX(IIF([key] = 'company_hash', [value], NULL))
             , @flag_time_zone_rm = MAX(IIF([key] = 'time_zone_rm', 1, 0))
             , @new_time_zone_rm = MAX(IIF([key] = 'time_zone_rm', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_default_currency_id = MAX(IIF([key] = 'default_currency_id', 1, 0))
             , @new_default_currency_id = MAX(IIF([key] = 'default_currency_id', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_default_reward_program_id = MAX(IIF([key] = 'default_reward_program_id', 1, 0))
             , @new_default_reward_program_id = MAX(IIF([key] = 'default_reward_program_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_default_reward_account_id = MAX(IIF([key] = 'default_reward_account_id', 1, 0))
             , @new_default_reward_account_id = MAX(IIF([key] = 'default_reward_account_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_referrer_id = MAX(IIF([key] = 'referrer_id', 1, 0))
             , @new_referrer_id = MAX(IIF([key] = 'referrer_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_site_id = MAX(IIF([key] = 'site_id', 1, 0))
             , @new_site_id = MAX(IIF([key] = 'site_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_idp_user_id = MAX(IIF([key] = 'idp_user_id', 1, 0))
             , @new_idp_user_id = MAX(IIF([key] = 'idp_user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_type_rm = MAX(IIF([key] = 'type_rm', 1, 0))
             , @new_type_rm = MAX(IIF([key] = 'type_rm', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_fraud_status = MAX(IIF([key] = 'fraud_status', 1, 0))
             , @new_fraud_status = MAX(IIF([key] = 'fraud_status', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_gaming_status = MAX(IIF([key] = 'gaming_status', 1, 0))
             , @new_gaming_status = MAX(IIF([key] = 'gaming_status', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_is_news_letter = MAX(IIF([key] = 'is_news_letter', 1, 0))
             , @new_is_news_letter = MAX(IIF([key] = 'is_news_letter', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_referral_signup_reward_uuid = MAX(IIF([key] = 'referral_signup_reward_uuid', 1, 0))
             , @new_referral_signup_reward_uuid = MAX(IIF([key] = 'referral_signup_reward_uuid', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_date_pushed_to_salesforce = MAX(IIF([key] = 'date_pushed_to_salesforce', 1, 0))
             , @new_date_pushed_to_salesforce = MAX(IIF([key] = 'date_pushed_to_salesforce', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_promotion_signup_UUID = MAX(IIF([key] = 'promotion_signup_UUID', 1, 0))
             , @new_promotion_signup_UUID = MAX(IIF([key] = 'promotion_signup_UUID', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_origin = MAX(IIF([key] = 'origin', 1, 0))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_user_id = tbl.user_id = IIF(@flag_user_id = 1, @new_user_id, tbl.user_id)
               , @new_add_company_profile_to_receipt = tbl.add_company_profile_to_receipt = CASE
                   WHEN @flag_add_company_profile_to_receipt = 0 THEN tbl.add_company_profile_to_receipt
                   WHEN @has_stale_protection = 0 THEN @new_add_company_profile_to_receipt
                   WHEN @new_add_company_profile_to_receipt = tbl.add_company_profile_to_receipt OR (@new_add_company_profile_to_receipt IS NULL AND tbl.add_company_profile_to_receipt IS NULL) THEN tbl.add_company_profile_to_receipt
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'add_company_profile_to_receipt' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.add_company_profile_to_receipt OR ([value] IS NULL AND tbl.add_company_profile_to_receipt IS NULL))) THEN @new_add_company_profile_to_receipt
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'add_company_profile_to_receipt' AS [column], CAST(add_company_profile_to_receipt AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.add_company_profile_to_receipt, NULL)
               END
               , @new_company = tbl.company = IIF(@flag_company_hash = 1, @new_company, tbl.company)
               , @new_company_hash = tbl.company_hash = CASE
                   WHEN @flag_company_hash = 0 THEN tbl.company_hash
                   WHEN @has_stale_protection = 0 THEN @new_company_hash
                   WHEN @new_company_hash = tbl.company_hash OR (@new_company_hash IS NULL AND tbl.company_hash IS NULL) THEN tbl.company_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'company_hash' AND (([value]) = tbl.company_hash OR ([value] IS NULL AND tbl.company_hash IS NULL))) THEN @new_company_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'company_hash' AS [column], company_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.company_hash, NULL)
               END
               , @new_time_zone_rm = tbl.time_zone_rm = CASE
                   WHEN @flag_time_zone_rm = 0 THEN tbl.time_zone_rm
                   WHEN @has_stale_protection = 0 THEN @new_time_zone_rm
                   WHEN @new_time_zone_rm = tbl.time_zone_rm OR (@new_time_zone_rm IS NULL AND tbl.time_zone_rm IS NULL) THEN tbl.time_zone_rm
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'time_zone_rm' AND (CAST(([value]) AS varchar(8000)) = tbl.time_zone_rm OR ([value] IS NULL AND tbl.time_zone_rm IS NULL))) THEN @new_time_zone_rm
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'time_zone_rm' AS [column], CAST(time_zone_rm AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.time_zone_rm, NULL)
               END
               , @new_default_currency_id = tbl.default_currency_id = CASE
                   WHEN @flag_default_currency_id = 0 THEN tbl.default_currency_id
                   WHEN @has_stale_protection = 0 THEN @new_default_currency_id
                   WHEN @new_default_currency_id = tbl.default_currency_id OR (@new_default_currency_id IS NULL AND tbl.default_currency_id IS NULL) THEN tbl.default_currency_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'default_currency_id' AND (CAST(([value]) AS varchar(8000)) = tbl.default_currency_id OR ([value] IS NULL AND tbl.default_currency_id IS NULL))) THEN @new_default_currency_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'default_currency_id' AS [column], CAST(default_currency_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.default_currency_id, NULL)
               END
               , @new_default_reward_program_id = tbl.default_reward_program_id = CASE
                   WHEN @flag_default_reward_program_id = 0 THEN tbl.default_reward_program_id
                   WHEN @has_stale_protection = 0 THEN @new_default_reward_program_id
                   WHEN @new_default_reward_program_id = tbl.default_reward_program_id OR (@new_default_reward_program_id IS NULL AND tbl.default_reward_program_id IS NULL) THEN tbl.default_reward_program_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'default_reward_program_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.default_reward_program_id OR ([value] IS NULL AND tbl.default_reward_program_id IS NULL))) THEN @new_default_reward_program_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'default_reward_program_id' AS [column], CAST(default_reward_program_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.default_reward_program_id, NULL)
               END
               , @new_default_reward_account_id = tbl.default_reward_account_id = CASE
                   WHEN @flag_default_reward_account_id = 0 THEN tbl.default_reward_account_id
                   WHEN @has_stale_protection = 0 THEN @new_default_reward_account_id
                   WHEN @new_default_reward_account_id = tbl.default_reward_account_id OR (@new_default_reward_account_id IS NULL AND tbl.default_reward_account_id IS NULL) THEN tbl.default_reward_account_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'default_reward_account_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.default_reward_account_id OR ([value] IS NULL AND tbl.default_reward_account_id IS NULL))) THEN @new_default_reward_account_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'default_reward_account_id' AS [column], CAST(default_reward_account_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.default_reward_account_id, NULL)
               END
               , @new_referrer_id = tbl.referrer_id = CASE
                   WHEN @flag_referrer_id = 0 THEN tbl.referrer_id
                   WHEN @has_stale_protection = 0 THEN @new_referrer_id
                   WHEN @new_referrer_id = tbl.referrer_id OR (@new_referrer_id IS NULL AND tbl.referrer_id IS NULL) THEN tbl.referrer_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'referrer_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.referrer_id OR ([value] IS NULL AND tbl.referrer_id IS NULL))) THEN @new_referrer_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'referrer_id' AS [column], CAST(referrer_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.referrer_id, NULL)
               END
               , @new_site_id = tbl.site_id = CASE
                   WHEN @flag_site_id = 0 THEN tbl.site_id
                   WHEN @has_stale_protection = 0 THEN @new_site_id
                   WHEN @new_site_id = tbl.site_id OR (@new_site_id IS NULL AND tbl.site_id IS NULL) THEN tbl.site_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'site_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.site_id OR ([value] IS NULL AND tbl.site_id IS NULL))) THEN @new_site_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'site_id' AS [column], CAST(site_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.site_id, NULL)
               END
               , @new_idp_user_id = tbl.idp_user_id = CASE
                   WHEN @flag_idp_user_id = 0 THEN tbl.idp_user_id
                   WHEN @has_stale_protection = 0 THEN @new_idp_user_id
                   WHEN @new_idp_user_id = tbl.idp_user_id OR (@new_idp_user_id IS NULL AND tbl.idp_user_id IS NULL) THEN tbl.idp_user_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'idp_user_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.idp_user_id OR ([value] IS NULL AND tbl.idp_user_id IS NULL))) THEN @new_idp_user_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'idp_user_id' AS [column], CAST(idp_user_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.idp_user_id, NULL)
               END
               , @new_type_rm = tbl.type_rm = CASE
                   WHEN @flag_type_rm = 0 THEN tbl.type_rm
                   WHEN @has_stale_protection = 0 THEN @new_type_rm
                   WHEN @new_type_rm = tbl.type_rm OR (@new_type_rm IS NULL AND tbl.type_rm IS NULL) THEN tbl.type_rm
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'type_rm' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.type_rm OR ([value] IS NULL AND tbl.type_rm IS NULL))) THEN @new_type_rm
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'type_rm' AS [column], CAST(type_rm AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.type_rm, NULL)
               END
               , @new_fraud_status = tbl.fraud_status = CASE
                   WHEN @flag_fraud_status = 0 THEN tbl.fraud_status
                   WHEN @has_stale_protection = 0 THEN @new_fraud_status
                   WHEN @new_fraud_status = tbl.fraud_status OR (@new_fraud_status IS NULL AND tbl.fraud_status IS NULL) THEN tbl.fraud_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'fraud_status' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.fraud_status OR ([value] IS NULL AND tbl.fraud_status IS NULL))) THEN @new_fraud_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'fraud_status' AS [column], CAST(fraud_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.fraud_status, NULL)
               END
               , @new_gaming_status = tbl.gaming_status = CASE
                   WHEN @flag_gaming_status = 0 THEN tbl.gaming_status
                   WHEN @has_stale_protection = 0 THEN @new_gaming_status
                   WHEN @new_gaming_status = tbl.gaming_status OR (@new_gaming_status IS NULL AND tbl.gaming_status IS NULL) THEN tbl.gaming_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'gaming_status' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.gaming_status OR ([value] IS NULL AND tbl.gaming_status IS NULL))) THEN @new_gaming_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'gaming_status' AS [column], CAST(gaming_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.gaming_status, NULL)
               END
               , @new_is_news_letter = tbl.is_news_letter = CASE
                   WHEN @flag_is_news_letter = 0 THEN tbl.is_news_letter
                   WHEN @has_stale_protection = 0 THEN @new_is_news_letter
                   WHEN @new_is_news_letter = tbl.is_news_letter OR (@new_is_news_letter IS NULL AND tbl.is_news_letter IS NULL) THEN tbl.is_news_letter
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'is_news_letter' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.is_news_letter OR ([value] IS NULL AND tbl.is_news_letter IS NULL))) THEN @new_is_news_letter
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'is_news_letter' AS [column], CAST(is_news_letter AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.is_news_letter, NULL)
               END
               , @new_referral_signup_reward_uuid = tbl.referral_signup_reward_uuid = CASE
                   WHEN @flag_referral_signup_reward_uuid = 0 THEN tbl.referral_signup_reward_uuid
                   WHEN @has_stale_protection = 0 THEN @new_referral_signup_reward_uuid
                   WHEN @new_referral_signup_reward_uuid = tbl.referral_signup_reward_uuid OR (@new_referral_signup_reward_uuid IS NULL AND tbl.referral_signup_reward_uuid IS NULL) THEN tbl.referral_signup_reward_uuid
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'referral_signup_reward_uuid' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.referral_signup_reward_uuid OR ([value] IS NULL AND tbl.referral_signup_reward_uuid IS NULL))) THEN @new_referral_signup_reward_uuid
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'referral_signup_reward_uuid' AS [column], CAST(referral_signup_reward_uuid AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.referral_signup_reward_uuid, NULL)
               END
               , @new_date_pushed_to_salesforce = tbl.date_pushed_to_salesforce = CASE
                   WHEN @flag_date_pushed_to_salesforce = 0 THEN tbl.date_pushed_to_salesforce
                   WHEN @has_stale_protection = 0 THEN @new_date_pushed_to_salesforce
                   WHEN @new_date_pushed_to_salesforce = tbl.date_pushed_to_salesforce OR (@new_date_pushed_to_salesforce IS NULL AND tbl.date_pushed_to_salesforce IS NULL) THEN tbl.date_pushed_to_salesforce
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'date_pushed_to_salesforce' AND (CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))) = tbl.date_pushed_to_salesforce OR ([value] IS NULL AND tbl.date_pushed_to_salesforce IS NULL))) THEN @new_date_pushed_to_salesforce
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'date_pushed_to_salesforce' AS [column], CAST(date_pushed_to_salesforce AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.date_pushed_to_salesforce, NULL)
               END
               , @new_promotion_signup_UUID = tbl.promotion_signup_UUID = CASE
                   WHEN @flag_promotion_signup_UUID = 0 THEN tbl.promotion_signup_UUID
                   WHEN @has_stale_protection = 0 THEN @new_promotion_signup_UUID
                   WHEN @new_promotion_signup_UUID = tbl.promotion_signup_UUID OR (@new_promotion_signup_UUID IS NULL AND tbl.promotion_signup_UUID IS NULL) THEN tbl.promotion_signup_UUID
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'promotion_signup_UUID' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.promotion_signup_UUID OR ([value] IS NULL AND tbl.promotion_signup_UUID IS NULL))) THEN @new_promotion_signup_UUID
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'promotion_signup_UUID' AS [column], CAST(promotion_signup_UUID AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.promotion_signup_UUID, NULL)
               END
               , @new_origin = tbl.origin = CASE
                   WHEN @flag_origin = 0 THEN tbl.origin
                   WHEN @has_stale_protection = 0 THEN @new_origin
                   WHEN @new_origin = tbl.origin OR (@new_origin IS NULL AND tbl.origin IS NULL) THEN tbl.origin
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'origin' AND (CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))) = tbl.origin OR ([value] IS NULL AND tbl.origin IS NULL))) THEN @new_origin
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'origin' AS [column], CAST(origin AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.origin, NULL)
               END
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when
        FROM   dbo.customer_whitelabel_properties_rm tbl
        WHERE  tbl.user_id = @user_id;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(max);
        SELECT @expected_value = [value_long] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_customer_whitelabel_properties_rm_by_user_id_v13] TO [customer_api_user]
GO
