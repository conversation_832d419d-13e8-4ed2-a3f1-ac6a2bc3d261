-----------------------------------------------------------------------------------------
-- Author		| Date          | Comment
-----------------------------------------------------------------------------------------
-- Than<PERSON><PERSON>	| 2019-10-28    | Inactive user device, set rec_status = 0
-- Than<PERSON>on <PERSON>d<PERSON>ruer	| 2019-11-07    | Change parameter to list, set rec_status = -1
-----------------------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE	@device_id_list AS dbo.uuid_table_type
INSERT INTO @device_id_list VALUES ('e1fcb365-17a1-497b-9226-dd66ba431d97')
EXEC [dbo].[user_devices_delete_v2] 'f56011a4-b109-478a-92b2-805c9f82d5f2', @device_id_list
REVERT;
END_EXEC_TEST
*/
-----------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[user_devices_delete_v2]
	@user_id	uniqueidentifier,
	@device_id_list dbo.uuid_table_type READONLY
AS
BEGIN

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	UPDATE	dbo.user_devices
	SET	rec_status		= -1
		, rec_modified_by	= @user_id
		, rec_modified_when	= GETDATE()
	WHERE	user_id			= @user_id
	AND	device_id IN ( SELECT uuid FROM @device_id_list )

	SELECT	user_id
		, device_id
		, rec_status
		, rec_created_by
		, rec_created_when
		, rec_modified_by
		, rec_modified_when
	FROM	dbo.user_devices
	WHERE	user_id = @user_id
	AND	device_id IN ( SELECT uuid FROM @device_id_list )
END
GO

GRANT EXECUTE ON [dbo].[user_devices_delete_v2] TO [customer_api_user]
GO

