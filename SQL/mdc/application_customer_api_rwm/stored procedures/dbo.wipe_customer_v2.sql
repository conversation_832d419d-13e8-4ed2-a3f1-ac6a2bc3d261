/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-03-18    | Deletes an encrypted customer data by specified identifier.
-- Yevhen Vatulin           | 2019-07-02    | Add force delete
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[wipe_customer_v2] '00000000-0000-0000-0000-000000000000', 0;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[wipe_customer_v2]
    @user_id uniqueidentifier,
    @force bit
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    IF @force = 1
    BEGIN
        DELETE FROM dbo.customers WHERE id = @user_id;
        DELETE FROM dbo.customer_auth_mappings WHERE user_id = @user_id;
    END
    ELSE
    BEGIN
        DELETE FROM dbo.customers WHERE id = @user_id AND rec_status = 1;
        DELETE FROM dbo.customer_auth_mappings WHERE user_id = @user_id AND rec_status = 1;
    END;
END
GO

GRANT EXECUTE ON [dbo].[wipe_customer_v2] TO [customer_api_user]
GO
