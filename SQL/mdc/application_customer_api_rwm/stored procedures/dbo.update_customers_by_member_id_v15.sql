/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 400

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2025-01-30    | Performs an update of record in the customers by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();
DECLARE @member_id_value int = CAST(RAND() * 2147483647 AS int);

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('member_id',CONVERT(VARBINARY(8000), CAST(@member_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@member_id_value AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('whitelabel_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('title',0x, 0x)
    , ('title_hash',0x, 0x)
    , ('first_name',0x, 0x)
    , ('first_name_hash',0x, 0x)
    , ('middle_name',0x, 0x)
    , ('middle_name_hash',0x, 0x)
    , ('last_name',0x, 0x)
    , ('last_name_hash',0x, 0x)
    , ('display_name',0x, 0x)
    , ('display_name_hash',0x, 0x)
    , ('birthdate',0x, 0x)
    , ('birthdate_hash',0x, 0x)
    , ('nationality_id',0x, 0x)
    , ('nationality_id_hash',0x, 0x)
    , ('language_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('tax_id',0x, 0x)
    , ('tax_id_hash',0x, 0x)
    , ('manager_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('department_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('location_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('ccpa_opt_out',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('ccpa_last_updated',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('ccof_opt_out',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('newsletter_opt_out',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('is_single_task_consumption',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('is_login_blocked',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('wallet_birthdate',0x, 0x)
    , ('wallet_birthdate_hash',0x, 0x)
    , ('wallet_country',0x, 0x)
    , ('wallet_country_hash',0x, 0x)
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_created_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[update_customers_by_member_id_v15] @sp_request_id, @member_id_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_customers_by_member_id_v15]
    @sp_request_id uniqueidentifier
    , @member_id int
    , @relations dbo.string_long_map READONLY
    , @attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_member_id int = NULL OUTPUT
    , @new_origin char(2) = NULL OUTPUT
    , @new_whitelabel_id smallint = NULL OUTPUT
    , @new_title varbinary(8000) = NULL OUTPUT
    , @new_first_name varbinary(8000) = NULL OUTPUT
    , @new_middle_name varbinary(8000) = NULL OUTPUT
    , @new_last_name varbinary(8000) = NULL OUTPUT
    , @new_display_name varbinary(8000) = NULL OUTPUT
    , @new_birthdate varbinary(8000) = NULL OUTPUT
    , @new_nationality_id varbinary(8000) = NULL OUTPUT
    , @new_language_id int = NULL OUTPUT
    , @new_tax_id varbinary(8000) = NULL OUTPUT
    , @new_manager_id uniqueidentifier = NULL OUTPUT
    , @new_department_id int = NULL OUTPUT
    , @new_location_id int = NULL OUTPUT
    , @new_ccpa_opt_out bit = NULL OUTPUT
    , @new_ccpa_last_updated datetime = NULL OUTPUT
    , @new_ccof_opt_out bit = NULL OUTPUT
    , @new_newsletter_opt_out bit = NULL OUTPUT
    , @new_is_single_task_consumption bit = NULL OUTPUT
    , @new_is_login_blocked bit = NULL OUTPUT
    , @new_wallet_birthdate varbinary(8000) = NULL OUTPUT
    , @new_wallet_country varbinary(8000) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_created_by uniqueidentifier = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_rec_modified_by uniqueidentifier = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_member_id bit = 0;
    DECLARE @flag_origin bit = 0;
    DECLARE @flag_whitelabel_id bit = 0;
    DECLARE @flag_title_hash bit = 0;
    DECLARE @new_title_hash binary(16) = NULL;
    DECLARE @flag_first_name_hash bit = 0;
    DECLARE @new_first_name_hash binary(16) = NULL;
    DECLARE @flag_middle_name_hash bit = 0;
    DECLARE @new_middle_name_hash binary(16) = NULL;
    DECLARE @flag_last_name_hash bit = 0;
    DECLARE @new_last_name_hash binary(16) = NULL;
    DECLARE @flag_display_name_hash bit = 0;
    DECLARE @new_display_name_hash binary(16) = NULL;
    DECLARE @flag_birthdate_hash bit = 0;
    DECLARE @new_birthdate_hash binary(16) = NULL;
    DECLARE @flag_nationality_id_hash bit = 0;
    DECLARE @new_nationality_id_hash binary(16) = NULL;
    DECLARE @flag_language_id bit = 0;
    DECLARE @flag_tax_id_hash bit = 0;
    DECLARE @new_tax_id_hash binary(16) = NULL;
    DECLARE @flag_manager_id bit = 0;
    DECLARE @flag_department_id bit = 0;
    DECLARE @flag_location_id bit = 0;
    DECLARE @flag_ccpa_opt_out bit = 0;
    DECLARE @flag_ccpa_last_updated bit = 0;
    DECLARE @flag_ccof_opt_out bit = 0;
    DECLARE @flag_newsletter_opt_out bit = 0;
    DECLARE @flag_is_single_task_consumption bit = 0;
    DECLARE @flag_is_login_blocked bit = 0;
    DECLARE @flag_wallet_birthdate_hash bit = 0;
    DECLARE @new_wallet_birthdate_hash binary(16) = NULL;
    DECLARE @flag_wallet_country_hash bit = 0;
    DECLARE @new_wallet_country_hash binary(16) = NULL;
    DECLARE @flag_rec_status bit = 0;
    DECLARE @flag_rec_created_when bit = 0;
    DECLARE @flag_rec_created_by bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_rec_modified_by bit = 0;
    DECLARE @flag_server bit = 0;
    SELECT   @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_member_id = MAX(IIF([key] = 'member_id', 1, 0))
             , @new_member_id = MAX(IIF([key] = 'member_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_origin = MAX(IIF([key] = 'origin', 1, 0))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', 1, 0))
             , @new_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_title_hash = MAX(IIF([key] = 'title_hash', 1, 0))
             , @new_title = MAX(IIF([key] = 'title', [value], NULL))
             , @new_title_hash = MAX(IIF([key] = 'title_hash', [value], NULL))
             , @flag_first_name_hash = MAX(IIF([key] = 'first_name_hash', 1, 0))
             , @new_first_name = MAX(IIF([key] = 'first_name', [value], NULL))
             , @new_first_name_hash = MAX(IIF([key] = 'first_name_hash', [value], NULL))
             , @flag_middle_name_hash = MAX(IIF([key] = 'middle_name_hash', 1, 0))
             , @new_middle_name = MAX(IIF([key] = 'middle_name', [value], NULL))
             , @new_middle_name_hash = MAX(IIF([key] = 'middle_name_hash', [value], NULL))
             , @flag_last_name_hash = MAX(IIF([key] = 'last_name_hash', 1, 0))
             , @new_last_name = MAX(IIF([key] = 'last_name', [value], NULL))
             , @new_last_name_hash = MAX(IIF([key] = 'last_name_hash', [value], NULL))
             , @flag_display_name_hash = MAX(IIF([key] = 'display_name_hash', 1, 0))
             , @new_display_name = MAX(IIF([key] = 'display_name', [value], NULL))
             , @new_display_name_hash = MAX(IIF([key] = 'display_name_hash', [value], NULL))
             , @flag_birthdate_hash = MAX(IIF([key] = 'birthdate_hash', 1, 0))
             , @new_birthdate = MAX(IIF([key] = 'birthdate', [value], NULL))
             , @new_birthdate_hash = MAX(IIF([key] = 'birthdate_hash', [value], NULL))
             , @flag_nationality_id_hash = MAX(IIF([key] = 'nationality_id_hash', 1, 0))
             , @new_nationality_id = MAX(IIF([key] = 'nationality_id', [value], NULL))
             , @new_nationality_id_hash = MAX(IIF([key] = 'nationality_id_hash', [value], NULL))
             , @flag_language_id = MAX(IIF([key] = 'language_id', 1, 0))
             , @new_language_id = MAX(IIF([key] = 'language_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_tax_id_hash = MAX(IIF([key] = 'tax_id_hash', 1, 0))
             , @new_tax_id = MAX(IIF([key] = 'tax_id', [value], NULL))
             , @new_tax_id_hash = MAX(IIF([key] = 'tax_id_hash', [value], NULL))
             , @flag_manager_id = MAX(IIF([key] = 'manager_id', 1, 0))
             , @new_manager_id = MAX(IIF([key] = 'manager_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_department_id = MAX(IIF([key] = 'department_id', 1, 0))
             , @new_department_id = MAX(IIF([key] = 'department_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_location_id = MAX(IIF([key] = 'location_id', 1, 0))
             , @new_location_id = MAX(IIF([key] = 'location_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_ccpa_opt_out = MAX(IIF([key] = 'ccpa_opt_out', 1, 0))
             , @new_ccpa_opt_out = MAX(IIF([key] = 'ccpa_opt_out', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_ccpa_last_updated = MAX(IIF([key] = 'ccpa_last_updated', 1, 0))
             , @new_ccpa_last_updated = MAX(IIF([key] = 'ccpa_last_updated', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_ccof_opt_out = MAX(IIF([key] = 'ccof_opt_out', 1, 0))
             , @new_ccof_opt_out = MAX(IIF([key] = 'ccof_opt_out', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_newsletter_opt_out = MAX(IIF([key] = 'newsletter_opt_out', 1, 0))
             , @new_newsletter_opt_out = MAX(IIF([key] = 'newsletter_opt_out', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_is_single_task_consumption = MAX(IIF([key] = 'is_single_task_consumption', 1, 0))
             , @new_is_single_task_consumption = MAX(IIF([key] = 'is_single_task_consumption', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_is_login_blocked = MAX(IIF([key] = 'is_login_blocked', 1, 0))
             , @new_is_login_blocked = MAX(IIF([key] = 'is_login_blocked', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_wallet_birthdate_hash = MAX(IIF([key] = 'wallet_birthdate_hash', 1, 0))
             , @new_wallet_birthdate = MAX(IIF([key] = 'wallet_birthdate', [value], NULL))
             , @new_wallet_birthdate_hash = MAX(IIF([key] = 'wallet_birthdate_hash', [value], NULL))
             , @flag_wallet_country_hash = MAX(IIF([key] = 'wallet_country_hash', 1, 0))
             , @new_wallet_country = MAX(IIF([key] = 'wallet_country', [value], NULL))
             , @new_wallet_country_hash = MAX(IIF([key] = 'wallet_country_hash', [value], NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_created_when = MAX(IIF([key] = 'rec_created_when', 1, 0))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_created_by = MAX(IIF([key] = 'rec_created_by', 1, 0))
             , @new_rec_created_by = MAX(IIF([key] = 'rec_created_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_rec_modified_by = MAX(IIF([key] = 'rec_modified_by', 1, 0))
             , @new_rec_modified_by = MAX(IIF([key] = 'rec_modified_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_user_id = tbl.user_id = IIF(@flag_user_id = 1, @new_user_id, tbl.user_id)
               , @new_member_id = tbl.member_id = IIF(@flag_member_id = 1, @new_member_id, tbl.member_id)
               , @new_origin = tbl.origin = CASE
                   WHEN @flag_origin = 0 THEN tbl.origin
                   WHEN @has_stale_protection = 0 THEN @new_origin
                   WHEN @new_origin = tbl.origin OR (@new_origin IS NULL AND tbl.origin IS NULL) THEN tbl.origin
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'origin' AND (CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))) = tbl.origin OR ([value] IS NULL AND tbl.origin IS NULL))) THEN @new_origin
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'origin' AS [column], CAST(origin AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.origin, NULL)
               END
               , @new_whitelabel_id = tbl.whitelabel_id = CASE
                   WHEN @flag_whitelabel_id = 0 THEN tbl.whitelabel_id
                   WHEN @has_stale_protection = 0 THEN @new_whitelabel_id
                   WHEN @new_whitelabel_id = tbl.whitelabel_id OR (@new_whitelabel_id IS NULL AND tbl.whitelabel_id IS NULL) THEN tbl.whitelabel_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'whitelabel_id' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.whitelabel_id OR ([value] IS NULL AND tbl.whitelabel_id IS NULL))) THEN @new_whitelabel_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'whitelabel_id' AS [column], CAST(whitelabel_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.whitelabel_id, NULL)
               END
               , @new_title = tbl.title = IIF(@flag_title_hash = 1, @new_title, tbl.title)
               , @new_title_hash = tbl.title_hash = CASE
                   WHEN @flag_title_hash = 0 THEN tbl.title_hash
                   WHEN @has_stale_protection = 0 THEN @new_title_hash
                   WHEN @new_title_hash = tbl.title_hash OR (@new_title_hash IS NULL AND tbl.title_hash IS NULL) THEN tbl.title_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'title_hash' AND (([value]) = tbl.title_hash OR ([value] IS NULL AND tbl.title_hash IS NULL))) THEN @new_title_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'title_hash' AS [column], title_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.title_hash, NULL)
               END
               , @new_first_name = tbl.first_name = IIF(@flag_first_name_hash = 1, @new_first_name, tbl.first_name)
               , @new_first_name_hash = tbl.first_name_hash = CASE
                   WHEN @flag_first_name_hash = 0 THEN tbl.first_name_hash
                   WHEN @has_stale_protection = 0 THEN @new_first_name_hash
                   WHEN @new_first_name_hash = tbl.first_name_hash OR (@new_first_name_hash IS NULL AND tbl.first_name_hash IS NULL) THEN tbl.first_name_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'first_name_hash' AND (([value]) = tbl.first_name_hash OR ([value] IS NULL AND tbl.first_name_hash IS NULL))) THEN @new_first_name_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'first_name_hash' AS [column], first_name_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.first_name_hash, NULL)
               END
               , @new_middle_name = tbl.middle_name = IIF(@flag_middle_name_hash = 1, @new_middle_name, tbl.middle_name)
               , @new_middle_name_hash = tbl.middle_name_hash = CASE
                   WHEN @flag_middle_name_hash = 0 THEN tbl.middle_name_hash
                   WHEN @has_stale_protection = 0 THEN @new_middle_name_hash
                   WHEN @new_middle_name_hash = tbl.middle_name_hash OR (@new_middle_name_hash IS NULL AND tbl.middle_name_hash IS NULL) THEN tbl.middle_name_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'middle_name_hash' AND (([value]) = tbl.middle_name_hash OR ([value] IS NULL AND tbl.middle_name_hash IS NULL))) THEN @new_middle_name_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'middle_name_hash' AS [column], middle_name_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.middle_name_hash, NULL)
               END
               , @new_last_name = tbl.last_name = IIF(@flag_last_name_hash = 1, @new_last_name, tbl.last_name)
               , @new_last_name_hash = tbl.last_name_hash = CASE
                   WHEN @flag_last_name_hash = 0 THEN tbl.last_name_hash
                   WHEN @has_stale_protection = 0 THEN @new_last_name_hash
                   WHEN @new_last_name_hash = tbl.last_name_hash OR (@new_last_name_hash IS NULL AND tbl.last_name_hash IS NULL) THEN tbl.last_name_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'last_name_hash' AND (([value]) = tbl.last_name_hash OR ([value] IS NULL AND tbl.last_name_hash IS NULL))) THEN @new_last_name_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'last_name_hash' AS [column], last_name_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.last_name_hash, NULL)
               END
               , @new_display_name = tbl.display_name = IIF(@flag_display_name_hash = 1, @new_display_name, tbl.display_name)
               , @new_display_name_hash = tbl.display_name_hash = CASE
                   WHEN @flag_display_name_hash = 0 THEN tbl.display_name_hash
                   WHEN @has_stale_protection = 0 THEN @new_display_name_hash
                   WHEN @new_display_name_hash = tbl.display_name_hash OR (@new_display_name_hash IS NULL AND tbl.display_name_hash IS NULL) THEN tbl.display_name_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'display_name_hash' AND (([value]) = tbl.display_name_hash OR ([value] IS NULL AND tbl.display_name_hash IS NULL))) THEN @new_display_name_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'display_name_hash' AS [column], display_name_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.display_name_hash, NULL)
               END
               , @new_birthdate = tbl.birthdate = IIF(@flag_birthdate_hash = 1, @new_birthdate, tbl.birthdate)
               , @new_birthdate_hash = tbl.birthdate_hash = CASE
                   WHEN @flag_birthdate_hash = 0 THEN tbl.birthdate_hash
                   WHEN @has_stale_protection = 0 THEN @new_birthdate_hash
                   WHEN @new_birthdate_hash = tbl.birthdate_hash OR (@new_birthdate_hash IS NULL AND tbl.birthdate_hash IS NULL) THEN tbl.birthdate_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'birthdate_hash' AND (([value]) = tbl.birthdate_hash OR ([value] IS NULL AND tbl.birthdate_hash IS NULL))) THEN @new_birthdate_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'birthdate_hash' AS [column], birthdate_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.birthdate_hash, NULL)
               END
               , @new_nationality_id = tbl.nationality_id = IIF(@flag_nationality_id_hash = 1, @new_nationality_id, tbl.nationality_id)
               , @new_nationality_id_hash = tbl.nationality_id_hash = CASE
                   WHEN @flag_nationality_id_hash = 0 THEN tbl.nationality_id_hash
                   WHEN @has_stale_protection = 0 THEN @new_nationality_id_hash
                   WHEN @new_nationality_id_hash = tbl.nationality_id_hash OR (@new_nationality_id_hash IS NULL AND tbl.nationality_id_hash IS NULL) THEN tbl.nationality_id_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'nationality_id_hash' AND (([value]) = tbl.nationality_id_hash OR ([value] IS NULL AND tbl.nationality_id_hash IS NULL))) THEN @new_nationality_id_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'nationality_id_hash' AS [column], nationality_id_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.nationality_id_hash, NULL)
               END
               , @new_language_id = tbl.language_id = CASE
                   WHEN @flag_language_id = 0 THEN tbl.language_id
                   WHEN @has_stale_protection = 0 THEN @new_language_id
                   WHEN @new_language_id = tbl.language_id OR (@new_language_id IS NULL AND tbl.language_id IS NULL) THEN tbl.language_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'language_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.language_id OR ([value] IS NULL AND tbl.language_id IS NULL))) THEN @new_language_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'language_id' AS [column], CAST(language_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.language_id, NULL)
               END
               , @new_tax_id = tbl.tax_id = IIF(@flag_tax_id_hash = 1, @new_tax_id, tbl.tax_id)
               , @new_tax_id_hash = tbl.tax_id_hash = CASE
                   WHEN @flag_tax_id_hash = 0 THEN tbl.tax_id_hash
                   WHEN @has_stale_protection = 0 THEN @new_tax_id_hash
                   WHEN @new_tax_id_hash = tbl.tax_id_hash OR (@new_tax_id_hash IS NULL AND tbl.tax_id_hash IS NULL) THEN tbl.tax_id_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'tax_id_hash' AND (([value]) = tbl.tax_id_hash OR ([value] IS NULL AND tbl.tax_id_hash IS NULL))) THEN @new_tax_id_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'tax_id_hash' AS [column], tax_id_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.tax_id_hash, NULL)
               END
               , @new_manager_id = tbl.manager_id = CASE
                   WHEN @flag_manager_id = 0 THEN tbl.manager_id
                   WHEN @has_stale_protection = 0 THEN @new_manager_id
                   WHEN @new_manager_id = tbl.manager_id OR (@new_manager_id IS NULL AND tbl.manager_id IS NULL) THEN tbl.manager_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'manager_id' AND (CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))) = tbl.manager_id OR ([value] IS NULL AND tbl.manager_id IS NULL))) THEN @new_manager_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'manager_id' AS [column], CAST(manager_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.manager_id, NULL)
               END
               , @new_department_id = tbl.department_id = CASE
                   WHEN @flag_department_id = 0 THEN tbl.department_id
                   WHEN @has_stale_protection = 0 THEN @new_department_id
                   WHEN @new_department_id = tbl.department_id OR (@new_department_id IS NULL AND tbl.department_id IS NULL) THEN tbl.department_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'department_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.department_id OR ([value] IS NULL AND tbl.department_id IS NULL))) THEN @new_department_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'department_id' AS [column], CAST(department_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.department_id, NULL)
               END
               , @new_location_id = tbl.location_id = CASE
                   WHEN @flag_location_id = 0 THEN tbl.location_id
                   WHEN @has_stale_protection = 0 THEN @new_location_id
                   WHEN @new_location_id = tbl.location_id OR (@new_location_id IS NULL AND tbl.location_id IS NULL) THEN tbl.location_id
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'location_id' AND (CONVERT(int, CAST(([value]) AS VARCHAR(8000))) = tbl.location_id OR ([value] IS NULL AND tbl.location_id IS NULL))) THEN @new_location_id
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'location_id' AS [column], CAST(location_id AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.location_id, NULL)
               END
               , @new_ccpa_opt_out = tbl.ccpa_opt_out = CASE
                   WHEN @flag_ccpa_opt_out = 0 THEN tbl.ccpa_opt_out
                   WHEN @has_stale_protection = 0 THEN @new_ccpa_opt_out
                   WHEN @new_ccpa_opt_out = tbl.ccpa_opt_out OR (@new_ccpa_opt_out IS NULL AND tbl.ccpa_opt_out IS NULL) THEN tbl.ccpa_opt_out
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'ccpa_opt_out' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.ccpa_opt_out OR ([value] IS NULL AND tbl.ccpa_opt_out IS NULL))) THEN @new_ccpa_opt_out
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'ccpa_opt_out' AS [column], CAST(ccpa_opt_out AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.ccpa_opt_out, NULL)
               END
               , @new_ccpa_last_updated = tbl.ccpa_last_updated = CASE
                   WHEN @flag_ccpa_last_updated = 0 THEN tbl.ccpa_last_updated
                   WHEN @has_stale_protection = 0 THEN @new_ccpa_last_updated
                   WHEN @new_ccpa_last_updated = tbl.ccpa_last_updated OR (@new_ccpa_last_updated IS NULL AND tbl.ccpa_last_updated IS NULL) THEN tbl.ccpa_last_updated
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'ccpa_last_updated' AND (CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))) = tbl.ccpa_last_updated OR ([value] IS NULL AND tbl.ccpa_last_updated IS NULL))) THEN @new_ccpa_last_updated
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'ccpa_last_updated' AS [column], CAST(ccpa_last_updated AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.ccpa_last_updated, NULL)
               END
               , @new_ccof_opt_out = tbl.ccof_opt_out = CASE
                   WHEN @flag_ccof_opt_out = 0 THEN tbl.ccof_opt_out
                   WHEN @has_stale_protection = 0 THEN @new_ccof_opt_out
                   WHEN @new_ccof_opt_out = tbl.ccof_opt_out OR (@new_ccof_opt_out IS NULL AND tbl.ccof_opt_out IS NULL) THEN tbl.ccof_opt_out
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'ccof_opt_out' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.ccof_opt_out OR ([value] IS NULL AND tbl.ccof_opt_out IS NULL))) THEN @new_ccof_opt_out
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'ccof_opt_out' AS [column], CAST(ccof_opt_out AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.ccof_opt_out, NULL)
               END
               , @new_newsletter_opt_out = tbl.newsletter_opt_out = CASE
                   WHEN @flag_newsletter_opt_out = 0 THEN tbl.newsletter_opt_out
                   WHEN @has_stale_protection = 0 THEN @new_newsletter_opt_out
                   WHEN @new_newsletter_opt_out = tbl.newsletter_opt_out OR (@new_newsletter_opt_out IS NULL AND tbl.newsletter_opt_out IS NULL) THEN tbl.newsletter_opt_out
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'newsletter_opt_out' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.newsletter_opt_out OR ([value] IS NULL AND tbl.newsletter_opt_out IS NULL))) THEN @new_newsletter_opt_out
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'newsletter_opt_out' AS [column], CAST(newsletter_opt_out AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.newsletter_opt_out, NULL)
               END
               , @new_is_single_task_consumption = tbl.is_single_task_consumption = CASE
                   WHEN @flag_is_single_task_consumption = 0 THEN tbl.is_single_task_consumption
                   WHEN @has_stale_protection = 0 THEN @new_is_single_task_consumption
                   WHEN @new_is_single_task_consumption = tbl.is_single_task_consumption OR (@new_is_single_task_consumption IS NULL AND tbl.is_single_task_consumption IS NULL) THEN tbl.is_single_task_consumption
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'is_single_task_consumption' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.is_single_task_consumption OR ([value] IS NULL AND tbl.is_single_task_consumption IS NULL))) THEN @new_is_single_task_consumption
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'is_single_task_consumption' AS [column], CAST(is_single_task_consumption AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.is_single_task_consumption, NULL)
               END
               , @new_is_login_blocked = tbl.is_login_blocked = CASE
                   WHEN @flag_is_login_blocked = 0 THEN tbl.is_login_blocked
                   WHEN @has_stale_protection = 0 THEN @new_is_login_blocked
                   WHEN @new_is_login_blocked = tbl.is_login_blocked OR (@new_is_login_blocked IS NULL AND tbl.is_login_blocked IS NULL) THEN tbl.is_login_blocked
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'is_login_blocked' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.is_login_blocked OR ([value] IS NULL AND tbl.is_login_blocked IS NULL))) THEN @new_is_login_blocked
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'is_login_blocked' AS [column], CAST(is_login_blocked AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.is_login_blocked, NULL)
               END
               , @new_wallet_birthdate = tbl.wallet_birthdate = IIF(@flag_wallet_birthdate_hash = 1, @new_wallet_birthdate, tbl.wallet_birthdate)
               , @new_wallet_birthdate_hash = tbl.wallet_birthdate_hash = CASE
                   WHEN @flag_wallet_birthdate_hash = 0 THEN tbl.wallet_birthdate_hash
                   WHEN @has_stale_protection = 0 THEN @new_wallet_birthdate_hash
                   WHEN @new_wallet_birthdate_hash = tbl.wallet_birthdate_hash OR (@new_wallet_birthdate_hash IS NULL AND tbl.wallet_birthdate_hash IS NULL) THEN tbl.wallet_birthdate_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'wallet_birthdate_hash' AND (([value]) = tbl.wallet_birthdate_hash OR ([value] IS NULL AND tbl.wallet_birthdate_hash IS NULL))) THEN @new_wallet_birthdate_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'wallet_birthdate_hash' AS [column], wallet_birthdate_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.wallet_birthdate_hash, NULL)
               END
               , @new_wallet_country = tbl.wallet_country = IIF(@flag_wallet_country_hash = 1, @new_wallet_country, tbl.wallet_country)
               , @new_wallet_country_hash = tbl.wallet_country_hash = CASE
                   WHEN @flag_wallet_country_hash = 0 THEN tbl.wallet_country_hash
                   WHEN @has_stale_protection = 0 THEN @new_wallet_country_hash
                   WHEN @new_wallet_country_hash = tbl.wallet_country_hash OR (@new_wallet_country_hash IS NULL AND tbl.wallet_country_hash IS NULL) THEN tbl.wallet_country_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'wallet_country_hash' AND (([value]) = tbl.wallet_country_hash OR ([value] IS NULL AND tbl.wallet_country_hash IS NULL))) THEN @new_wallet_country_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'wallet_country_hash' AS [column], wallet_country_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.wallet_country_hash, NULL)
               END
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when = IIF(@flag_rec_created_when = 1, @new_rec_created_when, tbl.rec_created_when)
               , @new_rec_created_by = tbl.rec_created_by = IIF(@flag_rec_created_by = 1, @new_rec_created_by, tbl.rec_created_by)
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_rec_modified_by = tbl.rec_modified_by = IIF(@flag_rec_modified_by = 1, @new_rec_modified_by, tbl.rec_modified_by)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
        FROM   dbo.customers_synonym tbl
        WHERE  tbl.member_id = @member_id;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(max);
        SELECT @expected_value = [value_long] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_customers_by_member_id_v15] TO [customer_api_user]
GO
