/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Anura<PERSON>         | 2021-25-03    | Update missing origin on CAPI MDB tables
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
	  DECLARE @origin_updates user_origin_update_type
	  INSERT INTO @origin_updates (user_id, member_id,origin_to_update)
	  VALUES ('97f1329c-1c8e-461c-854a-ef12cdd8b41f',1234, 'TH')
EXEC [dbo].[update_missing_origin_v1] @origin_updates, '1f46ce05-a1fb-4f28-8521-47e443ab6533';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[update_missing_origin_v1]
    @origin_updates dbo.user_origin_update_type READONLY,
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

    /*********  UPDATING DAL TABLES *********/

    /* Update origin in customers table if missing */
    UPDATE     cus
    SET        cus.origin = originup.origin_to_update,
               cus.rec_modified_when = GETDATE()
    FROM       dbo.customers_synonym AS cus
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cus.user_id
    WHERE cus.origin = 'XX' OR cus.origin IS NULL

    /* Update origin in customer_verified_contacts table if missing */
    UPDATE     cvc
    SET        cvc.origin = originup.origin_to_update,
               cvc.rec_modified_when = GETDATE()
    FROM       dbo.customer_verified_contacts AS cvc
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cvc.user_id
    WHERE cvc.origin = 'XX' OR cvc.origin IS NULL

    /* Update origin in customer_unverified_contacts table if missing */
    UPDATE     cuvc
    SET        cuvc.origin = originup.origin_to_update,
               cuvc.rec_modified_when = GETDATE()
    FROM       dbo.customer_unverified_contacts AS cuvc
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cuvc.user_id
    WHERE cuvc.origin = 'XX' OR cuvc.origin IS NULL

    /* Update origin in customer_notes table if missing */
    UPDATE     custn
    SET        custn.origin = originup.origin_to_update,
               custn.rec_modified_when = GETDATE()
    FROM       dbo.customer_notes AS custn
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = custn.user_id
    WHERE custn.origin = 'XX' OR custn.origin IS NULL

    /* Update origin in customer_password_history table if missing */
    UPDATE     cph
    SET        cph.origin = originup.origin_to_update
    FROM       dbo.customer_password_history AS cph
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cph.user_id
    WHERE cph.origin = 'XX' OR cph.origin IS NULL

     /* Update origin in partner_memberships table if missing */
    UPDATE     pmem
    SET        pmem.origin = originup.origin_to_update,
               pmem.rec_modified_when = GETDATE()
    FROM       dbo.partner_memberships AS pmem
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = pmem.user_id
    WHERE pmem.origin = 'XX' OR pmem.origin IS NULL

    /* Update origin in customer_whitelabel_properties_rurubu table if missing */
    UPDATE     cwlru
    SET        cwlru.origin = originup.origin_to_update,
               cwlru.rec_modified_when = GETDATE()
    FROM       dbo.customer_whitelabel_properties_rurubu AS cwlru
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cwlru.user_id
    WHERE cwlru.origin = 'XX' OR cwlru.origin IS NULL

    /* Update origin in customer_whitelabel_properties_japanican table if missing */
    UPDATE     cwlja
    SET        cwlja.origin = originup.origin_to_update,
               cwlja.rec_modified_when = GETDATE()
    FROM       dbo.customer_whitelabel_properties_japanican AS cwlja
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cwlja.user_id
    WHERE cwlja.origin = 'XX' OR cwlja.origin IS NULL

    /* Update origin in customer_whitelabel_properties_jtb table if missing */
    UPDATE     cwljt
    SET        cwljt.origin = originup.origin_to_update,
               cwljt.rec_modified_when = GETDATE()
    FROM       dbo.customer_whitelabel_properties_jtb AS cwljt
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cwljt.user_id
    WHERE cwljt.origin = 'XX' OR cwljt.origin IS NULL

    /* Update origin in customer_whitelabel_properties_priceline table if missing */
    UPDATE     cwlpr
    SET        cwlpr.origin = originup.origin_to_update,
               cwlpr.rec_modified_when = GETDATE()
    FROM       dbo.customer_whitelabel_properties_priceline AS cwlpr
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cwlpr.user_id
    WHERE cwlpr.origin = 'XX' OR cwlpr.origin IS NULL

    /* Update origin in customer_whitelabel_properties_rm table if missing */
    UPDATE     cwlrm
    SET        cwlrm.origin = originup.origin_to_update,
               cwlrm.rec_modified_when = GETDATE()
    FROM       dbo.customer_whitelabel_properties_rm AS cwlrm
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cwlrm.user_id
    WHERE cwlrm.origin = 'XX' OR cwlrm.origin IS NULL


    /*********  UPDATING LEGACY MDC TABLES *********/

    /* Update origin in customers table if missing */
    UPDATE     cust
    SET        cust.country_code = originup.origin_to_update,
               cust.rec_modified_when = GETDATE()
    FROM       dbo.customers AS cust
    INNER JOIN @origin_updates AS originup
    ON         originup.user_id = cust.id
    WHERE cust.country_code = 'XX' OR cust.country_code IS NULL

END
GO

GRANT EXECUTE ON [dbo].[update_missing_origin_v1] TO [customer_api_user]
GO
