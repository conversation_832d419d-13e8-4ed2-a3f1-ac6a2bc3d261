/*
-------------------------------------------------------------------------------------------
-- Author		| Date			| Comment
--------------- |---------------|----------------------------------------------------------
-- Virayut S.   | 2020-11-16    | update rec_created_by, rec_created_when
-------------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
    EXEC [dbo].[update_customer_createdby_v1] 'FA8E6122-A5BF-4327-A871-00005D1F87E8', 1, '1F46CE05-A1FB-4F28-8521-47E443AB6570', '2020-11-18 10:14:05'
REVERT
END_EXEC_TEST
----------------|---------------|----------------------------------------------------------
*/

CREATE PROCEDURE [dbo].[update_customer_createdby_v1]
          @user_id uniqueidentifier
        , @whitelabel_id smallint
        , @rec_created_by uniqueidentifier
        , @rec_created_when datetime
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	UPDATE C
	SET   rec_created_by = @rec_created_by
	    , rec_created_when = @rec_created_when
	FROM dbo.customers_synonym AS C
	WHERE
	    user_id = @user_id
	AND
	    whitelabel_id = @whitelabel_id

END

GO

GRANT EXECUTE ON [dbo].[update_customer_createdby_v1] TO [customer_api_user]
GO
