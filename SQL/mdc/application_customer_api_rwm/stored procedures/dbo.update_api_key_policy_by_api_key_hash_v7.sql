/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2020-11-20    | Performs an update of record in the api_key_policy by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @api_key_hash_value varbinary(8000) = CRYPT_GEN_RANDOM(8);

DECLARE @relations dbo.string_map;
DECLARE @attributes dbo.string_map;
INSERT INTO @attributes ([key], [value]) VALUES
    ('api_key', @api_key_hash_value)
    , ('api_key_hash', @api_key_hash_value)
    , ('white_label_tokens', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('get_endpoints', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('post_endpoints', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('put_endpoints', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('patch_endpoints', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('delete_endpoints', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('team_name', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('rec_status', CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[update_api_key_policy_by_api_key_hash_v7] @sp_request_id, @api_key_hash_value, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_api_key_policy_by_api_key_hash_v7]
    @sp_request_id uniqueidentifier
    , @api_key_hash varbinary(8000)
    , @relations dbo.string_map READONLY
    , @attributes dbo.string_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_api_key varbinary(8000) = NULL OUTPUT
    , @new_white_label_tokens varchar(max) = NULL OUTPUT
    , @new_get_endpoints varchar(max) = NULL OUTPUT
    , @new_post_endpoints varchar(max) = NULL OUTPUT
    , @new_put_endpoints varchar(max) = NULL OUTPUT
    , @new_patch_endpoints varchar(max) = NULL OUTPUT
    , @new_delete_endpoints varchar(max) = NULL OUTPUT
    , @new_team_name varchar(8000) = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_api_key_hash bit = 0;
    DECLARE @new_api_key_hash binary(16) = NULL;
    DECLARE @flag_white_label_tokens bit = 0;
    DECLARE @flag_get_endpoints bit = 0;
    DECLARE @flag_post_endpoints bit = 0;
    DECLARE @flag_put_endpoints bit = 0;
    DECLARE @flag_patch_endpoints bit = 0;
    DECLARE @flag_delete_endpoints bit = 0;
    DECLARE @flag_team_name bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_server bit = 0;
    DECLARE @flag_rec_status bit = 0;
    SELECT   @flag_api_key_hash = MAX(IIF([key] = 'api_key_hash', 1, 0))
             , @new_api_key = MAX(IIF([key] = 'api_key', [value], NULL))
             , @new_api_key_hash = MAX(IIF([key] = 'api_key_hash', [value], NULL))
             , @flag_white_label_tokens = MAX(IIF([key] = 'white_label_tokens', 1, 0))
             , @new_white_label_tokens = MAX(IIF([key] = 'white_label_tokens', CAST(([value]) AS varchar(max)), NULL))
             , @flag_get_endpoints = MAX(IIF([key] = 'get_endpoints', 1, 0))
             , @new_get_endpoints = MAX(IIF([key] = 'get_endpoints', CAST(([value]) AS varchar(max)), NULL))
             , @flag_post_endpoints = MAX(IIF([key] = 'post_endpoints', 1, 0))
             , @new_post_endpoints = MAX(IIF([key] = 'post_endpoints', CAST(([value]) AS varchar(max)), NULL))
             , @flag_put_endpoints = MAX(IIF([key] = 'put_endpoints', 1, 0))
             , @new_put_endpoints = MAX(IIF([key] = 'put_endpoints', CAST(([value]) AS varchar(max)), NULL))
             , @flag_patch_endpoints = MAX(IIF([key] = 'patch_endpoints', 1, 0))
             , @new_patch_endpoints = MAX(IIF([key] = 'patch_endpoints', CAST(([value]) AS varchar(max)), NULL))
             , @flag_delete_endpoints = MAX(IIF([key] = 'delete_endpoints', 1, 0))
             , @new_delete_endpoints = MAX(IIF([key] = 'delete_endpoints', CAST(([value]) AS varchar(max)), NULL))
             , @flag_team_name = MAX(IIF([key] = 'team_name', 1, 0))
             , @new_team_name = MAX(IIF([key] = 'team_name', CAST(([value]) AS varchar(8000)), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_api_key = tbl.api_key = IIF(@flag_api_key_hash = 1, @new_api_key, tbl.api_key)
               , tbl.api_key_hash = IIF(@flag_api_key_hash = 1, @new_api_key_hash, tbl.api_key_hash)
               , @new_white_label_tokens = tbl.white_label_tokens = CASE
                   WHEN @flag_white_label_tokens = 0 THEN tbl.white_label_tokens
                   WHEN @has_stale_protection = 0 THEN @new_white_label_tokens
                   WHEN @new_white_label_tokens = tbl.white_label_tokens OR (@new_white_label_tokens IS NULL AND tbl.white_label_tokens IS NULL) THEN tbl.white_label_tokens
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'white_label_tokens' AND (CAST(([value]) AS varchar(8000)) = tbl.white_label_tokens OR ([value] IS NULL AND tbl.white_label_tokens IS NULL))) THEN @new_white_label_tokens
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'white_label_tokens' AS [column], CAST(white_label_tokens AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.white_label_tokens, NULL)
               END
               , @new_get_endpoints = tbl.get_endpoints = CASE
                   WHEN @flag_get_endpoints = 0 THEN tbl.get_endpoints
                   WHEN @has_stale_protection = 0 THEN @new_get_endpoints
                   WHEN @new_get_endpoints = tbl.get_endpoints OR (@new_get_endpoints IS NULL AND tbl.get_endpoints IS NULL) THEN tbl.get_endpoints
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'get_endpoints' AND (CAST(([value]) AS varchar(8000)) = tbl.get_endpoints OR ([value] IS NULL AND tbl.get_endpoints IS NULL))) THEN @new_get_endpoints
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'get_endpoints' AS [column], CAST(get_endpoints AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.get_endpoints, NULL)
               END
               , @new_post_endpoints = tbl.post_endpoints = CASE
                   WHEN @flag_post_endpoints = 0 THEN tbl.post_endpoints
                   WHEN @has_stale_protection = 0 THEN @new_post_endpoints
                   WHEN @new_post_endpoints = tbl.post_endpoints OR (@new_post_endpoints IS NULL AND tbl.post_endpoints IS NULL) THEN tbl.post_endpoints
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'post_endpoints' AND (CAST(([value]) AS varchar(8000)) = tbl.post_endpoints OR ([value] IS NULL AND tbl.post_endpoints IS NULL))) THEN @new_post_endpoints
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'post_endpoints' AS [column], CAST(post_endpoints AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.post_endpoints, NULL)
               END
               , @new_put_endpoints = tbl.put_endpoints = CASE
                   WHEN @flag_put_endpoints = 0 THEN tbl.put_endpoints
                   WHEN @has_stale_protection = 0 THEN @new_put_endpoints
                   WHEN @new_put_endpoints = tbl.put_endpoints OR (@new_put_endpoints IS NULL AND tbl.put_endpoints IS NULL) THEN tbl.put_endpoints
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'put_endpoints' AND (CAST(([value]) AS varchar(8000)) = tbl.put_endpoints OR ([value] IS NULL AND tbl.put_endpoints IS NULL))) THEN @new_put_endpoints
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'put_endpoints' AS [column], CAST(put_endpoints AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.put_endpoints, NULL)
               END
               , @new_patch_endpoints = tbl.patch_endpoints = CASE
                   WHEN @flag_patch_endpoints = 0 THEN tbl.patch_endpoints
                   WHEN @has_stale_protection = 0 THEN @new_patch_endpoints
                   WHEN @new_patch_endpoints = tbl.patch_endpoints OR (@new_patch_endpoints IS NULL AND tbl.patch_endpoints IS NULL) THEN tbl.patch_endpoints
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'patch_endpoints' AND (CAST(([value]) AS varchar(8000)) = tbl.patch_endpoints OR ([value] IS NULL AND tbl.patch_endpoints IS NULL))) THEN @new_patch_endpoints
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'patch_endpoints' AS [column], CAST(patch_endpoints AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.patch_endpoints, NULL)
               END
               , @new_delete_endpoints = tbl.delete_endpoints = CASE
                   WHEN @flag_delete_endpoints = 0 THEN tbl.delete_endpoints
                   WHEN @has_stale_protection = 0 THEN @new_delete_endpoints
                   WHEN @new_delete_endpoints = tbl.delete_endpoints OR (@new_delete_endpoints IS NULL AND tbl.delete_endpoints IS NULL) THEN tbl.delete_endpoints
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'delete_endpoints' AND (CAST(([value]) AS varchar(8000)) = tbl.delete_endpoints OR ([value] IS NULL AND tbl.delete_endpoints IS NULL))) THEN @new_delete_endpoints
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'delete_endpoints' AS [column], CAST(delete_endpoints AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.delete_endpoints, NULL)
               END
               , @new_team_name = tbl.team_name = CASE
                   WHEN @flag_team_name = 0 THEN tbl.team_name
                   WHEN @has_stale_protection = 0 THEN @new_team_name
                   WHEN @new_team_name = tbl.team_name OR (@new_team_name IS NULL AND tbl.team_name IS NULL) THEN tbl.team_name
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'team_name' AND (CAST(([value]) AS varchar(8000)) = tbl.team_name OR ([value] IS NULL AND tbl.team_name IS NULL))) THEN @new_team_name
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'team_name' AS [column], CAST(team_name AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.team_name, NULL)
               END
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when
        FROM   dbo.api_key_policy tbl
        WHERE  tbl.api_key_hash = @api_key_hash;
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(8000);
        SELECT @expected_value = [value] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_api_key_policy_by_api_key_hash_v7] TO [customer_api_user]
GO
