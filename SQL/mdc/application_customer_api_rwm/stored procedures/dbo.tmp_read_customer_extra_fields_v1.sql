-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2020-12-11    | Delete ASAP this dirty hack for MDB data.
----------------------------|---------------|--------------------------------------------------------------------------
/*
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[tmp_read_customer_extra_fields_v1] '856C401B-3E32-429A-A8C0-FEB9F4539224';
REVERT;
END_EXEC_TEST
*/
-----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[tmp_read_customer_extra_fields_v1]
    @user_id uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON; -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON; -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    SELECT TOP(1) tax_id
           , ccpa_opt_out
           , preferred_currency
           , col1 AS ccpa_last_updated_when
           , last_successful_login
    FROM   dbo.customer_additional_info
    WHERE  user_id = @user_id;
END
GO

GRANT EXECUTE ON [dbo].[tmp_read_customer_extra_fields_v1] TO [customer_api_user]
GO
