/*
-------------------------------------------------------------------------------------------
-- Author		| Date			| Comment
--------------- |---------------|----------------------------------------------------------
-- Andrew L     | 2018-09-11	| First version
-- Anurag A     | 2019-02-14	| Refactored the merge to normal if cases
-- Yevhen V.    | 2019-08-08	| Save group id for each favorite hotel
-- Yevhen V.    | 2019-09-09	| Choose latest group id in case of duplicates has been found
-------------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @member_id int = 1;

DECLARE @to_replace dbo.int_table;
INSERT INTO @to_replace VALUES (123, 1234);
EXEC dbo.upsert_favorites_v6 @member_id, DEFAULT, @to_replace, DEFAULT;

DECLARE @to_add dbo.int_table;
INSERT INTO @to_add VALUES (123, 1234), (1234, 12345);
EXEC dbo.upsert_favorites_v6 @member_id, @to_add, DEFAULT, DEFAULT;

DECLARE @cnt int = (SELECT COUNT(*) FROM dbo.rew_member_favorite_hotels WHERE member_id = @member_id);
IF @cnt <> 2
BEGIN
    DECLARE @extended_error_msg varchar(max) = CONCAT('Test Failed: Expect 2 records but got ', @cnt, ' records');
    THROW 51000, @extended_error_msg, 1;
END;
REVERT;
END_EXEC_TEST
----------------|---------------|----------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[upsert_favorites_v6]
    @member_id int,
    @to_add dbo.int_table READONLY,
    @to_replace dbo.int_table READONLY,
    @to_remove dbo.id_table_type READONLY
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    BEGIN TRANSACTION;
    IF EXISTS(SELECT 1 FROM @to_replace)
    BEGIN
        DELETE FROM dbo.rew_member_favorite_hotels WHERE member_id = @member_id;
    END
    ELSE IF EXISTS (SELECT 1 FROM @to_remove)
    BEGIN
        DELETE      fav
        FROM        dbo.rew_member_favorite_hotels fav
        INNER JOIN  @to_remove ids
                ON  ids.id = fav.hotel_id
        WHERE       member_id = @member_id;
    END;

    DECLARE @index int = 0;
    DECLARE @count int =
        (SELECT MAX(val) FROM ((SELECT COUNT(*) AS val FROM @to_add) UNION (SELECT COUNT(*) As val FROM @to_replace)) AS t);
    WHILE @index < @count
    BEGIN
        -- Fetch the next record to add
        DECLARE @hotel_id int, @city_id int;
        IF EXISTS(SELECT 1 FROM @to_replace)
        BEGIN
            SELECT   @hotel_id = fav.[key]
                     , @city_id = fav.[value]
            FROM     @to_replace fav
            ORDER BY fav.[key], fav.[value]
            OFFSET   @index ROWS
            FETCH NEXT 1 ROWS ONLY;
        END
        ELSE
        BEGIN
            SELECT   @hotel_id = fav.[key]
                     , @city_id = fav.[value]
            FROM     @to_add fav
            ORDER BY fav.[key], fav.[value]
            OFFSET   @index ROWS
            FETCH NEXT 1 ROWS ONLY;
        END;

        -- Add missing favorite groups
        DECLARE @group_id bigint =
            (SELECT TOP(1) group_id
             FROM dbo.rew_member_favorite_groups
             WHERE member_id = @member_id AND ref_type_id = 1 AND ref_object_id = @city_id
             ORDER BY lastupdated_when);
        IF @group_id IS NULL
        BEGIN
            SET @group_id = NEXT VALUE FOR Agoda_Customer_RW.dbo.rew_member_favorite_groups_seq;
            INSERT INTO dbo.rew_member_favorite_groups
            (
                        group_id
                        , member_id
                        , ref_type_id
                        , ref_object_id
                        , shared_hash
                        , lastupdated_when
            )
            VALUES
            (
                        @group_id
                        , @member_id
                        , 1
                        , @city_id
                        , REPLACE(CONVERT(varchar(50), NEWID()), '-', '')
                        , GETDATE()
            );
        END;

        -- Insert non-existing favorite hotels
        IF NOT EXISTS(SELECT 1 FROM dbo.rew_member_favorite_hotels WHERE member_id = @member_id AND hotel_id = @hotel_id)
        BEGIN
            INSERT INTO dbo.rew_member_favorite_hotels
            (
                        member_id
                        , hotel_id
                        , lastupdated_when
                        , group_id
            )
            VALUES
            (
                        @member_id
                        , @hotel_id
                        , GETDATE()
                        , @group_id
            );
        END;

        SET @index = @index + 1;
    END;

    -- Clean empty favorite groups if any
    DELETE    grp
    FROM      dbo.rew_member_favorite_groups grp
    LEFT JOIN dbo.rew_member_favorite_hotels fav
           ON grp.group_id = fav.group_id
    WHERE     grp.member_id = @member_id
    AND       fav.hotel_id IS NULL;
    COMMIT TRANSACTION;
END
GO

GRANT EXECUTE ON [dbo].[upsert_favorites_v6] TO [customer_api_user]
GO
