
/*
-------------------------------------------------------------------------------------------
-- Author		| Date			| Comment
--------------- |---------------|----------------------------------------------------------
-- Andrew L | 2018-08-27	| Set effective level
-------------------------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[update_host_level] '1f46ce05-a1fb-4f28-8521-47e443ab6533', 1
REVERT
END_EXEC_TEST
----------------|---------------|----------------------------------------------------------
*/


CREATE PROCEDURE [dbo].[update_host_level]
	 @uuid UNIQUEIDENTIFIER,
   @level TINYINT
AS
BEGIN
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

  UPDATE dbo.rew_host_user_info set effective_level = @level where user_id = @uuid
END

GO

GRANT EXECUTE ON [dbo].[update_host_level] TO [customer_api_user]
GO
