/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-03-11    | Creates or Updates the record in the wechat_v1_cache by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @code_value varchar(8000) = REPLACE(NEWID(), '-', '');

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('code',CONVERT(VARBINARY(8000), CAST(@code_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@code_value AS VARCHAR(8000))))
    , ('user_info',0x, 0x)
    , ('user_info_hash',0x, 0x)
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[save_wechat_v1_cache_by_code_v2] @sp_request_id, @code_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_wechat_v1_cache_by_code_v2]
    @sp_request_id uniqueidentifier
    , @code varchar(8000)
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_code varchar(8000) = NULL;
    DECLARE @new_user_info_hash binary(16) = NULL;
    DECLARE @new_user_info varbinary(8000) = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_wechat_v1_cache_by_code_v2
         @sp_request_id
         , @code
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_code = @new_code OUT
         , @new_user_info = @new_user_info OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_code = MAX(IIF([key] = 'code', CAST(([value]) AS varchar(8000)), NULL))
             , @new_user_info = MAX(IIF([key] = 'user_info',[value], NULL))
             , @new_user_info_hash = MAX(IIF([key] = 'user_info_hash', [value], NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.wechat_v1_cache
        (
                    code
                    , user_info
                    , user_info_hash
                    , origin
                    , rec_created_when
                    , server
                    , rec_status
        )
        OUTPUT      INSERTED.code
                    , INSERTED.user_info
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.server
        VALUES
        (
                    @new_code
                    , @new_user_info
                    , @new_user_info_hash
                    , @new_origin
                    , @new_rec_created_when
                    , @new_server
                    , @new_rec_status
        );
    END
    ELSE
    BEGIN
        SELECT @new_code AS code
               , @new_user_info AS user_info
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_wechat_v1_cache_by_code_v2] TO [customer_api_user]
GO
