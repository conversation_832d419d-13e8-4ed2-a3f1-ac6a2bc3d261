/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-04-09    | Creates or Updates the record in the customer_whitelabel_properties_jtb by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('external_member_id',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('analysis_id',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('nationality_id',0x, 0x)
    , ('nationality_id_hash',0x, 0x)
    , ('username',0x, 0x)
    , ('username_hash',0x, 0x)
    , ('age',0x, 0x)
    , ('age_hash',0x, 0x)
    , ('gender',0x, 0x)
    , ('gender_hash',0x, 0x)
    , ('phone2',0x, 0x)
    , ('phone2_hash',0x, 0x)
    , ('first_name_eng',0x, 0x)
    , ('first_name_eng_hash',0x, 0x)
    , ('middle_name_eng',0x, 0x)
    , ('middle_name_eng_hash',0x, 0x)
    , ('last_name_eng',0x, 0x)
    , ('last_name_eng_hash',0x, 0x)
    , ('first_name_kana',0x, 0x)
    , ('first_name_kana_hash',0x, 0x)
    , ('last_name_kana',0x, 0x)
    , ('last_name_kana_hash',0x, 0x)
    , ('first_name_kanji',0x, 0x)
    , ('first_name_kanji_hash',0x, 0x)
    , ('last_name_kanji',0x, 0x)
    , ('last_name_kanji_hash',0x, 0x)
    , ('alternative_email',0x, 0x)
    , ('alternative_email_hash',0x, 0x)
    , ('address1',0x, 0x)
    , ('address1_hash',0x, 0x)
    , ('address2',0x, 0x)
    , ('address2_hash',0x, 0x)
    , ('postal_code',0x, 0x)
    , ('postal_code_hash',0x, 0x)
    , ('city',0x, 0x)
    , ('city_hash',0x, 0x)
    , ('area',0x, 0x)
    , ('area_hash',0x, 0x)
    , ('region',0x, 0x)
    , ('region_hash',0x, 0x)
    , ('state',0x, 0x)
    , ('state_hash',0x, 0x)
    , ('country',0x, 0x)
    , ('country_hash',0x, 0x)
    , ('rewards_points',0x, 0x)
    , ('rewards_points_hash',0x, 0x)
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('section',0x, 0x)
    , ('section_hash',0x, 0x);

EXEC [dbo].[save_customer_whitelabel_properties_jtb_by_user_id_v4] @sp_request_id, @user_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customer_whitelabel_properties_jtb_by_user_id_v4]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_external_member_id varchar(8000) = NULL;
    DECLARE @new_analysis_id varchar(8000) = NULL;
    DECLARE @new_nationality_id_hash binary(16) = NULL;
    DECLARE @new_nationality_id varbinary(8000) = NULL;
    DECLARE @new_username_hash binary(16) = NULL;
    DECLARE @new_username varbinary(8000) = NULL;
    DECLARE @new_age_hash binary(16) = NULL;
    DECLARE @new_age varbinary(8000) = NULL;
    DECLARE @new_gender_hash binary(16) = NULL;
    DECLARE @new_gender varbinary(8000) = NULL;
    DECLARE @new_phone2_hash binary(16) = NULL;
    DECLARE @new_phone2 varbinary(8000) = NULL;
    DECLARE @new_first_name_eng_hash binary(16) = NULL;
    DECLARE @new_first_name_eng varbinary(8000) = NULL;
    DECLARE @new_middle_name_eng_hash binary(16) = NULL;
    DECLARE @new_middle_name_eng varbinary(8000) = NULL;
    DECLARE @new_last_name_eng_hash binary(16) = NULL;
    DECLARE @new_last_name_eng varbinary(8000) = NULL;
    DECLARE @new_first_name_kana_hash binary(16) = NULL;
    DECLARE @new_first_name_kana varbinary(8000) = NULL;
    DECLARE @new_last_name_kana_hash binary(16) = NULL;
    DECLARE @new_last_name_kana varbinary(8000) = NULL;
    DECLARE @new_first_name_kanji_hash binary(16) = NULL;
    DECLARE @new_first_name_kanji varbinary(8000) = NULL;
    DECLARE @new_last_name_kanji_hash binary(16) = NULL;
    DECLARE @new_last_name_kanji varbinary(8000) = NULL;
    DECLARE @new_alternative_email_hash binary(16) = NULL;
    DECLARE @new_alternative_email varbinary(8000) = NULL;
    DECLARE @new_address1_hash binary(16) = NULL;
    DECLARE @new_address1 varbinary(8000) = NULL;
    DECLARE @new_address2_hash binary(16) = NULL;
    DECLARE @new_address2 varbinary(8000) = NULL;
    DECLARE @new_postal_code_hash binary(16) = NULL;
    DECLARE @new_postal_code varbinary(8000) = NULL;
    DECLARE @new_city_hash binary(16) = NULL;
    DECLARE @new_city varbinary(8000) = NULL;
    DECLARE @new_area_hash binary(16) = NULL;
    DECLARE @new_area varbinary(8000) = NULL;
    DECLARE @new_region_hash binary(16) = NULL;
    DECLARE @new_region varbinary(8000) = NULL;
    DECLARE @new_state_hash binary(16) = NULL;
    DECLARE @new_state varbinary(8000) = NULL;
    DECLARE @new_country_hash binary(16) = NULL;
    DECLARE @new_country varbinary(8000) = NULL;
    DECLARE @new_rewards_points_hash binary(16) = NULL;
    DECLARE @new_rewards_points varbinary(8000) = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @new_section_hash binary(16) = NULL;
    DECLARE @new_section varbinary(8000) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_customer_whitelabel_properties_jtb_by_user_id_v3
         @sp_request_id
         , @user_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_id = @new_user_id OUT
         , @new_external_member_id = @new_external_member_id OUT
         , @new_analysis_id = @new_analysis_id OUT
         , @new_nationality_id = @new_nationality_id OUT
         , @new_username = @new_username OUT
         , @new_age = @new_age OUT
         , @new_gender = @new_gender OUT
         , @new_phone2 = @new_phone2 OUT
         , @new_first_name_eng = @new_first_name_eng OUT
         , @new_middle_name_eng = @new_middle_name_eng OUT
         , @new_last_name_eng = @new_last_name_eng OUT
         , @new_first_name_kana = @new_first_name_kana OUT
         , @new_last_name_kana = @new_last_name_kana OUT
         , @new_first_name_kanji = @new_first_name_kanji OUT
         , @new_last_name_kanji = @new_last_name_kanji OUT
         , @new_alternative_email = @new_alternative_email OUT
         , @new_address1 = @new_address1 OUT
         , @new_address2 = @new_address2 OUT
         , @new_postal_code = @new_postal_code OUT
         , @new_city = @new_city OUT
         , @new_area = @new_area OUT
         , @new_region = @new_region OUT
         , @new_state = @new_state OUT
         , @new_country = @new_country OUT
         , @new_rewards_points = @new_rewards_points OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_server = @new_server OUT
         , @new_section = @new_section OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_external_member_id = MAX(IIF([key] = 'external_member_id', CAST(([value]) AS varchar(8000)), NULL))
             , @new_analysis_id = MAX(IIF([key] = 'analysis_id', CAST(([value]) AS varchar(8000)), NULL))
             , @new_nationality_id = MAX(IIF([key] = 'nationality_id', [value], NULL))
             , @new_nationality_id_hash = MAX(IIF([key] = 'nationality_id_hash', [value], NULL))
             , @new_username = MAX(IIF([key] = 'username', [value], NULL))
             , @new_username_hash = MAX(IIF([key] = 'username_hash', [value], NULL))
             , @new_age = MAX(IIF([key] = 'age', [value], NULL))
             , @new_age_hash = MAX(IIF([key] = 'age_hash', [value], NULL))
             , @new_gender = MAX(IIF([key] = 'gender', [value], NULL))
             , @new_gender_hash = MAX(IIF([key] = 'gender_hash', [value], NULL))
             , @new_phone2 = MAX(IIF([key] = 'phone2', [value], NULL))
             , @new_phone2_hash = MAX(IIF([key] = 'phone2_hash', [value], NULL))
             , @new_first_name_eng = MAX(IIF([key] = 'first_name_eng', [value], NULL))
             , @new_first_name_eng_hash = MAX(IIF([key] = 'first_name_eng_hash', [value], NULL))
             , @new_middle_name_eng = MAX(IIF([key] = 'middle_name_eng', [value], NULL))
             , @new_middle_name_eng_hash = MAX(IIF([key] = 'middle_name_eng_hash', [value], NULL))
             , @new_last_name_eng = MAX(IIF([key] = 'last_name_eng', [value], NULL))
             , @new_last_name_eng_hash = MAX(IIF([key] = 'last_name_eng_hash', [value], NULL))
             , @new_first_name_kana = MAX(IIF([key] = 'first_name_kana', [value], NULL))
             , @new_first_name_kana_hash = MAX(IIF([key] = 'first_name_kana_hash', [value], NULL))
             , @new_last_name_kana = MAX(IIF([key] = 'last_name_kana', [value], NULL))
             , @new_last_name_kana_hash = MAX(IIF([key] = 'last_name_kana_hash', [value], NULL))
             , @new_first_name_kanji = MAX(IIF([key] = 'first_name_kanji', [value], NULL))
             , @new_first_name_kanji_hash = MAX(IIF([key] = 'first_name_kanji_hash', [value], NULL))
             , @new_last_name_kanji = MAX(IIF([key] = 'last_name_kanji', [value], NULL))
             , @new_last_name_kanji_hash = MAX(IIF([key] = 'last_name_kanji_hash', [value], NULL))
             , @new_alternative_email = MAX(IIF([key] = 'alternative_email', [value], NULL))
             , @new_alternative_email_hash = MAX(IIF([key] = 'alternative_email_hash', [value], NULL))
             , @new_address1 = MAX(IIF([key] = 'address1', [value], NULL))
             , @new_address1_hash = MAX(IIF([key] = 'address1_hash', [value], NULL))
             , @new_address2 = MAX(IIF([key] = 'address2', [value], NULL))
             , @new_address2_hash = MAX(IIF([key] = 'address2_hash', [value], NULL))
             , @new_postal_code = MAX(IIF([key] = 'postal_code', [value], NULL))
             , @new_postal_code_hash = MAX(IIF([key] = 'postal_code_hash', [value], NULL))
             , @new_city = MAX(IIF([key] = 'city', [value], NULL))
             , @new_city_hash = MAX(IIF([key] = 'city_hash', [value], NULL))
             , @new_area = MAX(IIF([key] = 'area', [value], NULL))
             , @new_area_hash = MAX(IIF([key] = 'area_hash', [value], NULL))
             , @new_region = MAX(IIF([key] = 'region', [value], NULL))
             , @new_region_hash = MAX(IIF([key] = 'region_hash', [value], NULL))
             , @new_state = MAX(IIF([key] = 'state', [value], NULL))
             , @new_state_hash = MAX(IIF([key] = 'state_hash', [value], NULL))
             , @new_country = MAX(IIF([key] = 'country', [value], NULL))
             , @new_country_hash = MAX(IIF([key] = 'country_hash', [value], NULL))
             , @new_rewards_points = MAX(IIF([key] = 'rewards_points', [value], NULL))
             , @new_rewards_points_hash = MAX(IIF([key] = 'rewards_points_hash', [value], NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @new_section = MAX(IIF([key] = 'section', [value], NULL))
             , @new_section_hash = MAX(IIF([key] = 'section_hash', [value], NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.customer_whitelabel_properties_jtb
        (
                    user_id
                    , external_member_id
                    , analysis_id
                    , nationality_id
                    , nationality_id_hash
                    , username
                    , username_hash
                    , age
                    , age_hash
                    , gender
                    , gender_hash
                    , phone2
                    , phone2_hash
                    , first_name_eng
                    , first_name_eng_hash
                    , middle_name_eng
                    , middle_name_eng_hash
                    , last_name_eng
                    , last_name_eng_hash
                    , first_name_kana
                    , first_name_kana_hash
                    , last_name_kana
                    , last_name_kana_hash
                    , first_name_kanji
                    , first_name_kanji_hash
                    , last_name_kanji
                    , last_name_kanji_hash
                    , alternative_email
                    , alternative_email_hash
                    , address1
                    , address1_hash
                    , address2
                    , address2_hash
                    , postal_code
                    , postal_code_hash
                    , city
                    , city_hash
                    , area
                    , area_hash
                    , region
                    , region_hash
                    , state
                    , state_hash
                    , country
                    , country_hash
                    , rewards_points
                    , rewards_points_hash
                    , origin
                    , rec_created_when
                    , server
                    , section
                    , section_hash
                    , rec_status
        )
        OUTPUT      INSERTED.user_id
                    , INSERTED.external_member_id
                    , INSERTED.analysis_id
                    , INSERTED.nationality_id
                    , INSERTED.username
                    , INSERTED.age
                    , INSERTED.gender
                    , INSERTED.phone2
                    , INSERTED.first_name_eng
                    , INSERTED.middle_name_eng
                    , INSERTED.last_name_eng
                    , INSERTED.first_name_kana
                    , INSERTED.last_name_kana
                    , INSERTED.first_name_kanji
                    , INSERTED.last_name_kanji
                    , INSERTED.alternative_email
                    , INSERTED.address1
                    , INSERTED.address2
                    , INSERTED.postal_code
                    , INSERTED.city
                    , INSERTED.area
                    , INSERTED.region
                    , INSERTED.state
                    , INSERTED.country
                    , INSERTED.rewards_points
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
                    , INSERTED.server
                    , INSERTED.section
        VALUES
        (
                    @new_user_id
                    , @new_external_member_id
                    , @new_analysis_id
                    , @new_nationality_id
                    , @new_nationality_id_hash
                    , @new_username
                    , @new_username_hash
                    , @new_age
                    , @new_age_hash
                    , @new_gender
                    , @new_gender_hash
                    , @new_phone2
                    , @new_phone2_hash
                    , @new_first_name_eng
                    , @new_first_name_eng_hash
                    , @new_middle_name_eng
                    , @new_middle_name_eng_hash
                    , @new_last_name_eng
                    , @new_last_name_eng_hash
                    , @new_first_name_kana
                    , @new_first_name_kana_hash
                    , @new_last_name_kana
                    , @new_last_name_kana_hash
                    , @new_first_name_kanji
                    , @new_first_name_kanji_hash
                    , @new_last_name_kanji
                    , @new_last_name_kanji_hash
                    , @new_alternative_email
                    , @new_alternative_email_hash
                    , @new_address1
                    , @new_address1_hash
                    , @new_address2
                    , @new_address2_hash
                    , @new_postal_code
                    , @new_postal_code_hash
                    , @new_city
                    , @new_city_hash
                    , @new_area
                    , @new_area_hash
                    , @new_region
                    , @new_region_hash
                    , @new_state
                    , @new_state_hash
                    , @new_country
                    , @new_country_hash
                    , @new_rewards_points
                    , @new_rewards_points_hash
                    , @new_origin
                    , @new_rec_created_when
                    , @new_server
                    , @new_section
                    , @new_section_hash
                    , @new_rec_status
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_id AS user_id
               , @new_external_member_id AS external_member_id
               , @new_analysis_id AS analysis_id
               , @new_nationality_id AS nationality_id
               , @new_username AS username
               , @new_age AS age
               , @new_gender AS gender
               , @new_phone2 AS phone2
               , @new_first_name_eng AS first_name_eng
               , @new_middle_name_eng AS middle_name_eng
               , @new_last_name_eng AS last_name_eng
               , @new_first_name_kana AS first_name_kana
               , @new_last_name_kana AS last_name_kana
               , @new_first_name_kanji AS first_name_kanji
               , @new_last_name_kanji AS last_name_kanji
               , @new_alternative_email AS alternative_email
               , @new_address1 AS address1
               , @new_address2 AS address2
               , @new_postal_code AS postal_code
               , @new_city AS city
               , @new_area AS area
               , @new_region AS region
               , @new_state AS state
               , @new_country AS country
               , @new_rewards_points AS rewards_points
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when
               , @new_server AS server
               , @new_section AS section;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_customer_whitelabel_properties_jtb_by_user_id_v4] TO [customer_api_user]
GO
