/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- Yevhen Vatulin           | 2019-10-01    | Performs bulk operation for the role table.
----------------------------|---------------|--------------------------------------------------------------------------
EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[sync_user_roles_v1] DEFAULT, DEFAULT, DEFAULT, '00000000-0000-0000-0000-000000000000';
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[sync_user_roles_v1]
    @to_add dbo.customer_role_list READONLY,
    @hotels_to_remove dbo.uuid_table_type READONLY,
    @other_to_remove dbo.uuid_table_type READONLY,
    @modified_by uniqueidentifier
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    BEGIN TRANSACTION;
    UPDATE     cr
    SET        rec_status = -1
               , rec_modified_by = @modified_by
               , rec_modified_when = GETDATE()
    FROM       dbo.customer_references cr
    INNER JOIN @other_to_remove t
            ON cr.hash = t.uuid;

    UPDATE     ch
    SET        rec_status = -1
               , rec_modified_by = @modified_by
               , rec_modified_when = GETDATE()
    FROM       dbo.customer_hotels ch
    INNER JOIN @hotels_to_remove t
            ON ch.hash = t.uuid;

    DECLARE @index int = 0;
    DECLARE @count int = (SELECT COUNT(*) FROM @to_add);
    WHILE @index < @count
    BEGIN
        DECLARE @hash uniqueidentifier;
        DECLARE @user_id uniqueidentifier;
        DECLARE @role_id uniqueidentifier;
        DECLARE @ref_type char;
        DECLARE @ref_id int;
        SELECT   @hash = hash
                 , @user_id = user_id
                 , @role_id = role_id
                 , @ref_type = ref_type
                 , @ref_id = ref_id
        FROM     @to_add
        ORDER BY hash
        OFFSET   @index ROWS
        FETCH NEXT 1 ROWS ONLY;

        IF @ref_type = 'H'
        BEGIN
            IF EXISTS(SELECT 1 FROM dbo.customer_hotels WHERE hash = @hash)
            BEGIN
                UPDATE dbo.customer_hotels
                SET    rec_status = 1
                       , rec_modified_by = @modified_by
                       , rec_modified_when = GETDATE()
                WHERE  hash = @hash;
            END
            ELSE
            BEGIN
                INSERT INTO dbo.customer_hotels
                (
                            hash
                            , user_id
                            , role_id
                            , ref_id
                            , rec_status
                            , rec_created_by
                            , rec_created_when
                )
                VALUES
                (
                            @hash
                            , @user_id
                            , @role_id
                            , @ref_id
                            , 1
                            , @modified_by
                            , GETDATE()
                );
            END;
        END
        ELSE
        BEGIN
            IF EXISTS(SELECT 1 FROM dbo.customer_references WHERE hash = @hash)
            BEGIN
                UPDATE dbo.customer_references
                SET    rec_status = 1
                       , rec_modified_by = @modified_by
                       , rec_modified_when = GETDATE()
                WHERE  hash = @hash;
            END
            ELSE
            BEGIN
                INSERT INTO dbo.customer_references
                (
                            hash
                            , user_id
                            , role_id
                            , ref_type
                            , ref_id
                            , rec_status
                            , rec_created_by
                            , rec_created_when
                )
                VALUES
                (
                            @hash
                            , @user_id
                            , @role_id
                            , @ref_type
                            , @ref_id
                            , 1
                            , @modified_by
                            , GETDATE()
                );
            END;
        END;

        SET @index = @index + 1;
    END;
    COMMIT TRANSACTION;
END
GO

GRANT EXECUTE ON [dbo].[sync_user_roles_v1] TO [customer_api_user]
GO
