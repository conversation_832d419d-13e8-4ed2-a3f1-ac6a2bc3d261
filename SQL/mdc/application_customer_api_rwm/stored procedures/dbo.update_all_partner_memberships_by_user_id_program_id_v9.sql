/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2021-03-11    | Performs an update of record in the partner_memberships by the primary key or alternate key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @partner_memberships_list dbo.partner_memberships_list;
DECLARE @user_id_value uniqueidentifier = NEWID();
DECLARE @program_id_value int = CAST(RAND() * 2147483647 AS int);
INSERT INTO @partner_memberships_list VALUES (@user_id_value, @program_id_value);
DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('program_id',CONVERT(VARBINARY(8000), CAST(@program_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@program_id_value AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('membership_id',0x, 0x)
    , ('membership_id_hash',0x, 0x)
    , ('is_preferred',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[update_all_partner_memberships_by_user_id_program_id_v9] @sp_request_id, @partner_memberships_list, @relations, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[update_all_partner_memberships_by_user_id_program_id_v9]
    @sp_request_id uniqueidentifier
    , @partner_memberships_list dbo.partner_memberships_list READONLY
    , @relations dbo.string_long_map READONLY
    , @attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
    , @new_user_id uniqueidentifier = NULL OUTPUT
    , @new_program_id int = NULL OUTPUT
    , @new_origin char(2) = NULL OUTPUT
    , @new_membership_id varbinary(8000) = NULL OUTPUT
    , @new_is_preferred bit = NULL OUTPUT
    , @new_rec_status smallint = NULL OUTPUT
    , @new_rec_created_when datetime = NULL OUTPUT
    , @new_rec_modified_when datetime = NULL OUTPUT
    , @new_server varchar(255) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Unroll the row values before the update a row in the table
    DECLARE @flag_user_id bit = 0;
    DECLARE @flag_program_id bit = 0;
    DECLARE @flag_origin bit = 0;
    DECLARE @flag_membership_id_hash bit = 0;
    DECLARE @new_membership_id_hash binary(16) = NULL;
    DECLARE @flag_is_preferred bit = 0;
    DECLARE @flag_rec_modified_when bit = 0;
    DECLARE @flag_server bit = 0;
    DECLARE @flag_rec_status bit = 0;
    SELECT   @flag_user_id = MAX(IIF([key] = 'user_id', 1, 0))
             , @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_program_id = MAX(IIF([key] = 'program_id', 1, 0))
             , @new_program_id = MAX(IIF([key] = 'program_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_origin = MAX(IIF([key] = 'origin', 1, 0))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_membership_id_hash = MAX(IIF([key] = 'membership_id_hash', 1, 0))
             , @new_membership_id = MAX(IIF([key] = 'membership_id', [value], NULL))
             , @new_membership_id_hash = MAX(IIF([key] = 'membership_id_hash', [value], NULL))
             , @flag_is_preferred = MAX(IIF([key] = 'is_preferred', 1, 0))
             , @new_is_preferred = MAX(IIF([key] = 'is_preferred', CONVERT(int, CAST([value] AS CHAR(1))), NULL))
             , @flag_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', 1, 0))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @flag_server = MAX(IIF([key] = 'server', 1, 0))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @flag_rec_status = MAX(IIF([key] = 'rec_status', 1, 0))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
    FROM     @attributes
    GROUP BY ();

    DECLARE @has_stale_protection bit = IIF((SELECT COUNT(*) FROM @relations) > 0, 1, 0);
    DECLARE @affected_rows int = 0;
    BEGIN TRY
        UPDATE TOP(1) tbl
        SET    @new_user_id = tbl.user_id = IIF(@flag_user_id = 1, @new_user_id, tbl.user_id)
               , @new_program_id = tbl.program_id = IIF(@flag_program_id = 1, @new_program_id, tbl.program_id)
               , @new_origin = tbl.origin = CASE
                   WHEN @flag_origin = 0 THEN tbl.origin
                   WHEN @has_stale_protection = 0 THEN @new_origin
                   WHEN @new_origin = tbl.origin OR (@new_origin IS NULL AND tbl.origin IS NULL) THEN tbl.origin
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'origin' AND (CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))) = tbl.origin OR ([value] IS NULL AND tbl.origin IS NULL))) THEN @new_origin
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'origin' AS [column], CAST(origin AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.origin, NULL)
               END
               , @new_membership_id = tbl.membership_id = IIF(@flag_membership_id_hash = 1, @new_membership_id, tbl.membership_id)
               , @new_membership_id_hash = tbl.membership_id_hash = CASE
                   WHEN @flag_membership_id_hash = 0 THEN tbl.membership_id_hash
                   WHEN @has_stale_protection = 0 THEN @new_membership_id_hash
                   WHEN @new_membership_id_hash = tbl.membership_id_hash OR (@new_membership_id_hash IS NULL AND tbl.membership_id_hash IS NULL) THEN tbl.membership_id_hash
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'membership_id_hash' AND (([value]) = tbl.membership_id_hash OR ([value] IS NULL AND tbl.membership_id_hash IS NULL))) THEN @new_membership_id_hash
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'membership_id_hash' AS [column], membership_id_hash AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.membership_id_hash, NULL)
               END
               , @new_is_preferred = tbl.is_preferred = CASE
                   WHEN @flag_is_preferred = 0 THEN tbl.is_preferred
                   WHEN @has_stale_protection = 0 THEN @new_is_preferred
                   WHEN @new_is_preferred = tbl.is_preferred OR (@new_is_preferred IS NULL AND tbl.is_preferred IS NULL) THEN tbl.is_preferred
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'is_preferred' AND (CONVERT(bit, CAST(([value]) AS VARCHAR(8000))) = tbl.is_preferred OR ([value] IS NULL AND tbl.is_preferred IS NULL))) THEN @new_is_preferred
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'is_preferred' AS [column], CAST(is_preferred AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.is_preferred, NULL)
               END
               , @new_rec_modified_when = tbl.rec_modified_when = IIF(@flag_rec_modified_when = 1, @new_rec_modified_when, tbl.rec_modified_when)
               , @new_server = tbl.server = IIF(@flag_server = 1, @new_server, tbl.server)
               , @new_rec_status = tbl.rec_status = CASE
                   WHEN @flag_rec_status = 0 THEN tbl.rec_status
                   WHEN @has_stale_protection = 0 THEN @new_rec_status
                   WHEN @new_rec_status = tbl.rec_status OR (@new_rec_status IS NULL AND tbl.rec_status IS NULL) THEN tbl.rec_status
                   WHEN EXISTS (SELECT 1 FROM @relations WHERE [key] = 'rec_status' AND (CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))) = tbl.rec_status OR ([value] IS NULL AND tbl.rec_status IS NULL))) THEN @new_rec_status
                   ELSE IIF(CAST(CONCAT(CHAR(1), (SELECT 'rec_status' AS [column], CAST(rec_status AS varbinary(8000)) AS [current] FOR XML PATH(''), BINARY BASE64), CHAR(2)) AS int) = 0, tbl.rec_status, NULL)
               END
               , @new_rec_created_when = tbl.rec_created_when
        FROM   dbo.partner_memberships tbl
        WHERE  EXISTS (SELECT 1 FROM @partner_memberships_list list WHERE list.user_id = tbl.user_id AND list.program_id = tbl.program_id);
        SET @affected_rows = @@ROWCOUNT;
    END TRY
    BEGIN CATCH
        -- Replace 'conversion failed' error by concurrent modification error
        -- NB. Unfortunately error message is limited by 2048 chars
        IF ERROR_NUMBER() <> 245 THROW;
        DECLARE @start_err_details int = CHARINDEX(CHAR(1), ERROR_MESSAGE()) + 1;
        DECLARE @end_err_details int = CHARINDEX(CHAR(2), ERROR_MESSAGE());
        DECLARE @error_details xml = SUBSTRING(ERROR_MESSAGE(), @start_err_details, @end_err_details - @start_err_details);

        DECLARE @column_name sysname = @error_details.value('(/column)[1]','sysname');
        DECLARE @expected_value varbinary(max);
        SELECT @expected_value = [value_long] FROM @relations WHERE [key] = @column_name;
        SET @error_details.modify('insert element expected {sql:variable("@expected_value")} as last into (/)[1]');

        DECLARE @error_message nvarchar(2048) = CAST(@error_details AS nvarchar(2048));
        THROW 51205, @error_message, 1;
    END CATCH;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT('<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>');
    RETURN @affected_rows;
END
GO

GRANT EXECUTE ON [dbo].[update_all_partner_memberships_by_user_id_program_id_v9] TO [customer_api_user]
GO
