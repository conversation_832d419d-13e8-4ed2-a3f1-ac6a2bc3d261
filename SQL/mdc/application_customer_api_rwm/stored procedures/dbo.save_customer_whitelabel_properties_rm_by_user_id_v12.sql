/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2020-11-20    | Creates or Updates the record in the customer_whitelabel_properties_rm by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
EXECUTE AS LOGIN = 'customer_api_user'
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();

DECLARE @relations dbo.string_map;
DECLARE @attributes dbo.string_map;
INSERT INTO @attributes ([key], [value]) VALUES
    ('user_id', CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('add_company_profile_to_receipt', CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('company', 0x)
    , ('company_hash', 0x)
    , ('time_zone_rm', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('default_currency_id', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))))
    , ('default_reward_program_id', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('default_reward_account_id', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('referrer_id', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('site_id', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('idp_user_id', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('type_rm', CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('fraud_status', CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('gaming_status', CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('is_news_letter', CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('referral_signup_reward_uuid', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('date_pushed_to_salesforce', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('promotion_signup_UUID', CONVERT(VARBINARY(8000), CAST('********-0000-0000-0000-************' AS VARCHAR(8000))))
    , ('origin', CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('rec_status', CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_when', CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('server', CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXEC [dbo].[save_customer_whitelabel_properties_rm_by_user_id_v12] @sp_request_id, @user_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customer_whitelabel_properties_rm_by_user_id_v12]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_map READONLY
    , @all_attributes dbo.string_map READONLY
    , @new_attributes dbo.string_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_add_company_profile_to_receipt bit = NULL;
    DECLARE @new_company_hash binary(16) = NULL;
    DECLARE @new_company varbinary(8000) = NULL;
    DECLARE @new_time_zone_rm varchar(8000) = NULL;
    DECLARE @new_default_currency_id varchar(8000) = NULL;
    DECLARE @new_default_reward_program_id uniqueidentifier = NULL;
    DECLARE @new_default_reward_account_id uniqueidentifier = NULL;
    DECLARE @new_referrer_id uniqueidentifier = NULL;
    DECLARE @new_site_id uniqueidentifier = NULL;
    DECLARE @new_idp_user_id uniqueidentifier = NULL;
    DECLARE @new_type_rm int = NULL;
    DECLARE @new_fraud_status int = NULL;
    DECLARE @new_gaming_status int = NULL;
    DECLARE @new_is_news_letter bit = NULL;
    DECLARE @new_referral_signup_reward_uuid uniqueidentifier = NULL;
    DECLARE @new_date_pushed_to_salesforce datetime = NULL;
    DECLARE @new_promotion_signup_UUID uniqueidentifier = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = update_customer_whitelabel_properties_rm_by_user_id_v12
         @sp_request_id
         , @user_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_id = @new_user_id OUT
         , @new_add_company_profile_to_receipt = @new_add_company_profile_to_receipt OUT
         , @new_company = @new_company OUT
         , @new_time_zone_rm = @new_time_zone_rm OUT
         , @new_default_currency_id = @new_default_currency_id OUT
         , @new_default_reward_program_id = @new_default_reward_program_id OUT
         , @new_default_reward_account_id = @new_default_reward_account_id OUT
         , @new_referrer_id = @new_referrer_id OUT
         , @new_site_id = @new_site_id OUT
         , @new_idp_user_id = @new_idp_user_id OUT
         , @new_type_rm = @new_type_rm OUT
         , @new_fraud_status = @new_fraud_status OUT
         , @new_gaming_status = @new_gaming_status OUT
         , @new_is_news_letter = @new_is_news_letter OUT
         , @new_referral_signup_reward_uuid = @new_referral_signup_reward_uuid OUT
         , @new_date_pushed_to_salesforce = @new_date_pushed_to_salesforce OUT
         , @new_promotion_signup_UUID = @new_promotion_signup_UUID OUT
         , @new_origin = @new_origin OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_add_company_profile_to_receipt = MAX(IIF([key] = 'add_company_profile_to_receipt', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_company = MAX(IIF([key] = 'company', [value], NULL))
             , @new_company_hash = MAX(IIF([key] = 'company_hash', [value], NULL))
             , @new_time_zone_rm = MAX(IIF([key] = 'time_zone_rm', CAST(([value]) AS varchar(8000)), NULL))
             , @new_default_currency_id = MAX(IIF([key] = 'default_currency_id', CAST(([value]) AS varchar(8000)), NULL))
             , @new_default_reward_program_id = MAX(IIF([key] = 'default_reward_program_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_default_reward_account_id = MAX(IIF([key] = 'default_reward_account_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_referrer_id = MAX(IIF([key] = 'referrer_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_site_id = MAX(IIF([key] = 'site_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_idp_user_id = MAX(IIF([key] = 'idp_user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_type_rm = MAX(IIF([key] = 'type_rm', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_fraud_status = MAX(IIF([key] = 'fraud_status', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_gaming_status = MAX(IIF([key] = 'gaming_status', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_is_news_letter = MAX(IIF([key] = 'is_news_letter', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_referral_signup_reward_uuid = MAX(IIF([key] = 'referral_signup_reward_uuid', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_date_pushed_to_salesforce = MAX(IIF([key] = 'date_pushed_to_salesforce', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_promotion_signup_UUID = MAX(IIF([key] = 'promotion_signup_UUID', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.customer_whitelabel_properties_rm
        (
                    user_id
                    , add_company_profile_to_receipt
                    , company
                    , company_hash
                    , time_zone_rm
                    , default_currency_id
                    , default_reward_program_id
                    , default_reward_account_id
                    , referrer_id
                    , site_id
                    , idp_user_id
                    , type_rm
                    , fraud_status
                    , gaming_status
                    , is_news_letter
                    , referral_signup_reward_uuid
                    , date_pushed_to_salesforce
                    , promotion_signup_UUID
                    , origin
                    , rec_created_when
                    , server
                    , rec_status
        )
        OUTPUT      INSERTED.user_id
                    , INSERTED.add_company_profile_to_receipt
                    , INSERTED.company
                    , INSERTED.time_zone_rm
                    , INSERTED.default_currency_id
                    , INSERTED.default_reward_program_id
                    , INSERTED.default_reward_account_id
                    , INSERTED.referrer_id
                    , INSERTED.site_id
                    , INSERTED.idp_user_id
                    , INSERTED.type_rm
                    , INSERTED.fraud_status
                    , INSERTED.gaming_status
                    , INSERTED.is_news_letter
                    , INSERTED.referral_signup_reward_uuid
                    , INSERTED.date_pushed_to_salesforce
                    , INSERTED.promotion_signup_UUID
                    , INSERTED.origin
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_modified_when
                    , INSERTED.server
        VALUES
        (
                    @new_user_id
                    , @new_add_company_profile_to_receipt
                    , @new_company
                    , @new_company_hash
                    , @new_time_zone_rm
                    , @new_default_currency_id
                    , @new_default_reward_program_id
                    , @new_default_reward_account_id
                    , @new_referrer_id
                    , @new_site_id
                    , @new_idp_user_id
                    , @new_type_rm
                    , @new_fraud_status
                    , @new_gaming_status
                    , @new_is_news_letter
                    , @new_referral_signup_reward_uuid
                    , @new_date_pushed_to_salesforce
                    , @new_promotion_signup_UUID
                    , @new_origin
                    , @new_rec_created_when
                    , @new_server
                    , @new_rec_status
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_id AS user_id
               , @new_add_company_profile_to_receipt AS add_company_profile_to_receipt
               , @new_company AS company
               , @new_time_zone_rm AS time_zone_rm
               , @new_default_currency_id AS default_currency_id
               , @new_default_reward_program_id AS default_reward_program_id
               , @new_default_reward_account_id AS default_reward_account_id
               , @new_referrer_id AS referrer_id
               , @new_site_id AS site_id
               , @new_idp_user_id AS idp_user_id
               , @new_type_rm AS type_rm
               , @new_fraud_status AS fraud_status
               , @new_gaming_status AS gaming_status
               , @new_is_news_letter AS is_news_letter
               , @new_referral_signup_reward_uuid AS referral_signup_reward_uuid
               , @new_date_pushed_to_salesforce AS date_pushed_to_salesforce
               , @new_promotion_signup_UUID AS promotion_signup_UUID
               , @new_origin AS origin
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_modified_when AS rec_modified_when
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_customer_whitelabel_properties_rm_by_user_id_v12] TO [customer_api_user]
GO
