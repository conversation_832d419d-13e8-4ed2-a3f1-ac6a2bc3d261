/*
SQL_OBJECT_INFO

estimated_execution_count_per_minute: 400

END_SQL_OBJECT_INFO
*/
/*
-----------------------------------------------------------------------------------------------------------------------
-- Author                   | Date          | Comment
----------------------------|---------------|--------------------------------------------------------------------------
-- CustomerAPI              | 2025-01-30    | Creates or Updates the record in the customers by the primary key.
-- WARNING: Please don't modify auto-generated code!
----------------------------|---------------|--------------------------------------------------------------------------

EXEC_TEST
DECLARE @sp_request_id uniqueidentifier = NEWID();
DECLARE @user_id_value uniqueidentifier = NEWID();
DECLARE @member_id_value int = CAST(RAND() * 2147483647 AS int);

DECLARE @relations dbo.string_long_map;
DECLARE @attributes dbo.string_long_map;
INSERT INTO @attributes ([key], [value],[value_long]) VALUES
    ('user_id',CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@user_id_value AS VARCHAR(8000))))
    , ('member_id',CONVERT(VARBINARY(8000), CAST(@member_id_value AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(@member_id_value AS VARCHAR(8000))))
    , ('origin',CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('XX' AS VARCHAR(8000))))
    , ('whitelabel_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('title',0x, 0x)
    , ('title_hash',0x, 0x)
    , ('first_name',0x, 0x)
    , ('first_name_hash',0x, 0x)
    , ('middle_name',0x, 0x)
    , ('middle_name_hash',0x, 0x)
    , ('last_name',0x, 0x)
    , ('last_name_hash',0x, 0x)
    , ('display_name',0x, 0x)
    , ('display_name_hash',0x, 0x)
    , ('birthdate',0x, 0x)
    , ('birthdate_hash',0x, 0x)
    , ('nationality_id',0x, 0x)
    , ('nationality_id_hash',0x, 0x)
    , ('language_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('tax_id',0x, 0x)
    , ('tax_id_hash',0x, 0x)
    , ('manager_id',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('department_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('location_id',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('ccpa_opt_out',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('ccpa_last_updated',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('ccof_opt_out',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('newsletter_opt_out',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('is_single_task_consumption',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('is_login_blocked',CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(0 AS VARCHAR(8000))))
    , ('wallet_birthdate',0x, 0x)
    , ('wallet_birthdate_hash',0x, 0x)
    , ('wallet_country',0x, 0x)
    , ('wallet_country_hash',0x, 0x)
    , ('rec_status',CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST(1 AS VARCHAR(8000))))
    , ('rec_created_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_created_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('rec_modified_when',CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('1900-01-01 00:00:00' AS VARCHAR(8000))))
    , ('rec_modified_by',CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('00000000-0000-0000-0000-000000000000' AS VARCHAR(8000))))
    , ('server',CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))), CONVERT(VARBINARY(8000), CAST('' AS VARCHAR(8000))));

EXECUTE AS LOGIN = 'customer_api_user'
EXEC [dbo].[save_customers_by_user_id_v15] @sp_request_id, @user_id_value, @relations, @attributes, @attributes;
REVERT;
END_EXEC_TEST
-----------------------------------------------------------------------------------------------------------------------
*/
CREATE PROCEDURE [dbo].[save_customers_by_user_id_v15]
    @sp_request_id uniqueidentifier
    , @user_id uniqueidentifier
    , @relations dbo.string_long_map READONLY
    , @all_attributes dbo.string_long_map READONLY
    , @new_attributes dbo.string_long_map READONLY
    , @timeout_in_ms bigint = NULL
    , @kafka_topic varchar(255) = NULL
    , @kafka_partition int = NULL
    , @kafka_offset bigint = NULL
    , @kafka_timestamp bigint = NULL
    , @measurement_info varchar(8000) = NULL OUTPUT
AS
BEGIN
    SET NOCOUNT ON;  -- Prevents extra result sets from interfering with SELECT statements
    SET XACT_ABORT ON;  -- Turns on rollback if T-SQL statement raises a run-time error
    SET ARITHABORT ON; -- Ensure that divide-by-zero error is enabled
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    -- Use hard-timeout for auto-committed transactions
    IF @timeout_in_ms IS NOT NULL
    BEGIN
        DECLARE @set_timeout nvarchar(128) = CONCAT(N'SET LOCK_TIMEOUT ', @timeout_in_ms);
        EXEC sp_executesql @set_timeout;
    END;
    
    DECLARE @sp_start datetime2 = SYSDATETIME();

    -- Avoid nested transactions
    DECLARE @in_transaction bit = IIF(@@TRANCOUNT = 0, 0, 1);
    IF @in_transaction = 0 BEGIN TRANSACTION;

    -- Try to update before the data insertion
    DECLARE @new_user_id uniqueidentifier = NULL;
    DECLARE @new_member_id int = NULL;
    DECLARE @new_origin char(2) = NULL;
    DECLARE @new_whitelabel_id smallint = NULL;
    DECLARE @new_title_hash binary(16) = NULL;
    DECLARE @new_title varbinary(8000) = NULL;
    DECLARE @new_first_name_hash binary(16) = NULL;
    DECLARE @new_first_name varbinary(8000) = NULL;
    DECLARE @new_middle_name_hash binary(16) = NULL;
    DECLARE @new_middle_name varbinary(8000) = NULL;
    DECLARE @new_last_name_hash binary(16) = NULL;
    DECLARE @new_last_name varbinary(8000) = NULL;
    DECLARE @new_display_name_hash binary(16) = NULL;
    DECLARE @new_display_name varbinary(8000) = NULL;
    DECLARE @new_birthdate_hash binary(16) = NULL;
    DECLARE @new_birthdate varbinary(8000) = NULL;
    DECLARE @new_nationality_id_hash binary(16) = NULL;
    DECLARE @new_nationality_id varbinary(8000) = NULL;
    DECLARE @new_language_id int = NULL;
    DECLARE @new_tax_id_hash binary(16) = NULL;
    DECLARE @new_tax_id varbinary(8000) = NULL;
    DECLARE @new_manager_id uniqueidentifier = NULL;
    DECLARE @new_department_id int = NULL;
    DECLARE @new_location_id int = NULL;
    DECLARE @new_ccpa_opt_out bit = NULL;
    DECLARE @new_ccpa_last_updated datetime = NULL;
    DECLARE @new_ccof_opt_out bit = NULL;
    DECLARE @new_newsletter_opt_out bit = NULL;
    DECLARE @new_is_single_task_consumption bit = NULL;
    DECLARE @new_is_login_blocked bit = NULL;
    DECLARE @new_wallet_birthdate_hash binary(16) = NULL;
    DECLARE @new_wallet_birthdate varbinary(8000) = NULL;
    DECLARE @new_wallet_country_hash binary(16) = NULL;
    DECLARE @new_wallet_country varbinary(8000) = NULL;
    DECLARE @new_rec_status smallint = NULL;
    DECLARE @new_rec_created_when datetime = NULL;
    DECLARE @new_rec_created_by uniqueidentifier = NULL;
    DECLARE @new_rec_modified_when datetime = NULL;
    DECLARE @new_rec_modified_by uniqueidentifier = NULL;
    DECLARE @new_server varchar(255) = NULL;
    DECLARE @affected_rows int;
    EXEC @affected_rows = dbo.update_customers_by_user_id_v15
         @sp_request_id
         , @user_id
         , @relations
         , @new_attributes
         , @timeout_in_ms
         , @kafka_topic = NULL
         , @kafka_partition = NULL
         , @kafka_offset = NULL
         , @kafka_timestamp = NULL
         , @measurement_info = @measurement_info OUT
         , @new_user_id = @new_user_id OUT
         , @new_member_id = @new_member_id OUT
         , @new_origin = @new_origin OUT
         , @new_whitelabel_id = @new_whitelabel_id OUT
         , @new_title = @new_title OUT
         , @new_first_name = @new_first_name OUT
         , @new_middle_name = @new_middle_name OUT
         , @new_last_name = @new_last_name OUT
         , @new_display_name = @new_display_name OUT
         , @new_birthdate = @new_birthdate OUT
         , @new_nationality_id = @new_nationality_id OUT
         , @new_language_id = @new_language_id OUT
         , @new_tax_id = @new_tax_id OUT
         , @new_manager_id = @new_manager_id OUT
         , @new_department_id = @new_department_id OUT
         , @new_location_id = @new_location_id OUT
         , @new_ccpa_opt_out = @new_ccpa_opt_out OUT
         , @new_ccpa_last_updated = @new_ccpa_last_updated OUT
         , @new_ccof_opt_out = @new_ccof_opt_out OUT
         , @new_newsletter_opt_out = @new_newsletter_opt_out OUT
         , @new_is_single_task_consumption = @new_is_single_task_consumption OUT
         , @new_is_login_blocked = @new_is_login_blocked OUT
         , @new_wallet_birthdate = @new_wallet_birthdate OUT
         , @new_wallet_country = @new_wallet_country OUT
         , @new_rec_status = @new_rec_status OUT
         , @new_rec_created_when = @new_rec_created_when OUT
         , @new_rec_created_by = @new_rec_created_by OUT
         , @new_rec_modified_when = @new_rec_modified_when OUT
         , @new_rec_modified_by = @new_rec_modified_by OUT
         , @new_server = @new_server OUT;

    IF @affected_rows < 1
    BEGIN
        -- Unroll the row values before the insert a row in the table
        SELECT   @new_user_id = MAX(IIF([key] = 'user_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_member_id = MAX(IIF([key] = 'member_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_origin = MAX(IIF([key] = 'origin', CONVERT(char(2), CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_whitelabel_id = MAX(IIF([key] = 'whitelabel_id', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_title = MAX(IIF([key] = 'title', [value], NULL))
             , @new_title_hash = MAX(IIF([key] = 'title_hash', [value], NULL))
             , @new_first_name = MAX(IIF([key] = 'first_name', [value], NULL))
             , @new_first_name_hash = MAX(IIF([key] = 'first_name_hash', [value], NULL))
             , @new_middle_name = MAX(IIF([key] = 'middle_name', [value], NULL))
             , @new_middle_name_hash = MAX(IIF([key] = 'middle_name_hash', [value], NULL))
             , @new_last_name = MAX(IIF([key] = 'last_name', [value], NULL))
             , @new_last_name_hash = MAX(IIF([key] = 'last_name_hash', [value], NULL))
             , @new_display_name = MAX(IIF([key] = 'display_name', [value], NULL))
             , @new_display_name_hash = MAX(IIF([key] = 'display_name_hash', [value], NULL))
             , @new_birthdate = MAX(IIF([key] = 'birthdate', [value], NULL))
             , @new_birthdate_hash = MAX(IIF([key] = 'birthdate_hash', [value], NULL))
             , @new_nationality_id = MAX(IIF([key] = 'nationality_id', [value], NULL))
             , @new_nationality_id_hash = MAX(IIF([key] = 'nationality_id_hash', [value], NULL))
             , @new_language_id = MAX(IIF([key] = 'language_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_tax_id = MAX(IIF([key] = 'tax_id', [value], NULL))
             , @new_tax_id_hash = MAX(IIF([key] = 'tax_id_hash', [value], NULL))
             , @new_manager_id = MAX(IIF([key] = 'manager_id', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_department_id = MAX(IIF([key] = 'department_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_location_id = MAX(IIF([key] = 'location_id', CONVERT(int, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_ccpa_opt_out = MAX(IIF([key] = 'ccpa_opt_out', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_ccpa_last_updated = MAX(IIF([key] = 'ccpa_last_updated', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_ccof_opt_out = MAX(IIF([key] = 'ccof_opt_out', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_newsletter_opt_out = MAX(IIF([key] = 'newsletter_opt_out', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_is_single_task_consumption = MAX(IIF([key] = 'is_single_task_consumption', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_is_login_blocked = MAX(IIF([key] = 'is_login_blocked', CONVERT(int, CAST([value] AS VARCHAR(8000))), NULL))
             , @new_wallet_birthdate = MAX(IIF([key] = 'wallet_birthdate', [value], NULL))
             , @new_wallet_birthdate_hash = MAX(IIF([key] = 'wallet_birthdate_hash', [value], NULL))
             , @new_wallet_country = MAX(IIF([key] = 'wallet_country', [value], NULL))
             , @new_wallet_country_hash = MAX(IIF([key] = 'wallet_country_hash', [value], NULL))
             , @new_rec_status = MAX(IIF([key] = 'rec_status', CONVERT(smallint, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_when = MAX(IIF([key] = 'rec_created_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_created_by = MAX(IIF([key] = 'rec_created_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_modified_when = MAX(IIF([key] = 'rec_modified_when', CONVERT(datetime, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_rec_modified_by = MAX(IIF([key] = 'rec_modified_by', CONVERT(uniqueidentifier, CAST(([value]) AS VARCHAR(8000))), NULL))
             , @new_server = MAX(IIF([key] = 'server', CAST(([value]) AS varchar(255)), NULL))
        FROM     @all_attributes
        GROUP BY ();
        INSERT INTO dbo.customers_synonym
        (
                    user_id
                    , member_id
                    , origin
                    , whitelabel_id
                    , title
                    , title_hash
                    , first_name
                    , first_name_hash
                    , middle_name
                    , middle_name_hash
                    , last_name
                    , last_name_hash
                    , display_name
                    , display_name_hash
                    , birthdate
                    , birthdate_hash
                    , nationality_id
                    , nationality_id_hash
                    , language_id
                    , tax_id
                    , tax_id_hash
                    , manager_id
                    , department_id
                    , location_id
                    , ccpa_opt_out
                    , ccpa_last_updated
                    , ccof_opt_out
                    , newsletter_opt_out
                    , is_single_task_consumption
                    , is_login_blocked
                    , wallet_birthdate
                    , wallet_birthdate_hash
                    , wallet_country
                    , wallet_country_hash
                    , rec_status
                    , rec_created_when
                    , rec_created_by
                    , rec_modified_when
                    , rec_modified_by
                    , server
        )
        OUTPUT      INSERTED.user_id
                    , INSERTED.member_id
                    , INSERTED.origin
                    , INSERTED.whitelabel_id
                    , INSERTED.title
                    , INSERTED.first_name
                    , INSERTED.middle_name
                    , INSERTED.last_name
                    , INSERTED.display_name
                    , INSERTED.birthdate
                    , INSERTED.nationality_id
                    , INSERTED.language_id
                    , INSERTED.tax_id
                    , INSERTED.manager_id
                    , INSERTED.department_id
                    , INSERTED.location_id
                    , INSERTED.ccpa_opt_out
                    , INSERTED.ccpa_last_updated
                    , INSERTED.ccof_opt_out
                    , INSERTED.newsletter_opt_out
                    , INSERTED.is_single_task_consumption
                    , INSERTED.is_login_blocked
                    , INSERTED.wallet_birthdate
                    , INSERTED.wallet_country
                    , INSERTED.rec_status
                    , INSERTED.rec_created_when
                    , INSERTED.rec_created_by
                    , INSERTED.rec_modified_when
                    , INSERTED.rec_modified_by
                    , INSERTED.server
        VALUES
        (
                    @new_user_id
                    , @new_member_id
                    , @new_origin
                    , @new_whitelabel_id
                    , @new_title
                    , @new_title_hash
                    , @new_first_name
                    , @new_first_name_hash
                    , @new_middle_name
                    , @new_middle_name_hash
                    , @new_last_name
                    , @new_last_name_hash
                    , @new_display_name
                    , @new_display_name_hash
                    , @new_birthdate
                    , @new_birthdate_hash
                    , @new_nationality_id
                    , @new_nationality_id_hash
                    , @new_language_id
                    , @new_tax_id
                    , @new_tax_id_hash
                    , @new_manager_id
                    , @new_department_id
                    , @new_location_id
                    , @new_ccpa_opt_out
                    , @new_ccpa_last_updated
                    , @new_ccof_opt_out
                    , @new_newsletter_opt_out
                    , @new_is_single_task_consumption
                    , @new_is_login_blocked
                    , @new_wallet_birthdate
                    , @new_wallet_birthdate_hash
                    , @new_wallet_country
                    , @new_wallet_country_hash
                    , @new_rec_status
                    , @new_rec_created_when
                    , @new_rec_created_by
                    , @new_rec_modified_when
                    , @new_rec_modified_by
                    , @new_server
        );
    END
    ELSE
    BEGIN
        SELECT @new_user_id AS user_id
               , @new_member_id AS member_id
               , @new_origin AS origin
               , @new_whitelabel_id AS whitelabel_id
               , @new_title AS title
               , @new_first_name AS first_name
               , @new_middle_name AS middle_name
               , @new_last_name AS last_name
               , @new_display_name AS display_name
               , @new_birthdate AS birthdate
               , @new_nationality_id AS nationality_id
               , @new_language_id AS language_id
               , @new_tax_id AS tax_id
               , @new_manager_id AS manager_id
               , @new_department_id AS department_id
               , @new_location_id AS location_id
               , @new_ccpa_opt_out AS ccpa_opt_out
               , @new_ccpa_last_updated AS ccpa_last_updated
               , @new_ccof_opt_out AS ccof_opt_out
               , @new_newsletter_opt_out AS newsletter_opt_out
               , @new_is_single_task_consumption AS is_single_task_consumption
               , @new_is_login_blocked AS is_login_blocked
               , @new_wallet_birthdate AS wallet_birthdate
               , @new_wallet_country AS wallet_country
               , @new_rec_status AS rec_status
               , @new_rec_created_when AS rec_created_when
               , @new_rec_created_by AS rec_created_by
               , @new_rec_modified_when AS rec_modified_when
               , @new_rec_modified_by AS rec_modified_by
               , @new_server AS server;
    END;

    IF @kafka_topic IS NOT NULL EXEC dbo.write_consumer_offset_v2 @sp_request_id, @kafka_topic, @kafka_partition, @kafka_offset, @kafka_timestamp;
    IF @in_transaction = 0 COMMIT TRANSACTION;

    DECLARE @sp_end datetime2 = SYSDATETIME();
    SET @measurement_info = CONCAT(
        REPLACE(@measurement_info, 'elapsed', 'update-elapsed'),
        '<elapsed>', DATEDIFF(millisecond , @sp_start, @sp_end), '</elapsed>'
   );
END
GO

GRANT EXECUTE ON [dbo].[save_customers_by_user_id_v15] TO [customer_api_user]
GO
