------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Siravitch Ch.	| 2017-09-18	| Create SP for update gap data for migration customer api rw
-- Siravitch Ch.	| 2017-09-26	| Add insert log error
-- Siravitch Ch.	| 2017-10-12	| Fix for unique value
-- Rati P.		| 2017-10-17	| remove get Max ID 
-- Sir<PERSON><PERSON> Ch.	| 2018-04-26	| add userid column
-- Jantakarn A.		| 2018-05-18	| v3: add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
-- MONKEY_TEST EXEC dbo.dba_capi_sync_rew_members_tracking_v3
------------------------|---------------|------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_sync_rew_members_tracking_v3]
AS
BEGIN 

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	DECLARE @actiontype char(1)
		,@tracking_id int
		,@table_name varchar(50) = 'rew_members'
		,@getdate datetime = GETDATE()
		,@MemberCode varchar(50)

	BEGIN TRY

		SELECT	tracking_id
			,action_type
			,MemberID
		INTO	#tracking
		FROM	Agoda_dataupload.dbo.[rew_members_tracking_MDC] 
		ORDER BY tracking_id ASC

		WHILE EXISTS (SELECT * FROM #tracking)
		BEGIN
			BEGIN TRANSACTION

				-- SELECT TOP 1
				SELECT TOP 1 @actiontype = action_type
					,@tracking_id = tracking_id
				FROM	#tracking
				ORDER BY	tracking_id ASC

				--INSERT
				IF (@actiontype = 'I')
				BEGIN

					SELECT	@MemberCode = MemberCode
					FROM	Agoda_dataupload.dbo.[rew_members_tracking_MDC] 
					WHERE	tracking_id = @tracking_id


					IF EXISTS ( SELECT * FROM dbo.rew_members WHERE MemberCode IS NOT NULL AND MemberCode = @MemberCode)
					BEGIN 

						DELETE FROM dbo.rew_members
						WHERE	MemberCode IS NOT NULL 
						AND	MemberCode = @MemberCode

					END

					INSERT INTO dbo.rew_members (
						MemberID
						,MemberCode
						,Password
						,member_rating
						,title
						,first_name
						,middle_name
						,last_name
						,suffix
						,birth_date
						,nationality_id
						,organisation_id
						,language_id
						,points_earned
						,points_redeemed
						,point_balance
						,points_expire_this_year
						,auto_sign_up
						,is_elite_status
						,elite_activation_date
						,is_blacklisted
						,elite_expire_date
						,deactivation_date
						,deactivation_reason
						,merged_to
						,special_remarks
						,is_newsletter
						,rec_status
						,rec_created_by
						,rec_created_when
						,rec_modify_by
						,rec_modify_when
						,signup_mail_duedate
						,is_signup_sent
						,is_upgraded
						,is_point_eligible
						,is_cc_onfile_opt_out
						,loyalty_level
						,giftcard_status
						,giftcard_level
						,is_one_click_bf_ready
						,prefer_partner_loyalty_program_id
						,UserId
						,lastupdated_when
					)
					SELECT	MemberID
						,MemberCode
						,Password
						,member_rating
						,title
						,first_name
						,middle_name
						,last_name
						,suffix
						,birth_date
						,nationality_id
						,organisation_id
						,language_id
						,points_earned
						,points_redeemed
						,point_balance
						,points_expire_this_year
						,auto_sign_up
						,is_elite_status
						,elite_activation_date
						,is_blacklisted
						,elite_expire_date
						,deactivation_date
						,deactivation_reason
						,merged_to
						,special_remarks
						,is_newsletter
						,rec_status
						,rec_created_by
						,rec_created_when
						,rec_modify_by
						,rec_modify_when
						,signup_mail_duedate
						,is_signup_sent
						,is_upgraded
						,is_point_eligible
						,is_cc_onfile_opt_out
						,loyalty_level
						,giftcard_status
						,giftcard_level
						,is_one_click_bf_ready
						,prefer_partner_loyalty_program_id
						,UserId
						,lastupdated_when
					FROM	Agoda_dataupload.dbo.[rew_members_tracking_MDC] a
					WHERE	a.tracking_id = @tracking_id
					AND	NOT EXISTS (
								SELECT	*
								FROM	dbo.rew_members c
								WHERE	a.MemberID = c.MemberID
						)

				END

				-- UPDATE	
				ELSE IF (@actiontype = 'U')
				BEGIN

					UPDATE	a
					SET	a.MemberCode = b.MemberCode
						, a.Password = b.Password
						, a.member_rating = b.member_rating
						, a.title = b.title
						, a.first_name = b.first_name
						, a.middle_name = b.middle_name
						, a.last_name = b.last_name
						, a.suffix = b.suffix
						, a.birth_date = b.birth_date
						, a.nationality_id = b.nationality_id
						, a.organisation_id = b.organisation_id
						, a.language_id = b.language_id
						, a.points_earned = b.points_earned
						, a.points_redeemed = b.points_redeemed
						, a.point_balance = b.point_balance
						, a.points_expire_this_year = b.points_expire_this_year
						, a.auto_sign_up = b.auto_sign_up
						, a.is_elite_status = b.is_elite_status
						, a.elite_activation_date = b.elite_activation_date
						, a.is_blacklisted = b.is_blacklisted
						, a.elite_expire_date = b.elite_expire_date
						, a.deactivation_date = b.deactivation_date
						, a.deactivation_reason = b.deactivation_reason
						, a.merged_to = b.merged_to
						, a.special_remarks = b.special_remarks
						, a.is_newsletter = b.is_newsletter
						, a.rec_status = b.rec_status
						, a.rec_created_by = b.rec_created_by
						, a.rec_created_when = b.rec_created_when
						, a.rec_modify_by = b.rec_modify_by
						, a.rec_modify_when = b.rec_modify_when
						, a.signup_mail_duedate = b.signup_mail_duedate
						, a.is_signup_sent= b.is_signup_sent
						, a.is_upgraded = b.is_upgraded
						, a.is_point_eligible = b.is_point_eligible
						, a.is_cc_onfile_opt_out = b.is_cc_onfile_opt_out
						, a.loyalty_level = b.loyalty_level
						, a.giftcard_status = b.giftcard_status
						, a.giftcard_level = b.giftcard_level
						, a.is_one_click_bf_ready = b.is_one_click_bf_ready
						, a.prefer_partner_loyalty_program_id = b.prefer_partner_loyalty_program_id
						, a.UserId = b.UserId
						, a.lastupdated_when = b.lastupdated_when
					FROM	Agoda_dataupload.dbo.[rew_members_tracking_MDC] b
					INNER JOIN	dbo.rew_members a
						ON	a.MemberID = b.MemberID
					WHERE	b.tracking_id = @tracking_id

				END

				ELSE IF (@actiontype = 'D')
				BEGIN

					DELETE	a
					FROM	dbo.rew_members a
					INNER JOIN Agoda_dataupload.dbo.[rew_members_tracking_MDC] b 
						ON	a.MemberID = b.MemberID
					WHERE	b.tracking_id = @tracking_id

				END
					
				
				-- DELETE TOP1
				DELETE FROM #tracking WHERE tracking_id = @tracking_id

			COMMIT TRANSACTION

		END

	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END

			EXEC dbo.dba_capi_migration_job_error_log_insert @table_name, @err_msg_1 , @getdate
				
		RAISERROR(
				@err_msg_1
				,16
				,1
			)
	END CATCH
END
GO

