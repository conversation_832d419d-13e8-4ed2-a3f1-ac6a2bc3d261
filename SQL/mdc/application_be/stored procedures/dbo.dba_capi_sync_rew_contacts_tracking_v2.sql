------------------------|---------------|------------------------------------------------------------------------
-- Author		        | Date		    | Comment
------------------------|---------------|------------------------------------------------------------------------
-- Jantakarn A.	        | 2017-09-19	| Create SP for update gap data for migration customer api rw
-- Siravitch Ch.	| 2017-09-26	| Add insert log error
-- Siravitch Ch.	| 2017-10-12	| Fix for unique value
-- Rati P.		| 2017-10-17	| remove get Max ID 
-- Sir<PERSON>tch Ch.	| 2018-05-07	| update logic
-- Jantakarn A.		| 2018-05-18	| v2: add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
-- MONKEY_TEST EXEC dbo.dba_capi_sync_rew_contacts_tracking_v2
------------------------|---------------|------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_sync_rew_contacts_tracking_v2]
	
AS
BEGIN 
	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	DECLARE @actiontype char(1)
		,@tracking_id int
		,@table_name varchar(50) = 'rew_contacts'
		,@getdate datetime = GETDATE()
		,@contact_method_value nvarchar(200)
		,@table_id int
		,@contact_method_id int
		,@rec_status int 


	BEGIN TRY

		--SELECT	MAX(tracking_id) AS tracking_id
		--	,action_type
		--	,contact_id
		--INTO	#tracking
		--FROM	Agoda_dataupload.dbo.[rew_contacts_tracking_MDC] 
		--GROUP BY action_type, contact_id
		--ORDER BY tracking_id ASC

		SELECT	tracking_id
			,action_type
			,contact_id
		INTO	#tracking
		FROM	Agoda_dataupload.dbo.[rew_contacts_tracking_MDC] 
		ORDER BY tracking_id ASC
		
		SELECT	@table_id = ID
		FROM	Agoda_working.dbo.capi_migration_tables
		WHERE	Table_name = @table_name

		WHILE EXISTS (SELECT * FROM #tracking)
		BEGIN
			BEGIN TRANSACTION
				
				DECLARE	@capi_migration_tracking_id bigint

				-- SELECT TOP 1
				SELECT TOP 1 @actiontype = action_type
					,@tracking_id = tracking_id
				FROM	#tracking
				ORDER BY	tracking_id ASC

				INSERT INTO Agoda_working.dbo.capi_migration_tracking (
					table_id
					, tracking_id
					, migration_date
					) 
					VALUES (
					@table_id
					, @tracking_id
					, GETDATE()
					)

				SELECT @capi_migration_tracking_id = SCOPE_IDENTITY()

				--INSERT
				IF (@actiontype = 'I')
				BEGIN

					 SELECT  @contact_method_value = contact_method_value
						, @contact_method_id = contact_method_id
						, @rec_status = rec_status
					FROM	Agoda_dataupload.dbo.[rew_contacts_tracking_MDC]
					WHERE	tracking_id = @tracking_id

					IF EXISTS (	SELECT * FROM dbo.rew_contacts 
							WHERE rec_status = 1 
							AND contact_method_id = 1 
							AND contact_method_value = @contact_method_value
							AND @contact_method_id = 1
							AND @rec_status = 1
						)
					BEGIN 

						DELETE FROM dbo.rew_contacts
						WHERE	rec_status = 1 
						AND	contact_method_id = 1 
						AND	contact_method_value = @contact_method_value

					END


					INSERT INTO dbo.rew_contacts (
						[contact_id]
				      ,[contact_method_id]
				      ,[MemberID]
				      ,[organisation_id]
				      ,[contact_method_value]
				      ,[contact_method_remark]
				      ,[rec_status]
				      ,[rec_created_by]
				      ,[rec_created_when]
				      ,[rec_modify_by]
				      ,[rec_modify_when]
				      ,[is_valid]
				      ,[lastupdated_when]
					)
					SELECT	[contact_id]
				      ,[contact_method_id]
				      ,[MemberID]
				      ,[organisation_id]
				      ,[contact_method_value]
				      ,[contact_method_remark]
				      ,[rec_status]
				      ,[rec_created_by]
				      ,[rec_created_when]
				      ,[rec_modify_by]
				      ,[rec_modify_when]
				      ,[is_valid]
				      ,[lastupdated_when]
					FROM	Agoda_dataupload.dbo.[rew_contacts_tracking_MDC] a
					WHERE	a.tracking_id = @tracking_id
					AND	NOT EXISTS (
								SELECT	*
								FROM	dbo.rew_contacts c
								WHERE	A.contact_id = c.contact_id
							)

					IF(@@ROWCOUNT <> 0)
					BEGIN

						UPDATE	Agoda_working.dbo.capi_migration_tracking
						SET	[action_type] = @actiontype
						WHERE	id = @capi_migration_tracking_id

					END

				END

				-- UPDATE CASE
				ELSE IF (@actiontype = 'U')
				BEGIN

					UPDATE	a
					SET	a.[contact_method_id] = b.[contact_method_id]
					,a.[MemberID] = b.[MemberID]
					,a.[organisation_id] = b.[organisation_id]
					,a.[contact_method_value] = b.[contact_method_value]
					,a.[contact_method_remark] = b.[contact_method_remark]
					,a.[rec_status] = b.[rec_status]
					,a.[rec_created_by] = b.[rec_created_by]
					,a.[rec_created_when] = b.[rec_created_when]
					,a.[rec_modify_by] = b.[rec_modify_by]
					,a.[rec_modify_when] = b.[rec_modify_when]
					,a.[is_valid] = b.[is_valid]
					,a.[lastupdated_when] = b.[lastupdated_when]
					FROM	Agoda_dataupload.dbo.[rew_contacts_tracking_MDC] b
					INNER JOIN	dbo.rew_contacts a
						ON	a.contact_id = b.contact_id
					WHERE	b.tracking_id = @tracking_id

					IF(@@ROWCOUNT <> 0)
					BEGIN

						UPDATE	Agoda_working.dbo.capi_migration_tracking
						SET	[action_type] = @actiontype
						WHERE	id = @capi_migration_tracking_id

					END

				END

				-- DELETE CASE
				ELSE IF (@actiontype = 'D')
				BEGIN

					DELETE	a
					FROM	dbo.rew_contacts a
					INNER JOIN Agoda_dataupload.dbo.[rew_contacts_tracking_MDC] b 
						ON	a.contact_id = b.contact_id
					WHERE	b.tracking_id = @tracking_id

					IF(@@ROWCOUNT <> 0)
					BEGIN

						UPDATE	Agoda_working.dbo.capi_migration_tracking
						SET	[action_type] = @actiontype
						WHERE	id = @capi_migration_tracking_id

					END

				END
				-- DELETE TOP1
				DELETE FROM #tracking WHERE tracking_id = @tracking_id

			COMMIT TRANSACTION
		END


	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END

			EXEC dbo.dba_capi_migration_job_error_log_insert @table_name, @err_msg_1 , @getdate
				
		RAISERROR(
				@err_msg_1
				,16
				,1
			)
	END CATCH
END









GO

