------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Sumeth W. 		| 2018-01-10	| Create SP for update gap data for migration customer api rw
-- Sumeth W.		| 2018-05-17	| add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
-- EXEC dbo.dba_capi_bo_mdc_sync_rew_redeem_levels_tracking_v2
------------------------|---------------|------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_sync_rew_redeem_levels_tracking_v2]

AS
BEGIN 
 
 	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	DECLARE @actiontype char(1)
		, @tracking_id int
		, @table_name varchar(50) = 'rew_redeem_levels'
		, @getdate datetime = GETDATE()

	BEGIN TRY
		
		SELECT	tracking_id
			,action_type
			,redeem_level_id
		INTO	#rew_redeem_levels_tracking
		FROM	Agoda_dataupload.dbo.rew_redeem_levels_tracking_MDC 
		ORDER BY tracking_id ASC

		WHILE EXISTS (SELECT * FROM #rew_redeem_levels_tracking)
		BEGIN

			BEGIN TRANSACTION
				
				-- SELECT TOP 1
				SELECT TOP 1 @actiontype = action_type
					,@tracking_id = tracking_id
				FROM	#rew_redeem_levels_tracking
				ORDER BY	tracking_id ASC

				--INSERT
				IF (@actiontype = 'I')
				BEGIN

					INSERT INTO dbo.rew_redeem_levels (
						redeem_level_id
						, redeemed_points
						, award_value
						, is_elite_level
						, is_premier_level
						, rec_status
						, rec_created_by
						, rec_created_when
						, rec_modify_by
						, rec_modify_when
						, lastupdated_when
					)
					SELECT	redeem_level_id
						, redeemed_points
						, award_value
						, is_elite_level
						, is_premier_level
						, rec_status
						, rec_created_by
						, rec_created_when
						, rec_modify_by
						, rec_modify_when
						, lastupdated_when
					FROM	Agoda_dataupload.dbo.rew_redeem_levels_tracking_MDC rat
					WHERE	rat.tracking_id = @tracking_id
					AND	NOT EXISTS (
								SELECT	*
								FROM	dbo.rew_redeem_levels ra
								WHERE	rat.redeem_level_id = ra.redeem_level_id
							)
				END

				-- UPDATE	
				ELSE IF (@actiontype = 'U')
				BEGIN

					UPDATE	a
					SET	a.redeemed_points = b.redeemed_points 
						,a.award_value= b.award_value 
						,a.is_elite_level= b.is_elite_level
						,a.is_premier_level= b.is_premier_level
						,a.rec_status= b.rec_status
						,a.rec_created_by= b.rec_created_by
						,a.rec_created_when= b.rec_created_when
						,a.rec_modify_by= b.rec_modify_by
						,a.rec_modify_when= b.rec_modify_when
						,a.lastupdated_when = b.lastupdated_when
					FROM	Agoda_dataupload.dbo.rew_redeem_levels_tracking_MDC b
					INNER JOIN	dbo.rew_redeem_levels a
						ON	a.redeem_level_id = b.redeem_level_id 
					WHERE	b.tracking_id = @tracking_id

				END

				ELSE IF (@actiontype = 'D')
				BEGIN

					DELETE	a
					FROM	dbo.rew_redeem_levels a
					INNER JOIN Agoda_dataupload.dbo.rew_redeem_levels_tracking_MDC b 
						ON a.redeem_level_id = b.redeem_level_id 
					WHERE	b.tracking_id = @tracking_id

				END

				-- DELETE TOP1
				DELETE FROM #rew_redeem_levels_tracking WHERE tracking_id = @tracking_id

			COMMIT TRANSACTION

		END

	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END

			EXEC dbo.dba_capi_migration_job_error_log_insert @table_name, @err_msg_1 , @getdate
				
		RAISERROR(
				@err_msg_1
				,16
				,1
			)
		
	END CATCH
END







GO

