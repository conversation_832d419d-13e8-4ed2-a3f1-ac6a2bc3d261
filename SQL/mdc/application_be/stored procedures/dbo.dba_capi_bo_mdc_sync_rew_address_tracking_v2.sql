------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Sumeth W. 		| 2018-01-10	| Create SP for update gap data for migration customer api rw
-- Jantakarn A.		| 2018-05-18	| v2: add lastupdated_when column
------------------------|---------------|------------------------------------------------------------------------
--MONKEY TEST EXEC dbo.dba_capi_bo_mdc_sync_rew_address_tracking_v2
------------------------|---------------|------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_sync_rew_address_tracking_v2]

AS
BEGIN 

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	DECLARE @actiontype char(1)
		, @tracking_id int
		, @table_name varchar(50) = 'rew_address'
		, @getdate datetime = GETDATE()

	BEGIN TRY
		
		SELECT	tracking_id
			,action_type
			,address_id
		INTO	#rew_address_tracking
		FROM	Agoda_dataupload.dbo.rew_address_tracking_MDC 
		ORDER BY tracking_id ASC

		WHILE EXISTS (SELECT * FROM #rew_address_tracking)
		BEGIN

			BEGIN TRANSACTION
				
				-- SELECT TOP 1
				SELECT TOP 1 @actiontype = action_type
					,@tracking_id = tracking_id
				FROM	#rew_address_tracking
				ORDER BY	tracking_id ASC

				--INSERT
				IF (@actiontype = 'I')
				BEGIN

					INSERT INTO dbo.rew_address (
						address_id
						, address_type_id
						, MemberID
						, organisation_id
						, address_1
						, address_2
						, postal_code
						, region
						, country
						, [state]
						, city
						, area
						, rec_status
						, rec_created_by
						, rec_created_when
						, rec_modify_by
						, rec_modify_when
						, country_id
						, lastupdated_when
					)
					SELECT	address_id
						, address_type_id
						, MemberID
						, organisation_id
						, address_1
						, address_2
						, postal_code
						, region
						, country
						, [state]
						, city
						, area
						, rec_status
						, rec_created_by
						, rec_created_when
						, rec_modify_by
						, rec_modify_when
						, country_id
						, lastupdated_when
					FROM	Agoda_dataupload.dbo.rew_address_tracking_MDC rat
					WHERE	rat.tracking_id = @tracking_id
					AND	NOT EXISTS (
								SELECT	*
								FROM	dbo.rew_address ra
								WHERE	rat.address_id = ra.address_id
							)
				END

				-- UPDATE	
				ELSE IF (@actiontype = 'U')
				BEGIN

					UPDATE	a
					SET	a.address_type_id = b.address_type_id 
						,a.MemberID= b.MemberID 
						,a.organisation_id= b.organisation_id
						,a.address_1= b.address_1
						,a.address_2= b.address_2
						,a.postal_code= b.postal_code
						,a.region= b.region
						,a.country= b.country
						,a.[state]= b.[state]
						,a.city= b.city
						,a.area= b.area
						,a.rec_status= b.rec_status
						,a.rec_created_by= b.rec_created_by
						,a.rec_created_when= b.rec_created_when
						,a.rec_modify_by= b.rec_modify_by
						,a.rec_modify_when= b.rec_modify_when
						,a.country_id= b.country_id
						,a.lastupdated_when = b.lastupdated_when
					FROM	Agoda_dataupload.dbo.rew_address_tracking_MDC b
					INNER JOIN	dbo.rew_address a
						ON	a.address_id = b.address_id 
					WHERE	b.tracking_id = @tracking_id

				END

				ELSE IF (@actiontype = 'D')
				BEGIN

					DELETE	a
					FROM	dbo.rew_address a
					INNER JOIN Agoda_dataupload.dbo.rew_address_tracking_MDC b 
						ON a.address_id = b.address_id 
					WHERE	b.tracking_id = @tracking_id

				END

				-- DELETE TOP1
				DELETE FROM #rew_address_tracking WHERE tracking_id = @tracking_id

			COMMIT TRANSACTION

		END

	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END

			EXEC dbo.dba_capi_migration_job_error_log_insert @table_name, @err_msg_1 , @getdate
				
		RAISERROR(
				@err_msg_1
				,16
				,1
			)
		
	END CATCH
END







GO

