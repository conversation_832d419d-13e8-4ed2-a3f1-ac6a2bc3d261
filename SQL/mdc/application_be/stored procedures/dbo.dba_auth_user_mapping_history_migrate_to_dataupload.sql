
-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-03-07	| migrate old data to backup on dataupload
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_auth_user_mapping_history_migrate_to_dataupload]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_auth_user_mapping_history_migrate_to_dataupload]
	@batch_size int = 1000
	, @date date = '2018-02-22'
AS
BEGIN

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF NOT EXISTS (SELECT 1 FROM agoda_dataupload.sys.objects WHERE type ='U' AND name = 'auth_user_mapping_history_backup')
	BEGIN
		
		CREATE TABLE [agoda_dataupload].[dbo].[auth_user_mapping_history_backup](
			[logtime] [datetime] NOT NULL,
			[user_id] [uniqueidentifier] NOT NULL,
			[authentication_type_id] [tinyint] NOT NULL,
			[ActivityId] [int] NOT NULL,
			[rec_status] [int] NOT NULL,
			[rec_created_when] [datetime] NOT NULL,
			[rec_created_by] [uniqueidentifier] NOT NULL,
			[rec_modified_when] [datetime] NULL,
			[rec_modified_by] [uniqueidentifier] NULL,
			[history_id] [bigint] NOT NULL,
		 CONSTRAINT [PK_auth_user_mapping_history_backup] PRIMARY KEY CLUSTERED 
		(
			[history_id] ASC
		)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
		) ON [PRIMARY]
	
	END

	WHILE EXISTS ( SELECT 1 FROM [Agoda_Customer_API_RW].dbo.auth_user_mapping_history WHERE logtime <= @date ) 
	BEGIN

		BEGIN TRANSACTION

				;WITH CTE AS 
				(
					SELECT	TOP (@batch_size) [logtime]
					,[user_id]
					,[authentication_type_id]
					,[ActivityId]
					,[rec_status]
					,[rec_created_when]
					,[rec_created_by]
					,[rec_modified_when]
					,[rec_modified_by]
					,[history_id]
					FROM	[Agoda_Customer_API_RW].[dbo].[auth_user_mapping_history]
					WHERE	logtime <= @date
					ORDER BY	[history_id] ASC

				)

				INSERT INTO [agoda_dataupload].[dbo].[auth_user_mapping_history_backup] (
					[logtime]
					,[user_id]
					,[authentication_type_id]
					,[ActivityId]
					,[rec_status]
					,[rec_created_when]
					,[rec_created_by]
					,[rec_modified_when]
					,[rec_modified_by]
					,[history_id] )
				SELECT	TOP (@batch_size) [logtime]
					,[user_id]
					,[authentication_type_id]
					,[ActivityId]
					,[rec_status]
					,[rec_created_when]
					,[rec_created_by]
					,[rec_modified_when]
					,[rec_modified_by]
					,[history_id]
				FROM	CTE A
				WHERE	NOT EXISTS (	
							SELECT	B.[history_id]
							FROM	[agoda_dataupload].[dbo].[auth_user_mapping_history_backup] B
							WHERE	A.[history_id] = B.[history_id]
							)

				;WITH CTE AS 
				(
					SELECT	TOP (@batch_size) [logtime]
					,[user_id]
					,[authentication_type_id]
					,[ActivityId]
					,[rec_status]
					,[rec_created_when]
					,[rec_created_by]
					,[rec_modified_when]
					,[rec_modified_by]
					,[history_id]
					FROM	[Agoda_Customer_API_RW].[dbo].[auth_user_mapping_history]
					WHERE	logtime <= @date
					ORDER BY	[history_id] ASC

				)

				DELETE FROM CTE

		COMMIT TRANSACTION
		WAITFOR DELAY '00:00:03'
	END
END

GO
