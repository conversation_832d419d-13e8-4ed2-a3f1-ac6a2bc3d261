------------------------|---------------|------------------------------------------------------------------------
-- Author				| Date			| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Rati P.				| 2018-04-03    | Create SP for inseting, updating and deleting gap data.
------------------------|---------------|------------------------------------------------------------------------
-- EXEC dbo.dba_capi_bo_mdc_sync_rew2_reason_tracking
------------------------|---------------|------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_sync_rew2_reason_tracking]
    
AS
BEGIN 
    SET NOCOUNT ON
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    DECLARE @actiontype char(1)
        ,@tracking_id int
        ,@table_name varchar(50) = 'rew2_reason'
        ,@getdate datetime = GETDATE()
    BEGIN TRY
       

        SELECT  tracking_id
				,action_type
				,reason_id
        INTO    #tracking
        FROM    Agoda_dataupload.dbo.rew2_reason_tracking_MDC
        ORDER BY tracking_id ASC
        
        WHILE EXISTS (SELECT * FROM #tracking)
        BEGIN
            BEGIN TRANSACTION
                
                -- SELECT TOP 1
                SELECT TOP 1 @actiontype = action_type
                    ,@tracking_id = tracking_id
                FROM    #tracking
                ORDER BY    tracking_id ASC
                --INSERT
                IF (@actiontype = 'I')
                BEGIN
                    INSERT INTO dbo.rew2_reason (
                      	[reason_id] 
					  ,[reason_type_id]
					  ,[name]
					  ,[rec_status]
					  ,[rec_created_when]
					  ,[rec_created_by]
					  ,[rec_modified_when]
					  ,[rec_modified_by]
                    )
                    SELECT  	[reason_id] 
					  ,[reason_type_id]
					  ,[name]
					  ,[rec_status]
					  ,[rec_created_when]
					  ,[rec_created_by]
					  ,[rec_modified_when]
					  ,[rec_modified_by]
                    FROM    Agoda_dataupload.dbo.rew2_reason_tracking_MDC a
                    WHERE   a.tracking_id = @tracking_id
                    AND NOT EXISTS (
                                SELECT  *
                                FROM    dbo.rew2_reason c
                                WHERE   A.[reason_id] = c.[reason_id]
                            )
                END
                -- UPDATE CASE
                ELSE IF (@actiontype = 'U')
                BEGIN
                    UPDATE  a
                    SET 
                         a.[reason_type_id]	= b.[reason_type_id]		
			,a.[name]		= b.[name]				
			,a.[rec_status]		= b.[rec_status]			
			,a.[rec_created_when]	= b.[rec_created_when]	
			,a.[rec_created_by]	= b.[rec_created_by]		
			,a.[rec_modified_when]	= b.[rec_modified_when]	
			,a.[rec_modified_by]	= b.[rec_modified_by]	
                    FROM    Agoda_dataupload.dbo.rew2_reason_tracking_MDC b
                    INNER JOIN  dbo.rew2_reason a
                        ON  A.[reason_id] = b.[reason_id]
                    WHERE   b.tracking_id = @tracking_id
                END
                -- DELETE CASE
                ELSE IF (@actiontype = 'D')
                BEGIN
                    DELETE  a
                    FROM    dbo.rew2_reason a
                    INNER JOIN Agoda_dataupload.dbo.rew2_reason_tracking_MDC b 
                        ON  A.[reason_id] = b.[reason_id]
                    WHERE   b.tracking_id = @tracking_id
                END
                -- DELETE TOP1
                DELETE FROM #tracking WHERE tracking_id = @tracking_id
            COMMIT TRANSACTION
        END
    END TRY
    BEGIN CATCH
        DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()
        IF XACT_STATE() <> 0 
        BEGIN
            ROLLBACK TRANSACTION
        END
            EXEC dbo.dba_capi_migration_job_error_log_insert @table_name, @err_msg_1 , @getdate
                
        RAISERROR(
                @err_msg_1
                ,16
                ,1
            )
    END CATCH
END

GO
