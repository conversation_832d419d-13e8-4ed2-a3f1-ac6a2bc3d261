
-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-03-30	| create sp for capi backoffice migration
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_capi_bo_mdc_migration_initial_auth_user_exception]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_migration_initial_auth_user_exception]
AS
BEGIN

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @batch_size int = 500
		, @delay_time varchar(15) = '00:00:10'

	BEGIN TRY

	SELECT	@batch_size = batch_size
		, @delay_time = delay_time
	FROM	agoda_dataupload.dbo.capi_migration_parameter
	WHERE	table_name = 'rew_address'

	WHILE EXISTS (	SELECT	*
			FROM	agoda_dataupload.dbo.auth_user_exception_initial
			WHERE	is_processed = 0
			)
	BEGIN
	
		BEGIN TRANSACTION

			;WITH CTE
			AS
			(
				SELECT TOP (@batch_size) [user_id] 
					,[username]
				FROM	agoda_dataupload.dbo.auth_user_exception_initial
				WHERE	is_processed = 0
				ORDER BY user_id ASC

			)

			INSERT INTO Agoda_Customer_API_RW.dbo.auth_user_exception (
				[user_id] 
				,[username])
			SELECT	[user_id] 
				,[username]
			FROM	CTE a
			WHERE	NOT EXISTS ( SELECT user_id FROM Agoda_Customer_API_RW.dbo.auth_user_exception b WHERE a.user_id = b.user_id )
			
			;WITH CTE AS
			(
				SELECT TOP (@batch_size) is_processed
				FROM	agoda_dataupload.dbo.auth_user_exception_initial
				WHERE	is_processed = 0
				ORDER BY user_id ASC

			)

			UPDATE	a
			SET	is_processed = 1
			FROM	CTE a

		COMMIT TRANSACTION
		WAITFOR DELAY @delay_time



	END

	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END
	END CATCH
	
				

END
GO
