-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-03-27	| create sp for capi backoffice migration
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_capi_bo_mdc_migration_initial_rew2_comment]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_migration_initial_rew2_comment]
AS
BEGIN

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @batch_size int = 500
		, @delay_time varchar(15) = '00:00:10'

	BEGIN TRY

	SELECT	@batch_size = batch_size
		, @delay_time = delay_time
	FROM	agoda_dataupload.dbo.capi_migration_parameter
	WHERE	table_name = 'rew_address'

	WHILE EXISTS (	SELECT	*
			FROM	agoda_dataupload.dbo.rew2_comment_initial
			WHERE	is_processed = 0
			)
	BEGIN
	
		BEGIN TRANSACTION

			;WITH CTE
			AS
			(
				SELECT TOP (@batch_size) [comment_id]
					,[comment]
					,[rec_status]
					,[rec_created_when]
					,[rec_created_by]
					,[rec_modified_when]
					,[rec_modified_by]
				FROM	agoda_dataupload.dbo.rew2_comment_initial
				WHERE	is_processed = 0
				ORDER BY comment_id ASC

			)

			INSERT INTO Agoda_Customer_API_RW.dbo.rew2_comment (
				[comment_id]
				,[comment]
				,[rec_status]
				,[rec_created_when]
				,[rec_created_by]
				,[rec_modified_when]
				,[rec_modified_by])
			SELECT	[comment_id]
				,[comment]
				,[rec_status]
				,[rec_created_when]
				,[rec_created_by]
				,[rec_modified_when]
				,[rec_modified_by]
			FROM	CTE a
			WHERE	NOT EXISTS ( SELECT comment_id FROM Agoda_Customer_API_RW.dbo.rew2_comment b WHERE a.comment_id = b.comment_id )
			
			;WITH CTE AS
			(
				SELECT TOP (@batch_size) is_processed
				FROM	agoda_dataupload.dbo.rew2_comment_initial
				WHERE	is_processed = 0
				ORDER BY comment_id ASC

			)

			UPDATE	a
			SET	is_processed = 1
			FROM	CTE a

		COMMIT TRANSACTION
		WAITFOR DELAY @delay_time



	END

	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END
	END CATCH
	
				

END
GO
