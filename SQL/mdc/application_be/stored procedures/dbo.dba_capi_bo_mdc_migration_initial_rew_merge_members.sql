

-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-03-27	| create sp for capi backoffice migration
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_capi_bo_mdc_migration_initial_rew_merge_members]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_migration_initial_rew_merge_members]
AS
BEGIN

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @batch_size int = 500
		, @delay_time varchar(15) = '00:00:10'

	BEGIN TRY

	SELECT	@batch_size = batch_size
		, @delay_time = delay_time
	FROM	agoda_dataupload.dbo.capi_migration_parameter
	WHERE	table_name = 'rew_address'

	WHILE EXISTS (	SELECT	*
			FROM	agoda_dataupload.dbo.rew_merge_members_initial
			WHERE	is_processed = 0
			)
	BEGIN
	
		BEGIN TRANSACTION

			;WITH CTE
			AS
			(
				SELECT TOP (@batch_size) [MergeId]
				,[MemberId1]
				,[MemberId2]
				,[MergeStatusId]
				,[IsStatementConfirm]
				,[comment]
				,[Reason]
				,[Requester]
				,[RequestDate]
				,[Approver]
				,[ApproveDate]
				,[MergeTimeStamp]
				FROM	agoda_dataupload.dbo.rew_merge_members_initial
				WHERE	is_processed = 0
				ORDER BY MergeId ASC

			)

			INSERT INTO Agoda_Customer_API_RW.dbo.rew_merge_members (
				[MergeId]
				,[MemberId1]
				,[MemberId2]
				,[MergeStatusId]
				,[IsStatementConfirm]
				,[comment]
				,[Reason]
				,[Requester]
				,[RequestDate]
				,[Approver]
				,[ApproveDate]
				,[MergeTimeStamp])
			SELECT	[MergeId]
				,[MemberId1]
				,[MemberId2]
				,[MergeStatusId]
				,[IsStatementConfirm]
				,[comment]
				,[Reason]
				,[Requester]
				,[RequestDate]
				,[Approver]
				,[ApproveDate]
				,[MergeTimeStamp]
			FROM	CTE a
			WHERE	NOT EXISTS ( SELECT MergeId FROM Agoda_Customer_API_RW.dbo.rew_merge_members b WHERE a.MergeId = b.MergeId )
			
			;WITH CTE AS
			(
				SELECT TOP (@batch_size) is_processed
				FROM	agoda_dataupload.dbo.rew_merge_members_initial
				WHERE	is_processed = 0
				ORDER BY MergeId ASC

			)

			UPDATE	a
			SET	is_processed = 1
			FROM	CTE a

		COMMIT TRANSACTION
		WAITFOR DELAY @delay_time



	END

	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END
	END CATCH
	
				

END











GO
