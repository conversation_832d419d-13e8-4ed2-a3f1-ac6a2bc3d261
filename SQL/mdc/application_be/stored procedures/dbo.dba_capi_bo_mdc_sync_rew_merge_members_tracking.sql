
------------------------|---------------|------------------------------------------------------------------------
-- Author		| Date		| Comment
------------------------|---------------|------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-04-03	| Create SP for inseting, updating and deleting gap data.
------------------------|---------------|------------------------------------------------------------------------
-- EXEC dbo.dba_capi_bo_mdc_sync_rew_merge_members_tracking
------------------------|---------------|------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_bo_mdc_sync_rew_merge_members_tracking]
	
AS
BEGIN 

	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

	DECLARE @actiontype char(1)
		,@tracking_id int
		,@table_name varchar(50) = 'rew_merge_members'
		,@getdate datetime = GETDATE()

	BEGIN TRY

		--SELECT	MAX(tracking_id) AS tracking_id
		--	,action_type
		--	,contact_id
		--INTO	#tracking
		--FROM	Agoda_dataupload.dbo.[rew_contacts_tracking_MDC] 
		--GROUP BY action_type, contact_id
		--ORDER BY tracking_id ASC

		SELECT	tracking_id
			,action_type
			,[MergeId]
		INTO	#tracking
		FROM	Agoda_dataupload.dbo.rew_merge_members_tracking_MDC
		ORDER BY tracking_id ASC
		
		WHILE EXISTS (SELECT * FROM #tracking)
		BEGIN
			BEGIN TRANSACTION
				
				-- SELECT TOP 1
				SELECT TOP 1 @actiontype = action_type
					,@tracking_id = tracking_id
				FROM	#tracking
				ORDER BY	tracking_id ASC

				--INSERT
				IF (@actiontype = 'I')
				BEGIN

					INSERT INTO dbo.rew_merge_members (
						[MergeId]
						,[MemberId1]
						,[MemberId2]
						,[MergeStatusId]
						,[IsStatementConfirm]
						,[comment]
						,[Reason]
						,[Requester]
						,[RequestDate]
						,[Approver]
						,[ApproveDate]
						,[MergeTimeStamp]
					)
					SELECT	[MergeId]
						,[MemberId1]
						,[MemberId2]
						,[MergeStatusId]
						,[IsStatementConfirm]
						,[comment]
						,[Reason]
						,[Requester]
						,[RequestDate]
						,[Approver]
						,[ApproveDate]
						,[MergeTimeStamp]
					FROM	Agoda_dataupload.dbo.rew_merge_members_tracking_MDC a
					WHERE	a.tracking_id = @tracking_id
					AND	NOT EXISTS (
								SELECT	*
								FROM	dbo.rew_merge_members c
								WHERE	A.[MergeId] = c.[MergeId]
							)
				END

				-- UPDATE CASE
				ELSE IF (@actiontype = 'U')
				BEGIN

					UPDATE	a
					SET	a.[MemberId1] = b.[MemberId1]
						,a.[MemberId2] = b.[MemberId2]
						,a.[MergeStatusId] = b.[MergeStatusId]
						,a.[IsStatementConfirm] = b.[IsStatementConfirm]
						,a.[comment] = b.[comment]
						,a.[Reason] = b.[Reason]
						,a.[Requester] = b.[Requester]
						,a.[RequestDate] = b.[RequestDate]
						,a.[Approver] = b.[Approver]
						,a.[ApproveDate] = b.[ApproveDate]
						,a.[MergeTimeStamp] = b.[MergeTimeStamp]
					FROM	Agoda_dataupload.dbo.rew_merge_members_tracking_MDC b
					INNER JOIN	dbo.rew_merge_members a
						ON	A.[MergeId] = b.[MergeId]
					WHERE	b.tracking_id = @tracking_id

				END

				-- DELETE CASE
				ELSE IF (@actiontype = 'D')
				BEGIN

					DELETE	a
					FROM	dbo.rew_merge_members a
					INNER JOIN Agoda_dataupload.dbo.rew_merge_members_tracking_MDC b 
						ON	A.[MergeId] = b.[MergeId]
					WHERE	b.tracking_id = @tracking_id

				END
				-- DELETE TOP1
				DELETE FROM #tracking WHERE tracking_id = @tracking_id

			COMMIT TRANSACTION
		END


	END TRY
	BEGIN CATCH
		DECLARE @err_msg_1 NVARCHAR(MAX) = ERROR_MESSAGE()

		IF XACT_STATE() <> 0 
		BEGIN
			ROLLBACK TRANSACTION
		END

			EXEC dbo.dba_capi_migration_job_error_log_insert @table_name, @err_msg_1 , @getdate
				
		RAISERROR(
				@err_msg_1
				,16
				,1
			)
	END CATCH
END
GO


