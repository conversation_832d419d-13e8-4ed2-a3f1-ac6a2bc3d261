-------------------------------------------------------------------------------------------------------------
-- Author		| Date 		| Comment
-------------------------------------------------------------------------------------------------------------
-- Siravitch Ch.	| 2018-04-05	| create data for cleanup date of capi_lexis_people
-------------------------------------------------------------------------------------------------------------
-- EXEC [dbo].[dba_capi_lexis_people_cleanup]
-------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[dba_capi_lexis_people_cleanup]
	@batch_size int = 1000
	, @date date = '2018-03-28'
AS
BEGIN

	SET XACT_ABORT ON
	SET NOCOUNT ON
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF NOT EXISTS (SELECT 1 FROM agoda_dataupload.sys.objects WHERE type ='U' AND name = 'capi_lexis_people_cleanup_backup')
	BEGIN
		
		CREATE TABLE [agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup](
			[people_id] [bigint] NOT NULL,
			[first_name] [nvarchar](100) NOT NULL,
			[last_name] [nvarchar](100) NOT NULL,
			[score] [int] NOT NULL,
			[is_suspicious] [bit] NOT NULL,
			[created_when] [datetime] NOT NULL,
			[modified_when] [datetime] NULL,
			[detail] [nvarchar](max) NULL,
			[datacenter] [varchar](2) NOT NULL,
			[is_deleted] [bit] NOT NULL DEFAULT 0
		 CONSTRAINT [PK_capi_lexis_people_cleanup_backup] PRIMARY KEY CLUSTERED 
		(
			[people_id] ASC
		)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
		) ON [PRIMARY]

		CREATE NONCLUSTERED INDEX IX01_capi_lexis_people_cleanup_backup_is_deleted ON [agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup]
		( is_deleted ASC )  INCLUDE (people_id)

	
	END


	INSERT INTO [agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup] (
		[people_id]
		,[first_name]
		,[last_name]
		,[score]
		,[is_suspicious]
		,[created_when]
		,[modified_when]
		,[detail]
		,[datacenter]
		) 
	SELECT	[people_id]
		,[first_name]
		,[last_name]
		,[score]
		,[is_suspicious]
		,[created_when]
		,[modified_when]
		,[detail]
		,[datacenter]
	FROM	[Agoda_Customer_RW].[dbo].[capi_lexis_people] A
	WHERE	[created_when] < @date
	AND	NOT EXISTS	(
					SELECT	people_id
					FROM	[agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup] B
					WHERE	A.people_id = b.people_id
				)
	ORDER BY [people_id] ASC


	WHILE EXISTS ( SELECT 1 FROM [agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup] WHERE is_deleted = 0 ) 
	BEGIN

		BEGIN TRANSACTION

				;WITH CTE AS 
				(
					SELECT	TOP (@batch_size) [people_id]
					FROM	[agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup]
					WHERE	is_deleted = 0
					ORDER BY [people_id] ASC
				)

				DELETE FROM [Agoda_Customer_RW].[dbo].[capi_lexis_people]
				WHERE	people_id IN ( SELECT [people_id] FROM CTE )

				;WITH CTE AS 
				(
					SELECT	TOP (@batch_size) [people_id]
						, [is_deleted]
					FROM	[agoda_dataupload].[backup_data].[capi_lexis_people_cleanup_backup]
					WHERE	is_deleted = 0
					ORDER BY [people_id] ASC
				)
				
				UPDATE	CTE
				SET	is_deleted = 1


		COMMIT TRANSACTION
		WAITFOR DELAY '00:00:03'
	END

END
GO


