package com.agoda.winterfell.unified.clientSecret.message

import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.adp.messaging.scala.message.Message
import com.agoda.winterfell.input.ClientUserTrackingData
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.models.entities.CustomerModel
import com.agoda.winterfell.utils.CapiConfig

import java.util.UUID
import scala.util.Try

case class AddClientSecretMessage(
    @SchemaComment("capi user id") capiUserId: UUID,
    @SchemaComment("memberId") memberId: Int,
    @SchemaComment("request created by") recCreatedBy: UUID,
    @SchemaComment("is success") isSuccess: Boolean,
    @SchemaComment("exception Message") exceptionMessage: Option[String],
    // client request context
    @SchemaComment("Endpoint") endpoint: String,
    @SchemaComment("teamName") teamName: String,
    @SchemaComment("agInitiatorIp") agInitiatorIp: Option[String],
    @SchemaComment("capiClientIp") capiClientIp: Option[String],
    @SchemaComment("capiClientAppName") capiClientAppName: Option[String],
    @SchemaComment("capiCorrelationId") capiCorrelationId: Option[String],
    @SchemaComment("capiRequestId") capiRequestId: Option[String],
    @SchemaComment("capiSessionId") capiSessionId: Option[String],
    @SchemaComment("isBot") isBot: Boolean,
    @SchemaComment("clientDataUserAgent") clientDataUserAgent: Option[String],
    @SchemaComment("simplifiedSource") simplifiedSource: Option[String],
    @SchemaComment("apcSourceServiceName") apcSourceServiceName: Option[String],
    @SchemaComment("xAgodaSsotName") xAgodaSsotName: Option[String],
    @SchemaComment("sourceServiceName") sourceServiceName: Option[String],
    @SchemaComment("xSourceServiceName") xSourceServiceName: Option[String],
    // client data
    @SchemaComment("userId") feUserId: Option[String],
    @SchemaComment("ip") ip: Option[String],
    @SchemaComment("deviceId") deviceId: Option[String],
    @SchemaComment("appId") appId: Option[String],
    @SchemaComment("origin") feOrigin: Option[String],
    @SchemaComment("cookieId") cookieId: Option[String],
    @SchemaComment("sessionId") sessionId: Option[String],
    @SchemaComment("referralUrl") referralUrl: Option[String],
    @SchemaComment("userAgent") userAgent: Option[String],
    @SchemaComment("correlationId") correlationId: Option[String],
    @SchemaComment("transactionReferenceId") transactionReferenceId: Option[String])
    extends Message[AddClientSecretMessage]

object AddClientSecretMessage {
  def sendAsync(customerModel: CustomerModel, result: Try[Any], recCreatedBy: UUID)(
      implicit clientData: ClientUserTrackingData,
      capiCtx: CapiRequestContextData) = {

    AddClientSecretMessage(
      capiUserId = customerModel.userId,
      memberId = customerModel.memberId,
      recCreatedBy = recCreatedBy,
      isSuccess = result.isSuccess,
      exceptionMessage = if (result.isSuccess) None else result.failed.toOption.map(_.getMessage),
      endpoint = capiCtx.endpoint,
      teamName = capiCtx.teamName,
      capiClientIp = capiCtx.ipAddress,
      agInitiatorIp = capiCtx.agInitiatorIp,
      capiClientAppName = capiCtx.clientApp,
      capiCorrelationId = capiCtx.correlationId,
      capiRequestId = capiCtx.requestId,
      capiSessionId = capiCtx.sessionId,
      isBot = capiCtx.isBotDetected,
      clientDataUserAgent = capiCtx.clientDataUserAgent,
      simplifiedSource = capiCtx.simplifiedSource,
      apcSourceServiceName = capiCtx.apcSourceServiceName,
      xAgodaSsotName = capiCtx.xAgodaSsotName,
      sourceServiceName = capiCtx.sourceServiceName,
      xSourceServiceName = capiCtx.xSourceServiceName,
      feUserId = clientData.userId,
      ip = clientData.ipAddress,
      deviceId = clientData.deviceId,
      appId = clientData.appId,
      feOrigin = clientData.origin,
      cookieId = clientData.cookieId,
      sessionId = clientData.sessionId,
      referralUrl = clientData.referralUrl,
      userAgent = clientData.userAgent,
      correlationId = clientData.correlationId,
      transactionReferenceId = clientData.transactionReferenceId
    ).sendAsync(CapiConfig.AdpApiKey)
  }
}
