package com.agoda.winterfell.unified.clientSecret.message

import com.agoda.adp.messaging.scala.message.Message
import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.winterfell.input.ClientUserTrackingData
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.models.messages.ExpiredClientSecretLogMessage
import com.agoda.winterfell.utils.CapiConfig

import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

case class ClientSecretUsageMessage(
    @SchemaComment("capi user id") capiUserId: UUID,
    @SchemaComment("memberId") memberId: Int,
    @SchemaComment("secret issue date") secretIssueDate: Instant,
    @SchemaComment("secret expire date") secretExpiryDate: Instant,
    @SchemaComment("is secret expire date on use") isSecretExpired: <PERSON><PERSON><PERSON>,
    @SchemaComment("ipAddress") ipAddress: String,
    // client request context
    @SchemaComment("Endpoint") endpoint: String,
    @SchemaComment("teamName") teamName: String,
    @SchemaComment("agInitiatorIp") agInitiatorIp: Option[String],
    @SchemaComment("capiClientIp") capiClientIp: Option[String],
    @SchemaComment("capiClientAppName") capiClientAppName: Option[String],
    @SchemaComment("capiCorrelationId") capiCorrelationId: Option[String],
    @SchemaComment("capiRequestId") capiRequestId: Option[String],
    @SchemaComment("capiSessionId") capiSessionId: Option[String],
    @SchemaComment("isBot") isBot: Boolean,
    @SchemaComment("clientDataUserAgent") clientDataUserAgent: Option[String],
    @SchemaComment("simplifiedSource") simplifiedSource: Option[String],
    @SchemaComment("apcSourceServiceName") apcSourceServiceName: Option[String],
    @SchemaComment("xAgodaSsotName") xAgodaSsotName: Option[String],
    @SchemaComment("sourceServiceName") sourceServiceName: Option[String],
    @SchemaComment("xSourceServiceName") xSourceServiceName: Option[String],
    // client data
    @SchemaComment("userId") feUserId: Option[String],
    @SchemaComment("ip") ip: Option[String],
    @SchemaComment("deviceId") deviceId: Option[String],
    @SchemaComment("appId") appId: Option[String],
    @SchemaComment("origin") feOrigin: Option[String],
    @SchemaComment("cookieId") cookieId: Option[String],
    @SchemaComment("sessionId") sessionId: Option[String],
    @SchemaComment("referralUrl") referralUrl: Option[String],
    @SchemaComment("userAgent") userAgent: Option[String],
    @SchemaComment("correlationId") correlationId: Option[String],
    @SchemaComment("transactionReferenceId") transactionReferenceId: Option[String])
    extends Message[ClientSecretUsageMessage]

object ClientSecretUsageMessage {
  def sendAsync(
      memberId: Int,
      userId: UUID,
      origin: String,
      whitelabelId: Int,
      secretCreatedWhen: Long,
      ipAddress: String,
      clientData: ClientUserTrackingData)(implicit capiCtx: CapiRequestContextData): Unit = {
    val now = Instant.now()
    val secretIssueDate = Instant.ofEpochMilli(secretCreatedWhen)
    val expiryDate = secretIssueDate.plus(CapiConfig.clientSecretAuthTypeConfig.secretExpiryInDays, ChronoUnit.DAYS)
    val isExpired = (now.isAfter(expiryDate))
    if (isExpired)
      ExpiredClientSecretLogMessage(
        memberId = memberId,
        userId = userId,
        origin = origin,
        whitelabelId = whitelabelId,
        secretCreatedWhen = secretCreatedWhen,
        ipAddress = ipAddress).sendAsync(CapiConfig.AdpApiKey)

    ClientSecretUsageMessage(
      capiUserId = userId,
      memberId = memberId,
      secretIssueDate = secretIssueDate,
      secretExpiryDate = expiryDate,
      isSecretExpired = isExpired,
      ipAddress = ipAddress,
      endpoint = capiCtx.endpoint,
      teamName = capiCtx.teamName,
      capiClientIp = capiCtx.ipAddress,
      agInitiatorIp = capiCtx.agInitiatorIp,
      capiClientAppName = capiCtx.clientApp,
      capiCorrelationId = capiCtx.correlationId,
      capiRequestId = capiCtx.requestId,
      capiSessionId = capiCtx.sessionId,
      isBot = capiCtx.isBotDetected,
      clientDataUserAgent = capiCtx.clientDataUserAgent,
      simplifiedSource = capiCtx.simplifiedSource,
      apcSourceServiceName = capiCtx.apcSourceServiceName,
      xAgodaSsotName = capiCtx.xAgodaSsotName,
      sourceServiceName = capiCtx.sourceServiceName,
      xSourceServiceName = capiCtx.xSourceServiceName,
      feUserId = clientData.userId,
      ip = clientData.ipAddress,
      deviceId = clientData.deviceId,
      appId = clientData.appId,
      feOrigin = clientData.origin,
      cookieId = clientData.cookieId,
      sessionId = clientData.sessionId,
      referralUrl = clientData.referralUrl,
      userAgent = clientData.userAgent,
      correlationId = clientData.correlationId,
      transactionReferenceId = clientData.transactionReferenceId
    ).sendAsync(CapiConfig.AdpApiKey)
  }
}
