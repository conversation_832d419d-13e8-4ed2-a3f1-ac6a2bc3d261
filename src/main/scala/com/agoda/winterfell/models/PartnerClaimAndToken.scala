package com.agoda.winterfell.models

import com.agoda.winterfell.unified.PartnerToken
import com.agoda.winterfell.unified.login.external.social.PartnerClaim

case class PartnerClaimAndToken(
    partnerClaim: Option[PartnerClaim] = None,
    partnerToken: Option[PartnerToken] = None,
    token: String,
    partnerParams: Map[String, String] = Map.empty,
    loyaltyAccountNumber: Option[String] = None,
    externalMemberId: Option[String] = None
)
