package com.agoda.winterfell.models

import com.agoda.winterfell.models.SecurityUriServer.{
  computeOldInsecureSHA1Hash,
  computeSHA1HashToUTF8,
  computeSHA256HashToUTF8
}
import com.agoda.winterfell.models.entities.SecurityUri
import org.mindrot.jbcrypt.BCrypt

import scala.util.Try

trait SecurityHashingStrategy {
  def hash(plaintext: String): String

  def fromEmail(email: String, password: Option[String], hashFunc: String => String): Security<PERSON>ri
  def fromPhone(phone: String, password: String, hashFunc: String => String): SecurityUri
  def generateUri(email: String, password: Option[String], ignorePasswordHashing: Option[Boolean]): SecurityUri
  def generatePhoneUri(phone: String, password: String, ignorePasswordHashing: Option[Boolean]): SecurityUri

  def correctPassword(plainText: String, hashed: String): Boolean
}

class AgodaHashingStrategy extends SecurityHashingStrategy {
  private def conditionalBcrypt(plaintext: String) = BCrypt.hashpw(computeSHA1HashToUTF8(plaintext), BCrypt.gensalt(8))

  override def hash(plaintext: String): String = {
    //Don't allow slashes in the password for easy parsing
    val hashed = conditionalBcrypt(plaintext)
    if (hashed.filter(_ == '/').isEmpty) hashed else hash(plaintext)
  }

  override def fromEmail(email: String, password: Option[String], hashFunc: String => String): SecurityUri =
    SecurityUri("agoda", email, password.map(hashFunc))

  override def fromPhone(phone: String, password: String, hashFunc: String => String): SecurityUri =
    SecurityUri("phone", phone, Some(hashFunc(password)))

  override def generateUri(
      email: String,
      password: Option[String],
      ignorePasswordHashing: Option[Boolean]): SecurityUri = {
    val hashFunc: String => String = if (ignorePasswordHashing.getOrElse(false)) String => String else hash
    val filteredPassword = password.filterNot(_ => email.contains("internal.agoda.in"))
    fromEmail(email, filteredPassword, hashFunc)
  }

  override def generatePhoneUri(phone: String, password: String, ignorePasswordHashing: Option[Boolean]): SecurityUri =
    fromPhone(phone, password, hash)

  //N.B. make sure you call this if it was a basic login call
  override def correctPassword(plainText: String, hashed: String): Boolean = {
    //The old sha1 (while migration is in progress). Delete this case eventually when none remain
    val str = computeOldInsecureSHA1Hash(plainText)
    hashed == str ||
    //Default case, bcrypt(sha1(password)), should be first check
    Try(BCrypt.checkpw(str, hashed)).getOrElse(false) ||
    //Handle a pre-hashed password provided directly from the client
    //This reduces the risk of it being logged in transit. Additionally, there was a period of time when
    //phone logins were not pre-shaed so we need this block anyway
    Try(BCrypt.checkpw(plainText, hashed)).getOrElse(false)
  }
}

class RocketMilesHashingStrategy extends SecurityHashingStrategy {
  private def rmConditionalBcrypt(plaintext: String) =
    BCrypt.hashpw(computeSHA256HashToUTF8(plaintext), BCrypt.gensalt(10))

  override def hash(plaintext: String): String = {
    //Don't allow slashes in the password for easy parsing
    val hashed = rmConditionalBcrypt(plaintext)
    if (hashed.filter(_ == '/').isEmpty) hashed else hash(plaintext)
  }

  override def fromEmail(email: String, password: Option[String], hashFunc: String => String): SecurityUri =
    SecurityUri("agoda", email, password.map(hashFunc))

  override def fromPhone(phone: String, password: String, hashFunc: String => String): SecurityUri =
    SecurityUri("phone", phone, Some(hashFunc(password)))

  override def generateUri(
      email: String,
      password: Option[String],
      ignorePasswordHashing: Option[Boolean]): SecurityUri = {
    val hashFunc: String => String = if (ignorePasswordHashing.getOrElse(false)) String => String else hash
    val filteredPassword = password.filterNot(_ => email.contains("internal.agoda.in"))
    fromEmail(email, filteredPassword, hashFunc)
  }

  override def generatePhoneUri(
      phone: String,
      password: String,
      ignorePasswordHashing: Option[Boolean]): SecurityUri = {
    val hashFunc: String => String = if (ignorePasswordHashing.getOrElse(false)) String => String else hash
    fromPhone(phone, password, hashFunc)
  }

  override def correctPassword(plainText: String, hashed: String): Boolean = {
    val str = computeSHA256HashToUTF8(plainText)
    Try(BCrypt.checkpw(str, hashed)).getOrElse(false)
  }
}
