package com.agoda.winterfell.models

import com.agoda.winterfell.utils.DBGroups._
import com.agoda.winterfell.utils.DBPlugin
import com.agoda.winterfell.utils.QueriesHelper._

//noinspection TypeAnnotation
object UserQueries {
  def apiMergedMemberBookingSelect(mergeId: Int)(implicit db: DBPlugin) =
    resultSet(WriteGroup).q("EXEC dbo.api_merged_member_booking_select ?", mergeId)

  def apiMemberBookingSelectMmbV3(memberId: Int)(implicit db: DBPlugin) =
    // ebe table is not in CDB and no request to this SP, V1/RewardsApiService/GetBookingListForManageMyBooking endpoint
    resultSet(WriteGroup).q("EXEC dbo.api_member_booking_select_mmb_v4 ?", memberId)

  def apiMemberBookingArchivalSelectMmbV3(memberId: Int)(implicit db: DBPlugin) =
    resultSet(ArchivalGroup).q("EXEC dbo.api_member_booking_select_mmb_v4 ?", memberId)

  def boMemberBookingSelect(memberId: Int, hotelUrlVersion: Int)(implicit db: DBPlugin) =
    // booking_cte and ebe tables is not in CDB and not much requests to this SP, V1/BORewardsApiService/GetBookingList endpoint
    resultSet(WriteGroup).q("EXEC dbo.backoffice_member_booking_select ?, ?", memberId, hotelUrlVersion)
}
