package com.agoda.winterfell.models.errors

import com.agoda.winterfell.InvalidRequestException
import com.agoda.winterfell.client.InvalidResponseCodeException

object UserPermittedIpAddressExceptions {

  case class InvalidIpAddressException(message: String) extends RuntimeException(message) with InvalidRequestException
  case class InvalidIpAddressRangeException(message: String)
      extends RuntimeException(message)
      with InvalidRequestException
  case class PermittedIpRangeNotFoundException(message: String) extends InvalidResponseCodeException(404, message)

}
