package com.agoda.winterfell.models.errors

import com.agoda.winterfell.common.ExchangeLoginLinkStatusCode.ExchangeLoginLinkStatusCode
import com.agoda.winterfell.common.ExchangeLoginLinkStatusCode

abstract class BaseLoginLinkException(message: String) extends IllegalArgumentException(message) {
  def getCode: ExchangeLoginLinkStatusCode
}

class LimitExceedException(message: String) extends BaseLoginLinkException(message) {
  override def getCode: ExchangeLoginLinkStatusCode = ExchangeLoginLinkStatusCode.LimitExceed
}
class MetaDataMismatchException(message: String) extends BaseLoginLinkException(message) {
  override def getCode: ExchangeLoginLinkStatusCode = ExchangeLoginLinkStatusCode.MetaDataMismatch
}
class CBKeyNotFoundException(message: String) extends BaseLoginLinkException(message) {
  override def getCode: ExchangeLoginLinkStatusCode = ExchangeLoginLinkStatusCode.TokenNotFound
}
