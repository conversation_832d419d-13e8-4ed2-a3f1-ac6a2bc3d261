package com.agoda.winterfell.models

import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.adp.messaging.scala.message.Message
import com.agoda.winterfell.Constants

import java.util.UUID

trait UserPermittedIpRangeUpdateTrackingService {
  def send(
      userId: UUID,
      action: String,
      changedUserPermittedIpRangeId: Option[UUID] = None,
      updatedBy: Option[UUID] = None)(implicit capiCtx: CapiRequestContextData): Unit
}

class UserPermittedIpRangeUpdateTrackingServiceImpl extends UserPermittedIpRangeUpdateTrackingService {

  override def send(userId: UUID, action: String, changedUserPermittedIpRangeId: Option[UUID], updatedBy: Option[UUID])(
      implicit capiCtx: CapiRequestContextData): Unit = {
    UserPermittedIpRangeUpdateMessage(
      userId,
      action,
      changedUserPermittedIpRangeId,
      updatedBy,
      capiCtx.apiKey,
      capiCtx.ipAddress,
      capiCtx.endpoint,
      capiCtx.httpMethod,
      capiCtx.clientApp,
      capiCtx.clientServer,
      capiCtx.teamName,
      capiCtx.correlationId,
      capiCtx.agInitiatorIp,
      capiCtx.agUserId,
      capiCtx.clientDataUserAgent,
    ).sendAsync(Constants.AdpApiKey)
  }

  private case class UserPermittedIpRangeUpdateMessage(
      @SchemaComment("UserId of member") userId: UUID,
      @SchemaComment("Action") action: String,
      @SchemaComment("Changed UserPermittedIpRangeId") changedUserPermittedIpRangeId: Option[UUID],
      @SchemaComment("Updated by UserId") updatedBy: Option[UUID],
      @SchemaComment("User API Key") apiKey: Option[String],
      @SchemaComment("Client IP") capiClientIp: Option[String],
      @SchemaComment("Endpoint") endpoint: String,
      @SchemaComment("HttpMethod") httpMethod: String,
      @SchemaComment("Client App") clientApp: Option[String],
      @SchemaComment("Client Server") clientServer: Option[String],
      @SchemaComment("Team Name") teamName: String,
      @SchemaComment("CorrelationId") correlationId: Option[String],
      @SchemaComment("Initiator IP") agInitiatorIP: Option[String],
      @SchemaComment("agUserId") agUserId: Option[String],
      @SchemaComment("Client data user agent") clientDataUserAgent: Option[String])
      extends Message[UserPermittedIpRangeUpdateMessage] {}
}
