package com.agoda.winterfell.models.entities

import java.time.Instant
import java.util.UUID
import com.agoda.dal.model.{EntityMetadata, EntityStatus}
import com.agoda.dal.syntax.Column
import com.agoda.winterfell.constant.Constants

case class CustomerHashMetaData(
    @Column("rec_status", insertable = true, updatable = true)
    status: EntityStatus = EntityStatus.Active,
    @Column("rec_created_when", insertable = true, updatable = true)
    createdWhen: Instant = Instant.now(),
    @Column("rec_created_by", insertable = true, updatable = true)
    createdBy: UUID = Constants.AppId,
    @Column("rec_modified_when", insertable = true, updatable = true)
    modifiedWhen: Option[Instant] = Some(Instant.now()),
    @Column("rec_modified_by", insertable = true, updatable = true)
    modifiedBy: Option[UUID] = Some(Constants.AppId))
    extends EntityMetadata

object CustomerHashMetaData {
  def transformCustomMeta(metaData: CustomMetadata): CustomerHashMetaData = {
    CustomerHashMetaData(
      metaData.status,
      metaData.createdWhen,
      metaData.createdBy,
      metaData.modifiedWhen,
      metaData.modifiedBy)
  }
}
