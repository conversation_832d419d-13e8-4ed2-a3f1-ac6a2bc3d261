package com.agoda.winterfell.models.entities
import com.agoda.dal.model.{BasicEntity, EntityOrigin}
import com.agoda.dal.syntax.{Key, Table}

import java.util.UUID

@Table("customers_hash")
case class CustomerHashEntity(
    @Key userId: UUID,
    @Key memberID: Int,
    whitelabelId: Short,
    firstNameHash: Option[Array[Byte]],
    lastNameHash: Option[Array[Byte]],
    override val origin: Option[EntityOrigin],
    metadata: CustomerHashMetaData = CustomerHashMetaData()
) extends BasicEntity[CustomerHashEntity] {}
