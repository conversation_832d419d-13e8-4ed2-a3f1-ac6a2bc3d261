package com.agoda.winterfell.models.entities

import com.agoda.dal.UUID
import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{BasicEntity, EntityMetadata}
import com.agoda.dal.syntax.{Column, Key, Table}
import com.agoda.winterfell.models.queries.UserIdQuery

@Table("user_permitted_ip_range")
case class UserPermittedIpRange(
    @Key userPermittedIpRangeId: UUID,
    userId: UUID,
    @Column("start_ip_address", insertable = true, updatable = true, length = 15)
    startIpAddress: AsciiString,
    @Column("end_ip_address", insertable = true, updatable = true, length = 15)
    endIpAddress: AsciiString,
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[UserPermittedIpRange] {

  def userIdQuery: UserIdQuery = UserIdQuery(userId)
}
