package com.agoda.winterfell.models.entities

import java.util.UUID

import org.joda.time.DateTime

case class GetAuthUsersDataRow(
    userId: UUID,
    email: String,
    displayName: String,
    managerId: Option[UUID],
    isSingleTaskUser: <PERSON><PERSON>an,
    recStatus: Int,
    createdWhen: DateTime,
    createdBy: UUID,
    modifiedWhen: Option[DateTime],
    modifiedBy: Option[UUID],
    departmentId: Option[Int],
    locationId: Option[Int]
)
