package com.agoda.winterfell.models.entities

import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Encrypted, Key, Table}
import com.agoda.winterfell.output.PartnerMembership

@Table("partner_memberships")
case class CustomerPartnerMembership(
    @Key id: PartnerMembershipId,
    override val origin: Option[EntityOrigin],
    @Encrypted membershipId: String,
    isPreferred: Boolean,
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[CustomerPartnerMembership] {
  override def messageKey: Option[Any] = {
    Some(id.userId)
  }
}

object CustomerPartnerMembership {
  def from(model: CustomerModel, membership: PartnerMembership, isPreferred: Boolean): CustomerPartnerMembership = {
    CustomerPartnerMembership(
      PartnerMembershipId(model.userId, membership.programId),
      model.memberDetails.origin.flatMap(EntityOrigin(_)),
      membership.membershipId,
      isPreferred
    )
  }
}
