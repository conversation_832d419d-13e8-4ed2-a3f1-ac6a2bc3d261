package com.agoda.winterfell
package models.entities

import java.util.UUID
import akka.http.scaladsl.model.{HttpMethod, HttpMethods}
import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{BasicEntity, EntityMetadata}
import com.agoda.dal.syntax.{Column, Encrypted, Key, Table}
import com.agoda.winterfell.models.requests.ApiConsumer
import com.agoda.winterfell.security.model.{ApiKeyPolicy, ApiPermission}

@Table("api_key_policy")
case class ApiKeyPolicies(
    @Key @Encrypted apiKey: String,
    @Column("white_label_tokens", insertable = true, updatable = true, length = Int.MaxValue)
    whiteLabelTokens: AsciiString,
    @Column("get_endpoints", insertable = true, updatable = true, length = Int.MaxValue)
    getEndpoints: Option[AsciiString],
    @Column("post_endpoints", insertable = true, updatable = true, length = Int.MaxValue)
    postEndpoints: Option[AsciiString],
    @Column("put_endpoints", insertable = true, updatable = true, length = Int.MaxValue)
    putEndpoints: Option[AsciiString],
    @Column("patch_endpoints", insertable = true, updatable = true, length = Int.MaxValue)
    patchEndpoints: Option[AsciiString],
    @Column("delete_endpoints", insertable = true, updatable = true, length = Int.MaxValue)
    deleteEndpoints: Option[AsciiString],
    teamName: AsciiString,
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[ApiKeyPolicies]

object ApiKeyPolicies {
  def toApiKeyPolicy(policy: ApiKeyPolicies): ApiKeyPolicy = {
    val permissionList = toApiPermission(HttpMethods.GET, policy.getEndpoints) ++
      toApiPermission(HttpMethods.POST, policy.postEndpoints) ++
      toApiPermission(HttpMethods.PUT, policy.putEndpoints) ++
      toApiPermission(HttpMethods.PATCH, policy.patchEndpoints) ++
      toApiPermission(HttpMethods.DELETE, policy.deleteEndpoints)
    ApiKeyPolicy(
      ApiConsumer(policy.apiKey, whiteLabelTokensToSeq(policy.whiteLabelTokens), policy.teamName.toString()),
      permissionList
    )
  }

  def toApiPermission(method: HttpMethod, input: Option[AsciiString]): Seq[ApiPermission] = {
    input match {
      case Some(str) => str.toString.split("\n").toSeq.map(ApiPermission(method, _))
      case None => Seq.empty
    }
  }

  def endpointsToSeq(input: Option[AsciiString]): Seq[AsciiString] = {
    input.map(_.split("\n").toIndexedSeq).getOrElse(Vector.empty)
  }

  def whiteLabelTokensToSeq(input: AsciiString): Seq[UUID] = input.toString.split("\n").map(UUID.fromString)
}
