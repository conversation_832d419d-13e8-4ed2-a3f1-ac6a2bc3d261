package com.agoda.winterfell.models.entities

import shapeless.DefaultSymbolicLabelling

object EntityHelper {
  // TODO: use ProductNames!
  def convertToMap[T <: Product](product: T)(implicit symbols: DefaultSymbolicLabelling[T]) = {
    val fieldNames = symbols().runtimeList.map { case symbol: Symbol => symbol.name }.toArray
    product.productIterator.zipWithIndex.collect {
      case (vector: Vector[_], index) => fieldNames(index) -> (if (vector.isEmpty) None else vector.head)
      case (field, index) if field != null => fieldNames(index) -> field
    }.toMap
  }
}
