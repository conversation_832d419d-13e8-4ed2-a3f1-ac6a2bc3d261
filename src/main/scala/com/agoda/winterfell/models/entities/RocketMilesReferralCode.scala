package com.agoda.winterfell.models.entities

import java.util.UUID

import com.agoda.dal.model.{BasicEntity, EntityMetadata}
import com.agoda.dal.syntax.{Encrypted, Key, Table}

@Table("rm_referral_code")
case class RocketMilesReferralCode(
    @Key @Encrypted referralCode: String,
    userId: UUID,
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[RocketMilesReferralCode]
