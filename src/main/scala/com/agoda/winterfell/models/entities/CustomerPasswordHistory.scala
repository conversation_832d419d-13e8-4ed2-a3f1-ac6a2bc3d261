package com.agoda.winterfell.models.entities

import com.agoda.dal.{data, env}
import com.agoda.dal.data.AsciiString
import java.util.UUID

import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin, EntityStatus}
import com.agoda.dal.syntax.{Column, Key, Table}
import com.agoda.winterfell.{Constants, ExtendedOption}
import java.time.Instant

@Table("customer_password_history")
case class CustomerPasswordHistory(
    @Key historyId: UUID,
    userId: UUID,
    passwordHash: AsciiString,
    override val origin: Option[EntityOrigin],
    metadata: PasswordHistoryMetadata = PasswordHistoryMetadata()
) extends BasicEntity[CustomerPasswordHistory] {
  override def messageKey: Option[Any] = {
    Some(userId)
  }
}

object CustomerPasswordHistory {
  def withBlankId(
      userId: UUID,
      hash: String,
      origin: Option[EntityOrigin],
      createdWhen: Option[Instant] = None): CustomerPasswordHistory = {
    CustomerPasswordHistory(
      Constants.BlankId,
      userId,
      AsciiString(hash).getOrFail("Non-ASCII password's hash"),
      origin,
      metadata = PasswordHistoryMetadata(createdWhen = createdWhen.getOrElse(Instant.now()))
    )
  }
  def from(mdbModel: ResetPasswordHistory, origin: Option[EntityOrigin]): CustomerPasswordHistory = {
    CustomerPasswordHistory(
      Constants.BlankId,
      mdbModel.userId,
      AsciiString(mdbModel.hash).getOrFail("Non-ASCII password's hash"),
      origin,
      metadata = PasswordHistoryMetadata(createdWhen = Instant.ofEpochMilli(mdbModel.createdWhen.getMillis))
    )
  }

  /**
    * 1 second logic is for first changePassword call when we insert both new and existing passwords at same time
    * (or potentially more from legacy history w/o timestamps)
    * to separate them for future changePassword calls when records will be pushed out based on rec_created value
    * history.length - index, in case of read from MDC JSON, there is no createdWhen in password history
    *
    * @param history - history to save with potential blank history ids
    * @param createdWhen - history record on newly added password for a timestamp
    * @return ready to save list of CustomerPasswordHistory
    */
  def initBlankIds(history: Vector[CustomerPasswordHistory], createdWhen: Instant): Vector[CustomerPasswordHistory] = {
    history.zipWithIndex.map {
      case (h, index) =>
        if (h.historyId == Constants.BlankId) {
          val newCreatedWhen = createdWhen.minusSeconds(history.length - index)
          h.copy(
            historyId = UUID.randomUUID(),
            metadata = h.metadata.copy(
              createdWhen =
                if (h.metadata.createdWhen.isBefore(newCreatedWhen)) h.metadata.createdWhen else newCreatedWhen)
          )
        } else h
    }
  }
}

case class PasswordHistoryMetadata(
    @Column("rec_status", insertable = false, updatable = false)
    status: EntityStatus = EntityStatus.Active,
    @Column("rec_created_when", insertable = true, updatable = false)
    createdWhen: Instant = Instant.now(),
    @Column("server", insertable = true, updatable = false, length = 255)
    server: data.AsciiString = env.HostName)
    extends EntityMetadata
