package com.agoda.winterfell.models.entities

import java.util.UUID
import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Column, Encrypted, Key, Table}
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.unified.whitelabel.WlAdditionalPropertiesDal
import com.agoda.winterfell.unified.wl.{JapanicanWlProperties, WlAdditionalProperties}

@Table("customer_whitelabel_properties_japanican")
case class JapanicanAdditionalWlProperties(
    @Key userId: UUID,
    externalMemberId: Option[AsciiString],
    @Encrypted nationalityId: Option[String],
    @Column("birth_date")
    @Encrypted birthday: Option[String],
    @Encrypted telephoneNumber: Option[String],
    @Encrypted contactNumber: Option[String],
    @Encrypted age: Option[Int],
    @Encrypted gender: Option[String],
    @Encrypted phone2: Option[String],
    @Column("zip_code")
    @Encrypted zipcode: Option[String],
    @Encrypted address1: Option[String],
    @Encrypted address2: Option[String],
    @Encrypted address3: Option[String],
    @Encrypted postalCode: Option[String],
    @Encrypted city: Option[String],
    @Encrypted area: Option[String],
    @Encrypted region: Option[String],
    @Encrypted state: Option[String],
    @Encrypted country: Option[String],
    @Encrypted alternativeEmail: Option[String],
    subscription: Option[AsciiString],
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[JapanicanAdditionalWlProperties]
    with WlAdditionalPropertiesDal
    with HasExternalMemberId {

  override def messageKey: Option[Any] = {
    Some(userId)
  }
  override def toResponse(implicit capiCtx: CapiRequestContextData): WlAdditionalProperties = {
    JapanicanWlProperties(
      nationalityId,
      birthday,
      telephoneNumber,
      contactNumber,
      zipcode,
      alternativeEmail,
      subscription.map(_.toString),
      None,
      None,
      None,
      None,
      address1,
      address2,
      address3,
      postalCode,
      region,
      country,
      state,
      city,
      area,
      age,
      gender,
      phone2,
      externalMemberId.map(_.toString)
    )
  }

}
