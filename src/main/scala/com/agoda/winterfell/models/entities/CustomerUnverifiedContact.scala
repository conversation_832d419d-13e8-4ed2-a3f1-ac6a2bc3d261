package com.agoda.winterfell
package models.entities

import com.agoda.capi.enigma.shared_model.customer.CustomerUnverifiedContactDetail

import java.time.Instant
import java.util.UUID
import com.agoda.dal.data.ByteString
import com.agoda.dal.model.{BasicEntity, EntityOrigin, EntityStatus}
import com.agoda.dal.syntax.{Encrypted, Key, Table}
import com.agoda.winterfell.common.AuthenticationType
import com.agoda.winterfell.input.UpdateCustomerRequest
import com.agoda.winterfell.models.QueryUtil
import com.agoda.winterfell.output.ContactMethod.ContactMethod
import com.agoda.winterfell.output.{ContactMethod, MemberContact}
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.unified.{CustomerAuditMetadata, UtilCustomer}
import com.agoda.winterfell.utils.{CapiConfig, Counter, PhoneNumberFormatUtil}
import org.apache.commons.lang3.StringUtils

import scala.util.Try

@Table("customer_unverified_contacts")
case class CustomerUnverifiedContact(
    @Key contactId: UUID,
    userId: UUID,
    contactType: Short,
    @Encrypted contactValue: String,
    @Encrypted remark: Option[String],
    override val origin: Option[EntityOrigin],
    metadata: ContactsMetadata = ContactsMetadata())
    extends BasicEntity[CustomerUnverifiedContact] {
  override def messageKey: Option[Any] = {
    Some(userId)
  }
  def isActive: Boolean = metadata.status == EntityStatus.Active
  def isDelete: Boolean = !isActive
  def contactMethod: ContactMethod = ContactMethod(contactType)
  def legacyContactId: Int = ByteString.fromUuid(contactId).slice(12, 16).asInt.get

  def toMemberContact(memberId: Int, whitelabelId: Int): MemberContact = {
    val contactMethod = ContactMethod(contactType)
    MemberContact(
      Some(legacyContactId),
      contactMethod,
      contactValue,
      remark.getOrElse(""),
      contactMethodName = Some(ContactMethod.nameOf(contactMethod)),
      memberId,
      recordStatus = Some(metadata.status.id),
      recordCreatedBy = None,
      recordCreatedWhen = Some(metadata.createdWhen.toDateTime),
      recordModifiedBy = None,
      recordModifiedWhen = metadata.modifiedWhen.map(_.toDateTime),
      Some(whitelabelId)
    )
  }

  def toAuditMetadata(whitelabelId: Short, requesterUUID: Option[UUID]): CustomerAuditMetadata =
    CustomerAuditMetadata(
      whitelabelId,
      userId,
      requesterUUID,
      Some(ContactMethod(contactType).toString),
      Some(contactValue.toString()))
  // TODO: Should be remove after the migration!
  private[winterfell] var isDirty: Boolean = false
}

object CustomerUnverifiedContact {
  val phoneMethods = List(
    ContactMethod.HomePhone.id,
    ContactMethod.AlterHomePhone.id,
    ContactMethod.WorkPhone.id,
    ContactMethod.AlterWorkPhone.id,
    ContactMethod.Mobile.id)
  def nextContactId(contactValue: String, legacyContactId: Option[Int] = None): UUID = {
    val contactIdInt =
      legacyContactId.getOrElse(Counter.ContactCounter.nextId(CapiConfig.PrimaryMdcGroup)(TempSingletonHolder.db))
    ByteString
      .unsafeWrap(QueryUtil.hash(contactValue))
      .slice(0, 12)
      .appended(contactIdInt)
      .asUUID
      .get
  }
  def newContactFrom(model: CustomerModel): Vector[CustomerUnverifiedContact] = {
    model.contacts.view.map { contact =>
      val formattedContact =
        if (phoneMethods.contains(contact.contactMethod.id))
          Try(PhoneNumberFormatUtil.getE164FormattedNumber(Some(contact.contactMethodValue))).toOption
            .getOrElse(contact.contactMethodValue)
        else contact.contactMethodValue
      val contactId = nextContactId(contact.contactMethodValue, contact.contactId)
      CustomerUnverifiedContact(
        contactId = contactId,
        model.userId,
        contact.contactMethod.id.toShort,
        formattedContact,
        Some(contact.contactMethodRemark).filter(_.nonEmpty),
        model.memberDetails.origin.flatMap(EntityOrigin(_)).orElse(Some(EntityOrigin.Unspecified))
      )
    }.toVector
  }

  def from(
      userId: UUID,
      origin: Option[EntityOrigin],
      contactType: ContactMethod,
      contactValue: String,
      remark: Option[String] = None,
      legacyContactId: Option[Int] = None,
      metadata: ContactsMetadata = ContactsMetadata()): CustomerUnverifiedContact = {
    val contactId = nextContactId(contactValue, legacyContactId)
    val entity =
      CustomerUnverifiedContact(contactId, userId, contactType.id.toShort, contactValue, remark, origin, metadata)
    entity.isDirty = true
    entity
  }

  def from(model: CustomerModel): Vector[CustomerUnverifiedContact] = {
    model.contacts.map { contact =>
      val contactEntity = from(
        model.userId,
        model.memberDetails.origin.flatMap(EntityOrigin(_)).orElse(Some(EntityOrigin.Unspecified)),
        contact.contactMethod,
        contact.contactMethodValue,
        Option(contact.contactMethodRemark).filter(_.nonEmpty),
        contact.contactId,
      )
      contactEntity.copy(
        metadata = contactEntity.metadata.copy(
          status = if (contact.recordStatus.contains(1)) EntityStatus.Active else EntityStatus.SoftDeleted,
          createdWhen = contact.recordCreatedWhen.map(_.toJavaInstant).getOrElse(Instant.now()),
          modifiedWhen = contact.recordModifiedWhen.map(_.toJavaInstant)
        ))
    }
  }

  def fromEnigma(detail: CustomerUnverifiedContactDetail): CustomerUnverifiedContact = {
    CustomerUnverifiedContact(
      contactId = detail.contactId,
      userId = detail.userId,
      contactType = detail.contactType,
      contactValue = detail.contactValue,
      remark = detail.remark,
      origin = detail.origin.flatMap(EntityOrigin(_)),
      metadata = ContactsMetadata(
        status = EntityStatus(detail.metadata.status.value).getOrElse(EntityStatus.Active),
        createdWhen = detail.metadata.createdWhen,
        modifiedWhen = detail.metadata.modifiedWhen
      )
    )
  }

  @deprecated
  def fromRequest(
      customerToUpdate: UpdateCustomerRequest,
      userId: UUID): (Vector[CustomerUnverifiedContact], Vector[CustomerUnverifiedContact]) = {
    var toUpdate: Vector[CustomerUnverifiedContact] = Vector.empty
    var toDelete: Vector[CustomerUnverifiedContact] = Vector.empty
    customerToUpdate.regisTypes
      .filter(regisType => isValidUnverifiedContact(regisType.authType))
      .foreach(unVerContact => {
        unVerContact.value match {
          case head +: _ if head.nonEmpty => {
            val formatted = CustomerVerifiedContact.formatContactValue(unVerContact.authType, head)
            toUpdate = toUpdate :+ CustomerUnverifiedContact(
              Constants.BlankId,
              userId,
              UtilCustomer
                .toContactType(unVerContact.authType.toShort, unVerContact.verified, unVerContact.onlyUnverContact)
                .id
                .toShort,
              formatted,
              Option(customerToUpdate.specialRemark).flatMap(_.find(_.nonEmpty)),
              None
            )
          }
          case _ => {
            toDelete = toDelete :+ CustomerUnverifiedContact(
              Constants.BlankId,
              userId,
              UtilCustomer
                .toContactType(unVerContact.authType.toShort, unVerContact.verified, unVerContact.onlyUnverContact)
                .id
                .toShort,
              StringUtils.EMPTY,
              Option(customerToUpdate.specialRemark).flatMap(_.find(_.nonEmpty)),
              None
            )
          }
        }
      })
    (toUpdate, toDelete)
  }

  def isValidUnverifiedContact(authType: Int): Boolean = {
    authType == AuthenticationType.Basic.id || authType == AuthenticationType.PhoneNumber.id
  }
}
