package com.agoda.winterfell
package models.entities

import java.time.{Instant, LocalDate}
import java.util.UUID
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.capi.enigma.shared_model.customer.CustomerDetail
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.dal.data.{AsciiString, ByteString, CollatedString, Collation}
import com.agoda.dal.model.{BasicEntity, EntityOrigin, EntityStatus}
import com.agoda.dal.syntax.{Column, Encrypted, Key, Table}
import com.agoda.winterfell.Constants.isAllowDuplicateEmailForWL
import com.agoda.winterfell.common.{AuthenticationType, CustomerEmail}
import com.agoda.winterfell.common.AuthenticationType.AuthenticationType
import com.agoda.winterfell.input.{RegisTypeResp, SaveMemberContactRequest}
import com.agoda.winterfell.marshalling.JsonMarshalling
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.queries.{PartnerMembershipByUserIdQuery, UserIdQuery, VerifiedContactUserQuery}
import com.agoda.winterfell.models.{CapiRequestContextData, Queries, QueryUtil, RoleQueries}
import com.agoda.winterfell.output.ContactMethod.ContactMethod
import com.agoda.winterfell.output.{ContactMethod, Languages, MemberDetails, UserInfoEntity, UserRoleLogin}
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.streams.events.SyncMemberToDAL
import com.agoda.winterfell.unified.accessors.{AsyncMutator, VerifiedPhoneUtil}
import com.agoda.winterfell.unified.wallet.WalletUtil
import com.agoda.winterfell.unified.{
  Customer,
  CustomerModelResponse,
  EmailContact,
  Fields,
  HistoryMetadata,
  LegacyFields,
  LegacyModels,
  PhoneContact,
  Role,
  SignupParameters,
  Social,
  UtilCustomer,
  WalletInfo
}
import com.agoda.winterfell.unified.wl.{RocketMilesWlProperties, WlAdditionalProperties}
import com.agoda.winterfell.utils.{
  ByteStringHelper,
  CapiConfig,
  CapiConsul,
  Counter,
  CountryMapping,
  CustomerRole,
  Kafka
}
import com.fasterxml.jackson.annotation.JsonIgnore
import org.joda.time.DateTime

import scala.util.{Failure, Success, Try}

@Table("customers")
case class CustomerEntity(
    @Key userId: UUID,
    @Key memberId: Int,
    override val origin: Option[EntityOrigin],
    whitelabelId: Short,
    @Encrypted title: Option[String],
    @Encrypted firstName: Option[String],
    @Encrypted middleName: Option[String],
    @Encrypted lastName: Option[String],
    @Encrypted displayName: Option[String],
    @Encrypted birthdate: Option[LocalDate],
    @Encrypted nationalityId: Option[Int],
    languageId: Option[Int],
    @Encrypted taxId: Option[String],
    managerId: Option[UUID],
    departmentId: Option[Int],
    locationId: Option[Int],
    ccpaOptOut: Boolean,
    ccpaLastUpdated: Option[Instant],
    ccofOptOut: Boolean,
    @Column("newsletter_opt_out")
    newsletterOptIn: Option[Boolean] = Some(false),
    isSingleTaskConsumption: Boolean,
    isLoginBlocked: Boolean,
    @Encrypted walletBirthdate: Option[String],
    @Encrypted walletCountry: Option[String],
    metadata: CustomMetadata = CustomMetadata())
    extends BasicEntity[CustomerEntity] {

  override def messageKey: Option[Any] = {
    Some(userId)
  }
  lazy val toStringTags: Map[String, String] = Map(
    "memberId" -> memberId.toString,
    "userId" -> userId.toString,
    "whitelabelId" -> whitelabelId.toString
  )

  def validate(whitelabelIdToCheck: Int): CustomerEntity = {
    if (this.whitelabelId != whitelabelIdToCheck)
      throw AuthException(s"UserId $userId doesn't belong to whitelabelId $whitelabelIdToCheck")
    if (origin.isEmpty) this.copy(origin = Some(EntityOrigin.Unspecified))
    else this
  }

  def membershipQuery: PartnerMembershipByUserIdQuery = PartnerMembershipByUserIdQuery(userId)
  def userIdQuery: UserIdQuery = UserIdQuery(userId)
  def emailQuery: VerifiedContactUserQuery =
    VerifiedContactUserQuery(userId, AuthenticationType.Basic.id.toShort, whitelabelId)

  def whatsappQuery: VerifiedContactUserQuery =
    VerifiedContactUserQuery(userId, AuthenticationType.WhatsApp.id.toShort, whitelabelId)
  // TODO: implement joins!
  def roles: Customer.Roles = {
    CustomerRole.rolesFor(
      CustomerModel.empty.copy(
        userId = userId,
        memberDetails = MemberDetails.empty.copy(whiteLabelId = Some(whitelabelId.toInt))
      ))(TempSingletonHolder.db)
  }

  def isCNOrigin: Boolean = {
    origin.exists(_.toString().equalsIgnoreCase("CN"))
  }

  def isNhaUser: Boolean = {
    roles.contains(Constants.NhaRoleId) || roles.contains(Constants.NhaOtherSupplierRoleId)
  }
  def isAffiliateUser: Boolean = {
    roles.contains(Constants.AffiliateRoleId)
  }
  def isYcsUser: Boolean = {
    val ycsRoles = RoleQueries.readRolesByPermissionsId(Constants.YcsPermissionId)(TempSingletonHolder.db)
    roles.keySet.exists(ycsRoles.contains(_))
  }
  def hasSpecialRole: Boolean = isNhaUser || isAffiliateUser || isYcsUser

  // TODO: Should be remove after the migration!
  private[winterfell] var isDirty: Boolean = false

  def newVerifiedContactFor(
      contactType: AuthenticationType,
      contactValue: CollatedString,
      hash: Option[AsciiString],
      remark: Option[String] = None): CustomerVerifiedContact = {
    val contactId = CustomerContactId(contactType.id.toShort, contactValue, whitelabelId)
    val isVerified = contactType != AuthenticationType.Basic
    CustomerVerifiedContact(contactId, userId, hash, remark, Some(isVerified), origin)
  }

  def newUnverifiedContactFor(
      contactType: ContactMethod,
      contactValue: String,
      remark: Option[String] = None): CustomerUnverifiedContact = {
    CustomerUnverifiedContact.from(userId, origin, contactType, contactValue, remark)
  }

  /**
    * @note the result model represents a state of new customer,
    *       i.e. there's no favourite hotels, partner memberships, notes, addresses
    */
  def toFreshCustomer(
      verifiedContacts: IndexedSeq[CustomerVerifiedContact],
      unverifiedContacts: IndexedSeq[CustomerUnverifiedContact],
      wlProperties: Option[WlAdditionalProperties]): Customer = {
    val fakeDateTime = DateTime.now().plusYears(100)
    val displayName = this.displayName.getOrElse(s"${firstName.getOrElse("")} ${lastName.getOrElse("")}".trim)

    val emailVerifiedContact = verifiedContacts.filter(_.id.contactType == AuthenticationType.Basic.id)
    val validEmail =
      emailVerifiedContact.exists(contact => CustomerEmail.isEmailValid(contact.id.contactValue.underlying))

    val fixedVerifiedContacts = if (verifiedContacts.isEmpty || !validEmail) {
      // fixing email take email from unverifiedContacts
      val unverifiedEmail = unverifiedContacts
        .filter(contact =>
          contact.metadata.status == EntityStatus.Active && contact.contactMethod == ContactMethod.PrimaryEmail)
        .headOption
        .map(_.contactValue)
        .filter(CustomerEmail.isEmailValid)

      if (emailVerifiedContact.nonEmpty && unverifiedEmail.isDefined) {
        verifiedContacts.map { verifiedContact =>
          verifiedContact match {
            case contact if contact.id.contactType == AuthenticationType.Basic.id =>
              val newContact = contact.copy(
                id = contact.id.copy(contactValue = CollatedString(Collation.CaseInsensitive, unverifiedEmail.get)))
              implicit val fakeCapiCtx = CapiRequestContextData.emptyWlContext(whitelabelId)
              MeasuredFuture() {
                unverifiedEmail.map(email => {
                  TempSingletonHolder.customerServiceOps.memberByUserNameAndAuthType(AuthenticationType.Basic.id, email)
                })
              }(Implicits.slowExecutionContext).flatMap {
                case Some(memberResult) =>
                  if (CapiConsul.autoFixIncorrectEmail && memberResult.memberId == memberId) {
                    val syncContact =
                      AsyncMutator(memberId, whitelabelId, fakeCapiCtx)(
                        Implicits.slowExecutionContext,
                        TempSingletonHolder.customerServiceOps)
                        .saveMemberContact(
                          SaveMemberContactRequest(memberId, ContactMethod.PrimaryEmail, unverifiedEmail.get),
                          ignoreValidator = true)
                    syncContact.onComplete {
                      case Failure(exception) =>
                        CapiLogMessage(
                          "capi.dal.missing_verified_contact",
                          logLevel = LogLevel.ERROR,
                          stringTags = Map(
                            "success" -> "false",
                            "userId" -> userId.toString,
                            "memberId" -> memberId.toString,
                            "whitelabelId" -> whitelabelId.toString
                          ),
                          exception = Some(exception),
                          exceptionMessage = Some(exception.getMessage)
                        )
                      case Success(value) =>
                        CapiLogMessage(
                          "capi.dal.missing_verified_contact",
                          logLevel = LogLevel.INFO,
                          stringTags = Map(
                            "success" -> "true",
                            "userId" -> userId.toString,
                            "memberId" -> memberId.toString,
                            "whitelabelId" -> whitelabelId.toString,
                            "updatedTo" -> QueryUtil.encrypt(JsonMarshalling.pretty(value))
                          ),
                        )
                    }(Implicits.slowExecutionContext)
                    syncContact
                  } else {
                    CapiLogMessage(
                      "capi.dal.missing_verified_contact",
                      logLevel = LogLevel.ERROR,
                      stringTags = Map(
                        "userId" -> userId.toString,
                        "conflictUserId" -> memberResult.userId.toString,
                        "memberId" -> memberId.toString,
                        "conflictMemberId" -> memberResult.memberId.toString,
                        "whitelabelId" -> whitelabelId.toString,
                        "autoFixIncorrectEmail" -> CapiConsul.autoFixIncorrectEmail.toString
                      ),
                    )
                    MeasuredFuture.successful(None)
                  }
                case _ => {
                  CapiLogMessage(
                    "capi.dal.missing_verified_contact",
                    logLevel = LogLevel.ERROR,
                    stringTags = Map(
                      "userId" -> userId.toString,
                      "memberId" -> memberId.toString,
                      "whitelabelId" -> whitelabelId.toString
                    ),
                  )
                  MeasuredFuture.successful(None)
                }
              }(Implicits.slowExecutionContext)
              newContact
            case _ => verifiedContact
          }
        }
      } else {
        if (CapiConsul.enableConsumerPushMemberToDAL && (verifiedContacts.isEmpty || unverifiedContacts.isEmpty)) {
          val syncEvent = SyncMemberToDAL(Some(userId), Some(memberId))
          Kafka.sendLegacyEncryptedMsg(
            SyncMemberToDAL.Topic,
            Some(memberId),
            syncEvent,
            Map("memberId" -> memberId.toString))
        }
        CapiLogMessage(
          "capi.dal.missing_verified_contact",
          logLevel = LogLevel.WARN,
          stringTags = Map(
            "success" -> "false",
            "userId" -> userId.toString,
            "memberId" -> memberId.toString,
            "whitelabelId" -> whitelabelId.toString,
            "validEmail" -> validEmail.toString,
            "unverifiedEmail" -> unverifiedEmail.isDefined.toString,
            "nonEmptyVerifiedContact" -> emailVerifiedContact.nonEmpty.toString,
          )
        )
        verifiedContacts
      }
    } else verifiedContacts

    val email = fixedVerifiedContacts.collectFirst {
      case contact if contact.id.contactType == AuthenticationType.Basic.id =>
        val contactValue = if (isAllowDuplicateEmailForWL(contact.id.whitelabelId)) {
          unverifiedContacts
            .filter(contact =>
              contact.metadata.status == EntityStatus.Active && contact.contactMethod == ContactMethod.AlterEmail)
            .headOption
            .map(_.contactValue)
            .filter(CustomerEmail.isEmailValid)
            .getOrElse(contact.id.contactValue.toString)
        } else contact.id.contactValue.toString
        // is block login then disable verified email
        EmailContact(
          contactValue,
          contact.isVerified.filter(_ => !this.isLoginBlocked).getOrElse(false),
          Constants.isFakeEmail(contactValue),
          Constants.isRelayEmail(contactValue)
        )
    }

    val phone =
      fixedVerifiedContacts
        .collectFirst {
          case contact if contact.id.contactType == AuthenticationType.PhoneNumber.id =>
            val contactValue = contact.id.contactValue.toString
            val formattedPhone = Try(VerifiedPhoneUtil.toVerified(contactValue)).toOption
            PhoneContact(
              contactValue,
              verified = true,
              formattedPhone.map(_.countryCode),
              formattedPhone.map(_.nationalNumber))
        }
        .orElse {
          unverifiedContacts.iterator
            .filter(_.isActive)
            .collectFirst {
              case contact if contact.contactMethod == ContactMethod.HomePhone =>
                val formattedPhone = Try(VerifiedPhoneUtil.toVerified(contact.contactValue)).toOption
                PhoneContact(
                  contact.contactValue,
                  verified = false,
                  formattedPhone.map(_.countryCode),
                  formattedPhone.map(_.nationalNumber))
            }
        }

    val whatsAppPhoneNumber = unverifiedContacts.iterator
      .filter(_.isActive)
      .collectFirst {
        case contact if contact.contactMethod == ContactMethod.WhatsAppPhoneNumber =>
          val formattedPhone = Try(VerifiedPhoneUtil.toVerified(contact.contactValue)).toOption
          PhoneContact(
            value = contact.contactValue,
            verified = false,
            countryCode = formattedPhone.map(_.countryCode),
            national = formattedPhone.map(_.nationalNumber))
      }

    val walletInfo = Some(
      WalletInfo(
        birthdateHash = this.walletBirthdate.filterNot(ByteStringHelper.isEmptyBytesString),
        countryHash = this.walletCountry.filterNot(ByteStringHelper.isEmptyBytesString)
      )).filter(ob => ob.countryHash.nonEmpty || ob.birthdateHash.nonEmpty)

    val userWalletCountry =
      walletInfo.flatMap(_.countryHash.flatMap(countryHash => WalletUtil.getUserWalletCountry(countryHash)))

    val username = fixedVerifiedContacts
      .collectFirst {
        case contact if contact.id.contactType == AuthenticationType.Username.id => contact.id.contactValue.underlying
      }
      .orElse(email.map(_.value))

    val lineAuthTypes =
      List(AuthenticationType.LineThConnect, AuthenticationType.LineJpConnect, AuthenticationType.LineTwConnect)
    val lineContacts =
      fixedVerifiedContacts.filter(contact => lineAuthTypes.contains(AuthenticationType(contact.id.contactType)))
    val latestLineId = if (lineContacts.nonEmpty) {
      Try(lineContacts.maxBy(contact => contact.metadata.modifiedWhen.getOrElse(contact.metadata.createdWhen))).toOption
        .map(_.id.contactValue.underlying)
    } else None

    val social = fixedVerifiedContacts.foldLeft(Social.Empty) { (result, contact) =>
      AuthenticationType(contact.id.contactType) match {
        case AuthenticationType.Basic | AuthenticationType.PhoneNumber | AuthenticationType.Username |
            AuthenticationType.WhatsApp =>
          result
        case AuthenticationType.FacebookConnect => result.copy(facebookId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.WeChatConnect => result.copy(wechatUnionId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.LineConnect => result.copy(lineId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.LineThConnect =>
          result.copy(lineThId = Some(contact.id.contactValue.underlying), latestLineId = latestLineId)
        case AuthenticationType.LineJpConnect =>
          result.copy(lineJpId = Some(contact.id.contactValue.underlying), latestLineId = latestLineId)
        case AuthenticationType.LineTwConnect =>
          result.copy(lineTwId = Some(contact.id.contactValue.underlying), latestLineId = latestLineId)
        case AuthenticationType.GrabConnect => result.copy(grabId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.Apple => result.copy(appleId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.JegoTrip => result.copy(jegoTripId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.GoogleConnect => result.copy(googleId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.Kakao => result.copy(kakaoId = Some(contact.id.contactValue.underlying))
        case AuthenticationType.PaytmConnect => result.copy(paytmId = Some(contact.id.contactValue.underlying))
        case _ => result
      }
    }

    val legacyMappings = fixedVerifiedContacts.map { contact =>
      contact.toUserMapping.copy(
        createdBy = metadata.createdBy,
        modifiedBy = contact.metadata.modifiedWhen.flatMap(_ => metadata.modifiedBy)
      )
    }.toVector
    val legacyMemberContacts = unverifiedContacts.map { contact =>
      contact
        .toMemberContact(memberId, whitelabelId)
        .copy(
          recordCreatedBy = Some(metadata.createdBy),
          recordModifiedBy = contact.metadata.modifiedWhen.flatMap(_ => metadata.modifiedBy)
        )
    }.toVector
    val allContacts = {
      fixedVerifiedContacts.filter(_.metadata.status == EntityStatus.Active).map { contact =>
        RegisTypeResp(contact.id.contactValue.underlying, verified = true, authType = contact.id.contactType)
      } ++
        unverifiedContacts.filter(_.metadata.status == EntityStatus.Active).flatMap { contact =>
          UtilCustomer
            .toValidRegisType(contact.contactMethod.id)
            .map(contactType => RegisTypeResp(contact.contactValue, verified = false, contactType.id.toShort))
        }
    }

    val fields = Fields(
      ccof = ccofOptOut,
      memberships = Vector.empty,
      newsletter = newsletterOptIn.orElse(Some(false)),
      mergeTo = 0,
      prius = None,
      nationalityId = nationalityId.getOrElse(0),
      languageId = languageId.getOrElse(0),
      birthday = birthdate,
      taxId = taxId,
      hashedPasswords = Vector.empty,
      whiteLabelProperties = wlProperties
    )
    val preferCurrency = wlProperties.collect {
      case properties: RocketMilesWlProperties => properties.defaultCurrencyCode
    }.flatten
    val extraFields = CustomerExtraFields(
      taxId,
      preferCurrency = preferCurrency,
      isCCPAOptOut = Some(ccpaOptOut),
      ccpaLastUpdate = ccpaLastUpdated.map(_.toDateTime),
      lastSuccessLogin = None
    )
    val legacyFields = LegacyFields.empty.copy(
      title = title,
      memberCode = userId.toString.toUpperCase
    )

    val legacyUserInfo = UserInfoEntity(
      userId,
      displayName,
      email.map(_.value).getOrElse(""),
      username.getOrElse(""),
      authType = AuthenticationType.Basic.id,
      isSingleTaskUser = isSingleTaskConsumption,
      isBlocked = isLoginBlocked,
      isDeleted = metadata.status != EntityStatus.Active,
      recordStatus = metadata.status.id,
      rewardsMemberId = Some(memberId),
      departmentId = departmentId,
      locationId = locationId,
      managerId = managerId,
      managerEmail = None,
      managerDisplayName = None,
      expiredSince = Some(fakeDateTime)
    )
    val legacyMemberDetails = MemberDetails(
      userId = userId,
      memberId = memberId,
      memberCode = userId.toString.toUpperCase,
      title = title,
      firstName = firstName.getOrElse(""),
      lastName = lastName.getOrElse(""),
      birthDate = birthdate.map(_.toDateTime),
      nationalityId = nationalityId.getOrElse(0),
      nationalityName = nationalityId.flatMap(CountryMapping.forCountry),
      languageId = languageId,
      locale = languageId.flatMap(Languages.tagToId.get),
      taxId = taxId,
      whiteLabelId = Some(whitelabelId),
      origin = origin.map(_.toString),
      loginTypes = legacyMappings.filter(CustomerModelHelper.isLoginType).map(_.authType),
      email = email.map(_.value).getOrElse(""),
      username = username,
      homePhone = phone.map(_.value).getOrElse(""),
      mobilePhone = phone.map(_.value),
      isNewsletter = newsletterOptIn.orElse(Some(false)),
      recordStatus = metadata.status.id,
      recordCreatedBy = Some(Constants.AppId),
      recordCreatedWhen = Some(metadata.createdWhen.toDateTime),
      recordModifiedBy = metadata.modifiedBy,
      recordModifiedWhen = metadata.modifiedWhen.map(_.toDateTime),
      preferredPriusProgramId = None,
      specialRemarks = None,
      whiteLabelProperties = wlProperties,
      whatsAppPhoneNumber = whatsAppPhoneNumber.map(_.value),
      isUserHasWallet = walletInfo.exists(_.isUserHasWallet),
      userWalletCountry = userWalletCountry
    )
    val legacyCustomerModel = CustomerModel(
      email.map(_.value).getOrElse(""),
      memberId,
      userId,
      legacyMemberDetails,
      legacyUserInfo,
      legacyMappings,
      fields,
      legacyFields,
      legacyMemberContacts,
      customerExtraFields = Some(extraFields),
      walletInfo = walletInfo
    )
    val authTypes = legacyCustomerModel.authTypes
    val legacyLoginDetails = legacyMemberDetails.toLogin(
      isBlocked = false,
      roles = Vector(UserRoleLogin(userId, Constants.RewardsRole, memberId, "R", s"R$memberId")),
      authTypes = authTypes,
      authenBy =
        if (authTypes.contains(AuthenticationType.PhoneNumber.id)) AuthenticationType.PhoneNumber
        else AuthenticationType.Basic,
      membership = Vector.empty
    )
    val legacyModels = LegacyModels(
      Some(legacyCustomerModel),
      Some(legacyUserInfo),
      Some(legacyMemberDetails),
      Some(legacyLoginDetails)
    )

    val customerMetadata = HistoryMetadata(
      metadata.createdWhen,
      modified = metadata.modifiedWhen,
      Some(metadata.createdBy),
      modifiedBy = metadata.modifiedBy
    )
    Customer(
      memberId,
      userId,
      firstName.getOrElse(""),
      lastName.getOrElse(""),
      email,
      phone,
      if (social.nonEmpty) Some(social) else None,
      roles = Vector(Role(Constants.RewardsRole, name = "", memberId)),
      hasRoles = false,
      favoriteHotels = Vector.empty,
      fields,
      customerMetadata,
      recordStatus = metadata.status.id,
      hostInfo = None,
      legacyFields,
      Some(legacyModels),
      Some(whitelabelId.toInt),
      origin.map(_.toString),
      socialAppInfos = Vector.empty,
      devices = Vector.empty,
      customerExtraFields = Some(extraFields),
      whitelabelProperties = wlProperties,
      regisTypes = CustomerModelResponse.getUniqueRegisTypes(allContacts.toVector, whitelabelId),
      regisTypesAll = allContacts.toVector,
      isUserHasWallet = walletInfo.exists(_.isUserHasWallet),
      userWalletCountry = userWalletCountry
    )
  }
  def maskPiiData: CustomerEntity = {
    this.copy(
      origin = Some(EntityOrigin.Unspecified),
      title = Some(""),
      firstName = Some(""),
      middleName = Some(""),
      lastName = Some(""),
      displayName = Some(""),
      birthdate = Some(Constants.MinLocalDate),
      nationalityId = Some(0),
      languageId = Some(0),
      taxId = Some(""),
      managerId = Some(Constants.EmptyUUID),
      departmentId = Some(0),
      locationId = Some(0),
      ccpaOptOut = false,
      ccpaLastUpdated = Some(Constants.MinInstant),
      ccofOptOut = false,
      newsletterOptIn = None, // Default value is None
      isSingleTaskConsumption = false,
      isLoginBlocked = false,
      walletBirthdate = Some(""),
      walletCountry = Some("")
    )
  }
}

object CustomerEntity {
  def from(
      userId: UUID,
      memberId: Int,
      origin: Option[EntityOrigin],
      whitelabelId: Short,
      parameters: SignupParameters): CustomerEntity = {
    val displayName = Option(
      (parameters.firstName.toSeq ++ parameters.lastName.toSeq)
        .flatMap(Option(_))
        .filter(_.nonEmpty)
        .mkString(" ")
        .trim)
      .map(QueryUtil.removeIllegalChars)
      .filter(_.nonEmpty)

    CustomerEntity(
      userId = userId,
      memberId = memberId,
      origin = origin,
      whitelabelId = whitelabelId,
      title = parameters.title,
      firstName = parameters.firstName.map(QueryUtil.removeIllegalChars),
      middleName = None,
      lastName = parameters.lastName.map(QueryUtil.removeIllegalChars),
      displayName = displayName,
      birthdate = None,
      nationalityId = parameters.nationality,
      languageId = parameters.languageId,
      taxId = parameters.taxId,
      managerId = None,
      departmentId = None,
      locationId = None,
      ccpaOptOut = false,
      ccpaLastUpdated = None,
      ccofOptOut = false,
      newsletterOptIn = parameters.newsLetter,
      isSingleTaskConsumption = false,
      isLoginBlocked = parameters.isBlacklist.getOrElse(false),
      walletBirthdate = None,
      walletCountry = None,
      metadata = CustomMetadata(
        createdWhen = parameters.recCreatedWhen.get.toJavaInstant,
        createdBy = parameters.createdBy.getOrElse(Constants.AppId),
        modifiedBy = None,
        modifiedWhen = None
      )
    )
  }

  def from(model: CustomerModel): CustomerEntity = {
    val metadata = CustomMetadata(
      EntityStatus(model.memberDetails.recordStatus).getOrFail(s"Invalid status: ${model.memberDetails.recordStatus}"),
      model.memberDetails.recordCreatedWhen.map(_.toJavaInstant).getOrElse(Instant.now()),
      model.memberDetails.recordCreatedBy.getOrElse(Constants.AppId),
      model.memberDetails.recordModifiedWhen.map(_.toJavaInstant),
      model.memberDetails.recordModifiedBy
    )
    val entity = CustomerEntity(
      model.userId,
      model.memberId,
      model.memberDetails.origin.flatMap(EntityOrigin(_)).orElse(Some(EntityOrigin.Unspecified)),
      model.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId).toShort,
      model.legacyFields.title,
      Option(model.memberDetails.firstName).filter(_.nonEmpty),
      middleName = None,
      Option(model.memberDetails.lastName).filter(_.nonEmpty),
      Option(model.userInfoEntity.displayName).filter(_.nonEmpty),
      model.memberDetails.birthDate.map(datetime =>
        LocalDate.of(datetime.getYear, datetime.getMonthOfYear, datetime.getDayOfMonth)),
      Some(model.memberDetails.nationalityId).filter(_ > 0),
      model.memberDetails.languageId,
      model.memberDetails.taxId,
      model.userInfoEntity.managerId,
      model.userInfoEntity.departmentId,
      model.userInfoEntity.locationId,
      ccpaOptOut = model.customerExtraFields.flatMap(_.isCCPAOptOut).getOrElse(false),
      ccpaLastUpdated = model.customerExtraFields.flatMap(_.ccpaLastUpdate.map(_.toDate.toInstant)),
      ccofOptOut = model.fields.ccof,
      model.fields.newsletter,
      model.userInfoEntity.isSingleTaskUser,
      model.userInfoEntity.isBlocked,
      walletBirthdate = model.walletInfo.flatMap(_.birthdateHash),
      walletCountry = model.walletInfo.flatMap(_.countryHash),
      metadata
    )
    entity.isDirty = true
    entity
  }

  def fromEnigma(detail: CustomerDetail): CustomerEntity = {
    CustomerEntity(
      userId = detail.userId,
      memberId = detail.memberId,
      origin = detail.origin.flatMap(EntityOrigin(_)),
      whitelabelId = detail.whitelabelId,
      title = detail.title,
      firstName = detail.firstName,
      middleName = detail.middleName,
      lastName = detail.lastName,
      displayName = detail.displayName,
      birthdate = detail.birthdate,
      nationalityId = detail.nationalityId,
      languageId = detail.languageId,
      taxId = detail.taxId,
      managerId = detail.managerId,
      departmentId = detail.departmentId,
      locationId = detail.locationId,
      ccpaOptOut = detail.ccpaOptOut,
      ccpaLastUpdated = detail.ccpaLastUpdated,
      ccofOptOut = detail.ccofOptOut,
      newsletterOptIn = detail.newsletterOptIn,
      isSingleTaskConsumption = detail.isSingleTaskConsumption,
      isLoginBlocked = detail.isLoginBlocked,
      walletBirthdate = detail.walletBirthdate,
      walletCountry = detail.walletCountry,
      metadata = CustomMetadata(
        status = EntityStatus(detail.metadata.status.value).getOrElse(EntityStatus.Active),
        createdWhen = detail.metadata.createdWhen,
        createdBy = detail.metadata.createdBy,
        modifiedWhen = detail.metadata.modifiedWhen,
        modifiedBy = detail.metadata.modifiedBy
      )
    )
  }

  def isGDPRErasedCustomer(entity: CustomerEntity): Boolean =
    entity.metadata.status == EntityStatus.SoftDeleted &&
      entity.firstName.exists(_.isEmpty) &&
      entity.lastName.exists(_.isEmpty) &&
      entity.displayName.exists(_.isEmpty) &&
      entity.birthdate.exists(_.equals(Constants.MinLocalDate))
}
