package com.agoda.winterfell
package models.entities

import java.util.UUID
import java.util.concurrent.TimeUnit
import com.agoda.winterfell.common.AuthenticationType
import com.agoda.winterfell.dbutiltomove.RSHelper
import com.agoda.winterfell.helper.WhiteLabelHelper
import com.agoda.winterfell.internal.SQLRow
import com.agoda.winterfell.models.ModelImplicits.{MemberContactI, UserInfoEntityI}
import com.agoda.winterfell.models.{ModelImplicits, RSParsing, SecurityUriServer}
import com.agoda.winterfell.output._
import com.agoda.winterfell.unified.wl.{
  RocketMilesWlProperties,
  WlAdditionalProperties,
  WlAdditionalPropertiesDeserializer
}
import com.agoda.winterfell.unified.{HostInfo => NewHostInfo}
import com.agoda.winterfell.utils.{CapiConfig, DBPlugin, QueriesHelper, SSoServiceDeser}
import org.joda.time.DateTime

import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{Await, Future}
import scala.language.implicitConversions

case class ResetPasswordHistory(userId: UUID, hash: String, salt: String, createdBy: UUID, createdWhen: DateTime)

object ResetPasswordHistory extends RSHelper {
  implicit def convV(rows: Vector[SQLRow]): Vector[ResetPasswordHistory] =
    rows.map(implicit r => fromRow(r))

  implicit def fromRow(implicit r: SQLRow): ResetPasswordHistory = {
    ResetPasswordHistory(
      userId = c("user_id"),
      hash = c("pwhash"),
      salt = "",
      createdBy = c("rec_created_by"),
      createdWhen = r.dateTime("rec_created_when")
    )
  }
}

object CustomerModelHelperServer {

  def fromSet(
      rowSet: Vector[Vector[SQLRow]],
      addresses: Vector[MemberAddress],
      travellerTypes: Vector[TravellerBox],
      destinations: Vector[MemberDestination],
      getExtraFields: UUID => Option[CustomerExtraFields],
      getAllRecStatus: Boolean)(implicit db: DBPlugin): Option[CustomerModel] = {
    val (rew, contacts, theMappings, theRoles, memberships) =
      (rowSet.head, rowSet(1), rowSet(2), rowSet(3), rowSet.lift(4))
    if (rew.isEmpty) {
      None
    } else {
      val mappings = UserMappingModelI.convV(theMappings)
      val theContacts = {
        // Convert only first mobile phone to home phone if nothing is found
        val rawContacts = MemberContactI.convV(contacts)
        if (rawContacts.exists(
            c => c.contactMethod == ContactMethod.HomePhone && (getAllRecStatus || c.recordStatus.contains(1))))
          rawContacts
        else {
          rawContacts
            .find(c => c.contactMethod == ContactMethod.Mobile && (getAllRecStatus || c.recordStatus.contains(1)))
            .map { mobile =>
              rawContacts.filterNot(_.contactId == mobile.contactId) :+ mobile.copy(
                contactMethod = ContactMethod.HomePhone)
            }
            .getOrElse(rawContacts)
        }
      }

      val (tmp, tmpFields, tmpLegacyFields, converted, walletInfo) =
        ModelImplicits.MemberDetailsI.fromRow(rew.maxBy(_.int("rec_status")))
      // Update email & phone
      val email =
        if (tmp.whiteLabelId.exists(Constants.isAllowDuplicateEmailForWL(_))) {
          theContacts
            .find(x => (x.contactMethod == ContactMethod.AlterEmail && (getAllRecStatus || x.recordStatus.contains(1))))
            .map(_.contactMethodValue)
            .getOrElse {
              mappings
                .find(x => (x.authType == AuthenticationType.Basic.id && (getAllRecStatus || x.recStatus)))
                .map(_.username)
                .getOrElse("")
            }
        } else {
          theContacts
            .find(x =>
              (x.contactMethod == ContactMethod.PrimaryEmail && (getAllRecStatus || x.recordStatus.contains(1))))
            .map(_.contactMethodValue)
            .getOrElse {
              mappings
                .find(x => (x.authType == AuthenticationType.Basic.id && (getAllRecStatus || x.recStatus)))
                .map(_.username)
                .getOrElse("")
            }
        }
      val phone = theContacts
        .filter(c => c.contactMethod == ContactMethod.HomePhone && (getAllRecStatus || c.recordStatus.contains(1)))
        .sortBy(-_.recordCreatedWhen.get.toInstant.getMillis)
        .sortWith((contact, _) => !contact.recordCreatedBy.contains(Constants.CuscoAppId))
        .headOption
        .map(_.contactMethodValue)
        .orElse(theContacts
          .find(c => c.contactMethod == ContactMethod.Mobile && (getAllRecStatus || c.recordStatus.contains(1)))
          .map(_.contactMethodValue))
      val memberDetails = tmp
        .copy(
          loginTypes = mappings.filter(CustomerModelHelper.isLoginType).map(_.authType),
          whiteLabelProperties = SSoServiceDeser.deserWlProp(tmp.whiteLabelProperties),
          email = email,
          homePhone = phone.getOrElse(""),
          mobilePhone = phone
        )

      val maybeEntity = UserInfoEntityI.convV(theMappings).find(_.authType == AuthenticationType.Basic.id)
      if (maybeEntity.isEmpty) {
        baseLogger.error(s"Unable to find full data for ${memberDetails.memberId} : ${memberDetails.userId}")
      }
      val userEntity = maybeEntity.requiredGet(s"No mappings found for member: ${memberDetails.memberId}")

      val overrideMappings =
        if (memberDetails.whiteLabelId.exists(Constants.isJtbWhitelabel) &&
          memberDetails.whiteLabelId
            .map(WhiteLabelHelper.getLoginFeature(_).isUsernameCaseSensitive.getOrElse(false))
            .exists(r => r))
          UserMappingModelI.convV(theMappings, memberDetails.whiteLabelId)
        else {
          if (userEntity.isBlocked)
            mappings.map { mapping =>
              if (mapping.authType == AuthenticationType.Basic.id) mapping.copy(isVerified = false)
              else mapping
            } else
            mappings
        }

      val extraFields = getExtraFields(userEntity.id)
      val taxId = extraFields.flatMap(_.taxId).orElse(tmpFields.taxId)
      val defaultCurrency = getDefaultCurrency(
        tmpFields.whiteLabelProperties,
        memberDetails.whiteLabelId,
        extraFields.flatMap(_.preferCurrency))
      val additionalFieldsWithTaxId = extraFields
        .map(_.copy(taxId = taxId, preferCurrency = defaultCurrency))
        .orElse(Option(CustomerExtraFields(taxId = taxId, preferCurrency = defaultCurrency)))

      val convertedMemberships = memberships.map(_.map(RSParsing.toMembership)).getOrElse(Vector.empty)
      val withFallback = (if (tmpFields.memberships.isEmpty) convertedMemberships else tmpFields.memberships)
        .filter(m => m.programId > 0 && m.membershipId.nonEmpty)
      val fields = tmpFields.copy(
        memberships = withFallback,
        taxId = taxId,
        whiteLabelProperties = SSoServiceDeser.deserWlProp(tmpFields.whiteLabelProperties))

      val withAll = if (converted) tmpLegacyFields else tmpLegacyFields.copy(addresses = addresses).withDefaults

      val roles = UserRoleI.convV(theRoles)
      val favorites =
        Await.result(getFavorites(memberDetails.memberId)(db, Implicits.slowExecutionContext), Duration.apply(2, "s"))

      // Copy name from auth_users if data in rew_member is empty
      val name = userEntity.displayName.split(' ')
      val fixedMemberDetails = memberDetails.copy(
        userId = userEntity.id,
        firstName =
          if (memberDetails.firstName.nonEmpty || (name.length == 1 && memberDetails.lastName.nonEmpty))
            memberDetails.firstName
          else name.headOption.getOrElse(""),
        lastName =
          if (memberDetails.lastName.nonEmpty || (name.length == 1 && memberDetails.firstName.nonEmpty))
            memberDetails.lastName
          else name.lastOption.getOrElse(""),
        taxId = taxId,
        recordStatus = if (memberDetails.recordStatus != 1 || userEntity.recordStatus != 1) -1 else 1
      )

      Some(
        CustomerModel(
          fixedMemberDetails.email,
          fixedMemberDetails.memberId,
          userEntity.id,
          fixedMemberDetails,
          userEntity,
          overrideMappings,
          fields,
          withAll,
          theContacts,
          withAll.addresses,
          withAll.notes,
          withAll.travellerType,
          withAll.preferredDestinations,
          favorites,
          None,
          None,
          additionalFieldsWithTaxId,
          walletInfo = walletInfo
        ))
    }
  }

  def getDefaultCurrency(
      wlProp: Option[WlAdditionalProperties],
      whitelabelId: Option[Int],
      existing: Option[String]) = {
    val wlId = whitelabelId.getOrElse(Constants.AgodaId)
    wlProp match {
      case wlProp: Some[_] if Constants.RocketMilesWhitelabelIdRange.contains(wlId) => {
        SSoServiceDeser.deserWlProp(wlProp).flatMap(_.asInstanceOf[RocketMilesWlProperties].defaultCurrencyCode)
      }
      case _ => existing
    }
  }

  def getFavorites(memberId: Int)(implicit db: DBPlugin, ec: ExecutionContext): Future[Vector[Int]] = {
    //Shorter timeout, not very important function
    val rows = Try(
      QueriesHelper
        .resultSet(CapiConfig.PrimaryMdcGroup, timeout = Some(FiniteDuration(1, TimeUnit.SECONDS)))
        .q("EXEC dbo.retrieve_favorites ?", memberId)
        .map(_.int("hotel_id")))
    Future.fromTry(rows).recover { case _ => Vector.empty } //Potentially loses data if lost as part of a sync, but not important data
  }

  def isLoginType(m: UserMappingModel): Boolean = {
    CustomerModelHelper.isLoginType(m)
  }

}

case class RegisterRewardsResponse(isSucceed: Boolean, userId: Option[UUID], rewardsId: Int)

object RegisterRewardsResponse {
  val failed = RegisterRewardsResponse(isSucceed = false, None, 0)
}

case class LoyaltyData(
    memberId: Int,
    loyaltyLevel: Int,
    bookingsLifetimeCount: Int,
    bookingsLifetimeUsd: BigDecimal,
    bookingsLifetimeLastUpdated: Option[DateTime],
    couponPeriodCount: Int,
    couponLastDate: Option[DateTime],
    isPointEligible: Boolean
)
object LoyaltyData extends RSHelper {
  implicit def convO(o: Option[SQLRow]): Option[LoyaltyData] = o.map(implicit r => fromRow(r))

  implicit def fromRow(r: SQLRow): LoyaltyData = {
    LoyaltyData(
      memberId = 0,
      loyaltyLevel = r.intOption("loyalty_level").getOrElse(0),
      bookingsLifetimeCount = r.intOption("bookings_lifetime_count").getOrElse(0),
      bookingsLifetimeUsd = r.bigDecimalOption("bookings_lifetime_usd").getOrElse(BigDecimal(0.00)),
      bookingsLifetimeLastUpdated = r.dateTimeOption("bookings_lifetime_lastupdated"),
      couponPeriodCount = r.intOption("coupon_period_count").getOrElse(0),
      couponLastDate = r.strOption("coupon_period_lastupdated").map(f => DateTime.parse(f)),
      isPointEligible = r.booleanOption("is_point_eligible").getOrElse(false)
    )
  }
}

case class AfmConfig(key: String, value: String)

object AfmConfig extends RSHelper {
  implicit def convV(o: Vector[SQLRow]): Vector[AfmConfig] = o.map(implicit r => fromRow(r))

  implicit def fromRow(implicit row: SQLRow): AfmConfig = {
    new AfmConfig(
      key = c("configuration_key"),
      value = c("configuration_value")
    )
  }
}

case class TravelerType(
    id: Int,
    name: String
)

/**
  * Created by ysubba on 1/2/2017.
  */
object UserRoleI extends RSHelper {
  implicit def convV(o: Vector[SQLRow]): Vector[UserRole] = o.map(implicit r => fromRow(r))
  implicit def convO(o: Option[SQLRow]): Option[UserRole] = o.map(implicit r => fromRow(r))

  implicit def fromRow(s: SQLRow): UserRole = {
    implicit val s2: SQLRow = s
    //noinspection RedundantNewCaseClass
    new UserRole(
      if (s.map.contains("UserID")) s.uuid("UserID") else new UUID(0, 0),
      if (s.map.contains("RoleId")) s.uuid("RoleId") else new UUID(0, 0),
      if (s.map.contains("ObjectID")) s.int("ObjectID") else s.int("ObjectId"),
      if (s.map.contains("ObjectType")) s.str("ObjectType") else "",
      if (s.map.contains("skill_code")) s.str("skill_code") else ""
    )
  }
}

/**
  * Created by ysubba on 1/4/2017.
  */
case class PropertyData(
    propertyId: Int,
    propertyName: String,
    firstLiveDate: Option[DateTime],
    activeStatus: Int,
    hotelActiveStatus: Int,
    isNha: Boolean,
    photoTitle: Option[String],
    photoUrl: Option[String])
    extends DataRow

object PropertyData extends RSHelper {
  implicit def convV(o: Vector[SQLRow]): Vector[PropertyData] = o.map(implicit r => fromRow(r))

  implicit def convO(o: Option[SQLRow]): Option[PropertyData] = o.map(implicit r => fromRow(r))

  implicit def fromRow(s: SQLRow): PropertyData = {
    implicit val s2: SQLRow = s
    PropertyData(
      s.int("hotel_id"),
      s.str("hotel_name"),
      s.dateTimeOption("first_live_date"),
      s.int("active_status"),
      s.int("hotel_active_status"),
      s.boolean("is_nha"),
      s.strOption("picture_caption_title"),
      s.strOption("picture_location")
    )
  }
}

object UserMappingModelI extends RSHelper {
  implicit def convV(o: Vector[SQLRow], whitelabelId: Option[Int] = None): Vector[UserMappingModel] =
    o.map(implicit r => fromRow(r, whitelabelId = whitelabelId))

  implicit def convO(o: Option[SQLRow]): Option[UserMappingModel] = o.map(implicit r => fromRow(r))

  implicit def fromRow(s: SQLRow, whitelabelId: Option[Int] = None): UserMappingModel = {
    implicit val s2: SQLRow = s
    val uri = SecurityUriServer.parseUri(s.str("uri"))
    val userName = s.str("user_name")
    val authenticationTypeId = s.int("authentication_type_id")
    //Preserve the original case if we can extract it from the uri, which wasn't lower cased
    val usernameToUse =
      if (whitelabelId.exists(Constants.isJtbWhitelabel) && authenticationTypeId == AuthenticationType.Username.id)
        userName
      else if (uri.uriIdentityComponent.toLowerCase == userName) uri.uriIdentityComponent
      else userName
    UserMappingModel(
      c("user_id"),
      c("authentication_type_id"),
      uri.toString,
      usernameToUse,
      if (s.map.contains("salt")) c("salt") else None,
      s.dateTime("password_last_changed"),
      s.int("rec_status") > 0,
      s.dateTime("rec_created_when"),
      c("rec_created_by"),
      s.dateTimeOption("rec_modified_when"),
      c("rec_modified_by"),
      s.dateTime("expired_since"),
      isVerified = if (s.map.contains("is_verified")) s.booleanOption("is_verified").getOrElse(false) else false
    )
  }
}

object HostInfoModel {
  def fromRow(row: SQLRow): HostInfo = {
    HostInfo(
      displayName = row.strOption("display_name"),
      firstJoinDate = row.dateTimeOption("first_join_date"),
      countryId = row.intOption("country_id"),
      cityId = row.intOption("city_id"),
      stateId = row.intOption("state_id"),
      userDescription = row.strOption("user_description"),
      avgResponseRate = row.bigDecimalOption("avg_response_rate").map(_.floatValue()),
      avgResponseTime = row.intOption("avg_response_time"),
      photoUrl = row.strOption("photo_url"),
      contactPersonId = row.intOption("contact_person_id"),
      gender = row.strOption("gender"),
      spokenLanguages = row.strOption("speak_languages").map(_.split(",").map(_.toInt).toVector).getOrElse(Vector.empty),
      tprmHostType = row.intOption("tprm_host_type"),
      tprmQuestionnaireStatus = row.intOption("tprm_questionnaire_status"),
      tprmQuestionnaireChangedDate = row.dateTimeOption("tprm_questionnaire_changed_date"),
      userProfileType = row.intOption("user_profile_type")
    )
  }
}

object CustomerIdData {
  def fromRow(row: SQLRow): (Int, UUID) = {
    (row.int("memberid"), row.uuidOption("userid").getOrElse(Constants.BlankId))
  }
}
