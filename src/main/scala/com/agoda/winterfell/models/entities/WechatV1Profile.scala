package com.agoda.winterfell.models.entities

import com.agoda.dal.data.AsciiString

import java.util.UUID
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Encrypted, Key, Table}
import com.agoda.winterfell.ExtendedOption
import com.agoda.winterfell.common.SocialAppType
import com.agoda.winterfell.marshalling.JsonMarshalling
import com.agoda.winterfell.models.QueryUtil
import com.agoda.winterfell.output.SocialAppUserInfo
import com.agoda.winterfell.services.WeChatSocialAppUserInfo

@Table("wechat_v1_profile")
case class WechatV1Profile(
    @Key infoId: UUID,
    userId: UUID,
    @Encrypted appUserId: AsciiString,
    appId: Short,
    @Encrypted userInfo: Option[String],
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata()
) extends BasicEntity[WechatV1Profile] {
  override def messageKey: Option[Any] = {
    Some(userId)
  }
  def toSocialAppInfo: SocialAppUserInfo = {
    SocialAppUserInfo(
      userId,
      appUserId.toString,
      SocialAppType(appId),
      userInfo.map(JsonMarshalling.asMap).getOrElse(Map.empty)
    )
  }
}

object WechatV1Profile {
  def from(entity: CustomerEntity, info: WeChatSocialAppUserInfo): WechatV1Profile = {
    val infoId = QueryUtil.hashAsUuid(s"${entity.userId}/${info.appType}/${info.appUserId}")
    WechatV1Profile(
      infoId,
      entity.userId,
      AsciiString(info.appUserId).getOrFail("Non-ASCII appUserId"),
      info.appType.id.toShort,
      Some(info.appUserInfo),
      entity.origin
    )
  }
}
