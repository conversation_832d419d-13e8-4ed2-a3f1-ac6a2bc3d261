package com.agoda.winterfell.models.entities

import com.agoda.dal.model.{BasicEntity, EntityOrigin}
import com.agoda.dal.syntax.{Key, Table}
import java.util.UUID

@Table("customer_unverified_contacts_hash")
case class CustomerUnverifiedContactHashEntity(
    @Key contactId: UUID,
    userId: UUID,
    contactType: Short,
    contact_value_hash: Array[Byte],
    override val origin: Option[EntityOrigin],
    metadata: CustomerContactsHashMetaData = CustomerContactsHashMetaData()
) extends BasicEntity[CustomerUnverifiedContactHashEntity] {}
