package com.agoda.winterfell.models.entities

import java.time.Instant
import java.util.UUID

import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin, EntityStatus}
import com.agoda.dal.syntax.{Encrypted, Key, Table}
import com.agoda.winterfell.marshalling.JsonMarshalling
import com.agoda.winterfell.output.{MemberAddress, StaffNote}
import com.agoda.winterfell._

@Table("customer_notes")
case class CustomerNote(
    @Key noteId: UUID,
    userId: UUID,
    noteType: Short,
    @Encrypted noteValue: String,
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[CustomerNote] {
  override def messageKey: Option[Any] = {
    Some(userId)
  }
}

object CustomerNote {
  def noteIdFor(note: StaffNote, defaultId: Int): UUID = {
    UUID.nameUUIDFromBytes(
      s"${note.memberId}-${CustomerNoteType.Other.id}-${note.optStaffNoteId.filter(_ > 0).getOrElse(defaultId)}".getBytes)
  }

  def noteIdFor(address: MemberAddress, defaultId: Int): UUID = {
    UUID.nameUUIDFromBytes(
      s"${address.memberId}-${CustomerNoteType.Address.id}-${address.addressId.filter(_ > 0).getOrElse(defaultId)}".getBytes)
  }

  def noteIdFor(userId: UUID): UUID = {
    UUID.nameUUIDFromBytes(s"${CustomerNoteType.SpecialRemarks.id}-$userId".getBytes)
  }

  def from(noteId: UUID, model: CustomerModel, note: StaffNote): CustomerNote = {
    CustomerNote(
      noteId,
      model.userId,
      CustomerNoteType.Other.id,
      JsonMarshalling.short(note),
      model.memberDetails.origin.flatMap(EntityOrigin(_)),
      metadata = EntityMetadata().copy(
        status = note.recordStatus.flatMap(status => EntityStatus(status)).getOrElse(EntityStatus.Active),
        createdWhen = note.recordCreatedWhen.map(_.toJavaInstant).getOrElse(Instant.now()),
        modifiedWhen = note.recordModifiedWhen.map(_.toJavaInstant)
      )
    )
  }

  def from(noteId: UUID, model: CustomerModel, address: MemberAddress): CustomerNote = {
    CustomerNote(
      noteId,
      model.userId,
      CustomerNoteType.Address.id,
      JsonMarshalling.short(address),
      model.memberDetails.origin.flatMap(EntityOrigin(_)),
      metadata = EntityMetadata().copy(
        status = address.recordStatus.flatMap(status => EntityStatus(status)).getOrElse(EntityStatus.Active),
        createdWhen = address.recordCreatedWhen.map(_.toJavaInstant).getOrElse(Instant.now()),
        modifiedWhen = address.recordModifiedWhen.map(_.toJavaInstant)
      )
    )
  }
}
