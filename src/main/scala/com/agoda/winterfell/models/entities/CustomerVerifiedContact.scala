package com.agoda.winterfell
package models.entities

import com.agoda.capi.enigma.shared_model.customer.CustomerVerifiedContactDetail

import java.util.UUID
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin, EntityStatus}
import com.agoda.dal.syntax.{Encrypted, Key, Table}
import com.agoda.dal.data.{AsciiString, CollatedString, Collation}
import com.agoda.winterfell.common.AuthenticationType
import com.agoda.winterfell.common.AuthenticationType.AuthenticationType
import com.agoda.winterfell.crypto.RandomUtil
import com.agoda.winterfell.helper.WhiteLabelHelper
import com.agoda.winterfell.input.UpdateCustomerRequest
import com.agoda.winterfell.unified.CustomerAuditMetadata
import com.agoda.winterfell.utils.PhoneNumberFormatUtil
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime

@Table("customer_verified_contacts")
case class CustomerVerifiedContact(
    @Key id: CustomerContactId,
    userId: UUID,
    passwordHash: Option[AsciiString],
    @Encrypted remark: Option[String],
    isVerified: Option[Boolean],
    override val origin: Option[EntityOrigin],
    metadata: ContactsMetadata = ContactsMetadata())
    extends BasicEntity[CustomerVerifiedContact] {

  def authType: AuthenticationType = AuthenticationType(id.contactType)
  override def messageKey: Option[Any] = {
    Some(userId)
  }

  def toUserMapping: UserMappingModel = {
    val securityUri =
      if (authType.id == AuthenticationType.ActiveDirectory.id)
        SecurityUri(authType.toString.toUpperCase(), id.contactValue.underlying, None).toString
      else
        SecurityUri(authType.toString, id.contactValue.underlying, passwordHash.map(_.toString)).toString

    UserMappingModel(
      userId,
      id.contactType,
      securityUri,
      username = id.contactValue.underlying,
      salt = None,
      recStatus = metadata.status == EntityStatus.Active,
      createdWhen = metadata.createdWhen.toDateTime,
      createdBy = Constants.AppId,
      modifiedWhen = metadata.modifiedWhen.map(_.toDateTime),
      modifiedBy = None,
      passwordLastChanged = metadata.modifiedWhen.getOrElse(metadata.createdWhen).toDateTime,
      expiredSince = DateTime.now().plusYears(20),
      isVerified = isVerified.getOrElse(false)
    )
  }

  def toAuditMetadata(whitelabelId: Short, requesterUUID: Option[UUID]): CustomerAuditMetadata =
    CustomerAuditMetadata(
      whitelabelId,
      userId,
      requesterUUID,
      Some(AuthenticationType(id.contactType).toString),
      Some(id.contactValue.toString()))

  // TODO: Should be remove after the migration!
  private[winterfell] var isDirty: Boolean = false
}

object CustomerVerifiedContact {
  def from(model: CustomerModel): Vector[CustomerVerifiedContact] = {
    val isCaseSensitive = isUsernameCaseSensitive(model.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId))
    model.mappings.map { contact =>
      val username =
        if (contact.authType == AuthenticationType.Username.id && isCaseSensitive)
          CollatedString(Collation.CaseSensitive, contact.username)
        else CollatedString(Collation.CaseInsensitive, contact.username)
      val entity = CustomerVerifiedContact(
        CustomerContactId(
          contact.authType.toShort,
          username,
          model.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId).toShort),
        model.userId,
        SecurityUriHelper
          .parseUri(contact.uriString)
          .uriSecurityComponent
          .map(AsciiString(_).getOrFail("Non-ASCII password")),
        None,
        Some(contact.isVerified),
        model.memberDetails.origin.flatMap(EntityOrigin(_)).orElse(Some(EntityOrigin.Unspecified)),
        ContactsMetadata().copy(
          status = if (contact.recStatus) EntityStatus.Active else EntityStatus.SoftDeleted,
          createdWhen = contact.createdWhen.toJavaInstant,
          modifiedWhen = contact.modifiedWhen.map(_.toJavaInstant)
        )
      )
      entity.isDirty = true
      entity
    }
  }

  def from(model: CustomerModel, mapping: UserMappingModel, remark: Option[String] = None): CustomerVerifiedContact = {
    val isCaseSensitive = isUsernameCaseSensitive(model.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId))
    val username =
      if (mapping.authType == AuthenticationType.Username.id && isCaseSensitive)
        CollatedString(Collation.CaseSensitive, mapping.username)
      else CollatedString(Collation.CaseInsensitive, mapping.username)
    CustomerVerifiedContact(
      CustomerContactId(
        mapping.authType.toShort,
        username,
        model.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId).toShort),
      model.userId,
      SecurityUriHelper
        .parseUri(mapping.uriString)
        .uriSecurityComponent
        .map(AsciiString(_).getOrFail("Non-ASCII password")),
      remark,
      Some(mapping.isVerified),
      model.memberDetails.origin.flatMap(EntityOrigin(_))
    )
  }

  def from(
      customerToPatch: UpdateCustomerRequest,
      userId: UUID,
      whitelabelId: Int): (Vector[CustomerVerifiedContact], Vector[CustomerVerifiedContact]) = {
    var toUpdate: Vector[CustomerVerifiedContact] = Vector.empty
    var toDelete: Vector[CustomerVerifiedContact] = Vector.empty
    customerToPatch.regisTypes
      .filter(_.verified)
      .filterNot(_.onlyUnverContact)
      .foreach(verContact => {
        verContact.value match {
          case head +: _ if head.nonEmpty => {
            val formatted = formatContactValue(verContact.authType, head)
            val customerContactId = CustomerContactId(
              verContact.authType.toShort,
              CollatedString(Collation.CaseInsensitive, formatted),
              whitelabelId.toShort)
            toUpdate = toUpdate :+ CustomerVerifiedContact(
              customerContactId,
              userId,
              verContact.password.map(AsciiString(_).getOrFail("Non-ASCII password")),
              Option(customerToPatch.specialRemark).flatMap(_.find(_.nonEmpty)),
              None,
              None
            )
          }
          case head +: _
              if verContact.authType == AuthenticationType.Basic.id => { // Special handling for allowing deletion of primary email (changing it to fake email back to support mdb flow which doesn't allow primary email deletion)
            val fakeEmail = "_" + RandomUtil.randomPrintableStringForTests(5) + "@internal.agoda.in"
            val customerContactId = CustomerContactId(
              verContact.authType.toShort,
              CollatedString(Collation.CaseInsensitive, fakeEmail),
              whitelabelId.toShort)
            toUpdate = toUpdate :+ CustomerVerifiedContact(
              customerContactId,
              userId,
              verContact.password.map(AsciiString(_).getOrFail("Non-ASCII password")),
              Option(customerToPatch.specialRemark).flatMap(_.find(_.nonEmpty)),
              None,
              None
            )
          }
          case _ => {
            // TODO: fix this deletion!
            val customerContactId = CustomerContactId(
              verContact.authType.toShort,
              CollatedString(Collation.CaseInsensitive, StringUtils.EMPTY),
              whitelabelId.toShort)
            toDelete = toDelete :+ CustomerVerifiedContact(
              customerContactId,
              userId,
              verContact.password.map(AsciiString(_).getOrFail("Non-ASCII password")),
              None,
              None,
              None)
          }
        }
      })
    (toUpdate, toDelete)
  }

  def fromEnigma(detail: CustomerVerifiedContactDetail): CustomerVerifiedContact = {
    CustomerVerifiedContact(
      id = CustomerContactId(
        contactType = detail.contactType,
        contactValue = CollatedString.fromString(detail.contactValue),
        whitelabelId = detail.whitelabelId
      ),
      userId = detail.userId,
      passwordHash = detail.passwordHash.flatMap(AsciiString(_)),
      remark = detail.remark,
      isVerified = detail.isVerified,
      origin = detail.origin.flatMap(EntityOrigin(_)),
      metadata = ContactsMetadata(
        status = EntityStatus(detail.metadata.status.value).getOrElse(EntityStatus.Active),
        createdWhen = detail.metadata.createdWhen,
        modifiedWhen = detail.metadata.modifiedWhen
      )
    )
  }

  def formatContactValue(authType: Int, contactValue: String): String = {
    if (authType == AuthenticationType.PhoneNumber.id) PhoneNumberFormatUtil.getE164FormattedNumber(Some(contactValue))
    else contactValue
  }

  def isUsernameCaseSensitive(whiteLabelId: Int) = {
    WhiteLabelHelper.getLoginFeature(whiteLabelId).isUsernameCaseSensitive.getOrElse(false)
  }
}
