package com.agoda.winterfell.models.entities

import java.util.UUID
import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Column, Encrypted, Key, Table}
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.unified.whitelabel.WlAdditionalPropertiesDal
import com.agoda.winterfell.unified.wl.{RurubuWlProperties, WlAdditionalProperties}
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

@Table("customer_whitelabel_properties_rurubu")
case class RurubuAdditionalWlProperties(
    @Key userId: UUID,
    externalMemberId: Option[AsciiString],
    @Column("analysis_id")
    analysisID: Option[AsciiString],
    @JsonDeserialize(contentAs = classOf[java.lang.Integer])
    @Encrypted nationalityId: Option[Int],
    @Encrypted username: Option[String],
    @Encrypted age: Option[Int],
    @Encrypted gender: Option[String],
    @Encrypted phone2: Option[String],
    @Encrypted firstNameEng: Option[String],
    @Encrypted middleNameEng: Option[String],
    @Encrypted lastNameEng: Option[String],
    @Encrypted firstNameKana: Option[String],
    @Encrypted lastNameKana: Option[String],
    @Encrypted firstNameKanji: Option[String],
    @Encrypted lastNameKanji: Option[String],
    @Encrypted alternativeEmail: Option[String],
    @Encrypted address1: Option[String],
    @Encrypted address2: Option[String],
    @Encrypted postalCode: Option[String],
    @Encrypted city: Option[String],
    @Encrypted area: Option[String],
    @Encrypted region: Option[String],
    @Encrypted state: Option[String],
    @Encrypted country: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    @Column("rewards_points")
    @Encrypted rewardPoints: Option[Double],
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[RurubuAdditionalWlProperties]
    with WlAdditionalPropertiesDal
    with HasExternalMemberId {

  override def toResponse(implicit capiCtx: CapiRequestContextData): WlAdditionalProperties = {
    RurubuWlProperties(
      nationalityId,
      username,
      analysisID.map(_.toString),
      firstNameEng,
      middleNameEng,
      lastNameEng,
      firstNameKana,
      lastNameKana,
      firstNameKanji,
      lastNameKanji,
      alternativeEmail,
      rewardPoints,
      None,
      address1,
      address2,
      postalCode,
      region,
      country,
      state,
      city,
      area,
      age,
      gender,
      phone2,
      externalMemberId.map(_.toString)
    )
  }

}
