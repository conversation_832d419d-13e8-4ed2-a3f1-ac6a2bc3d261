package com.agoda.winterfell.models.entities

import com.agoda.dal.data.AsciiString

import java.util.{Date, UUID}
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Encrypted, Key, Table}
import com.agoda.winterfell.constant.Constants
import com.agoda.winterfell.output.{HostInfo => LegacyHostInfo}
import com.agoda.winterfell.unified.ConverterShim
import com.agoda.winterfell.utils.{CapiConfig, CountryMapping}

import java.time.{Instant, LocalDateTime, ZoneId}
import org.joda.time.DateTime

/**
  * com.agoda.winterfell.output.HostInfo (LegacyHostInfo) - req/res model for hosts end-points
  * com.agoda.winterfell.unified.HostInfo (NewHostInfo) - model saved in MDC json
  * com.agoda.winterfell.models.entities.HostUserInfo - DAL model
  */

@Table("host_user_info")
case class HostUserInfo(
    @Key userId: UUID,
    @Encrypted displayName: Option[String],
    countryId: Option[Int],
    stateId: Option[Int],
    cityId: Option[Int],
    @Encrypted userDescription: Option[String],
    avgResponseRate: Option[BigDecimal],
    avgResponseTime: Option[Int],
    photoUrl: Option[AsciiString],
    lastupdatedBy: Option[UUID],
    contactPersonId: Option[Int],
    effectiveLevel: Option[Byte],
    speakLanguages: Option[AsciiString],
    gender: Option[AsciiString],
    tprmHostType: Option[Byte],
    tprmQuestionnaireStatus: Option[Byte],
    tprmQuestionnaireChangedDate: Option[LocalDateTime],
    userProfileType: Option[Byte],
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata()
) extends BasicEntity[HostUserInfo] {
  def toLegacyHostInfo(entity: CustomerEntity): LegacyHostInfo = {
    LegacyHostInfo(
      // customer fields
      firstName = entity.firstName,
      lastName = entity.lastName,
      birthDate = entity.birthdate.map(m => ConverterShim.toJodaLocalDate(m).toDateTimeAtStartOfDay),
      contactLanguage = entity.languageId,
      nationalityId = entity.nationalityId,
      countryName = entity.nationalityId.flatMap(CountryMapping.forCountry),
      memberId = Some(entity.memberId),
      // host fields
      displayName = displayName,
      firstJoinDate = Some(new DateTime(metadata.createdWhen.toEpochMilli)),
      countryId = countryId,
      cityId = cityId,
      stateId = stateId,
      userDescription = userDescription,
      avgResponseRate = avgResponseRate.map(_.toFloat),
      avgResponseTime = avgResponseTime,
      photoUrl = photoUrl.map(_.toString),
      contactPersonId = contactPersonId,
      gender = gender.map(_.toString),
      spokenLanguages = speakLanguages
        .map(_.toString)
        .filter(_.nonEmpty)
        .map(_.split(",").map(_.toInt).toVector)
        .getOrElse(Vector.empty),
      tprmHostType = tprmHostType.map(_.toInt),
      tprmQuestionnaireStatus = tprmQuestionnaireStatus.map(_.toInt),
      tprmQuestionnaireChangedDate =
        tprmQuestionnaireChangedDate.map(l => new DateTime(l.atZone(ZoneId.of("Asia/Bangkok")).toInstant.toEpochMilli)),
      userProfileType = userProfileType.map(_.toInt)
    )
  }

  // code duplicate with fromLegacyHostInfo, but req/res and db schema aren't exactly same
  def updateFrom(req: LegacyHostInfo): HostUserInfo = {
    this.copy(
      displayName = req.displayName.orElse(this.displayName),
      countryId = req.countryId.orElse(this.countryId),
      stateId = req.stateId.orElse(this.stateId),
      cityId = req.cityId.orElse(this.cityId),
      userDescription = req.userDescription.orElse(this.userDescription),
      photoUrl = req.photoUrl.flatMap(AsciiString(_)).orElse(this.photoUrl),
      contactPersonId = req.contactPersonId.orElse(this.contactPersonId),
      speakLanguages =
        Option(req.spokenLanguages).flatMap(v => AsciiString(v.mkString(","))).orElse(this.speakLanguages),
      gender = req.gender.flatMap(AsciiString(_)).orElse(this.gender),
      tprmHostType = req.tprmHostType.map(_.toByte).orElse(this.tprmHostType),
      tprmQuestionnaireStatus = req.tprmQuestionnaireStatus.map(_.toByte).orElse(this.tprmQuestionnaireStatus),
      tprmQuestionnaireChangedDate = req.tprmQuestionnaireChangedDate
        .map(l => LocalDateTime.of(l.getYear, l.getMonthOfYear, l.getDayOfMonth, l.getHourOfDay, l.getMinuteOfHour))
        .orElse(this.tprmQuestionnaireChangedDate),
      userProfileType = req.userProfileType.map(_.toByte).orElse(this.userProfileType)
    )
  }

  // not allow to write avgResponseRate, and avgResponseTime. there is a sql job to update it ! more detail https://confluence.agodadev.io/display/NHA/Host+Manage+-+Response+Rate+and+Time
  def writeFrom(req: HostUserInfo): HostUserInfo = {
    this.copy(
      userId = if (this.userId == Constants.BlankId) req.userId else this.userId,
      displayName = req.displayName.orElse(this.displayName),
      countryId = req.countryId.orElse(this.countryId),
      stateId = req.stateId.orElse(this.stateId),
      cityId = req.cityId.orElse(this.cityId),
      userDescription = req.userDescription.orElse(this.userDescription),
      photoUrl = req.photoUrl.orElse(this.photoUrl),
      lastupdatedBy = req.lastupdatedBy.orElse(this.lastupdatedBy),
      contactPersonId = req.contactPersonId.orElse(this.contactPersonId),
      effectiveLevel = req.effectiveLevel.orElse(this.effectiveLevel),
      speakLanguages = req.speakLanguages.orElse(this.speakLanguages),
      gender = req.gender.orElse(this.gender),
      tprmHostType = req.tprmHostType.orElse(this.tprmHostType),
      tprmQuestionnaireStatus = req.tprmQuestionnaireStatus.orElse(this.tprmQuestionnaireStatus),
      tprmQuestionnaireChangedDate = req.tprmQuestionnaireChangedDate.orElse(this.tprmQuestionnaireChangedDate),
      userProfileType = req.userProfileType.orElse(this.userProfileType),
      origin = req.origin.orElse(this.origin),
      metadata = if (req.metadata.createdWhen.compareTo(this.metadata.createdWhen) <= 0) req.metadata else this.metadata // for not override meta data, rec_created_when is represent for firstJoinDate (legacy model)
    )
  }
}

object HostUserInfo {
  def fromLegacyHostInfo(entity: CustomerEntity, info: LegacyHostInfo): HostUserInfo = {
    val firstJoinDate = info.firstJoinDate.map(dt => Instant.ofEpochMilli(dt.toInstant.getMillis))
    HostUserInfo(
      userId = entity.userId,
      displayName = info.displayName,
      countryId = info.countryId,
      stateId = info.stateId,
      cityId = info.cityId,
      userDescription = info.userDescription,
      avgResponseRate = info.avgResponseRate.map(f => BigDecimal(f.toDouble)),
      avgResponseTime = info.avgResponseTime,
      photoUrl = info.photoUrl.flatMap(AsciiString(_)),
      lastupdatedBy = None,
      contactPersonId = info.contactPersonId,
      effectiveLevel = None,
      speakLanguages = Option(info.spokenLanguages).flatMap(v => AsciiString(v.mkString(","))),
      gender = info.gender.flatMap(AsciiString(_)),
      tprmHostType = info.tprmHostType.map(_.toByte),
      tprmQuestionnaireStatus = info.tprmQuestionnaireStatus.map(_.toByte),
      tprmQuestionnaireChangedDate = info.tprmQuestionnaireChangedDate.map(l =>
        LocalDateTime.of(l.getYear, l.getMonthOfYear, l.getDayOfMonth, l.getHourOfDay, l.getMinuteOfHour)),
      userProfileType = info.userProfileType.map(_.toByte),
      origin = entity.origin,
      metadata = EntityMetadata().copy(createdWhen = firstJoinDate.getOrElse(Instant.now()))
    )
  }
}
