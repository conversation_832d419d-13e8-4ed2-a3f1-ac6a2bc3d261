package com.agoda.winterfell.models.entities

import java.sql.{Connection, PreparedStatement}
import java.util.UUID

import org.joda.time.DateTime

/**
  * Abstraction of the ability to actually set parameters on the statement produced by a [[StatementSource]].
  * Again to avoid a dependency on the JDBC API for unit testing.
  */
trait SettableStatement {
  def setParam(index: Int, value: AnyRef): SettableStatement

  def toJDBC(implicit c: Connection): PreparedStatement
}

/**
  * Provide an abstraction for the method of obtaining a statement to set parameters on,
  * to facilitate easier unit testing without being bound to the horrible
  */
trait StatementSource {
  def getStatement(sql: String): SettableStatement
}
