package com.agoda.winterfell.models.entities

import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Column, Key, Table}
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.utils.CapiConfig

import java.util.UUID

@Table("partnerclaim_storage")
case class PartnerClaimStorageEntity(
    @Key partnerClaimKey: PartnerClaimKey,
    @Column("claim_value", insertable = true, updatable = false, length = 8001)
    claimValue: String,
    userId: UUID,
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[PartnerClaimStorageEntity]

object PartnerClaimStorageEntity {
  def from(claimKey: UUID, memberId: Int, claimValue: String, userId: UUID)(implicit ctx: CapiRequestContextData) = {
    val origin = ctx.agOrigin.flatMap(EntityOrigin(_)).orElse { EntityOrigin(CapiConfig.CountryCode) }
    PartnerClaimStorageEntity(PartnerClaimKey(claimKey, memberId, ctx.whiteLabelId.toShort), claimValue, userId, origin)
  }
}
