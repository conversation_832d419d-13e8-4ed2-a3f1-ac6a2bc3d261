package com.agoda.winterfell
package models.entities

import com.agoda.dal.data.AsciiString

trait HasExternalMemberId {
  def externalMemberId: Option[AsciiString]
}

object HasExternalMemberId {
  def apply(whitelabelId: Int): Boolean = whitelabelId match {
    case Constants.JapanicanId | Constants.JapanicanUatId | Constants.RurubuId | Constants.RurubuUatId |
        Constants.JtbId | Constants.JtbUatId =>
      true
    case _ => false
  }
}
