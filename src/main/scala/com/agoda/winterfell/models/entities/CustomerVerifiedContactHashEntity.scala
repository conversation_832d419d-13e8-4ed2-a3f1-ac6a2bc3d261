package com.agoda.winterfell.models.entities

import com.agoda.dal.model.{BasicEntity, EntityOrigin}
import com.agoda.dal.syntax.{Key, Table}
import java.util.UUID

@Table("customer_verified_contacts_hash")
case class CustomerVerifiedContactHashEntity(
    @Key id: CustomerContactHashId,
    userId: UUID,
    override val origin: Option[EntityOrigin],
    metadata: CustomerContactsHashMetaData = CustomerContactsHashMetaData()
) extends BasicEntity[CustomerVerifiedContactHashEntity] {}
