package com.agoda.winterfell.models.entities

import com.agoda.dal.{data, env}
import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin, EntityStatus}
import com.agoda.dal.syntax.{Column, Encrypted, Key, Table}
import com.agoda.winterfell.services.WeChatSocialAppUserInfo
import com.agoda.winterfell.ExtendedOption
import com.agoda.winterfell.marshalling.JsonMarshalling
import com.agoda.winterfell.utils.CapiConfig

import java.time.Instant

@Table("wechat_v1_cache")
case class WechatV1Cache(
    @Key code: AsciiString,
    @Encrypted userInfo: Option[String],
    override val origin: Option[EntityOrigin],
    metadata: WechatV1CacheMetadata = WechatV1CacheMetadata()
) extends BasicEntity[WechatV1Cache] {
  def toWeChatSocialAppUserInfo: List[WeChatSocialAppUserInfo] = {
    userInfo.map(JsonMarshalling.Mapper.readValue[List[WeChatSocialAppUserInfo]](_)).getOrElse(List.empty)
  }
}

case class WechatV1CacheMetadata(
    @Column("rec_status", insertable = false, updatable = false)
    status: EntityStatus = EntityStatus.Active,
    @Column("rec_created_when", insertable = true, updatable = false)
    createdWhen: Instant = Instant.now(),
    @Column("server", insertable = true, updatable = false, length = 255)
    server: data.AsciiString = env.HostName)
    extends EntityMetadata

object WechatV1Cache {
  def apply(authCode: String, socialUserInfos: List[WeChatSocialAppUserInfo]): WechatV1Cache = {
    new WechatV1Cache(
      AsciiString(authCode).getOrFail("Non-ASCII wechat v1 auth_code"),
      Some(JsonMarshalling.pretty(socialUserInfos)),
      EntityOrigin(CapiConfig.CountryCode))
  }
}
