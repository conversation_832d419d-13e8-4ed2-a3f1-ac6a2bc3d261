package com.agoda.winterfell.models.entities

import java.time.Instant

import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{EntityMetadata, EntityStatus}
import com.agoda.dal.syntax.Column

case class ContactsMetadata(
    @Column("rec_status", insertable = true, updatable = true)
    status: EntityStatus = EntityStatus.Active,
    @Column("rec_created_when", insertable = true, updatable = true)
    createdWhen: Instant = Instant.now(),
    @Column("rec_modified_when", insertable = true, updatable = true)
    modifiedWhen: Option[Instant] = Some(Instant.now()),
    @Column("server", insertable = true, updatable = true, length = 255)
    server: AsciiString = com.agoda.dal.env.HostName)
    extends EntityMetadata
