package com.agoda.winterfell.models.entities

import java.time.Instant
import java.util.UUID
import com.agoda.dal.data.AsciiString
import com.agoda.dal.model.{BasicEntity, EntityMetadata, EntityOrigin}
import com.agoda.dal.syntax.{Column, Encrypted, Key, Table}
import com.agoda.winterfell.Constants
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.models.queries.UserIdQuery
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.unified.whitelabel.WlAdditionalPropertiesDal
import com.agoda.winterfell.unified.wl.{RocketMilesWlProperties, WlAdditionalProperties}

@Table("customer_whitelabel_properties_rm")
case class RocketMilesAdditionalWlProperties(
    @Key userId: UUID,
    addCompanyProfileToReceipt: Boolean,
    @Encrypted company: Option[String],
    timeZoneRm: Option[AsciiString],
    @Column("default_currency_id")
    defaultCurrencyCode: Option[AsciiString],
    defaultRewardProgramId: Option[UUID],
    defaultRewardAccountId: Option[UUID],
    referrerId: Option[UUID],
    @Column("site_id")
    userAccessGroupId: Option[UUID],
    idpUserId: Option[UUID],
    typeRm: Option[Int],
    fraudStatus: Option[Int],
    gamingStatus: Option[Int],
    //TODO: For customers table, has been set as nullable in featire [ACCWL-262]
    isNewsLetter: Boolean,
    @Column("referral_signup_reward_uuid")
    referralSignupRewardUuid: Option[UUID],
    @Column("date_pushed_to_salesforce")
    datePushedToSalesforce: Option[Instant],
    @Column("promotion_signup_UUID")
    promotionSignupUUID: Option[UUID],
    override val origin: Option[EntityOrigin],
    metadata: EntityMetadata.Default = EntityMetadata())
    extends BasicEntity[RocketMilesAdditionalWlProperties]
    with WlAdditionalPropertiesDal {
  override def messageKey: Option[Any] = {
    Some(userId)
  }
  override def toResponse(implicit capiCtx: CapiRequestContextData): WlAdditionalProperties = {
    RocketMilesAdditionalWlProperties.toResponse(this)
  }
}

object RocketMilesAdditionalWlProperties {
  def toResponse(wlProp: RocketMilesAdditionalWlProperties)(
      implicit capiCtx: CapiRequestContextData): WlAdditionalProperties = {
    val referralCode = TempSingletonHolder.rocketMilesReferralCodeRepo
      .get()
      .searchBy(UserIdQuery(wlProp.userId), Constants.DefaultQueryParams)()
      .blockFor(Constants.MdcQueryTimeout)
      .headOption
      .map(e => Some(e.referralCode))
      .getOrElse(None)

    RocketMilesWlProperties(
      rocketMilesUUID = None,
      externalMemberId = None,
      addCompanyProfileToReceipt = wlProp.addCompanyProfileToReceipt,
      company = wlProp.company,
      timeZoneRm = wlProp.timeZoneRm.map(_.toString),
      defaultCurrencyCode = wlProp.defaultCurrencyCode.map(_.toString),
      defaultRewardProgramId = wlProp.defaultRewardProgramId,
      defaultRewardAccountId = wlProp.defaultRewardAccountId,
      referrerId = wlProp.referrerId,
      referrerCode = None,
      referralCode = referralCode,
      userAccessGroupId = wlProp.userAccessGroupId,
      idpUserId = wlProp.idpUserId,
      typeRm = wlProp.typeRm,
      fraudStatus = wlProp.fraudStatus,
      gamingStatus = wlProp.gamingStatus,
      isNewsLetter = wlProp.isNewsLetter,
      referralSignupRewardUuid = wlProp.referralSignupRewardUuid,
      datePushedToSalesforce = wlProp.datePushedToSalesforce,
      promotionSignupUUID = wlProp.promotionSignupUUID
    )
  }
}
