package com.agoda.winterfell.models.entities

import com.agoda.dal.model.{EntityMetadata, EntityStatus}
import com.agoda.dal.syntax.Column

import java.time.Instant

case class CustomerContactsHashMetaData(
    @Column("rec_status", insertable = true, updatable = true)
    status: EntityStatus = EntityStatus.Active,
    @Column("rec_created_when", insertable = true, updatable = true)
    createdWhen: Instant = Instant.now(),
    @Column("rec_modified_when", insertable = true, updatable = true)
    modifiedWhen: Option[Instant] = Some(Instant.now()))
    extends EntityMetadata {}
object CustomerContactsHashMetaData {
  def transformCustomMeta(metaData: ContactsMetadata): CustomerContactsHashMetaData = {
    CustomerContactsHashMetaData(metaData.status, metaData.createdWhen, metaData.modifiedWhen)
  }
}
