package com.agoda.winterfell.models

import java.util.UUID
import com.agoda.winterfell.internal.SQLRow
import com.agoda.winterfell.models.RSParsing.toTableInt
import com.agoda.winterfell.utils._

/**
  * Created by nrojcharoenp on 25/01/2018
  */
object SecurityQueries {

  import DBGroups._
  import QueriesHelper._

  def authAssignRoleApprovalRoleGet(userId: UUID)(implicit dB: DBPlugin): Vector[SQLRow] = {
    resultSet(ReadGroup).q("EXEC dbo.auth_assignrole_approvalrole_get ?", userId)
  }

  def authAssignRoleGet(userId: UUID)(implicit dB: DBPlugin): Vector[SQLRow] = {
    resultSet(ReadGroup).q("EXEC dbo.backoffice_assigned_roles_select_v1 ?", userId)
  }

  def assignRoleListByObjectTypeAndObjectIdGet(
      userId: UUID,
      objectType: Char,
      objectIds: Vector[Int],
      recStatus: Option[Int])(implicit dB: DBPlugin): Vector[SQLRow] = {
    var objectIdsTable = toTableInt(objectIds)
    resultSet(ReadGroup).q(
      "EXEC dbo.assigned_roles_select_by_objecttype_and_objectids_v4 ?, ?, ?, ?",
      userId,
      objectType,
      objectIdsTable,
      recStatus)
  }

  def securityGetUsersByManager(managerId: UUID, onlyActive: Boolean)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.security_get_users_by_manager_v3 ?, ?", managerId, onlyActive)

  def boUserSelectCurrentRelations(objectType: Char, objectId: Option[Int])(implicit db: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.backoffice_user_select_current_relations_v3 ?, ?", objectType, objectId)

  def boUserSelectMoveToRelations()(implicit db: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.backoffice_select_ldap_users_v1")

  def boDownloadAuthUserRelationsCurrentRelations(objectType: Char, userId: UUID)(
      implicit db: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q(
      "EXEC dbo.backoffice_download_auth_user_relations_current_relations ?, ?",
      objectType,
      userId)

  def boUserSelectAuthUserRelationship(objectType: Char, objectId: Int)(implicit db: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q(
      "EXEC dbo.backoffice_user_select_auth_user_relations_relationship ?, ?",
      objectId,
      objectType)

  def boUserSelectRelationshipMoveToUser(objectType: Char, objectId: Int)(implicit db: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.backoffice_user_select_relationship_move_to_user ?, ?", objectId, objectType)

  def insertAuthAssignedRoleAudit(
      userId: UUID,
      assignRoleId: UUID,
      assignUserId: UUID,
      assignObjectType: Char,
      assignObjectId: Int,
      activityId: Int)(implicit db: DBPlugin): Vector[SQLRow] = {
    resultSet(WriteGroup).q(
      "EXEC dbo.backoffice_auth_assigned_role_audit_insert_v2 ?, ?, ?, ?, ?, ?",
      userId,
      assignUserId,
      assignRoleId,
      assignObjectType,
      assignObjectId,
      activityId)
  }
}
