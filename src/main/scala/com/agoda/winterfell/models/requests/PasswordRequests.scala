package com.agoda.winterfell.models.requests

case class ForgotPasswordRequestHolder(args: ForgotPasswordRequest)
case class ForgotPasswordV1RequestHolder(username: String)
case class ForgotPasswordRequest(
    email: String,
    languageId: Int,
    returnUrl: String,
    options: Map[String, String],
    templateId: String,
    preference: String) {
  def withOptions: ForgotPasswordRequest =
    this.copy(options = Option(options).getOrElse(Map.empty) + ("langId" -> languageId.toString))
}

case class ChangePasswordWithTokenRequestHolder(arguments: ChangePasswordWithTokenRequest)

case class ChangePasswordWithTokenRequest(
    forgottenPasswordToken: String,
    plainNewPassword: String,
    options: Map[String, String]) {
  def disallowLastXPasswords: Option[Int] = options.get("disallow_last_x_passwords").map(_.toInt)
}
