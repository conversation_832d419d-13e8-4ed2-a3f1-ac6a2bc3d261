package com.agoda.winterfell
package models.requests

import java.util.UUID

import com.agoda.dal.data.AsciiString
import com.agoda.winterfell.models.entities.ApiKeyPolicies

import scala.util.matching.Regex

case class ApiKey(key: String)

case class ApiConsumer(apiKey: String, whiteLabelTokens: Seq[UUID], teamName: String)

object ApiConsumer {
  def toApiKeyPolicies(consumer: ApiConsumer): ApiKeyPolicies = {
    ApiKeyPolicies(
      consumer.apiKey,
      AsciiString(consumer.whiteLabelTokens.map(_.toString).mkString("\n")).getOrFail("Non-ASCII wl tokens"),
      getEndpoints = None,
      postEndpoints = None,
      putEndpoints = None,
      patchEndpoints = None,
      deleteEndpoints = None,
      AsciiString(consumer.teamName).getOrFail("Non-ASCII team name")
    )
  }
}

case class ApiKeyPolicyRequest(apiKey: String, httpMethod: String, endpoints: Seq[String]) {
  endpoints.foreach(new Regex(_))
}

case class WhitelabelTokenRequest(apiKey: String, whiteLabelTokens: Seq[UUID])
