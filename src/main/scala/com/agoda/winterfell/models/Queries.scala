package com.agoda
package winterfell
package models

import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.capi.enigma.shared_model.booking.BookingInfo

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future, TimeoutException}
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.sql.{Connection, ResultSet, Types}
import java.time.{Instant, LocalDate}
import java.util.concurrent.TimeUnit
import java.util.{Base64, UUID}
import com.agoda.dal.{data, env}
import com.agoda.winterfell.backoffice.entity.AuthUserInRoleTableType
import com.agoda.winterfell.backoffice.model.CastHelper.toUserUuidList
import com.agoda.winterfell.basiclogin.model.BasicLoginConfig
import com.agoda.winterfell.common.AuthenticationType.AuthenticationType
import com.agoda.winterfell.common.PropertyStatus.PropertyStatus
import com.agoda.winterfell.common.RecordStatus.RecordStatus
import com.agoda.winterfell.common.SocialAppType.SocialAppType
import com.agoda.winterfell.common.{AdditionalProperty, AuthenticationType, PropertyStatus, RecordStatus}
import com.agoda.winterfell.helper.{Utils, WhiteLabelHelper}
import com.agoda.winterfell.input.UserDetailToDelete
import com.agoda.winterfell.internal.{CustomerToken, SQLRow}
import com.agoda.winterfell.marshalling.JsonMarshalling
import com.agoda.winterfell.metrics.{CapiLogMessage, CapiMeasurementMessage}
import com.agoda.winterfell.models.ModelImplicits._
import com.agoda.winterfell.models.QueryUtil.fallbackFetch
import com.agoda.winterfell.models.RSParsing.MemberMDCStatusRow
import com.agoda.winterfell.models.entities.{UserRole, _}
import com.agoda.winterfell.models.errors.{GDPREntityException, UnknownEntityException}
import com.agoda.winterfell.output.ContactMethod.ContactMethod
import com.agoda.winterfell.output.{EmailVerificationMapping, _}
import com.agoda.winterfell.services.cncustomers.BookingTableMappings
import com.agoda.winterfell.streams.events._
import com.agoda.winterfell.unified._
import com.agoda.winterfell.unified.accessors.{DefaultSyncer, DefaultSyncerHelper}
import com.agoda.winterfell.unified.progress.{RoleRow, TokenBlackListItem}
import com.agoda.winterfell.unified.wl.{JapanicanWlProperties, RocketMilesWlProperties, WlAdditionalProperties}
import com.agoda.winterfell.utils.DBGroups._
import com.agoda.winterfell.utils.QueriesHelper.{execute, result, resultSet, resultSets}
import com.agoda.winterfell.utils.{DBGroups, DBPlugin, QueriesHelper, _}
import com.google.common.cache.CacheBuilder
import com.microsoft.sqlserver.jdbc.SQLServerDataTable

import javax.crypto.spec.SecretKeySpec
import javax.crypto.{Cipher, Mac}
import org.joda.time.{DateTime, DateTimeZone}

import java.security.MessageDigest
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.FiniteDuration
import scala.language.postfixOps
import scala.util.matching.Regex
import scala.util.{Failure, Success, Try}
import com.agoda.winterfell.utils.CapiConfig
import com.agoda.winterfell.services.TempSingletonHolder
import com.google.common.annotations.VisibleForTesting

object QueryUtil {
  QueryUtilShim.function = encrypt

  def removeIllegalChars(name: String): String = {
    Option(name).map(_.replaceAll(Constants.IllegalChars, "")).orNull
  }
  def isContainIllegalChars(str: String): Boolean = Constants.IllegalChars.exists(str.contains(_))

  def isLatin(name: String): Boolean = {
    name.isEmpty || name.matches(Constants.extendLatinPattern)
  }

  def isIncludeNonLatin(name: String): Boolean = !isLatin(name)

  def hash(data: Array[Byte]): Array[Byte] = {
    val hasher = Mac.getInstance("HmacMD5")
    hasher.init(new SecretKeySpec(CapiConsul.dbKey, "AES"))
    hasher.doFinal(data)
  }

  def geniusHash(data: String): Array[Byte] = {
    val hasher = Mac.getInstance("HmacSHA512")
    hasher.init(new SecretKeySpec(CapiConsul.geniusKey, "HmacSHA512"))
    hasher.doFinal(data.getBytes(StandardCharsets.UTF_8))
  }

  def hash(data: String): Array[Byte] = hash(data.getBytes(StandardCharsets.UTF_8))

  def hashWithBase64(data: String): String = {
    val digests = TempSingletonHolder.dalContext.get().cryptoServiceV3.hash(data).head.bytes.unsafeArray
    s"_!${Base64.getEncoder.encodeToString(digests)}_"
  }

  def boPiiHash(data: String) = {
    MessageDigest
      .getInstance("SHA-256")
      .digest((data + CapiConsul.backofficeHashSalt).getBytes("UTF-8"))
      .map("%02x".format(_))
      .mkString
  }

  def hashAsUuid(data: String): UUID = {
    val buffer = ByteBuffer.wrap(hash(data))
    new UUID(buffer.getLong(), buffer.getLong())
  }

  def roleHash(userId: UUID, roleId: UUID, objectType: String, objectId: Int): UUID = {
    hashAsUuid(s"${userId}/${roleId}/${objectType}/${objectId}")
  }

  def isEncrypted(value: String): Boolean = {
    if (value == null) false else value.startsWith("_?") && value.endsWith("_")
  }

  def isMasked(value: String): Boolean = {
    if (value == null) false else value.startsWith("_!") && value.endsWith("_")
  }

  def decrypt(maybeCipher: String): String = {
    if (isEncrypted(maybeCipher)) {
      val key = new SecretKeySpec(CapiConsul.dbKey, "AES")
      val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
      cipher.init(Cipher.DECRYPT_MODE, key)
      val stripped = Base64.getDecoder.decode(maybeCipher.substring(2, maybeCipher.length - 1))
      val bytes: Array[Byte] = cipher.doFinal(stripped)
      new String(bytes, "UTF-8")
    } else maybeCipher
  }

  def encrypt(plain: String): String = {
    if (plain == null) plain
    else {
      import javax.crypto.spec.SecretKeySpec
      val key = new SecretKeySpec(CapiConsul.dbKey, "AES")
      val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
      cipher.init(Cipher.ENCRYPT_MODE, key)
      val bytes = cipher.doFinal(plain.getBytes("UTF-8"))
      s"_?${Base64.getEncoder.encodeToString(bytes)}_"
    }
  }
  def encryptO(plain: Option[String]): Option[String] = plain.map(p => encrypt(p))

  def masking(data: String, whitelabelId: Option[Int], forceMaskingPII: Boolean): String = {
    if (!DBUtil.isEnableWritePiiData(whitelabelId) || forceMaskingPII) {
      if (!isMasked(data)) hashWithBase64(data) else data
    } else {
      encrypt(data)
    }
  }

  def maskingO(data: Option[String], whitelabelId: Option[Int], forceMaskingPII: Boolean): Option[String] =
    data.map(masking(_, whitelabelId, forceMaskingPII))

  @inline
  def fallbackFetch[T](
      f: String => Vector[T],
      group: String = DBGroups.ReadGroup,
      fallbackTo: String = DBGroups.WriteGroup,
      numberOfRetry: Int = 2,
      fallbackWhenEmpty: Boolean = true): Vector[T] = {
    val result = Utils.tryRetryStandard(numberOfRetry)(f(group))
    def executeFallback = {
      Try(f(fallbackTo))
        .recoverWith {
          case e: Throwable =>
            CapiLogMessage(
              "capi.db.fallback",
              LogLevel.WARN,
              stringTags = Map("function" -> "fallbackFetch"),
              exception = Some(e))
            result
        }
        .getOrElse(result.get)
    }
    result match {
      case Failure(_) =>
        executeFallback
      case Success(value) =>
        if (value.isEmpty && fallbackWhenEmpty)
          executeFallback
        else
          value
    }
  }
  @inline
  def fallbackFetchVectorOfVector[T](
      f: String => Vector[Vector[T]],
      group: String = DBGroups.ReadGroup,
      checkEmpty: Boolean = true,
      config: () => BasicLoginConfig = CapiConsul.basicLoginConfig _): Vector[Vector[T]] = {
    val result = Utils.tryRetryStandard(2)(f(group))
    def executeFallback = {
      val fallbackGroup = if (config().disableFindCustomerMdbFallback) DBGroups.ReadGroup else DBGroups.WriteGroup
      Try(f(fallbackGroup))
        .recoverWith {
          case e: Throwable =>
            CapiLogMessage(
              "capi.db.fallback",
              LogLevel.WARN,
              stringTags = Map("function" -> "fallbackFetchVectorOfVecter"),
              exception = Some(e))
            result
        }
        .getOrElse(result.get)
    }
    result match {
      case Failure(_) =>
        executeFallback
      case Success(value) =>
        val headOption = value.headOption
        if (!checkEmpty) {
          value
        } else if (headOption.exists(_.nonEmpty))
          value
        else
          executeFallback
    }
  }
  @inline
  def fallbackFetchOption[T](
      f: String => Option[T],
      group: String = DBGroups.ReadGroup,
      forceMDB: Boolean = false): Option[T] = {
    val result = Utils.tryRetryStandard(2)(f(group))
    val executeFallback: Option[T] = {
      Try(f(DBGroups.WriteGroup)).toOption.getOrElse(result.toOption.flatten)
    }
    result match {
      case Failure(_) =>
        executeFallback
      case Success(value) =>
        if (!forceMDB) value
        else value.orElse(executeFallback)
    }
  }
  @inline
  def fallbackGroupFor(group: String): String =
    if (group == CapiConfig.PrimaryMdcGroup) CapiConfig.SecondaryMdcGroup else CapiConfig.PrimaryMdcGroup

  @inline
  def getFallbackRolesGroup(group: String) = if (CapiConsul.fallbackReadRolesToMDB) WriteStandbyGroup else group
}

/**
  * <AUTHOR>
  */
//noinspection TypeAnnotation
object Queries {

  import DBGroups._
  import QueriesHelper._
  import QueryUtil._
  import RSParsing._

  /**
    * @note this method should be use only with mapping validation due to broken data in customer_auth_mapping table!
    * @see [[modelByAuth()]]
    */
  private def findUserId(
      authentication: String,
      authType: AuthenticationType.AuthenticationType,
      statusOverride: Boolean = false,
      whiteLabelId: Option[Int] = None,
      group: String = CapiConfig.PrimaryMdcGroup)(implicit dB: DBPlugin): Option[UUID] = {
    val whiteLabel = whiteLabelId.getOrElse(Constants.AgodaId)
    // TODO: check mapping rec_status!
    DefaultSyncerHelper
      .uuidFor(authentication, authType.id, whiteLabel, group, statusOverride)
      .orElse(legacyFindUserId(authentication, authType, statusOverride, whiteLabel))
  }

  private def legacyFindUserId(
      name: String,
      authType: AuthenticationType.AuthenticationType,
      statusOverride: Boolean,
      whiteLabelId: Int
  )(implicit dB: DBPlugin): Option[UUID] = {
    val username = {
      val loginFeature = WhiteLabelHelper.getLoginFeature(whiteLabelId)
      if (authType == AuthenticationType.Username && loginFeature.isUsernameCaseSensitive.getOrElse(false)) name
      else name.toLowerCase
    }
    val params = Seq(name, encrypt(name), encrypt(username), statusOverride, "all", whiteLabelId)
    CapiMeasurementMessage(
      "customerapi.cdb_fallback",
      Map(
        "endpoint" -> Profiler.endPointTeamName.value.map(_._1).getOrElse("Unknown"),
        "teamName" -> Profiler.endPointTeamName.value.map(_._2).getOrElse("Unknown")
      )
    )
    fallbackFetchVectorOfVector(
      { group: String =>
        resultSets(
          group,
          timeout = Some(Constants.MdcQueryTimeout) // 1 seconds in prod
        ).q(s"EXEC dbo.find_user_id_v2 ?, ?, ?, ?, ?, ?", params: _*)
      }
    ).flatMap(_.map(RSParsing.toLookup).headOption)
      .collectFirst { case RSParsing.LookupUserResult(id, _, _) => id }
  }

  private def findExternalMemberMapping(externalMemberId: String, whiteLabelId: Int)(implicit dB: DBPlugin) = {
    result(CapiConfig.PrimaryMdcGroup).q(s"EXEC dbo.find_external_user_id_v3 ?,?", externalMemberId, whiteLabelId)
  }

  def findUserIdByExternalMemberId(externalMemberId: String, whiteLabelId: Int)(implicit dB: DBPlugin): Option[UUID] = {
    findExternalMemberMapping(externalMemberId, whiteLabelId).collectFirst { case row => row.uuid("user_id") }
  }

  def findMemberIdByExternalMemberId(externalMemberId: String, whiteLabelId: Int)(
      implicit dB: DBPlugin): Option[Int] = {
    findExternalMemberMapping(externalMemberId, whiteLabelId).collectFirst { case row => row.int("member_id") }
  }

  def findUserIdByAnalysisId(analysisId: String, whiteLabelId: Int)(implicit dB: DBPlugin): Option[UUID] = {
    result(CapiConfig.PrimaryMdcGroup)
      .q(s"EXEC dbo.find_analysis_user_id_v1 ?,?", analysisId, whiteLabelId)
      .collectFirst { case row => row.uuid("user_id") }
  }

  def securityGetPasswordHistoryRead(userId: UUID, limit: Int)(implicit dB: DBPlugin): Vector[ResetPasswordHistory] =
    resultSet(ReadGroup).q("EXEC dbo.security_get_password_history ?, ?", userId, limit)

  def customerByMemberId(memberId: Int, allRec: Boolean = false)(implicit dB: DBPlugin): Customer = {
    modelByUUID(null, allRec, memberId = Some(memberId))
      .map(CustomerConverters.from)
      .getOrFail(s"No member id found for $memberId")
  }

  def modelByAuth(
      username: String,
      authType: AuthenticationType.AuthenticationType,
      getAllRecStatus: Boolean = false,
      whiteLabelId: Option[Int] = None,
      roles: Boolean = true)(implicit dB: DBPlugin): Option[CustomerModel] = {
    if (username.isEmpty) None
    else {
      findUserId(username, authType, getAllRecStatus, whiteLabelId)
        .flatMap(u => modelByUUID(u, getAllRecStatus, roles = roles))
        .flatMap { customer =>
          val hasActiveMapping = {
            val loginFeature = WhiteLabelHelper.getLoginFeature(whiteLabelId.getOrElse(Constants.AgodaId))

            if (authType == AuthenticationType.Username && loginFeature.isUsernameCaseSensitive.getOrElse(false))
              customer.mappings.exists(m =>
                m.authType == authType.id && m.username.equals(username) && (getAllRecStatus || m.recStatus))
            else
              customer.mappings.exists(m =>
                m.authType == authType.id && m.username.equalsIgnoreCase(username) && (getAllRecStatus || m.recStatus))
          }

          if (getAllRecStatus || hasActiveMapping) Some(customer)
          else if (authType == AuthenticationType.Basic && customer.email.equalsIgnoreCase(username))
            Some(customer) // NOTE: YCS users have email and different username for basic auth mapping
          else if (!hasActiveMapping) {
            nonCachedModelByUUID(customer.userId, None, getAllRecStatus, roles = roles)
              .filter(model =>
                model.mappings.exists { m =>
                  m.authType == authType.id && m.username.equalsIgnoreCase(username)
              })
              .map(model => customer.copy(mappings = model.mappings))
          } else None
        }
    }
  }

  def modelByUUID(
      uuid: UUID,
      getAllRecStatus: Boolean = false,
      memberId: Option[Int] = None,
      group: String = CapiConfig.PrimaryMdcGroup,
      whiteLabelId: Option[Int] = None,
      roles: Boolean = true)(implicit dB: DBPlugin, customerCache: CustomerCache): Option[CustomerModel] = {
    def execute: Option[CustomerModel] = {
      implicit val capiCtx: CapiRequestContextData =
        CapiRequestContextData.emptyWlContext(wlId = whiteLabelId.getOrElse(Constants.AgodaId))
      val cachedValue =
        if (whiteLabelId.exists(Constants.isRmWhitelabelId) || CapiConsul.lookupWithOutMDCJson) None
        else if (memberId.isEmpty) DefaultSyncerHelper.customerFor(uuid, group)
        else DefaultSyncerHelper.customerFor(memberId.get, group)

      cachedValue
        .orElse { nonCachedModelByUUID(uuid, memberId, getAllRecStatus, roles = roles) }
        .filter(c => c.memberDetails.recordStatus == 1 || getAllRecStatus)
    }
    if (customerCache == CustomerCache.NonCache)
      return execute
    (uuid, memberId) match {
      case (_, Some(mId)) if mId > 0 =>
        DefaultSyncerHelper.memberIdCache.getIfPresent(mId) match {
          case null => execute
          case userId =>
            DefaultSyncerHelper.customerCache.getIfPresent(userId) match {
              case null => execute
              case customerModel => Some(customerModel)
            }
        }
      case (userId, _) =>
        DefaultSyncerHelper.customerCache.getIfPresent(userId) match {
          case null => execute
          case customerModel => Some(customerModel)
        }
    }
  }
  def nonCachedModelByUUID(
      userId: UUID,
      memberIdOpt: Option[Int],
      getAllRecStatus: Boolean,
      group: String = ReadGroup,
      roles: Boolean = true,
      checkEmpty: Boolean = true)(implicit db: DBPlugin, customerCache: CustomerCache): Option[CustomerModel] = {
    def execute: Option[CustomerModel] = {
      val tupleF = Future {
        val additional = memberIdOpt
          .map(memberId =>
            fallbackFetchVectorOfVector({ fallbackGroup =>
              resultSets(fallbackGroup).q("EXEC legacy_properties_mid_v1 ?", memberId)
            }, group, checkEmpty = false))
          .getOrElse(
            fallbackFetchVectorOfVector({ fallbackGroup =>
              resultSets(fallbackGroup).q("EXEC dbo.legacy_properties_v1 ?", userId)
            }, group, checkEmpty = false)
          )
        val addresses: Vector[MemberAddress] = MemberAddressI.convV(additional.head)
        val travellerType: Vector[TravellerBox] = additional(1).map(x => TravellerBox(x.int("traveler_type_id")))
        val preferred: Vector[MemberDestination] = MemberDestinationI.convV(additional(2))
        (addresses, travellerType, preferred)
      }(Implicits.slowExecutionContext).recover { case _ => (Vector.empty, Vector.empty, Vector.empty) }(
        Implicits.globalExecutor)

      val vector = memberIdOpt
        .map(memberId => rewMemberByMemberId(memberId, getAllRecStatus, group, roles, checkEmpty))
        .getOrElse(rewMemberByUUID(userId, getAllRecStatus, group, roles, checkEmpty))
      val (addresses, travellerType, preferred) = Try(tupleF.blockFor(10)).recover {
        case _: TimeoutException => (Vector.empty, Vector.empty, Vector.empty)
        case exception: Throwable =>
          val stringTags = Map.empty ++ Try(userId.toString).toOption.map("userId" -> _) ++ memberIdOpt.map(
            "memberId" -> _.toString)
          CapiLogMessage(
            "capi.get_legacy_properties",
            LogLevel.ERROR,
            exception = Some(exception),
            stringTags = stringTags)
          (Vector.empty, Vector.empty, Vector.empty)
      }.get
      val customerModel = CustomerModelHelperServer.fromSet(
        vector,
        addresses,
        travellerType,
        preferred,
        readCustomerExtraFields(_),
        getAllRecStatus)

      if (customerModel.isDefined && customerModel.map(_.memberId > 0).getOrElse(false)) {
        customerModel.map(DefaultSyncerHelper.populateCache)
      }
      customerModel
    }
    if (customerCache == CustomerCache.NonCache)
      return execute
    (userId, memberIdOpt) match {
      case (_, Some(memberId)) if memberId > 0 =>
        DefaultSyncerHelper.memberIdCache.getIfPresent(memberId) match {
          case null => execute
          case userId =>
            DefaultSyncerHelper.customerCache.getIfPresent(userId) match {
              case null => execute
              case customerModel => Some(customerModel)
            }
        }
      case (userId, _) =>
        DefaultSyncerHelper.customerCache.getIfPresent(userId) match {
          case null => execute
          case customerModel => Some(customerModel)
        }
    }
  }

  def rewMemberByUUID(
      uuid: UUID,
      getAllRecStatus: Boolean = false,
      group: String = ReadGroup,
      roles: Boolean = true,
      checkEmpty: Boolean = true)(implicit dB: DBPlugin): Vector[Vector[SQLRow]] = {
    fallbackFetchVectorOfVector(
      { fallbackGroup =>
        resultSets(
          fallbackGroup,
          timeout = Some(Constants.MdcQueryTimeout) // 1 seconds in prod
        ).q(
          "EXEC dbo.api_rew_member_by_uuid_v11 ?, ?, ?",
          uuid,
          getAllRecStatus,
          roles && !CapiConsul.massiveRolesUsers.contains(uuid))
      },
      group,
      checkEmpty = checkEmpty
    )
  }

  def rewMemberByMemberId(
      memberId: Int,
      getAllRecStatus: Boolean = false,
      group: String = ReadGroup,
      roles: Boolean = true,
      checkEmpty: Boolean = true)(implicit dB: DBPlugin): Vector[Vector[SQLRow]] = {
    fallbackFetchVectorOfVector(
      { fallbackGroup =>
        resultSets(
          fallbackGroup,
          timeout = Some(Constants.MdcQueryTimeout) // 1 seconds in prod
        ).q("EXEC dbo.api_rew_member_by_uuid_and_mid_v5 ?, ?, ?", memberId, getAllRecStatus, roles)
      },
      group,
      checkEmpty = checkEmpty
    )
  }

  // Host Info (NHA but a bit different)
  def apiGetHostInfo(userId: UUID)(implicit dB: DBPlugin) = {
    Try {
      resultSet(group = CapiConfig.PrimaryMdcGroup, timeout = Some(FiniteDuration(1, TimeUnit.SECONDS)))
        .q("EXEC dbo.api_host_userinfo_fetch_v5 ?", userId)
    }.getOrElse {
      resultSet(group = CapiConfig.SecondaryMdcGroup, timeout = Some(FiniteDuration(1, TimeUnit.SECONDS)))
        .q("EXEC dbo.api_host_userinfo_fetch_v5 ?", userId)
    }
  }

  // TODO: migrate to MDC!
  def apiMergedMemberSelect(memberId: Int)(implicit db: DBPlugin) = {
    fallbackFetch(resultSet(_: String).q("EXEC dbo.api_merged_member_select_v2 ?", memberId))
  }

  def checkIfUserRestricted(memberId: Int)(implicit dB: DBPlugin): Option[Boolean] =
    result(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.gdpr_check_restriction_MDC_v2 ?", memberId)
      .map(_.boolean("is_restriction"))

  def getAllRestrictedUsers(implicit db: DBPlugin): Vector[(UUID, Option[Int], String)] =
    resultSet(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.gdpr_get_restricted_users_MDC_v2")
      .map(x => (x.uuid("userid"), x.intOption("member_id"), x.str("emailaddress")))

  // BO Rewards API
  def rewMembersSearch(
      memberCode: Option[String],
      bookingId: Option[Int],
      firstName: Option[String],
      lastName: Option[String],
      emailAddress: Option[String],
      whiteLabelId: Option[Int])(implicit dB: DBPlugin) =
    resultSet(WriteGroup).q(
      "EXEC dbo.rew_members_search_v5 ?, ?, ?, ?, ?, ?, ?, ?, ?, ?",
      memberCode,
      bookingId,
      firstName.getOrElse("").trim(),
      encryptO(firstName).getOrElse("").trim(),
      lastName.getOrElse("").trim(),
      encryptO(lastName).getOrElse("").trim(),
      emailAddress.getOrElse("").trim(),
      encryptO(emailAddress).getOrElse("").trim(),
      encryptO(emailAddress.map(_.toLowerCase)).getOrElse("").trim(),
      whiteLabelId.getOrElse(Constants.AgodaId)
    )

  def getMemberContactsByContactValueQuery(
      contactMethod: ContactMethod,
      contactValue: String,
      group: String,
      whiteLabelId: Int)(implicit db: DBPlugin) = {
    resultSet(group).q(
      "EXEC dbo.capi_get_user_contacts_by_contact_value_v4 ?, ?, ?, ?",
      contactMethod.id,
      contactValue,
      encrypt(contactValue),
      whiteLabelId)
  }

  def findUserIdByMemberCode(memberCode: String)(implicit db: DBPlugin): Option[UUID] = {
    fallbackFetchOption(result(_: String).q("EXEC dbo.api_customer_find_by_member_code_v1 ?", memberCode))
      .map { _.uuidOption("UserId").getOrFail(s"Member with code #$memberCode doesn't have the UserID") }
  }

  def findUserIdByBookingId(bookingId: Int)(implicit db: DBPlugin): Option[Int] = {
    // ebe table is not in CDB
    result(WriteGroup).q("EXEC dbo.api_customer_find_by_bookingid_v2 ?", bookingId).map(_.int("MemberID"))
  }

  /** @note whitelabel id must be included in the hash. */
  def findUserIdByAuthHash(hash: UUID, group: String, statusOverride: Boolean)(implicit db: DBPlugin): Option[UUID] = {
    Try { result(group, Some(Constants.MdcQueryTimeout)).q("EXEC dbo.find_customer_id_v2 ?, ?", hash, statusOverride) }
      .orElse(Try {
        result(fallbackGroupFor(group), Some(Constants.MdcQueryTimeout))
          .q("EXEC dbo.find_customer_id_v2 ?, ?", hash, statusOverride)
      })
      .get
      .map(_.uuid("user_id"))
  }
  def findUserIdsByUnverifiedPhone(group: String, phones: Seq[String], whitelabelId: Option[Int] = None)(
      implicit db: DBPlugin): Vector[UUID] = {
    val hashTable = new SQLServerDataTable
    hashTable.setTvpName("dbo.hash_list")
    hashTable.addColumnMetadata("hash", java.sql.Types.BINARY)
    phones.foreach(phone => hashTable.addRow(QueryUtil.hash(phone)))
    resultSet(group, Some(Constants.MdcQueryTimeout))
      .q("EXEC dbo.find_customer_ids_by_phone_v2 ?, ?", hashTable, whitelabelId.getOrElse(Constants.AgodaId))
      .map(_.uuid("id"))
  }
  // TODO: drop after MDC cache will be permanent!
  def getUserTypeByPhoneNumber(rewContactDataSearch: Seq[String], userMappingDataSearch: Seq[String], authTypeId: Int)(
      implicit dBPlugin: DBPlugin) = {
    result(ReadGroup).q(
      "EXEC dbo.rew_checkIfUserDataExist_v5 ?, ?, ? ",
      rewContactDataSearch.mkString(","),
      userMappingDataSearch.mkString(","),
      authTypeId)
  }

  def nextCounterValue(name: String, group: String)(implicit db: DBPlugin): Int = {
    result(group, Some(Constants.MdcQueryTimeout))
      .q("EXEC dbo.next_counter_value_v3 ?", name)
      .map(_.int("counter"))
      .getOrFail("Unable to fetch a current value of counter")
  }

  def getAllApiKeyPolicy(implicit db: DBPlugin): Vector[SQLRow] = {
    QueriesHelper.mdcFallbackResultSet("EXEC dbo.read_api_key_policy_by_all_v3")
  }

  // TODO: read from MDC table!
  def readNhaHotels(userId: UUID)(implicit db: DBPlugin, context: CapiRequestContextData): Vector[Int] = {
    if (CapiConsul.limitNHAHotelIdsByTeamName.contains(context.teamName))
      resultSet(ReadGroup)
        .q("EXEC dbo.read_customer_nha_hotels_by_limit_v1 ?, ?", userId, CapiConsul.noLimitNHAHotelIds)
        .map(_.int("hotel_id")) // NHA query
    else
      resultSet(ReadGroup).q("EXEC dbo.read_customer_nha_hotels_v1 ?", userId).map(_.int("hotel_id")) // NHA query
  }

  def readNhaHotelsV2(propertyId: Int)(implicit db: DBPlugin): Vector[(Int, UUID, Int)] = {
    val group =
      if (Constants.UseMocks) QAReadGroup
      else ReadGroup
    resultSet(group)
      .q("EXEC dbo.read_customer_nha_hotels_v6 ?", propertyId)
      .map(row => (row.long("hotel_id").toInt, row.uuid("userid"), row.long("booking_count").toInt)) // NHA query
  }

  def readNhaHotelsV2ByUserId(userId: UUID)(implicit db: DBPlugin): Vector[(Int, UUID, Int)] = {
    val group =
      if (Constants.UseMocks) QAReadGroup
      else ReadGroup
    resultSet(group)
      .q("EXEC dbo.read_customer_nha_hotels_by_userid_v1 ?", userId)
      .map(row => (row.long("hotel_id").toInt, row.uuid("userid"), row.long("booking_count").toInt))
  }

  def readCustomerDistinctRoleId(userId: UUID)(implicit db: DBPlugin): Vector[UUID] = {
    RoleQueries.readCustomerDistinctRoleId(userId)
  }

  def readCustomerDistinctRoleId(userId: UUID, objectSearchRequest: ObjectSearchRequest)(
      implicit db: DBPlugin): Vector[UUID] = {
    RoleQueries.readCustomerDistinctRoleId(userId, objectSearchRequest.objectType, objectSearchRequest.objectId)
  }

  def readCustomerRoles(userId: UUID)(implicit db: DBPlugin): Customer.Roles = {
    def parseCdbRoles(records: Vector[SQLRow]) = {
      records
        .groupBy(_.uuid("role_id"))
        .view
        .map {
          case (roleId, rows) =>
            // TODO: avoid auto-boxing!
            val references = rows.map { row =>
              row.str("ref_type").charAt(0) -> row.str("references").split(',').map(_.trim.toInt)
            }.toMap
            roleId -> references
        }
        .toMap
    }

    if (CapiConsul.massiveRolesUsers.contains(userId)) Map.empty
    else if (CapiConfig.isShanghaiDc)
      parseCdbRoles(
        fallbackFetch(
          { fallbackGroup =>
            resultSet(fallbackGroup)
              .q("EXEC dbo.read_customer_roles_v3 ?, ?", userId, CapiConsul.numberOfRolesToExclude)
          },
          group = RoleGroup,
          fallbackTo = getFallbackRolesGroup(RoleGroup),
          numberOfRetry = 1,
          fallbackWhenEmpty = false
        )
      )
    else {
      val group = if (CapiConfig.config.getBoolean("hasRoleGroup")) RoleGroup else ReadGroup
      parseCdbRoles(
        fallbackFetch(
          { fallbackGroup =>
            resultSet(fallbackGroup)
              .q("EXEC dbo.read_customer_roles_v3 ?, ?", userId, CapiConsul.numberOfRolesToExclude)
          },
          group = group,
          fallbackTo = getFallbackRolesGroup(group),
          numberOfRetry = 1,
          fallbackWhenEmpty = false
        )
      )
    }
  }
  /*
    def hasCustomerRole(userId: UUID, roleId: Option[UUID], refType: Option[Char], refId: Option[Int])(implicit db: DBPlugin): Boolean = {
      (roleId, refType, refId) match {
        case (Some(roleId), Some(refType), Some(refId)) =>
          Try { result(CapiConfig.PrimaryMdcGroup, Some(Constants.MdcQueryTimeout))
            .q("EXEC dbo.find_customer_role_v1 NULL, NULL, NULL, NULL, ?", hashAsUuid(s"$userId/$roleId/$refType/$refId"))
          }.orElse(Try { result(CapiConfig.SecondaryMdcGroup, Some(Constants.MdcQueryTimeout))
            .q("EXEC dbo.find_customer_role_v1 NULL, NULL, NULL, NULL, ?", hashAsUuid(s"$userId/$roleId/$refType/$refId"))
          }).get.nonEmpty
        case _ =>
          Try { result(CapiConfig.PrimaryMdcGroup, Some(Constants.MdcQueryTimeout))
            .q("EXEC dbo.find_customer_role_v1 ?, ?, ?, ?", userId, roleId, refType, refId)
          }.orElse(Try { result(CapiConfig.SecondaryMdcGroup, Some(Constants.MdcQueryTimeout))
            .q("EXEC dbo.find_customer_role_v1 ?, ?, ?, ?", userId, roleId, refType, refId)
          }).get.nonEmpty
      }
    }
   */

  def readCustomerOrigin(userId: UUID)(implicit db: DBPlugin): Option[String] = {
    Try {
      result(CapiConfig.PrimaryMdcGroup, Some(Constants.MdcQueryTimeout)).q(
        "EXEC dbo.read_origin_by_userid_v1 ?",
        userId)
    }.orElse(Try {
        result(CapiConfig.SecondaryMdcGroup, Some(Constants.MdcQueryTimeout))
          .q("EXEC dbo.read_origin_by_userid_v1 ?", userId)
      })
      .get
      .map(_.str("country_code"))
  }
  def readCustomerExtraFields(userId: UUID)(implicit db: DBPlugin): Option[CustomerExtraFields] = {
    result(CapiConfig.PrimaryMdcGroup, Some(Constants.MdcQueryTimeout))
      .q("EXEC dbo.tmp_read_customer_extra_fields_v1 ?", userId)
      .map { row =>
        CustomerExtraFields(
          taxId = row.strOption("tax_id"),
          isCCPAOptOut = row.booleanOption("ccpa_opt_out"),
          preferCurrency = row.strOption("preferred_currency"),
          ccpaLastUpdate = getDateTimeFromVarBinary(row, "ccpa_last_updated_when"),
          lastSuccessLogin = row.dateTimeOption("last_successful_login")
        )
      }
  }
  def readEncryptedCustomer(userId: UUID, group: String)(
      implicit db: DBPlugin): Option[(Int, String, RecordStatus, CustomerExtraFields)] = {
    Try { result(group, Some(Constants.MdcQueryTimeout)).q("EXEC dbo.read_customer_by_userid_v3 ?", userId) }
      .orElse(Try {
        result(fallbackGroupFor(group), Some(Constants.MdcQueryTimeout))
          .q("EXEC dbo.read_customer_by_userid_v3 ?", userId)
      })
      .get
      .map { row =>
        (
          row.int("member_id"),
          row.str("value"),
          RecordStatus(row.int("rec_status")),
          CustomerExtraFields(
            taxId = row.strOption("tax_id"),
            isCCPAOptOut = row.booleanOption("ccpa_opt_out"),
            preferCurrency = row.strOption("preferred_currency"),
            ccpaLastUpdate = getDateTimeFromVarBinary(row, "ccpa_last_updated_when"),
            lastSuccessLogin = row.dateTimeOption("last_successful_login")
          ))
      }
  }
  def readEncryptedCustomer(memberId: Int, group: String)(
      implicit db: DBPlugin): Option[(UUID, String, RecordStatus, CustomerExtraFields)] = {
    Try { result(group, Some(Constants.MdcQueryTimeout)).q("EXEC dbo.read_customer_by_memberid_v3 ?", memberId) }
      .orElse(Try {
        result(fallbackGroupFor(group), Some(Constants.MdcQueryTimeout))
          .q("EXEC dbo.read_customer_by_memberid_v3 ?", memberId)
      })
      .get
      .map { row =>
        (
          row.uuid("id"),
          row.str("value"),
          RecordStatus(row.int("rec_status")),
          CustomerExtraFields(
            taxId = row.strOption("tax_id"),
            isCCPAOptOut = row.booleanOption("ccpa_opt_out"),
            preferCurrency = row.strOption("preferred_currency"),
            ccpaLastUpdate = getDateTimeFromVarBinary(row, "ccpa_last_updated_when"),
            lastSuccessLogin = row.dateTimeOption("last_successful_login")
          ))
      }
  }
  def readUserLastSuccessLogin(userId: UUID)(implicit db: DBPlugin): Option[DateTime] = {
    Try {
      result(CapiConfig.PrimaryMdcGroup, Some(Constants.MdcQueryTimeout)).q(
        "EXEC dbo.read_customer_last_login_by_userid_v1 ?",
        userId)
    }.orElse(Try {
        result(CapiConfig.SecondaryMdcGroup, Some(Constants.MdcQueryTimeout))
          .q("EXEC dbo.read_customer_last_login_by_userid_v1 ?", userId)
      })
      .get
      .flatMap(_.dateTimeOption("last_successful_login"))
  }
  def getDateTimeFromVarBinary(row: SQLRow, keyName: String): Option[DateTime] = {
    row.map
      .get(keyName)
      .flatMap(v => Option(v.asInstanceOf[Array[Byte]]))
      .filter(_.size > 0)
      .map { value =>
        DateTime.parse(new String(value, StandardCharsets.UTF_8)).withZone(DateTimeZone.getDefault)
      }
  }
  def readEncryptedCustomers(memberIds: Array[Int])(implicit db: DBPlugin): Vector[String] = {
    resultSet(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.read_customers_by_memberid_v1 ?", toTableInt(memberIds))
      .map { row =>
        row.str("value")
      }
  }
  def saveEncryptedCustomer(
      userId: UUID,
      memberId: Int,
      authMappings: Vector[UUID],
      encryptedEntity: String,
      fingerprint: Array[Byte],
      unverifiedPhoneHash: Option[Array[Byte]],
      emailHash: Array[Byte],
      status: Int,
      origin: Option[String],
      whitelabelId: Option[Int],
      lastUpdated: DateTime,
      recCreatedWhen: DateTime = DateTime.now())(group: String)(implicit db: DBPlugin): Unit = {
    val countryCode = origin.getOrElse("XX")
    val phoneHash = unverifiedPhoneHash.getOrElse(Array.emptyByteArray)

    execute(group, timeout = Some(Constants.MdcQueryTimeout)).q(
      "EXEC dbo.save_customer_v19 ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?",
      CapiConsul.hardQueryTimeout.toMillis,
      userId,
      memberId,
      toUUIDTable(authMappings),
      encryptedEntity,
      fingerprint,
      phoneHash,
      emailHash,
      countryCode,
      whitelabelId.getOrElse(Constants.AgodaId),
      Constants.AppId,
      status,
      lastUpdated,
      recCreatedWhen
    )
  }
  def saveEmailGenius(memberId: Int, emailHash: Array[Byte], lastUpdated: DateTime)(implicit db: DBPlugin): Unit = {
    def writeFunction = {
      execute(CapiConfig.PrimaryMdcGroup, timeout = Some(Constants.MdcQueryTimeout)).q(
        "EXEC dbo.save_email_genius_v1 ?, ?, ?",
        memberId,
        emailHash,
        lastUpdated
      )
    }
    Future {
      Try(writeFunction).recover {
        case _ =>
          writeFunction
      }
    }(Implicits.slowExecutionContext).onComplete {
      case Failure(exception) =>
        CapiLogMessage("capi.write.email_genius", LogLevel.ERROR, exception = Some(exception))
      case Success(_) => // do nothing
    }(Implicits.slowExecutionContext)
  }

  def wipeEncryptedCustomer(userId: UUID, force: Boolean = false)(implicit db: DBPlugin): Unit = {
    execute(CapiConfig.PrimaryMdcGroup, timeout = Some(Constants.MdcQueryTimeout))
      .q("EXEC dbo.wipe_customer_v2 ?, ?", userId, force)
  }

  def saveUserDevice(userId: UUID, deviceId: UUID, whenLastLogin: Option[DateTime])(
      implicit db: DBPlugin): Option[(UUID, UUID)] = {
    result(CapiConfig.PrimaryMdcGroup, Some(Constants.MdcQueryTimeout))
      .q("EXEC dbo.user_devices_upsert_v4 ?, ?, ?", userId, deviceId, whenLastLogin)
      .map { row =>
        (row.uuid("user_id"), row.uuid("device_id"))
      }
  }

  def findDevicesByUserId(userId: UUID)(implicit db: DBPlugin): Vector[Device] = {
    resultSet(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.user_devices_fetch_v1 ?", userId)
      .map(row =>
        Device(
          row.uuid("user_id"),
          row.uuid("device_id"),
          row.short("rec_status"),
          row.uuid("rec_created_by"),
          row.dateTime("rec_created_when"),
          row.uuidOption("rec_modified_by"),
          row.dateTimeOption("rec_modified_when")
      ))
  }

  def deleteUserDevice(userId: UUID, deviceIds: Vector[UUID])(implicit db: DBPlugin): Vector[(UUID, Int)] = {
    if (deviceIds == null) {
      Vector.empty
    } else {
      resultSet(CapiConfig.PrimaryMdcGroup)
        .q("EXEC dbo.user_devices_delete_v2 ?, ?", userId, toUUIDTable(deviceIds))
        .filter(row => row.uuid("user_id") == userId)
        .map(row => (row.uuid("device_id"), row.int("rec_status")))
    }
  }

  // TODO: Delete it!
  def tmpReadEmails(memberIds: Array[Int])(implicit db: DBPlugin): Vector[(Int, String)] = {
    resultSet(DBGroups.ReadGroup)
      .q("EXEC dbo.tmp_read_customer_emails_v1 ?", toTable(memberIds))
      .map(row => row.int("member_id") -> row.str("email"))
  }

  def updateAdditionalInfo(
      userId: UUID,
      customerExtraFields: Option[CustomerExtraFields],
      now: DateTime,
      groups: Seq[String] = Seq(CapiConfig.PrimaryMdcGroup))(implicit db: DBPlugin): Unit = {
    val placeholders = "?, ? " + (", ?" * AdditionalProperty.ADD_PROP_SEQS.size)
    val params = Seq(userId, now)
    if (customerExtraFields.isDefined) {
      Seq(CapiConfig.PrimaryMdcGroup).map(group => {
        val paramsAdd = params ++ encryptPIIFields(group, customerExtraFields.get)
          .toFields(group.equalsIgnoreCase(CapiConfig.PrimaryMdcGroup))
        execute(group).q(s"EXEC dbo.upsert_additional_info_v3 $placeholders", paramsAdd: _*)
      })
    }
  }

  def updateCreatedBy(userId: UUID, whitelabelId: Short, createdBy: UUID, createdWhen: Instant)(
      implicit db: DBPlugin): Unit = {
    execute(CapiConfig.PrimaryMdcGroup).q(
      "EXEC dbo.update_customer_createdby_v1 ?,?,?,?",
      userId,
      whitelabelId,
      createdBy,
      createdWhen)
  }

  private def encryptPIIFields(group: String, fields: CustomerExtraFields): CustomerExtraFields = {
    fields.copy(taxId = QueryUtil.encryptO(fields.taxId))
  }

  def getBlackListItems(since: Instant)(implicit db: DBPlugin): Vector[TokenBlackListItem] = {
    val rows = resultSet(WriteGroup).q("EXEC dbo.blacklist_read ?", since)
    rows.map(s => {
      TokenBlackListItem(
        ConverterShim.toInstant(s.dateTime("revoked")),
        s.uuidOption("user_id"),
        s.strOption("token_id"),
        None)
    })
  }

  @legacyRead
  def findExternalMemberIdByMemberId(memberId: Int, whiteLabelId: Option[Int])(implicit db: DBPlugin): Vector[String] =
    resultSet(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.find_external_member_id_by_member_id_v1 ?, ?", memberId, whiteLabelId)
      .flatMap(row => row.strOption("raw_external_member_id"))

  def getTokenRoleMappings()(implicit db: DBPlugin): Vector[RoleRow] = {
    resultSet(ReadGroup)
      .q("EXEC dbo.auth_token_role_mappings_v1")
      .map(s => {
        RoleRow(
          s.uuid("RoleId"),
          s.int("SeqId"),
          s.str("RoleName"),
          s.uuidOption("PermissionId"),
          s.strOption("PermissionName"),
          s.uuidOption("ComponentId"),
          s.strOption("ComponentName"),
          s.dateTime("rec_created_when")
        )
      })
  }

  def getTokenAssignedRoles()(implicit db: DBPlugin): Map[UUID, List[UUID]] = {
    resultSet(ReadGroup)
      .q("EXEC dbo.auth_token_assigned_roles_v1")
      .map(s => {
        (s.uuid("RoleId"), s.uuid("AssignedRoleId"))
      })
      .toList
      .groupBy(_._1)
      .mapValues(_.map(_._2))
  }

  // TODO: replace by DAL joins
  def getMember[T](
      id: T,
      parsers: Vector[Vector[Iterator[AnyRef]] => AnyRef],
      mdcGroup: String = CapiConfig.PrimaryMdcGroup)(implicit db: DBPlugin): Vector[AnyRef] = {
    val query = id match {
      case _: Int => s"EXEC dbo.fetch_dal_customer_by_member_id_with_fav_${CapiConsul.useFetchVersion} ?, ?, ?, ?"
      case _: UUID => s"EXEC dbo.fetch_dal_customer_by_user_id_with_fav_${CapiConsul.useFetchVersion} ?, ?, ?, ?"
    }
    def executeResult =
      resultSets(mdcGroup, Some(CapiConsul.readSoftTimeout))
        .q(query, UUID.randomUUID(), id, Constants.MaxFavoriteHotels, CapiConsul.readSoftTimeout.toMillis)
        .zipWithIndex
        .map { case (rows, index) => parsers(index).apply(rows.map(_.map.values.iterator)) }
    Utils
      .tryRetryStandard(2)(executeResult)
      .recover {
        case _ => Vector(None, Vector.empty, Vector.empty, Vector.empty, Vector.empty, Vector.empty)
      }
      .get
  }

  def getCustomerExists[T](id: T)(implicit db: DBPlugin): Boolean = {
    val query = id match {
      case _: Int => "EXEC dbo.fetch_customer_exists_by_member_id_v1 ?"
      case _: UUID => "EXEC dbo.fetch_customer_exists_by_user_id_v1 ?"
    }
    result(DBGroups.ReadGroup).q(query, id).exists(_.boolean("customer_exists"))
  }

  def getLatestUserIdByName(firstname: data.ByteString, lastname: data.ByteString, whitelabelId: Int)(
      implicit db: DBPlugin): Option[UUID] = {
    result(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.find_recent_user_id_created_by_name_v2 ?, ?, ?", firstname, lastname, whitelabelId)
      .map(_.uuid("user_id"))
  }

  def getRecentRmFraudUserIdByName(firstname: data.ByteString, lastname: data.ByteString, whitelabelId: Int)(
      implicit db: DBPlugin): Option[UUID] = {
    result(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.find_recent_rm_fraud_user_id_created_by_name_v1 ?, ?, ?", firstname, lastname, whitelabelId)
      .map(_.uuid("user_id"))
  }

  def updateCaseSensitiveUsername(
      userId: UUID,
      usernameToBeUpdated: String,
      authTypeId: Int = AuthenticationType.Username.id)(implicit db: DBPlugin) = {
    result(DBGroups.WriteGroup).q(
      "EXEC dbo.update_rurubu_case_sensitive_usernames_v2 ?, ?, ?",
      userId,
      QueryUtil.encrypt(usernameToBeUpdated),
      authTypeId)
  }

  def fetchClaimKeysByWhitelabelIds(whitelabelIds: List[Int], until: Instant = Instant.now())(
      implicit db: DBPlugin): Vector[PartnerClaimKey] = {
    resultSet(CapiConfig.PrimaryMdcGroup)
      .q("EXEC dbo.fetch_partnerclaim_key_by_whitelabel_ids_v1 ?, ?", RSParsing.toListInt(whitelabelIds), until)
      .map { row =>
        PartnerClaimKey(
          claimKey = row.uuid("claim_key"),
          memberId = row.int("member_id"),
          whitelabelId = row.short("whitelabel_id"))
      }
  }
}

// TODO: implement caching!
object RoleQueries {
  private val roleByName = CacheBuilder
    .newBuilder()
    .expireAfterWrite(3, TimeUnit.HOURS)
    .maximumSize(CapiConfig.config.getInt("repository.cache.defaultCacheSize"))
    .build[String, Option[UUID]]
  private val roleByPermissionId = CacheBuilder
    .newBuilder()
    .expireAfterWrite(3, TimeUnit.HOURS)
    .maximumSize(CapiConfig.config.getInt("repository.cache.defaultCacheSize"))
    .build[UUID, Vector[UUID]]
  private val roleByUserId = CacheBuilder
    .newBuilder()
    .expireAfterWrite(1, TimeUnit.HOURS)
    .maximumSize(CapiConfig.config.getInt("repository.cache.defaultCacheSize"))
    .build[UUID, Vector[UUID]]
  private val roleByUserIdObjectIdType = CacheBuilder
    .newBuilder()
    .expireAfterWrite(1, TimeUnit.HOURS)
    .maximumSize(CapiConfig.config.getInt("repository.cache.defaultCacheSize"))
    .build[(UUID, String, Option[Int]), Vector[UUID]]

  def fineRoleByName(name: String)(implicit db: DBPlugin): Option[UUID] = {
    roleByName.get(
      name,
      () => result(DBGroups.ReadGroup).q("EXEC dbo.find_role_by_name_v1 ?", name).map(_.uuid("RoleId")))
  }

  def readRolesByPermissionsId(id: UUID)(implicit db: DBPlugin): Vector[UUID] = {
    roleByPermissionId.get(
      id,
      () => resultSet(DBGroups.ReadGroup).q("EXEC dbo.read_roles_by_permission_v1 ?", id).map(_.uuid("RoleId")))
  }

  def readCustomerDistinctRoleId(userId: UUID)(implicit db: DBPlugin): Vector[UUID] = {
    roleByUserId.get(
      userId,
      () =>
        resultSet(RoleGroup, Some(Constants.ReadElapsedTimeout))
          .q("EXEC dbo.read_customer_distinct_role_id_v1 ?", userId)
          .map(_.uuid("role_id")))
  }

  def readCustomerDistinctRoleId(userId: UUID, objectType: String, objectId: Option[Int])(
      implicit db: DBPlugin): Vector[UUID] = {
    roleByUserIdObjectIdType.get(
      (userId, objectType, objectId),
      () =>
        resultSet(RoleGroup, Some(Constants.ReadElapsedTimeout))
          .q(
            "EXEC dbo.read_customer_distinct_role_id_by_userid_objecttype_objectid_v1 ?, ?, ?",
            userId,
            objectType,
            objectId)
          .map(_.uuid("role_id"))
    )
  }
}

//noinspection TypeAnnotation
object PersonalisationQueries {
  // Personalization - Preferences (doesn't appear to write anymore, most likely can be removed entirely)
  def apiGetUserPreference(memberId: Int, preferenceId: Int)(implicit dB: DBPlugin) =
    resultSet(group = CapiConfig.PrimaryMdcGroup).q("EXEC dbo.api_get_user_preference_v1 ?, ?", memberId, preferenceId)
  def apiGetUserPreferences(memberId: Int)(implicit dB: DBPlugin) =
    resultSet(group = CapiConfig.PrimaryMdcGroup).q("EXEC dbo.api_get_user_preferences_v1 ?", memberId)
}

//noinspection TypeAnnotation
object MutatingQueries {
  import QueryUtil._
  //See also RewardsQueries
  // token only in LegacyTokenAndValidationQueries
  // wechat only in LegacyWechatQueries
  // updateNhaMemberLevel / updateHosts for NHA trusted host only
  // InsertHelper (api_customer_insert_simplified_v2)
  // PersonalisationQueries for mostly unrelated preferences
  // securitySetPasswordHistory

  def saveBlacklistItem(r: TokenBlackListItem)(implicit db: DBPlugin): Unit = {
    result(WriteGroup).q("EXEC dbo.blacklist_insert_v2 ?, ?, ?, ?", r.revoked, r.tokenId, r.userId, r.whitelabelId)
  }

  def cleanupBlacklistItem(until: Instant)(implicit db: DBPlugin): Int = {
    result(WriteGroup, Some(FiniteDuration(CapiConsul.cleanupBlacklistTimeout, TimeUnit.SECONDS)))
      .q("EXEC dbo.blacklist_cleanup_v2 @until=?, @deleted_rows=?", until, 0)
      .map(row => row.int("deleted_rows"))
      .getOrElse(0)
  }

  //Safe
  def apiSaveHostInfo(
      userId: UUID,
      displayName: Option[String],
      countryId: Option[Int],
      stateId: Option[Int],
      cityId: Option[Int],
      userDescription: Option[String],
      photoUrl: Option[String],
      contactPersonId: Option[Int],
      spokenLanguages: Option[String],
      gender: Option[String],
      tprmHostType: Option[Int],
      tprmQuestionnaireStatus: Option[Int],
      tprmQuestionnaireChangedDate: Option[DateTime],
      userProfileType: Option[Int])(implicit dB: DBPlugin) =
    resultSet(group = CapiConfig.PrimaryMdcGroup).q(
      "EXEC dbo.api_host_userinfo_update_v6 ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?",
      userId,
      encryptO(displayName),
      countryId,
      stateId,
      cityId,
      encryptO(userDescription),
      photoUrl,
      contactPersonId,
      spokenLanguages,
      gender,
      tprmHostType,
      tprmQuestionnaireStatus,
      tprmQuestionnaireChangedDate,
      userProfileType
    )

  //Currently safe, not part of model
  def apiUserMappingHistoryInsert(memberId: Int, activityId: Int, createdBy: UUID, authTypeId: Int)(
      implicit db: DBPlugin) = {
    result(WriteGroup).q(
      "EXEC dbo.api_user_mapping_history_insert ?, ?, ?, ?",
      memberId,
      activityId,
      createdBy,
      authTypeId)
  }

  //Safe, not used
  /*def saveCustomerProperties(userId: UUID, properties: String, modifiedBy: UUID)(implicit db: DBPlugin): Unit = {
    execute(WriteGroup).q("EXEC dbo.api_customer_set_properties_v1 ?, ?, ?", userId, properties, modifiedBy)
  }*/

  // Only for ModelSyncer and pushMemberToMDB methods
  def apiMemberUpdate(
      c: Customer,
      group: String = WriteGroup,
      forceMaskingPII: Boolean = false
  )(implicit dB: DBPlugin): Unit = {

    try {
      val birthday = c.fields.birthday.map(
        c =>
          if (c.isBefore(LocalDate.of(1901, 1, 1))
            || c.isAfter(LocalDate.of(2078, 12, 31))) LocalDate.of(1901, 1, 1)
          else c)
      val walletInfo = c.legacyModels.flatMap(_.legacyModel).flatMap(_.walletInfo)
      execute(group).q(
        "EXEC dbo.api_member_update_v13 ?,?,?, ?,?,? ,?,?,? ,?,?,? ,?,?, ?,?, ?,?",
        c.memberId,
        masking(QueryUtil.removeIllegalChars(c.firstName), c.whiteLabelId, forceMaskingPII),
        masking(QueryUtil.removeIllegalChars(c.lastName), c.whiteLabelId, forceMaskingPII),
        masking(QueryUtil.removeIllegalChars(c.legacyDisplayName), c.whiteLabelId, forceMaskingPII),
        c.fields.ccof,
        birthday,
        c.fields.prius,
        c.fields.nationalityId,
        c.fields.languageId,
        c.jsonProperties,
        c.fields.newsletter,
        c.recordStatus,
        c.log.modified.getOrElse(Instant.now()),
        c.log.modifiedBy.getOrElse(Constants.AppId),
        c.legacyFields.title,
        c.userId,
        walletInfo
          .flatMap(_.birthdateHash)
          .filter(_.nonEmpty)
          .map(ByteStringHelper.hexStringToArrayByte)
          .getOrElse(Array.emptyByteArray),
        walletInfo
          .flatMap(_.countryHash)
          .filter(_.nonEmpty)
          .map(ByteStringHelper.hexStringToArrayByte)
          .getOrElse(Array.emptyByteArray),
      )
    } catch {
      case e: Exception =>
        baseLogger.warn(s"Unable to save ${c.memberId}  ${c.legacyFields} due to $e")
        throw e
    }

  }

  def syncUserMappings(event: SyncUserMappings, forceMaskingPII: Boolean = false)(implicit db: DBPlugin): Unit = {
    val usersToUnassign = new SQLServerDataTable
    usersToUnassign.setTvpName("dbo.uuid_table_type")
    usersToUnassign.addColumnMetadata("uuid", java.sql.Types.CHAR)
    event.usersToUnassign.distinct.foreach(usersToUnassign.addRow(_))

    if (event.mappingToAdd.isDefined) {
      val mapping = event.mappingToAdd.get
      val contactId = event.contactId.orElse(event.authType match {
        case AuthenticationType.Basic | AuthenticationType.PhoneNumber =>
          Some(
            Try(Counter.ContactCounter.nextId(CapiConfig.PrimaryMdcGroup))
              .getOrElse(Counter.ContactCounter.nextId(CapiConfig.SecondaryMdcGroup)))
        case _ => None
      })

      Queries.modelByUUID(mapping.userId).map { customer =>
        val username = if (event.authType == AuthenticationType.Username) {
          val loginFeature =
            WhiteLabelHelper.getLoginFeature(customer.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId))
          if (loginFeature.isUsernameCaseSensitive.getOrElse(false))
            mapping.username
          else
            mapping.username.toLowerCase
        } else mapping.username.toLowerCase

        execute(WriteGroup).q(
          "EXEC dbo.sync_user_mappings_v5 ?, ?, ?, ?, ?, ?, ?, ?",
          usersToUnassign,
          event.authType.id,
          mapping.userId,
          masking(mapping.uriString, Some(customer.whitelabelId), forceMaskingPII),
          masking(username, Some(customer.whitelabelId), forceMaskingPII),
          masking(mapping.username, Some(customer.whitelabelId), forceMaskingPII),
          mapping.createdBy,
          contactId
        )
      }
    } else execute(WriteGroup).q("EXEC dbo.delete_user_mappings_v3 ?, ?", usersToUnassign, event.authType.id)
  }

  def updateTaxId(event: SyncUserAdditionalInfo)(implicit db: DBPlugin): Unit = {
    val taxId = event.customerExtraFields.taxId.getOrElse("")
    if (taxId.trim.nonEmpty) {
      execute(CapiConfig.PrimaryMdcGroup).q(
        "EXEC dbo.upsert_tax_id_v3 ?, ?, ?",
        event.userId,
        QueryUtilShim.encrypt(taxId),
        event.timestamp)
    }
  }

  def updateLastSuccessfulLogin(event: Seq[SyncUserLogin])(implicit db: DBPlugin): Unit = {
    val userLogin = new SQLServerDataTable
    userLogin.setTvpName("dbo.user_last_login_time_type")
    userLogin.addColumnMetadata("uuid", java.sql.Types.CHAR)
    userLogin.addColumnMetadata("ts", java.sql.Types.TIMESTAMP)
    event.foreach(data => userLogin.addRow(data.userId.toString, new java.sql.Timestamp(data.time.getMillis)))

    execute(WriteGroup).q("EXEC dbo.upsert_success_login_v2 ?", userLogin)
    execute(CapiConfig.PrimaryMdcGroup).q("EXEC dbo.upsert_success_login_v3 ?", userLogin)
  }

  def updateOriginPostLogin(events: Seq[SyncOrigin])(implicit db: DBPlugin): Unit = {
    val currentTime = new java.sql.Timestamp(System.currentTimeMillis())
    val syncOrigin = new SQLServerDataTable
    syncOrigin.setTvpName("dbo.user_origin_update_type")
    syncOrigin.addColumnMetadata("user_id", java.sql.Types.CHAR)
    syncOrigin.addColumnMetadata("member_id", java.sql.Types.INTEGER)
    syncOrigin.addColumnMetadata("origin_to_update", java.sql.Types.VARCHAR)
    events.foreach(event => syncOrigin.addRow(event.userId.toString, Int.box(event.memberId), event.originToUpdate))
    //Sync origin in rew_member (MDB table)
    execute(WriteGroup).q("EXEC dbo.update_missing_origin_rew_members_v1 ?, ?", syncOrigin, currentTime)

    if (CapiConsul.enableOriginUpdateOnLoginContacts) {
      //Sync origin in rew_contacts (MDB table)
      updateOriginRewContact(events, currentTime)(db)
    }
    //Sync origin in  DAL and MDC tables
    execute(CapiConfig.PrimaryMdcGroup).q("EXEC dbo.update_missing_origin_v1 ?, ?", syncOrigin, Constants.AppId)
  }

  private def updateOriginRewContact(events: Seq[SyncOrigin], currentTime: java.sql.Timestamp)(
      implicit db: DBPlugin): Unit = {
    val syncOriginContact = new SQLServerDataTable
    syncOriginContact.setTvpName("dbo.user_origin_update_contacts_type")
    syncOriginContact.addColumnMetadata("contact_id", java.sql.Types.INTEGER)
    syncOriginContact.addColumnMetadata("origin_to_update", java.sql.Types.CHAR)
    events.foreach(event => {
      event.contactIds.foreach { id =>
        syncOriginContact.addRow(Int.box(id), event.originToUpdate)
      }
    })
    execute(WriteGroup).q("EXEC dbo.update_missing_origin_rew_contact_v1 ?, ?", syncOriginContact, currentTime)
  }

  def syncUserPasswords(event: SyncUserPasswords)(implicit db: DBPlugin): Unit = {
    val passwords = new SQLServerDataTable
    passwords.setTvpName("dbo.user_mapping_table")
    passwords.addColumnMetadata("auth_type", java.sql.Types.INTEGER)
    passwords.addColumnMetadata("uri", java.sql.Types.VARCHAR)
    event.passwords.foreach { uri =>
      passwords.addRow(Int.box(uri.authTypeInt), encrypt(uri.toString))
    }
    val passwordHash = event.passwords.headOption.flatMap(_.uriSecurityComponent).getOrFail("Empty password")
    execute(WriteGroup).q(
      "EXEC dbo.sync_user_passwords_v2 ?, ?, ?, ?",
      event.userId,
      passwords,
      passwordHash,
      event.modifiedBy)
  }

  def syncUserContact(
      event: SyncUserContact,
      isKafka: Boolean = false,
      group: String = WriteStandbyGroup,
      writeGroup: String = WriteGroup,
      forceMaskingPII: Boolean = false)(implicit db: DBPlugin): Unit = {
    if (event.contact.isDefined) {
      val contact = event.contact.get

      if (isKafka) {
        Queries
          .nonCachedModelByUUID(userId = null, Some(contact.memberId), getAllRecStatus = true, group = group)
          .getOrElse {
            throw new UnknownEntityException(s"member ${contact.memberId.toString} doesn't exist in mdb")
          }
      }

      execute(writeGroup).q(
        "EXEC dbo.sync_user_contact_v2 ?, ?, ?, ?, ?, ?",
        contact.memberId,
        event.contactId,
        contact.contactMethod.id,
        masking(contact.contactMethodValue, contact.whitelabelId, forceMaskingPII),
        masking(contact.contactMethodRemark, contact.whitelabelId, forceMaskingPII),
        contact.recordCreatedBy.getOrElse(Constants.AppId)
      )
    } else {
      if (event.isHardDelete.contains(true))
        execute(writeGroup).q("EXEC dbo.hard_delete_user_contact_v1 ?", event.contactId)
      else
        execute(writeGroup).q("EXEC dbo.delete_user_contact_v1 ?, ?", event.contactId, Constants.AppId)
    }
  }

  def syncMdbUserRoles(event: SyncUserRoles, whitelabelId: Int)(implicit db: DBPlugin): Unit = {
    // TODO: replace these trash stored procedure by single transactional stored procedure!
    val addUserRoles = event.toAdd.map(r =>
      AuthUserInRoleTableType(r.userId.toString, r.roleId.toString, r.objectType.toString, r.objectId))
    val removeUserRoles = event.toRemove.map(r =>
      AuthUserInRoleTableType(r.userId.toString, r.roleId.toString, r.objectType.toString, r.objectId))
    val userRoles = addUserRoles ++ removeUserRoles

    val localTable = AuthUserInRoleTableType.create(userRoles)

    val fraudulentRows = resultSet(DBGroups.ReadGroup)
      .q("EXEC dbo.get_auth_user_in_role_by_userid_roleid_objectid_object_bulk ?", localTable)

    if (fraudulentRows.nonEmpty) {
      val blockedUserList = toUserUuidList(fraudulentRows).mkString(" || ")
      throw new ArgumentInvalidException(
        s"Users in request have Fraudulent users, Actions are blocked. Contact <EMAIL> Team. Blocked Users :: $blockedUserList")
    } else {
      event.toRemove.foreach { role =>
        resultSets(WriteGroup).q(
          s"EXEC dbo.auth_user_in_role_removerole_v3 ?, ?, ?, ?, ?, ?",
          role.roleId,
          role.objectId,
          role.objectType,
          role.userId,
          event.modifiedBy,
          whitelabelId
        )
      }
      event.toAdd.foreach { role =>
        result(WriteGroup).q(
          "EXEC dbo.auth_user_in_role_adduser_byroleid_check_blocked_v3 ?, ?, ?, ?, ?, ?",
          role.roleId,
          role.objectId,
          role.objectType,
          role.userId,
          event.modifiedBy,
          whitelabelId
        )
      }
    }
  }

  def syncMdcUserRoles(event: SyncUserRoles)(implicit db: DBPlugin): Unit = {
    def hashFor(role: UserRole): UUID = roleHash(role.userId, role.roleId, role.objectType, role.objectId)
    def toRoleTable(roles: Vector[UserRole]): SQLServerDataTable = {
      val table = new SQLServerDataTable
      table.setTvpName("dbo.customer_role_list")
      table.addColumnMetadata("hash", java.sql.Types.CHAR)
      table.addColumnMetadata("user_id", java.sql.Types.CHAR)
      table.addColumnMetadata("role_id", java.sql.Types.CHAR)
      table.addColumnMetadata("ref_type", java.sql.Types.VARCHAR)
      table.addColumnMetadata("ref_id", java.sql.Types.INTEGER)
      roles.foreach { role =>
        table.addRow(hashFor(role), role.userId, role.roleId, role.objectType, Int.box(role.objectId))
      }
      table
    }

    val (hotelsToRemove, otherToRemove) = event.toRemove.span(_.objectType == "H")
    execute(CapiConfig.PrimaryMdcGroup).q(
      "EXEC dbo.sync_user_roles_v1 ?, ?, ?, ?",
      toRoleTable(event.toAdd),
      RSParsing.toUUIDTable(hotelsToRemove.map(hashFor)),
      RSParsing.toUUIDTable(otherToRemove.map(hashFor)),
      event.modifiedBy
    )
  }

  // TODO: delete after Cusco will be migrated to the new storage!
  def saveCuscoContact(contact: MemberContact, contactId: Int, whitelabelId: Int, origin: Option[String])(
      implicit db: DBPlugin): Option[SQLRow] = {
    result(WriteGroup).q(
      "EXEC dbo.save_deprecated_cusco_contact_v3 ?, ?, ?, ?, ?, ?, ?, ?, ?",
      contactId,
      contact.memberId,
      contact.contactMethod.id,
      encrypt(contact.contactMethodValue),
      encrypt(contact.contactMethodRemark),
      contact.recordStatus.getOrElse(1),
      contact.recordModifiedBy.orElse(contact.recordCreatedBy).getOrElse(Constants.AppId),
      origin,
      whitelabelId
    )
  }

  def findBookingNoByMemberId(memberId: Int)(implicit db: DBPlugin) = {
    result(WriteGroup).q("EXEC dbo.find_booking_no_by_member_id_v1 ?", memberId)
  }

  //Safe via reload
  def rew2MergeMemberApprove(activeId: Int, deactiveId: Int, reason: String, isStatementConfirm: Int, approver: UUID)(
      implicit db: DBPlugin) = {
    result(WriteGroup, timeout = Some(CapiConfig.mergeTimeout)).q(
      "EXEC dbo.rew2_merge_member_approve_v6 ?, ?, ?, ?, ?",
      activeId,
      deactiveId,
      reason,
      isStatementConfirm,
      approver)
  }

  //Not currently safe
  def securityUsermappingUpdateIsverified(userId: UUID, authTypeId: Int, modified: UUID, isVerified: Boolean)(
      implicit dB: DBPlugin) = {
    result(WriteGroup).q(
      "EXEC dbo.security_usermapping_update_isverified_v2 ?, ?, ?, ?",
      userId,
      authTypeId,
      modified,
      isVerified)
  }

  //Safe via reload
  def authUsersMappingRemove(userId: UUID, authTypeId: Int, removeBy: UUID)(implicit dB: DBPlugin): Vector[SQLRow] = {
    resultSet(WriteGroup).q("EXEC dbo.auth_users_mapping_remove_v2 ?, ?, ?", userId, authTypeId, removeBy)
  }
}

//noinspection TypeAnnotation
object FavoriteQueries {
  import RSParsing.{toTable => toSqlTable}
  def getHotelCities(hotelIds: Seq[Int], recStatus: Int = 1)(implicit db: DBPlugin): Map[Int, Int] = {
    resultSet(DBGroups.ReadGroup)
      .q("EXEC dbo.get_hotel_cities_v2 ?, ?", toSqlTable(hotelIds.distinct), recStatus)
      .map { row =>
        val hotelId = row.int("hotel_id")
        hotelId -> row.intOption("city_id").getOrElse(throw new IllegalStateException(s"Hotel $hotelId without city"))
      }
      .toMap
  }

  def upsertFavorites(memberId: Int, toAdd: Map[Int, Int], toReplace: Map[Int, Int], toRemove: Vector[Int])(
      implicit db: DBPlugin): Unit = {
    def toTable(records: Map[Int, Int]): SQLServerDataTable = {
      val table = new SQLServerDataTable
      table.setTvpName("dbo.int_table")
      table.addColumnMetadata("key", java.sql.Types.INTEGER)
      table.addColumnMetadata("value", java.sql.Types.INTEGER)
      records.foreach { case (hotelId, cityId) => table.addRow(Int.box(hotelId), Int.box(cityId)) }
      table
    }
    execute(CapiConfig.PrimaryMdcGroup).q(
      "EXEC dbo.upsert_favorites_v7 ?, ?, ?, ?",
      memberId,
      toTable(toAdd),
      toTable(toReplace),
      toSqlTable(toRemove))
  }

  def apiMemberFavoriteHotelsSelectV3(memberId: Int)(implicit dB: DBPlugin): Vector[SQLRow] =
    QueriesHelper.mdcFallbackResultSet("EXEC dbo.api_member_favorite_hotels_select_V3 ?", memberId)
  def apiMemberFavoriteGroupsSelectV3(memberId: Int)(implicit dB: DBPlugin): Vector[SQLRow] =
    QueriesHelper.mdcFallbackResultSet("EXEC dbo.api_member_favorite_groups_select_v3 ?", memberId)
  def apiMemberFavoriteHotelsSelectByGroupV3(memberId: Int, groupId: Long)(implicit dB: DBPlugin): Vector[SQLRow] =
    QueriesHelper.mdcFallbackResultSet("EXEC dbo.api_member_favorite_hotels_select_by_group_V3 ?,?", groupId, memberId)
  def apiMemberFavoriteHotelsSelectBySharedHashV1(sharedHash: String)(implicit dB: DBPlugin): Vector[SQLRow] =
    QueriesHelper.mdcFallbackResultSet("EXEC dbo.api_member_favorite_hotels_select_by_shared_hash_V1 ?", sharedHash)
  def apiMemberFavoriteHotelsWithCityId(memberId: Int)(implicit dB: DBPlugin): Vector[SQLRow] =
    QueriesHelper.mdcFallbackResultSet("EXEC dbo.api_member_select_favorite_hotels_with_city_id_v2 ?", memberId)
}

//noinspection TypeAnnotation
object LegacyWechatQueries {
  import QueryUtil._

  def securitySocialUserInfoInsert(
      userId: UUID,
      appUserId: String,
      appId: Int,
      userInfo: String,
      whitelabelId: Int,
      forceMaskingPII: Boolean = false)(implicit dB: DBPlugin): Vector[SQLRow] = {
    resultSet(WriteGroup).q(
      "EXEC dbo.security_social_user_info_insert_v1 ?, ?, ?, ?",
      userId,
      appUserId,
      appId,
      masking(userInfo, Some(whitelabelId), forceMaskingPII))
  }
  def securitySocialUserInfoDelete(userId: UUID, appType: SocialAppType)(implicit dB: DBPlugin): Unit = {
    execute().q("EXEC dbo.security_social_user_info_delete ?, ?", userId, appType.id)
  }
  def securitySocialUserInfoSelectByUserid(userId: UUID)(implicit dB: DBPlugin): Vector[SQLRow] = {
    resultSet(ReadGroup).q("EXEC dbo.security_social_user_info_select_by_userid ?", userId)
  }
  def securitySocialUserInfoSelectOne(userId: UUID, appUserId: String, appId: Int)(
      implicit dB: DBPlugin): Option[SQLRow] = {
    result(ReadGroup).q("EXEC dbo.security_social_user_info_select_one ?, ?, ?", userId, appUserId, appId)
  }
}

//noinspection TypeAnnotation
object LegacyTokenAndValidationQueries {
  import QueryUtil._

  def securityCustomertokenSelectByuserid(userId: UUID, deviceId: UUID, data: Option[String])(
      implicit dB: DBPlugin): Option[CustomerToken] = {
    fallbackFetchOption(
      result(_: String).q("EXEC security_customertoken_select_byuserid_v2 ?, ?, ?", userId, deviceId, data),
      forceMDB = true)
      .map(x => CustomerTokenI.fromRow(x))
  }

  def securityCustomertokenSelectByuseridRead(userId: UUID, deviceId: UUID)(
      implicit dB: DBPlugin): Vector[CustomerToken] =
    //    resultSet(ReadGroup).q("EXEC dbo.security_customertoken_select_byuserid_v2 ?, ?, ?", userId, deviceId,None.orNull).map(x => CustomerTokenI.fromRow(x))
    resultSet(ReadGroup)
      .q("EXEC security_customertoken_select_byuserid_v2 ?, ?, ?", userId, deviceId, None.orNull)
      .map(x => CustomerTokenI.fromRow(x))

  def securityCustomertokenInvalidate(userId: UUID, deviceId: UUID)(implicit dB: DBPlugin): Unit = {
    execute(WriteGroup).q("EXEC dbo.security_customertoken_invalidate ?,?", userId, deviceId)
  }
}

//noinspection TypeAnnotation
object LegacySecurityQueries {
  def getUserInRoleForObjectQuery(objectId: Int, objectType: String, roleId: Option[UUID], allRecStatuses: Boolean)(
      implicit dB: DBPlugin) = {
    resultSet(ReadGroup)
      .q("EXEC dbo.api_get_authorized_user_v4 ?, ?, ?, ?", objectId, objectType, roleId, allRecStatuses)
      .map(AuthorizedUserI.fromRow)
  }

  def securityRoleObjectSelectByRoleId(uuid: UUID, rewardsRole: UUID, objectId: Char)(implicit dB: DBPlugin) =
    resultSet(ReadGroup).q("EXEC dbo.security_roleobject_select_byroleid_v2 ?, ?, ?", uuid, rewardsRole, objectId)

  def securityUserInRoleBlockByProperty(
      propertyId: Int,
      appId: UUID,
      roleId: UUID,
      userToBlock: UUID,
      isFraudulent: Boolean,
      recStatus: Boolean)(implicit dB: DBPlugin) =
    resultSet(WriteGroup).q(
      "EXEC dbo.security_user_in_role_block_by_property_v2 ?, ?, ?, ?, ?, ?",
      propertyId,
      appId,
      roleId,
      userToBlock,
      isFraudulent,
      recStatus)

  def securityUserInRoleGetMMByProperty(propertyId: Int, roleId: UUID)(implicit dB: DBPlugin) =
    result(ReadGroup).q("EXEC dbo.security_user_in_role_get_mm_by_property ?, ?", propertyId, roleId)

  // TODO: remove!
  def authUserInRoleSelectExists(userId: UUID, roleName: String, objectType: Character, objectId: Int)(
      implicit dB: DBPlugin) =
    result(ReadGroup).q("EXEC dbo.auth_userinrole_select_exists ?, ?, ?, ?", userId, roleName, objectType, objectId)

  def securityGetUsers(usersTable: SQLServerDataTable, onlyActive: Boolean)(implicit dB: DBPlugin) =
    resultSet(ReadGroup).q("EXEC dbo.security_get_users_v4 ?, ?", usersTable, onlyActive)

  def securityGetUserNames(userTable: SQLServerDataTable, onlyActive: Boolean)(implicit db: DBPlugin) =
    resultSet(ReadGroup).q("EXEC dbo.security_auth_usernames_v1 ?,?", userTable, onlyActive)

  def securityGetUsersByObjects(
      idTable: SQLServerDataTable,
      objectType: String,
      roleId: Option[UUID],
      onlyActive: Boolean)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q(
      "EXEC dbo.security_get_users_by_objects_v3 ?, ?, ?, ?",
      idTable,
      objectType,
      roleId,
      onlyActive)

  def authRoleGetV2(userId: UUID, componentId: UUID)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.auth_role_get_v3 ?, ?", userId, componentId)
  def authPermissionsGetV2(userId: UUID, componentId: UUID)(implicit dB: DBPlugin): Vector[SQLRow] = {
    resultSet(ReadGroup).q(s"EXEC dbo.auth_permissions_get_v3 ?, ?", userId, componentId)
  }
  def authComponentsGetV2(userId: UUID, systemId: UUID, languageId: Int)(
      implicit dB: DBPlugin): Vector[Vector[SQLRow]] =
    resultSets(ReadGroup).q("EXEC dbo.auth_components_get_v3 ?, ?, ?", userId, systemId, languageId)
  def securityRoleIdSelectByUserId(userId: UUID)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.security_roleid_select_byuserid ?", userId)
  def securityRoleObjectSelectByRolename(userId: UUID, roleName: String, objectType: Char)(
      implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.security_roleobject_select_byrolename ?, ?, ?", userId, roleName, objectType)
  def authUserInRoleGetByRoleId(roleId: UUID, authType: Option[Int], whiteLabelId: Option[Int])(
      implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q("EXEC dbo.auth_user_in_role_get_by_role_id_v2 ?, ?, ?", roleId, authType, whiteLabelId)
  def authUserInRoleGetByObjectType(
      roleId: UUID,
      authType: Option[Int],
      objectType: Option[Char],
      whiteLabelId: Option[Int])(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup, timeout = Some(FiniteDuration(20, TimeUnit.SECONDS)))
      .q("EXEC dbo.auth_user_in_role_get_by_object_type_v2 ?, ?, ?, ?", roleId, authType, objectType, whiteLabelId)
  def authUserInRoleGetByObjectId(
      roleId: UUID,
      authType: Option[Int],
      objectType: Option[Char],
      objectId: Option[Int],
      whiteLabelId: Option[Int])(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q(
      "EXEC dbo.auth_user_in_role_get_by_object_id_v2 ?, ?, ?, ?, ?",
      roleId,
      authType,
      objectType,
      objectId,
      whiteLabelId)
  def authUserInRoleGetByObjects(
      idTable: SQLServerDataTable,
      objectType: String,
      roleId: Option[UUID],
      onlyActive: Boolean)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet(ReadGroup).q(
      "EXEC dbo.auth_user_in_role_get_by_objects_v4 ?, ?, ?, ?",
      idTable,
      objectType,
      roleId,
      onlyActive)

  def authGetRoleObjectsByUserId(
      userId: UUID,
      objectType: Option[Char],
      roleId: Option[UUID],
      allRecStatuses: Boolean,
      skipMassiveRolesUsers: Boolean)(implicit dB: DBPlugin): Vector[RoleObjectByUserId] = {
    if (skipMassiveRolesUsers && CapiConsul.massiveRolesUsers.contains(userId) || CapiConsul.massiveRolesUsersForced
        .contains(userId)) Vector.empty
    else {
      import RoleObjectByUserIdI.convV
      resultSet(ReadGroup).q(
        s"EXEC dbo.auth_get_role_objects_by_user_id_v4 ?, ?, ?, ?",
        userId,
        objectType,
        roleId,
        allRecStatuses)
    }
  }

}

object ConfigQueries {
  def redemptionOptions(elite: Boolean)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet().q("EXEC dbo.api_redeem_level_select ?", elite)
  def exchangeRateSelect(code: String)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet().q("EXEC dbo.api_exchange_rate_fetch ?", code)
  def afmConfigurationSelectV1(rows: Int)(implicit dB: DBPlugin): Vector[AfmConfig] =
    resultSet().q("EXEC dbo.afm_configuration_select_v1 ?", rows)
  def afmConfigurationSelectV2(storeFrontId: Int, machineName: String)(implicit dB: DBPlugin): Vector[AfmConfig] =
    resultSet().q("EXEC dbo.afm_configuration_select_v2 ?, ?", storeFrontId, machineName)
  def apiConfigFetch()(implicit dB: DBPlugin): Vector[SQLRow] = resultSet().q("EXEC dbo.api_configuration_fetch")
  def apiLOVValuesSelect(lovSetId: Int, languageId: Int)(implicit dB: DBPlugin): Vector[SQLRow] =
    resultSet().q("EXEC api_LOV_values_select ?, ?", lovSetId, languageId)

}

object NhaQueries {
  import RSParsing.{toTable => toSqlTable}
  private val slowRead = Some(FiniteDuration(30, TimeUnit.SECONDS))

  val cmsQuery = "EXEC get_cms_by_id @language_id = ?, @cms_id = ?, @cms_version = ?"

  def updateNhaMemberLevel(userId: UUID, level: Int)(implicit dB: DBPlugin): Unit =
    execute(CapiConfig.PrimaryMdcGroup).q("EXEC update_host_level ?,?", userId, level)

  def nhaMembers()(implicit dB: DBPlugin): Vector[(Int, UUID)] =
    RSParsing.nhaMembers(resultSet(ReadGroup, timeout = slowRead).q("EXEC all_hosts_v3"))
  import RSParsing.{toTable, toUUIDTable}

  def verifyStatus(t: Vector[UUID])(implicit dB: DBPlugin): Map[UUID, (Boolean, Boolean)] =
    RSParsing.verifyStatus(resultSet(ReadGroup, timeout = slowRead).q("EXEC host_verify_status ?", toUUIDTable(t)))

  def mdcStatusForMembers(t: Vector[UUID], loadFull: Boolean)(implicit dB: DBPlugin): Map[UUID, MemberMDCStatusRow] =
    RSParsing.mdcStatusForMembers(
      QueriesHelper.mdcFallbackResultSet("EXEC hosts_status_v6 ?,?", toUUIDTable(t), loadFull))

  def previousHostLevelsFromDb(implicit dB: DBPlugin): Vector[(Int, Int)] =
    RSParsing.previousHostLevels(resultSet(ReadGroup).q("exec hotel_nha_status"))

  def updateHosts(unverifiedHotels: Vector[Int], verifiedHotels: Vector[Int], topHotels: Vector[Int])(
      implicit dB: DBPlugin): Unit = {
    execute().q(
      "exec update_hotel_nha_status ?,?,?",
      toTable(unverifiedHotels),
      toTable(verifiedHotels),
      toTable(topHotels))
  }

  def namesFor(memberIds: Vector[Int])(implicit dB: DBPlugin): Map[Int, (Option[String], Option[String])] = {
    RSParsing.namesFor(resultSet(ReadGroup, timeout = slowRead).q("EXEC host_names ?", toTable(memberIds)))
  }

  def reviewsFor(hotelIds: Vector[Int])(implicit dB: DBPlugin): Map[Int, (Int, Double, Double)] = {
    RSParsing.reviewsFor(resultSet(ReadGroup, timeout = slowRead).q("EXEC host_reviews_v2 ?", toTable(hotelIds)))
  }

  def financeFor(hotelIds: Vector[Int])(implicit dB: DBPlugin): Map[Int, (Boolean, Int)] = {
    RSParsing.financeFor(resultSet(ReadGroup, timeout = slowRead).q("EXEC host_finance_v2 ?", toTable(hotelIds)))
  }

  def bookingsFor(hotelIds: Vector[Int], standby: Boolean)(implicit dB: DBPlugin): Map[Int, Map[Int, Int]] = {
    RSParsing.bookingsFor(
      resultSet(if (standby) WriteStandbyGroup else WriteGroup, timeout = CapiConsul.NHAFetchBookingTimeoutDuration)
        .q("EXEC dbo.nha_bookings_v2 ?, ?", toTable(hotelIds), Constants.AgodaId))
  }

  def bookingsForFromFeatureStore(
      hotelIds: Vector[Int]
  ): Future[Map[Int, Map[Int, Int]]] = {

    val features = List("booking_count")
    val featureSet = "capi_nha_aggregated_booking_data"

    TempSingletonHolder.featureStoreService.fetchBookings(hotelIds, features, featureSet)
  }

  def ycs41GetHotelList(eitherId: Either[Int, UUID])(implicit dB: DBPlugin): Vector[SQLRow] = {
    Utils.retryStandard(2)(
      resultSet(DBGroups.ReadGroup, Some(CapiConsul.ycs41GetHotelListCDBTimeout))
        .q("EXEC dbo.ycs41_get_hotel_list_v7 ?, ?", eitherId.left.getOrElse(None), eitherId.right.getOrElse(None))
    )
  }

  def getHotelIds(userId: UUID, ycsRecStatus: Option[Int], isNha: Option[Boolean], noOfRow: Int)(
      implicit dB: DBPlugin): Vector[PropertyIdsResponse] = {
    Utils.retryStandard(2)(
      resultSet(DBGroups.ReadGroup, Some(CapiConsul.ycs41GetHotelListCDBTimeout))
        .q("EXEC dbo.get_hotelids_v2 ?, ?, ?, ?", userId, ycsRecStatus, isNha, noOfRow)
        .map(
          result =>
            PropertyIdsResponse(
              result.int("hotel_id"),
              Try(PropertyStatus(result.int("hotel_active_status"))).getOrElse(PropertyStatus.Unknown),
              result.boolean("is_nha")
          ))
    )
  }

  def getCountryName(countryIds: Set[Int], languageId: Int)(implicit dB: DBPlugin): Map[Int, Option[String]] = {
    RSParsing.countryNameFor(
      resultSet(ReadGroup).q("EXEC dbo.get_country_name_v1 ?, ?", toSqlTable(countryIds), languageId))
  }

  def getCityName(cityIds: Set[Int], languageId: Int)(implicit dB: DBPlugin): Map[Int, Option[String]] = {
    RSParsing.cityNameFor(resultSet(ReadGroup).q("EXEC dbo.get_city_name_v1 ?, ?", toSqlTable(cityIds), languageId))
  }

  def getStateName(stateIds: Set[Int], languageId: Int)(implicit dB: DBPlugin): Map[Int, Option[String]] = {
    RSParsing.stateNameFor(resultSet(ReadGroup).q("EXEC dbo.get_state_name_v1 ?, ?", toSqlTable(stateIds), languageId))
  }
}

object InsertHelper {
  import QueryUtil._

  case class AuthMappingForInsert(username: String, uri: SecurityUri, authType: AuthenticationType, verified: Boolean)

  // REW migrate this as well
  def apiCustomerInsertSimple(
      customer: Customer,
      whitelabelProperties: Option[WlAdditionalProperties],
      group: String = WriteGroup,
      forceMaskingPII: Boolean = false)(implicit dB: DBPlugin): Unit = {

    def generateXMLMapping(username: String, uri: String, authType: Int, isVerified: Int): String =
      s"<Mappings><Username>$username</Username><Uri>$uri</Uri><Type>$authType</Type><Verified>$isVerified</Verified></Mappings>"

    def createMapping(m: UserMappingModel) = {
      val loginFeature = WhiteLabelHelper.getLoginFeature(customer.whiteLabelId.getOrElse(Constants.AgodaId))
      val username = {
        if (m.authType == AuthenticationType.Username.id && loginFeature.isUsernameCaseSensitive.getOrElse(false))
          m.username
        else m.username.toLowerCase
      }

      generateXMLMapping(
        username = masking(username, customer.whiteLabelId, forceMaskingPII),
        uri = masking(m.uriString, customer.whiteLabelId, forceMaskingPII),
        authType = m.authType,
        isVerified = if (m.isVerified) 1 else 0
      )
    }

    val xml = s"<Result>${customer.originalModel().mappings.map(createMapping).mkString("\n")}</Result>"
    val properties = customer.jsonProperties

    val isBlacklisted = customer.legacyModels.flatMap(_.legacyEntity).exists(_.isBlocked)
    val recStatus = if (isBlacklisted) -1 else 1

    execute(group).q(
      "EXEC dbo.api_customer_insert_simplified_v14 ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?",
      maskingO(customer.email.map(_.value), customer.whiteLabelId, forceMaskingPII),
      customer.userId,
      masking(QueryUtil.removeIllegalChars(customer.firstName), customer.whiteLabelId, forceMaskingPII),
      QueryUtil.removeIllegalChars(masking(customer.lastName, customer.whiteLabelId, forceMaskingPII)),
      masking(QueryUtil.removeIllegalChars(customer.legacyDisplayName), customer.whiteLabelId, forceMaskingPII),
      properties,
      customer.fields.newsletter,
      maskingO(customer.phone.map(_.value), customer.whiteLabelId, forceMaskingPII),
      customer.log.createdBy.getOrElse(Constants.AppId),
      customer.log.created,
      customer.log.createdBy.getOrElse(Constants.AppId),
      Instant.now(),
      xml,
      customer.whiteLabelId,
      customer.origin,
      customer.memberId,
      customer.legacyFields.title,
      recStatus,
      isBlacklisted
    )
    customer.legacyAllContacts
      .filter(_.recordStatus.exists(status => status == RecordStatus.Active.id))
      .foreach(contact => {
        MutatingQueries.syncUserContact(
          SyncUserContact(
            contact.contactId.getOrElse(Counter.ContactCounter.nextId(CapiConfig.PrimaryMdcGroup)),
            Some(contact)
          ),
          group = group
        )
      })
  }

  def externalMemberInsert(
      customerModel: CustomerModel,
      whitelabelId: Int,
      externalMemberId: String,
      analysisId: Option[String])(implicit dB: DBPlugin): Unit = {
    val params = Seq(externalMemberId, whitelabelId, customerModel.userId, customerModel.memberId, analysisId)
    execute(CapiConfig.PrimaryMdcGroup).q("EXEC dbo.add_external_user_id_v5 ?,?,?,?,?", params: _*)
  }

  def upsertCustomer(customer: Customer, group: String = SHMdbWriteGroup)(implicit dB: DBPlugin): Unit = {
    val isBlacklisted = customer.legacyModels.flatMap(_.legacyEntity).exists(_.isBlocked)
    val recStatus = if (isBlacklisted) -1 else 1
    val birthdate = customer.fields.birthday.map(
      b =>
        if (b.isBefore(LocalDate.of(1901, 1, 1))
          || b.isAfter(LocalDate.of(2078, 12, 31))) LocalDate.of(1901, 1, 1)
        else LocalDate.of(b.getYear, b.getMonth, b.getDayOfMonth))

    execute(group).q(
      "EXEC dbo.upsert_rew_members_v1 ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?,?, ?,?, ?,?",
      customer.memberId,
      customer.userId,
      encrypt(QueryUtil.removeIllegalChars(customer.firstName)),
      QueryUtil.removeIllegalChars(encrypt(customer.lastName)),
      customer.jsonProperties,
      customer.fields.ccof,
      birthdate,
      customer.fields.prius,
      customer.fields.nationalityId,
      customer.fields.languageId,
      customer.fields.newsletter,
      recStatus,
      customer.log.createdBy.getOrElse(Constants.AppId),
      customer.log.created,
      customer.log.createdBy.getOrElse(Constants.AppId),
      Instant.now(),
      Instant.now(),
      customer.whiteLabelId,
      customer.origin,
      customer.legacyFields.title,
      isBlacklisted
    )
    customer.legacyAllContacts
      .filter(_.recordStatus.exists(status => status == RecordStatus.Active.id))
      .foreach(contact => {
        MutatingQueries.syncUserContact(
          SyncUserContact(
            contact.contactId.getOrElse(Counter.ContactCounter.nextId(CapiConfig.PrimaryMdcGroup)),
            Some(contact)
          ),
          writeGroup = group
        )
      })
  }
}

object AuditQueries {
  import QueryUtil._

  def readRmAuditLogs(userId: UUID)(implicit db: DBPlugin) = {
    resultSet(AuditGroup).q("EXEC dbo.read_rm_audit_log_v2 ?,?", userId, Constants.AuditLogQueryLimit)
  }
  def saveRmAuditLogs(params: CustomerAuditLog)(implicit db: DBPlugin) = {
    val paramSeq = Seq(
      params.auditId,
      params.userId,
      params.whitelabelId,
      params.eventName.toString,
      encrypt(params.oldValue),
      encrypt(params.newValue),
      params.propertyName,
      params.recCreatedBy,
      params.recCreatedWhen
    )
    execute(AuditGroup).q("EXEC dbo.save_rm_audit_log_v1 ?,?,?,?,?,?,?,?,?", paramSeq: _*)
  }
  def bulkSaveRmAuditLogs(params: Vector[CustomerAuditLog], batchSize: Int)(implicit db: DBPlugin) = {
    execute(AuditGroup, timeout = Some(Constants.AuditLogBatchTimeout))
      .q("EXEC dbo.bulk_save_rm_audit_log_v1 ?,?", toAuditTable(params), batchSize)
  }
  def deleteRmAuditLogs(userId: UUID, whitelabelId: Int, batchSize: Int)(implicit db: DBPlugin) = {
    execute(AuditGroup).q("EXEC dbo.delete_rm_audit_log_v1 ?,?,?", userId, whitelabelId, batchSize)
  }

  private def toAuditTable(params: Vector[CustomerAuditLog]): SQLServerDataTable = {
    val table = new SQLServerDataTable
    table.setTvpName("dbo.save_rm_audit_log_table_type")
    table.addColumnMetadata("audit_logs_id", java.sql.Types.CHAR)
    table.addColumnMetadata("user_id", java.sql.Types.CHAR)
    table.addColumnMetadata("whitelabel_id", java.sql.Types.SMALLINT)
    table.addColumnMetadata("event_name", java.sql.Types.VARCHAR)
    table.addColumnMetadata("old_value", java.sql.Types.VARCHAR)
    table.addColumnMetadata("new_value", java.sql.Types.VARCHAR)
    table.addColumnMetadata("property_name", java.sql.Types.VARCHAR)
    table.addColumnMetadata("rec_created_by", java.sql.Types.CHAR)
    table.addColumnMetadata("rec_created_when", java.sql.Types.TIMESTAMP)
    params.foreach { audit =>
      table.addRow(
        audit.auditId,
        audit.userId,
        Short.box(audit.whitelabelId),
        audit.eventName.toString,
        encrypt(audit.oldValue),
        encrypt(audit.newValue),
        audit.propertyName,
        audit.recCreatedBy,
        audit.recCreatedWhen
      )
    }
    table
  }
}

object TestDataHolder {
  var holder: Option[ListBuffer[UserDetailToDelete]] = None
  var holderMemberId: Option[ListBuffer[Int]] = None
}

//noinspection TypeAnnotation
object ToRemoveCompatibilityQueries {
  def apiMemberPartnerMembershipUpdate(memberId: Int, programId: Int, membershipId: String)(implicit dB: DBPlugin) = {
    execute(WriteGroup).q("EXEC api_member_partner_membership_update ?,?,?", memberId, programId, membershipId)
  }
  def apiMemberPartnerMembershipInsert(memberId: Int, programId: Int, membershipId: String)(implicit dB: DBPlugin) = {
    execute(WriteGroup).q("EXEC api_member_partner_membership_insert ?,?,?", memberId, programId, membershipId)

  }
  def apiMemberPartnerMembershipDelete(memberId: Int, programId: Int)(implicit dB: DBPlugin) = {
    execute(WriteGroup).q("EXEC api_member_partner_membership_delete ?,?", memberId, programId)
  }
}

object ArchivalQueries {
  def updateDeactivateMember(activeId: Int, deactiveId: Int, approver: UUID)(implicit dB: DBPlugin) = {
    execute(ArchivalGroup).q("EXEC update_deactivate_to_new_member_id_v1 ?,?,?", activeId, deactiveId, approver)
  }
}

object CNCustomerQueries {
  val defaultCustomerConsent = false
  def getBookingData(bookingId: Int)(implicit dB: DBPlugin): Vector[Vector[SQLRow]] = {
    // SP link: https://gitlab.agodadev.io/dbdev/database_object/-/merge_requests/14423/diffs#diff-content-bf026ba9fe1d1ea4acc0fd00323a94eec6667530
    resultSets(WriteStandbyGroup).q("EXEC dbo.read_cn_booking_data_v3 ?", bookingId)
  }

  def getBookingDataWithOutBookingPax(bookingId: Int)(implicit dB: DBPlugin): Vector[Vector[SQLRow]] = {
    // SP link: https://gitlab.agodadev.io/dbdev/database_object/-/merge_requests/14423/diffs#diff-content-bf026ba9fe1d1ea4acc0fd00323a94eec6667530
    resultSets(WriteStandbyGroup).q("EXEC dbo.read_cn_booking_data_v4 ?", bookingId)
  }

  private def convertSqlRowsToTable(
      tableName: String,
      columnMapping: Iterable[(String, Int)],
      rows: Vector[SQLRow]): SQLServerDataTable = {
    val table = new SQLServerDataTable
    table.setTvpName(tableName)
    for ((columnName, columnType) <- columnMapping) table.addColumnMetadata(columnName, columnType)
    for (row <- rows) {
      val rowValues = for (column <- columnMapping.map(_._1)) yield row.map(column)
      table.addRow(rowValues.toVector: _*)
    }
    table
  }

  def upsertBookingPax(bookingData: BookingInfo, whitelabelId: Int, siteId: Int)(implicit dB: DBPlugin): Unit = {
    val customer_consent = defaultCustomerConsent
    execute(SHMdbWriteGroup).q(
      "EXEC dbo.upsert_booking_guest_v1 ?,?,?,?,?  ,?,?,?,?,? ,?",
      bookingData.bookingId,
      QueryUtil.encrypt(JsonMarshalling.short(bookingData.guests)),
      bookingData.affiliateSiteId.getOrElse(siteId),
      bookingData.whiteLabelId.getOrElse(whitelabelId),
      customer_consent,
      env.HostName.toString(),
      bookingData.metadata.map(_.status.value).getOrElse(-1),
      bookingData.metadata.map(_.createdBy).getOrElse(Constants.EmptyUUID),
      bookingData.metadata.map(_.createdWhen).getOrElse(Instant.now()),
      bookingData.metadata.flatMap(_.modifiedBy),
      bookingData.metadata.flatMap(_.modifiedWhen)
    )
  }

  def updateCidBookingPaxByBookingRoomId(bookingRoomId: Int, cid: String)(implicit dB: DBPlugin): Unit = {
    val customer_consent = defaultCustomerConsent
    execute(SHMdbWriteGroup).q(
      "EXEC dbo.update_cid_ebe_booking_pax_by_booking_room_id_v1 ?,?,?",
      bookingRoomId.toLong,
      cid,
      customer_consent)
  }

  def upsertBookingDataV2(sqlRows: Vector[Vector[SQLRow]])(implicit dB: DBPlugin): Unit = {
    val booking = convertSqlRowsToTable("dbo.ebe_booking_table_type", BookingTableMappings.ebeBooking, sqlRows.head)
    val itinerary = convertSqlRowsToTable("dbo.ebe_itinerary_table_type", BookingTableMappings.ebeItinerary, sqlRows(1))
    val hotel =
      convertSqlRowsToTable("dbo.ebe_booking_hotel_table_type", BookingTableMappings.ebeBookingHotel, sqlRows(2))
    val pax = convertSqlRowsToTable("dbo.ebe_booking_pax_table_type", BookingTableMappings.ebeBookingPax, sqlRows(3))
    val payment =
      convertSqlRowsToTable("dbo.ebe_booking_payments_table_type", BookingTableMappings.ebeBookingPayments, sqlRows(4))
    val creditCard = convertSqlRowsToTable(
      "dbo.ebe_booking_creditcards_table_type_v2",
      BookingTableMappings.ebeBookingCreditCard,
      sqlRows(5))
    val amendment = convertSqlRowsToTable(
      "dbo.ebe_booking_amendment_table_type",
      BookingTableMappings.ebeBookingAmendment,
      sqlRows(6))

    execute(SHMdbWriteGroup).q(
      "EXEC dbo.upsert_chinese_booking_data_v4 ?, ?, ?, ?, ?, ?, ?",
      booking,
      itinerary,
      hotel,
      pax,
      payment,
      creditCard,
      amendment)
  }

  def upsertBookingDataV3(sqlRows: Vector[Vector[SQLRow]])(implicit dB: DBPlugin): Unit = {
    val booking = convertSqlRowsToTable("dbo.ebe_booking_table_type", BookingTableMappings.ebeBooking, sqlRows.head)
    val itinerary = convertSqlRowsToTable("dbo.ebe_itinerary_table_type", BookingTableMappings.ebeItinerary, sqlRows(1))
    val hotel =
      convertSqlRowsToTable("dbo.ebe_booking_hotel_table_type", BookingTableMappings.ebeBookingHotel, sqlRows(2))
    val payment =
      convertSqlRowsToTable("dbo.ebe_booking_payments_table_type", BookingTableMappings.ebeBookingPayments, sqlRows(3))
    val creditCard = convertSqlRowsToTable(
      "dbo.ebe_booking_creditcards_table_type_v2",
      BookingTableMappings.ebeBookingCreditCard,
      sqlRows(4))
    val amendment = convertSqlRowsToTable(
      "dbo.ebe_booking_amendment_table_type",
      BookingTableMappings.ebeBookingAmendment,
      sqlRows(5))

    execute(SHMdbWriteGroup).q(
      "EXEC dbo.upsert_chinese_booking_data_v5 ?, ?, ?, ?, ?, ?",
      booking,
      itinerary,
      hotel,
      payment,
      creditCard,
      amendment)
  }
}
