package com.agoda.winterfell.models

import java.security.MessageDigest

import com.agoda.winterfell.Constants
import com.agoda.winterfell.common.AuthenticationType.AuthenticationType
import com.agoda.winterfell.models.entities.{SecurityUri, SecurityUriHelper, UserMappingModelHelper}
import com.google.common.base.{Supplier, Suppliers}

object SecurityUriServer {

  def forAuth(auth: AuthenticationType, id: String, component: Option[String] = None) =
    new SecurityUri(auth.toString, id, component)

  def apply(uriTypeComponent: String, uriIdentityComponent: String, uriSecurityComponent: String): SecurityUri =
    new SecurityUri(uriTypeComponent, uriIdentityComponent, Some(uriSecurityComponent))

  def parseUri(uriString: String): SecurityUri = {
    SecurityUriHelper.parseUri(uriString)
  }

  def generateUri(
      email: String,
      password: Option[String],
      ignorePasswordHashing: Option[Boolean],
      whitelabelId: Int = Constants.AgodaId): SecurityUri =
    HashStrategy.getHashStrategy(whitelabelId).generateUri(email, password, ignorePasswordHashing)

  def generatePhoneUri(
      phone: String,
      password: String,
      ignorePasswordHashing: Option[Boolean],
      whitelabelId: Int = Constants.AgodaId): SecurityUri =
    HashStrategy.getHashStrategy(whitelabelId).generatePhoneUri(phone, password, ignorePasswordHashing)

  UserMappingModelHelper.passwordFunction = correctPassword

  def correctPassword(plainText: String, hashed: String, whitelabelId: Int): Boolean =
    HashStrategy.getHashStrategy(whitelabelId).correctPassword(plainText, hashed)

  def computeOldInsecureSHA1Hash(data: String): String = computeSHA1HashToUTF8(data)

  def computeSHA256HashToUTF8(data: String): String = {
    val sha1: MessageDigest = java.security.MessageDigest.getInstance("SHA-256")
    binToHex(sha1.digest(data.getBytes("UTF-8")))
  }

  def computeSHA1HashToUTF8(data: String): String = {
    val sha1: MessageDigest = java.security.MessageDigest.getInstance("SHA-1")
    binToHex(sha1.digest(data.getBytes("UTF-8")))
  }

  private def binToHex(data: Array[Byte]): String = data.map("%02x" format _).mkString
}

object HashStrategy {
  private val agodaHashingStrategy: Supplier[AgodaHashingStrategy] = Suppliers.memoize(() => {
    new AgodaHashingStrategy
  })
  private val rocketMilesHashingStrategy: Supplier[RocketMilesHashingStrategy] = Suppliers.memoize(() => {
    new RocketMilesHashingStrategy
  })

  def getHashStrategy(whitelabelId: Int): SecurityHashingStrategy = {
    whitelabelId match {
      case id if Constants.isRmWhitelabelId(id) => rocketMilesHashingStrategy.get
      case _ => agodaHashingStrategy.get
    }
  }
}
