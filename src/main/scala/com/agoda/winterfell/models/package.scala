package com.agoda.winterfell

import java.util.UUID
import com.softwaremill.diffx._
import com.fasterxml.jackson.databind.node.ObjectNode
import com.agoda.winterfell.models.entities.{CustomerExtraFields, CustomerModel, UserMappingModel}
import com.agoda.winterfell.output.{GiftCardBalanceInfo, LoyaltyProfile, MemberContact, MemberDetails, UserInfoEntity}
import com.agoda.winterfell.unified.Fields
import org.joda.time.DateTime

package object models {
  implicit val customerFieldsDiff: Diff[Fields] = Derived[Diff[Fields]]
    .ignore[Fields, Vector[String]](_.hashedPasswords)

  implicit val customerExtraFieldsDiff: Diff[CustomerExtraFields] = Derived[Diff[CustomerExtraFields]]
    .ignore[CustomerExtraFields, Option[String]](_.preferCurrency)
    .ignore[CustomerExtraFields, Option[DateTime]](_.lastSuccessLogin)

  implicit val memberDetailsDiff: Diff[MemberDetails] = Derived[Diff[MemberDetails]]
    .ignore[MemberDetails, Int](_.mergeTo)
    .ignore[MemberDetails, Option[GiftCardBalanceInfo]](_.giftCardInfo)
    .ignore[MemberDetails, Option[ObjectNode]](_.loyaltyProfile)
    .ignore[MemberDetails, Option[LoyaltyProfile]](_.loyaltyProfileContext)
    .ignore[MemberDetails, Option[UUID]](_.recordCreatedBy)
    .ignore[MemberDetails, Option[DateTime]](_.recordCreatedWhen)
    .ignore[MemberDetails, Option[UUID]](_.recordModifiedBy)
    .ignore[MemberDetails, Option[DateTime]](_.recordModifiedWhen)

  implicit val userInfoEntityDiff: Diff[UserInfoEntity] = Derived[Diff[UserInfoEntity]]
    .ignore[UserInfoEntity, Option[DateTime]](_.expiredSince)
    .ignore[UserInfoEntity, Option[Boolean]](_.isTwoFactorEnabled)
    .ignore[UserInfoEntity, Int](_.failedLoginAttempts)
    .ignore[UserInfoEntity, Option[Boolean]](_.isPasswordMeetStrength)

  implicit val userMappingDiff: Diff[UserMappingModel] = Derived[Diff[UserMappingModel]]
    .ignore[UserMappingModel, Option[String]](_.salt)
    .ignore[UserMappingModel, DateTime](_.passwordLastChanged)
    .ignore[UserMappingModel, DateTime](_.createdWhen)
    .ignore[UserMappingModel, UUID](_.createdBy)
    .ignore[UserMappingModel, Option[DateTime]](_.modifiedWhen)
    .ignore[UserMappingModel, Option[UUID]](_.modifiedBy)
    .ignore[UserMappingModel, DateTime](_.expiredSince)

  implicit val memberContactDiff: Diff[MemberContact] = Derived[Diff[MemberContact]]
    .ignore[MemberContact, Option[UUID]](_.recordCreatedBy)
    .ignore[MemberContact, Option[DateTime]](_.recordCreatedWhen)
    .ignore[MemberContact, Option[UUID]](_.recordModifiedBy)
    .ignore[MemberContact, Option[DateTime]](_.recordModifiedWhen)

  final implicit class RichCustomerModel(val model: CustomerModel) extends AnyVal {
    def isIdentical(other: CustomerModel): DiffResult = {
      if (model.userId != other.userId)
        DiffResultObject("", Map("userId" -> DiffResultValue(model.userId, other.userId)))
      else if (model.memberId != other.memberId)
        DiffResultObject("", Map("memberId" -> DiffResultValue(model.memberId, other.memberId)))
      else if (model.email != other.email)
        DiffResultObject("", Map("email" -> DiffResultValue(model.email, other.email)))
      else {
        compare(model.fields, other.fields)
          .withPrefix("fields")
          .orElse { compare(model.customerExtraFields, other.customerExtraFields).withPrefix("customerExtraFields") }
          .orElse { compare(model.legacyFields, other.legacyFields).withPrefix("legacyFields") }
          .orElse { compare(model.memberDetails, other.memberDetails).withPrefix("memberDetails") }
          .orElse { compare(model.userInfoEntity, other.userInfoEntity).withPrefix("userInfoEntity") }
          .orElse { compare(model.mappings, other.mappings).withPrefix("mappings") }
          .orElse { compare(model.contacts, other.contacts).withPrefix("contacts") }
          .orElse { compare(model.favorites, other.favorites).withPrefix("favorites") }
      }
    }
  }

  final implicit class RichDiffResult(val result: DiffResult) extends AnyVal {
    def firstFieldMismatch: Option[(String, String)] = {
      @inline def findFieldName(result: DiffResult, path: List[String]): Option[(String, String)] = result match {
        case DiffResultObject(_, fields) =>
          fields.iterator
            .flatMap { case (fieldName, result) => findFieldName(result, path :+ fieldName) }
            .collectFirst { case fullPath => fullPath }
        case diff: DiffResultDifferent => Some(path.mkString(".") -> diff.show)
        case _ => None
      }
      findFieldName(result, List.empty)
    }

    def withPrefix(prefix: String): DiffResult = {
      if (result.isInstanceOf[DiffResultDifferent]) DiffResultObject("", Map(prefix -> result)) else result
    }
    def orElse(f: => DiffResult): DiffResult = if (result.isInstanceOf[DiffResultDifferent]) result else f
  }
}
