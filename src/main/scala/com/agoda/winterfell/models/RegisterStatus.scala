package com.agoda.winterfell.models

import com.agoda.winterfell.common.{AuthenticationType, ResultStatus}
import com.agoda.winterfell.common.AuthenticationType.AuthenticationType
import com.agoda.winterfell.common.ResultStatus.ResultStatus
import com.agoda.winterfell.constant.Constants
import com.agoda.winterfell.unified.AuthResult

sealed class RegisterStatus(val isSuccess: <PERSON>olean, val result: ResultStatus, val defaultMsg: Option[String]) {
  def toAuthResult: AuthResult = {
    AuthResult(
      success = isSuccess,
      code = result.id,
      message = Some(result.toString),
      errorMessage = defaultMsg,
      result = None,
      additional = None,
      socialClaims = None
    )
  }
}

object RegisterStatus {
  val insertFailedMarker = "- insert failed"

  case object Successful extends RegisterStatus(isSuccess = true, ResultStatus.Complete, None)
  case object InvalidEmail extends RegisterStatus(isSuccess = false, ResultStatus.Failure, Some("Email is not valid"))
  case object InvalidUsername
      extends RegisterStatus(isSuccess = false, ResultStatus.Failure, Some("Username is not valid"))
  case object InvalidPassword
      extends RegisterStatus(isSuccess = false, ResultStatus.Failure, Some("Password is less than 8 character"))
  case object InvalidNetwork
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.Failure,
        Some("Agoda emails can only be used in the internal network"))
  case object OnlyAgodaEmail
      extends RegisterStatus(isSuccess = false, ResultStatus.Failure, Some("Only @agoda.com emails are supported"))

  case object EmailExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given email"))
  case object UsernameExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given username"))
  case object PhoneExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given phone"))
  case object WechatExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given WeChat id"))
  case object LineExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given Line id"))
  case object GrabExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given Grab id"))
  case object FacebookExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given Facebook id"))
  case object GoogleExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given Google id"))
  case object RadissonChainSSOExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given Radisson Chain SSO id"))

  case object RmSSOExists
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some("User already exists with the given RM SSO id for specific given type"))
  case class ContactExists(authTypes: String)
      extends RegisterStatus(
        isSuccess = false,
        ResultStatus.EmailExists,
        Some(s"User already exists with at least one of the given contact types [$authTypes] $insertFailedMarker"))

  def forExistingType(authType: AuthenticationType): RegisterStatus = authType match {
    case AuthenticationType.Basic => EmailExists
    case AuthenticationType.Username => UsernameExists
    case AuthenticationType.PhoneNumber => PhoneExists
    case AuthenticationType.WeChatConnect => WechatExists
    case AuthenticationType.LineConnect => LineExists
    case AuthenticationType.GrabConnect => GrabExists
    case AuthenticationType.FacebookConnect => FacebookExists
    case AuthenticationType.GoogleConnect => GoogleExists
    case AuthenticationType.RadissonChainSSO => RadissonChainSSOExists
    case authType: AuthenticationType if Constants.RocketMilesSSOTypesRange contains authType.id => RmSSOExists
  }
}
