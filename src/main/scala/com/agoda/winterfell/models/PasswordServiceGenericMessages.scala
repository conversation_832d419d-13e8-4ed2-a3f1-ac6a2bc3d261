package com.agoda.winterfell.models

import java.util.UUID

import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.adp.messaging.scala.message.Message

object PasswordServiceGenericMessages {

  @SchemaComment("Reset password endpoint access history")
  case class BulkPasswordsResettingHistory(
      @SchemaComment("Member id")
      memberId: Int,
      @SchemaComment("Is password reset?")
      isReset: <PERSON><PERSON><PERSON>,
      @SchemaComment("Modifier id")
      resetBy: UUID,
      @SchemaComment("Is email sent out for this resetting?")
      sendEmail: <PERSON><PERSON>an, //Never actually used
      @SchemaComment("Email template id to be sent")
      templateId: Option[Int], //Never used
      @SchemaComment("Cusco's request id")
      requestId: Option[Int],
      @SchemaComment("Exception message")
      exception: String)
      extends Message[BulkPasswordsResettingHistory]

  @SchemaComment("Password change history")
  final case class PasswordChangeHistory(
      @SchemaComment("Member id")
      memberId: Int,
      @SchemaComment("Who initiated password change: User or AccountProtection")
      initiatedBy: String,
      @SchemaComment(
        "If password is changed due to APAPI, here will be event uuid which can be used for the future investigations")
      eventUUID: Option[String]
  ) extends Message[PasswordChangeHistory]
}
