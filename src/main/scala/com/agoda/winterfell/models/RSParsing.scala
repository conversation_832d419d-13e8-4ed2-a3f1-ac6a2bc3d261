package com.agoda.winterfell.models

import java.util.UUID

import com.agoda.winterfell.internal.SQLRow
import com.agoda.winterfell.output._
import com.microsoft.sqlserver.jdbc.SQLServerDataTable
import org.joda.time.{DateTime, LocalDate}

import scala.language.postfixOps

object RSParsing {

  case class LookupUserResult(id: UUID, username: String, authType: Option[Int])

  def toLookup(r: SQLRow): LookupUserResult = {
    LookupUserResult(
      r.uuid("UserId"),
      r.str("username"),
      r.map.get("authentication_type_id").map(_.asInstanceOf[Short].toInt))
  }

  def toMembership(r: SQLRow): PartnerMembership = {
    PartnerMembership(r.int("program_id"), r.str("membership_id"))
  }

  def nhaMembers(rows: Vector[SQLRow]): Vector[(Int, UUID)] = {
    rows.map(x => (x.intOption("m"), x.uuid("u"))).filter(_._1.isDefined).map(x => (x._1.get, x._2))
  }

  def verifyStatus(rows: Vector[SQLRow]): Map[UUID, (Boolean, Boolean)] = {
    rows
      .map(s => (s.uuid("u"), s.int("i"), s.booleanOption("v").getOrElse(false)))
      .groupBy(_._1)
      .map {
        case (u, v) => u -> (v.exists(a => a._2 == 2 && a._3), v.exists(a => a._2 == 7))
      }
  }

  def mdcStatusForMembers(rows: Vector[SQLRow]): Map[UUID, MemberMDCStatusRow] = {
    rows.map { s =>
      s.uuid("u") ->
        MemberMDCStatusRow(
          s.strOption("p"),
          s.bigDecimalOption("r").map(_.doubleValue()).getOrElse(0d),
          s.intOption("rt"),
          s.dateTimeOption("j").map(_.toLocalDate),
          s.intOption("city"),
          s.intOption("co"),
          s.intOption("st"),
          s.strOption("d"),
          s.strOption("ud"),
          if (s.map.contains("eff")) s.intOption("eff") else None,
          s.strOption("g"),
          s.strOption("sl").map(_.split(",").map(_.toInt).toVector).getOrElse(Vector.empty),
          s.intOption("tht"),
          s.intOption("tqs"),
          s.dateTimeOption("tqcd")
        )
    }.toMap
  }

  def previousHostLevels(rows: Vector[SQLRow]): Vector[(Int, Int)] = {
    rows.map(r => (r.int("h"), r.int("l")))
  }

  case class MemberMDCStatusRow(
      pic: Option[String],
      responseRate: Double,
      responseTime: Option[Int],
      joined: Option[LocalDate],
      cityId: Option[Int],
      countryId: Option[Int],
      stateId: Option[Int],
      displayName: Option[String],
      userDescription: Option[String],
      effectiveLevel: Option[Int],
      gender: Option[String],
      spokenLanguages: Vector[Int],
      tprmHostType: Option[Int],
      tprmQuestionnaireStatus: Option[Int],
      tprmQuestionnaireChangedDate: Option[DateTime])

  def reviewsFor(rows: Vector[SQLRow]): Map[Int, (Int, Double, Double)] = {
    val average = rows.map(
      s =>
        (
          s.int("h"),
          s.int("c"),
          s.double("a"),
          s.bigDecimalOption("avg_staff_score").getOrElse(BigDecimal(0)).doubleValue()))
    average.map(x => (x._1, (x._2, x._3, x._4))).toMap
  }

  def namesFor(rows: Vector[SQLRow]): Map[Int, (Option[String], Option[String])] = {
    val average = rows.map(s => (s.int("m"), s.strOption("f"), s.strOption("l")))
    average.map(x => (x._1, (x._2, x._3))).toMap
  }

  def financeFor(rows: Vector[SQLRow]): Map[Int, (Boolean, Int)] = {
    rows.map(s => (s.int("h"), (s.booleanOption("a").getOrElse(false), s.int("p")))).toMap
  }

  def bookingsFor(rows: Vector[SQLRow]): Map[Int, Map[Int, Int]] = {
    rows.map(s => (s.int("h"), s.int("s"), s.int("c"))).groupBy(_._1).mapValues(x => x.map(c => (c._2, c._3)).toMap)
  }

  def countryNameFor(rows: Vector[SQLRow]): Map[Int, Option[String]] = {
    rows.map(r => r.int("country_id") -> r.strOption("country_name")).toMap
  }

  def stateNameFor(rows: Vector[SQLRow]): Map[Int, Option[String]] = {
    rows.map(r => r.int("state_id") -> r.strOption("state_name")).toMap
  }

  def cityNameFor(rows: Vector[SQLRow]): Map[Int, Option[String]] = {
    rows.map(r => r.int("city_id") -> r.strOption("city_name")).toMap
  }

  def cityNamesFor(rows: Vector[SQLRow]): Map[Int, String] = {
    rows
      .flatMap(r => {
        val cityNameO = r.strOption("city_name")
        val languageIdO = r.intOption("language_id")
        (cityNameO, languageIdO) match {
          case (Some(cityName), Some(languageId)) => Some((languageId, cityName))
          case _ => None
        }
      })
      .toMap
  }

  def stateNamesFor(rows: Vector[SQLRow]): Map[Int, String] = {
    rows
      .flatMap(r => {
        val stateNameO = r.strOption("state_name")
        val languageIdO = r.intOption("language_id")
        (stateNameO, languageIdO) match {
          case (Some(stateNameO), Some(languageId)) => Some((languageId, stateNameO))
          case _ => None
        }
      })
      .toMap
  }

  def countryNamesFor(rows: Vector[SQLRow]): Map[Int, String] = {
    rows
      .flatMap(r => {
        val countryNameO = r.strOption("country_name")
        val languageIdO = r.intOption("language_id")
        (countryNameO, languageIdO) match {
          case (Some(countryName), Some(languageId)) => Some((languageId, countryName))
          case _ => None
        }
      })
      .toMap
  }

  def toTable(ids: Traversable[Int]): SQLServerDataTable = {
    val table = new SQLServerDataTable
    table.setTvpName("dbo.id_table_type")
    table.addColumnMetadata("id", java.sql.Types.INTEGER)
    ids.foreach(x => table.addRow(Int.box(x)))
    table
  }

  def toTableInt(ids: Traversable[Int]): SQLServerDataTable = {
    val table = new SQLServerDataTable
    table.setTvpName("dbo.int_id_list")
    table.addColumnMetadata("id", java.sql.Types.INTEGER)
    ids.foreach(x => table.addRow(Int.box(x)))
    table
  }

  def toUUIDTable(hosts: Vector[UUID]): SQLServerDataTable = {
    val hostUuidTable = new SQLServerDataTable
    hostUuidTable.setTvpName("dbo.uuid_table_type")
    hostUuidTable.addColumnMetadata("uuid", java.sql.Types.CHAR)
    hosts.foreach(x => hostUuidTable.addRow(x))
    hostUuidTable
  }

  def toListInt(values: List[Int]): SQLServerDataTable = {
    val table = new SQLServerDataTable
    table.setTvpName("dbo.int_list")
    table.addColumnMetadata("value", java.sql.Types.INTEGER)
    values.foreach(value => table.addRow(Int.box(value)))
    table
  }
}
