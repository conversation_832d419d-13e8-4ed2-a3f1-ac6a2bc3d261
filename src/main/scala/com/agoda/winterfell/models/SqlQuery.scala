package com.agoda.winterfell.models

import com.agoda.dal.UUID
import com.agoda.dal.data.ByteString
import com.agoda.winterfell.hacks.JsonScalaIntEnumeration
import com.fasterxml.jackson.core.`type`.TypeReference

class SQLProxyQueryTypeType extends TypeReference[SQLProxyQueryType.type]

object SQLProxyQueryType extends Enumeration {
  type SQLProxyQueryType = Value
  val TRANSACTION = Value(1, "TRANSACTION")
  val SINGLE_QUERY = Value(2, "SINGLE_QUERY")
  val TRANSACTION_HASH = Value(3, "TRANSACTION_HASH")
}

object SqlQuery {

  case class Request(
      @JsonScalaIntEnumeration(classOf[SQLProxyQueryTypeType]) queryType: SQLProxyQueryType.SQLProxyQueryType,
      query: String,
      hashQuery: Option[String],
      requestId: UUID) {
    def decode: ByteString = ByteString.fromBase64String(query)
    def hashDecode: ByteString = hashQuery.map(ByteString.fromBase64String).getOrElse(ByteString.Empty)
  }

  case class Response(queryType: Int, result: String)

  case class ExecFallbackRequest(memberId: Int)

}
