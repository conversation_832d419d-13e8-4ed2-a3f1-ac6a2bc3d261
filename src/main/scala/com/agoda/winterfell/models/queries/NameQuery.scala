package com.agoda.winterfell.models.queries

import java.util.UUID
import com.agoda.dal.model.{BasicQuery, EntityStatus}
import com.agoda.dal.syntax.{Path, SearchBy}

case class NameQuery(firstName: Option[String], lastName: Option[String], whitelabelId: Short) extends BasicQuery

case class VerifiedContactUserQuery(
    userId: UUID,
    @SearchBy(Path.id.contactType) contactType: Short,
    @SearchBy(Path.id.whitelabelId) whitelabelId: Short)
    extends BasicQuery

case class UnVerifiedContactQuery(userId: UUID, contactType: Short) extends BasicQuery

case class PartnerMembershipByUserIdQuery(@SearchBy(Path.id.userId) userId: UUID) extends BasicQuery {
  def withDeleted: PartnerMembershipByUserIdQuery = new PartnerMembershipByUserIdQuery(userId) {
    override def entityStatuses: Set[EntityStatus] = super.entityStatuses + EntityStatus.SoftDeleted
  }
}
