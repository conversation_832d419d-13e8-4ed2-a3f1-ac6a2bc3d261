package com.agoda.winterfell.models.queries

import java.util.UUID
import com.agoda.dal.model.{BasicQuery, EntityStatus}

case class UserIdQuery(userId: UUID) extends BasicQuery {
  def withDeleted: UserIdQuery = new UserIdQuery(userId) {
    override def entityStatuses: Set[EntityStatus] = EntityStatus.All
  }
  def onlyDeleted: UserIdQuery = new UserIdQuery(userId) {
    override def entityStatuses: Set[EntityStatus] = EntityStatus.OnlyDeleted
  }
}
