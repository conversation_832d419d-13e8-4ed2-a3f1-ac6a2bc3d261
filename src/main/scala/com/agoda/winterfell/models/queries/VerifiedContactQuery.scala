package com.agoda.winterfell.models.queries

import com.agoda.dal.data.CollatedString
import com.agoda.dal.model.{BasicQuery, EntityStatus}
import com.agoda.dal.syntax.{Path, SearchBy}

case class VerifiedContactQuery(
    @SearchBy(Path.id.contactType) contactType: Short,
    @SearchBy(Path.id.contactValue) contactValue: CollatedString,
    @SearchBy(Path.id.whitelabelId) whitelabelId: Short)
    extends BasicQuery {
  def withDeleted: VerifiedContactQuery = new VerifiedContactQuery(contactType, contactValue, whitelabelId) {
    override def entityStatuses: Set[EntityStatus] = super.entityStatuses + EntityStatus.SoftDeleted
  }
  def onlyDeleted: VerifiedContactQuery = new VerifiedContactQuery(contactType, contactValue, whitelabelId) {
    override def entityStatuses: Set[EntityStatus] = Set(EntityStatus.SoftDeleted)
  }
}
