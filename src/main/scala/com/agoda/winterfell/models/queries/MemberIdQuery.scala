package com.agoda.winterfell.models.queries

import com.agoda.dal.model.{BasicQuery, EntityStatus}

case class MemberIdQuery(memberId: Int) extends BasicQuery {
  def withDeleted: MemberIdQuery = new MemberIdQuery(memberId) {
    override def entityStatuses: Set[EntityStatus] = EntityStatus.All
  }
  def onlyDeleted: MemberIdQuery = new MemberIdQuery(memberId) {
    override def entityStatuses: Set[EntityStatus] = EntityStatus.OnlyDeleted
  }
}
