package com.agoda
package winterfell.models

import java.util.UUID
import com.agoda.winterfell.common.AuthenticationType
import com.agoda.winterfell.common.AuthenticationType.AuthenticationType
import com.agoda.winterfell.constant.Constants
import com.agoda.winterfell.models.entities.{CustomerEntity, CustomerModel}
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.unified.{Customer, CustomerModelResponse, Role}
import com.agoda.winterfell.utils.CustomerRole

case class TokenizableCustomer(
    userId: UUID,
    memberId: Int,
    whitelabelId: Int,
    firstName: Option[String],
    lastName: Option[String],
    contacts: Map[AuthenticationType, String],
    roles: IndexedSeq[Role]) {
  def email: Option[String] = contacts.get(AuthenticationType.Basic)
  def displayName: String = (firstName.iterator ++ lastName.iterator).mkString(" ")
}

object TokenizableCustomer {
  def from(customer: CustomerModelResponse): TokenizableCustomer = {
    val contacts = customer.regisTypes.iterator.map { contact =>
      AuthenticationType(contact.authType) -> contact.value
    }.toMap
    TokenizableCustomer(
      customer.userId,
      customer.memberId,
      customer.whiteLabelId,
      Option(customer.firstName).filter(_.nonEmpty),
      Option(customer.lastName).filter(_.nonEmpty),
      contacts,
      roles = Vector.empty
    )
  }

  def from(customer: CustomerEntity): TokenizableCustomer = {
    TokenizableCustomer(
      customer.userId,
      customer.memberId,
      customer.whitelabelId,
      customer.firstName.filter(_.nonEmpty),
      customer.lastName.filter(_.nonEmpty),
      Map.empty,
      roles = Vector.empty
    )
  }

  def from(customer: Customer): TokenizableCustomer = {
    val contacts = Iterator(
      customer.email.map(AuthenticationType.Basic -> _.value),
      customer.phone.map(AuthenticationType.PhoneNumber -> _.value)
    ).flatten.toMap ++ customer.social.map(_.toMap).getOrElse(Map.empty)
    TokenizableCustomer(
      customer.userId,
      customer.memberId,
      customer.whiteLabelId.getOrElse(Constants.AgodaId),
      Option(customer.firstName).filter(_.nonEmpty),
      Option(customer.lastName).filter(_.nonEmpty),
      contacts,
      customer.roles
    )
  }

  @legacyRead
  def from(customer: CustomerModel): TokenizableCustomer = {
    val roles = CustomerRole
      .rolesFor(customer)(TempSingletonHolder.db)
      .iterator
      .flatMap {
        case (roleId, mappings) => mappings.iterator.flatMap(_._2.map(Role(roleId, "", _)))
      }
      .toVector
    val contacts = customer.mappings.iterator.map { contact =>
      AuthenticationType(contact.authType) -> contact.username
    }.toMap
    TokenizableCustomer(
      customer.userId,
      customer.memberId,
      customer.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId),
      Option(customer.memberDetails.firstName).filter(_.nonEmpty),
      Option(customer.memberDetails.lastName).filter(_.nonEmpty),
      contacts,
      roles
    )
  }
}

case class TokenizableInChatLoginCustomer(memberId: Int, bookingId: String, ccLast4Digits: Option[Int])
