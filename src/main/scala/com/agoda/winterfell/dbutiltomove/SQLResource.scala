package com.agoda.winterfell.dbutiltomove

import com.agoda.commons.rr.PositiveNumber.PositiveInt
import com.agoda.commons.rr.WeightedResource
import com.agoda.commons.rr.circuit._
import com.agoda.winterfell.utils.{CapiConfig, SQLResourceUnavailableError}
import com.zaxxer.hikari.HikariDataSource

import scala.concurrent.duration.{Duration, FiniteDuration}

// define your resource
case class SQLResource(var dataSource: HikariDataSource, group: String, id: String, initialWeight: PositiveInt)
    extends WeightedResource

class WeightedRoundRobinSelectorCB(private val allResources: List[WeightedResource]) {
  // Number of failures required to mark a resource as dead/open_circuit
  val failureLimit = CapiConfig.config.getInt("roundRobin.cbFailureLimit")
  // Delay in between trying again connecting a dead/open_circuit resource
  val closeCircuitDelay = Duration(CapiConfig.config.getString("roundRobin.closeCircuitDelay"))
    .asInstanceOf[FiniteDuration]
  // Number of weight unit to decrement for a resource in case of failure - currently as per the implementation in common it divides the weight by this factor
  val decWeightOnFailure = CapiConfig.config.getInt("roundRobin.decrementOnFailure")
  // Number of weight unit to increment/plus for a resource in case of a success
  val incWeightOnSuccess = CapiConfig.config.getInt("roundRobin.incrementOnSuccess")

  val circuitConfig = CircuitConfig(
    failureLimit = failureLimit,
    closeCircuitDelay = StaticRetryDelay(closeCircuitDelay),
    isResourceError = SQLResourceUnavailableError)
  val apiCBRoundRobin =
    CircuitRoundRobin(allResources, ex => Some(decWeightOnFailure), Some(incWeightOnSuccess), circuitConfig)

  def getCBRoundRobin(): CircuitRoundRobin[WeightedResource] = {
    apiCBRoundRobin
  }
}
