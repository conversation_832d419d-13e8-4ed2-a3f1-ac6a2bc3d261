package com.agoda.winterfell.dbutiltomove

import java.sql.{<PERSON><PERSON>b, Connection, PreparedStatement, ResultSet, ResultSetMetaData, SQLException, Timestamp}
import java.util.concurrent.TimeUnit
import java.util.{Date, UUID}
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.dal.data.ByteString
import com.agoda.winterfell.dbutiltomove.QueryLogger.theServerName
import com.agoda.winterfell.internal.{PreColumn, SQLRow}
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.QueryUtil
import com.agoda.winterfell.models.entities.SettableStatement
import com.agoda.winterfell.utils._
import com.agoda.winterfell.{Constants, Implicits}
import com.google.common.base.CaseFormat
import com.microsoft.sqlserver.jdbc.{SQLServerConnection, SQLServerDataTable, SQLServerPreparedStatement}
import com.zaxxer.hikari.pool.ProxyConnection
import net.sourceforge.jtds.jdbc.JtdsConnection
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.collection.immutable.ListMap
import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{blocking, ExecutionContext, Future, TimeoutException}
import scala.io.Source
import scala.language.{dynamics, higherKinds, implicitConversions, postfixOps}
import scala.reflect.ClassTag
import scala.util.{Failure, Success, Try}

trait RSHelper {

  import scala.language.implicitConversions

  def m[A, B, T[X] <: Traversable[X], M[Y, Z] <: collection.Map[Y, Z]](m: M[A, T[B]]): M[A, T[B]] = m

  implicit def mapO[B](implicit o: Option[SQLRow], f: SQLRow ⇒ B): Option[B] = o.map(f)

  implicit def mapV[B](implicit o: Vector[SQLRow], f: SQLRow ⇒ B): Vector[B] = o.map(f)

  def testRow(map: Map[String, Any]): SQLRow = {
    SQLRow(map.map((tuple: (String, Any)) => (tuple._1, tuple._2.asInstanceOf[AnyRef])))
  }

  def c(name: String)(implicit row: SQLRow) = PreColumn(name)

  private def getCol[T](ctx: SQLRow, name: String): AnyRef = {
    ctx.map.getOrElse(
      name,
      throw new IllegalStateException(s"No column found for $name, columns were ${ctx.map.keys.mkString(",")}"))
  }

  def reportNullError[T](name: String, tag: ClassTag[T]): Nothing = {
    throw new IllegalStateException(
      s"Column $name expected a not null ${tag.runtimeClass.getSimpleName} but result was null")
  }

  def reportTypeMismatch[T](name: String, tag: ClassTag[T], v: AnyRef): Nothing = {
    throw new IllegalStateException(
      s"Column $name expected a ${tag.runtimeClass.getSimpleName} but result was ${v.getClass.getSimpleName}")
  }

  def checkResult[T](name: String)(implicit tag: ClassTag[T], ctx: SQLRow): T = {
    def mapOp(x: AnyRef): Option[T] = {
      if (x == null) None
      else
        x match { //No runtime type information to check against null ... could potentially delay finding mismatched column. JDBC Metadata might be better
          case expected: T => Option(expected)
          case bigD: java.math.BigDecimal =>
            if (tag.runtimeClass == classOf[scala.math.BigDecimal]) Some(scala.math.BigDecimal(bigD).asInstanceOf[T])
            else reportTypeMismatch(name, tag, x)
          case byte: java.lang.Byte =>
            if (tag.runtimeClass == classOf[Int]) Some(byte.toInt.asInstanceOf[T]) else reportTypeMismatch(name, tag, x)
          case short: java.lang.Short =>
            if (tag.runtimeClass == classOf[Int]) Some(short.toInt.asInstanceOf[T])
            else reportTypeMismatch(name, tag, x)
          case _ =>
            throw new IllegalStateException(
              s"column $name expected a ${tag.runtimeClass.getSimpleName} but found ${x.getClass.getSimpleName}")
        }
    }

    mapOp(getCol(ctx, name)).getOrElse(reportNullError(name, tag))
  }

  def checkOptionalResult[T](name: String)(implicit tag: ClassTag[T], ctx: SQLRow): Option[T] = {
    val rawOption = getCol(ctx, name)
    Option(rawOption).map {
      case expected: T => expected
      case x @ (bigD: java.math.BigDecimal) =>
        if (tag.runtimeClass == classOf[scala.math.BigDecimal]) scala.math.BigDecimal(bigD).asInstanceOf[T]
        else reportTypeMismatch(name, tag, x)
      case x @ (short: java.lang.Short) =>
        if (tag.runtimeClass == classOf[Int]) short.toInt.asInstanceOf[T] else reportTypeMismatch(name, tag, x)
      case x => reportTypeMismatch(name, tag, x)
    }
  }

  implicit def cToLong(c2: PreColumn)(implicit ctx: SQLRow): Long = ctx.long(c2.name)
  implicit def cToInt(c2: PreColumn)(implicit ctx: SQLRow): Int = ctx.int(c2.name)
  implicit def cToStr(c2: PreColumn)(implicit ctx: SQLRow): String = ctx.str(c2.name)
  implicit def cToDouble(c2: PreColumn)(implicit ctx: SQLRow): Double = ctx.double(c2.name)
  implicit def cToShort(c2: PreColumn)(implicit ctx: SQLRow): Short = ctx.short(c2.name)
  implicit def cToBool(c2: PreColumn)(implicit ctx: SQLRow): Boolean = ctx.boolean(c2.name)
  implicit def cToUuid(c2: PreColumn)(implicit ctx: SQLRow): UUID = ctx.uuid(c2.name)

  implicit def cToBytes(c2: PreColumn)(implicit ctx: SQLRow): scala.Array[Byte] = ctx.bytes(c2.name)
  implicit def cToDate(c2: PreColumn)(implicit ctx: SQLRow): Date = ctx.date(c2.name)
  implicit def cToBigD(c2: PreColumn)(implicit ctx: SQLRow): BigDecimal = ctx.bigDecimal(c2.name)

  implicit def cToLongO(c2: PreColumn)(implicit ctx: SQLRow): Option[Long] = ctx.longOption(c2.name)
  implicit def cToIntO(c2: PreColumn)(implicit ctx: SQLRow): Option[Int] = ctx.intOption(c2.name)
  implicit def cToStrO(c2: PreColumn)(implicit ctx: SQLRow): Option[String] = ctx.strOption(c2.name)
  implicit def cToDoubleO(c2: PreColumn)(implicit ctx: SQLRow): Option[Double] = ctx.doubleOption(c2.name)
  implicit def cToShortO(c2: PreColumn)(implicit ctx: SQLRow): Option[Short] = ctx.shortOption(c2.name)
  implicit def cToBoolO(c2: PreColumn)(implicit ctx: SQLRow): Option[Boolean] = ctx.booleanOption(c2.name)
  implicit def cToBigDO(c2: PreColumn)(implicit ctx: SQLRow): Option[BigDecimal] = ctx.bigDecimalOption(c2.name)
  implicit def cToDateO(c2: PreColumn)(implicit ctx: SQLRow): Option[Date] = ctx.dateOption(c2.name)
  implicit def cToUuidO(c2: PreColumn)(implicit ctx: SQLRow): Option[UUID] = ctx.uuidOption(c2.name)

  implicit def cToDateTime(c: PreColumn)(implicit row: SQLRow): DateTime = new DateTime(row.date(c.name))
  implicit def cToOptionDateTime(c: PreColumn)(implicit row: SQLRow): Option[DateTime] =
    cToDateO(c).map(new DateTime(_))
  implicit def cToDateTimeO(c2: PreColumn)(implicit ctx: SQLRow): Option[DateTime] = ctx.dateTimeOption(c2.name)
}

case class Query(
    sql: String,
    params: Seq[Any] = Nil,
    timeout: FiniteDuration = Constants.QueryTimeout,
    statement: Option[SettableStatement] = None) {
  require(timeout.toMillis > 30, "Query timeout should be more than 30 millis")

  private val storeProcPattern = "EXEC\\s+([\\w\\.\\[\\]]*)".r
  private val tempStoreProcPattern = "EXEC\\s+(.*)(\\s|$)".r

  def storeProcName: String = storeProcPattern.findFirstMatchIn(sql.replaceAll("#", "")).map(_.group(1)).getOrElse(sql)
  def tempStoreProcOption: Option[String] =
    tempStoreProcPattern.findFirstMatchIn(sql.replaceAll("#", "")).map(_.group(1))
}

object QueryLogger {

  def theServerName(connection: Connection): (String, String) = {
    Try {
      val (proxy, name) = connection match {
        case connection1: ProxyConnection => (connection1, "transaction")
        case _ =>
          val conHack1: AutoCleanConnection = connection.asInstanceOf[AutoCleanConnection]
          (conHack1.connection.asInstanceOf[ProxyConnection], conHack1.groupName)
      }

      val conHackF2 = classOf[ProxyConnection].getDeclaredField("delegate")
      conHackF2.setAccessible(true)
      val value = conHackF2.get(proxy)
      value match {
        case jtds: JtdsConnection =>
          val conHackF3 = classOf[JtdsConnection].getDeclaredField("serverName")
          conHackF3.setAccessible(true)
          (name, conHackF3.get(jtds).asInstanceOf[String])
        case s: SQLServerConnection =>
          val conHackF3 = classOf[SQLServerConnection].getDeclaredField("trustedServerNameAE")
          conHackF3.setAccessible(true)
          (name, conHackF3.get(s).asInstanceOf[String])
      }
    } getOrElse ("?", "?")
  }
}

object SpamLog {
  val queryLogger: Logger = LoggerFactory.getLogger("query")
  val bigQueryLogger: Logger = LoggerFactory.getLogger("big-query")
}

case class MetricData(id: String, value: Long, tags: Map[String, String])

trait DBMetrics {
  self: ExtendedDBSupport =>

  def time[A](stmt: PreparedStatement, q: Query, name: String, group: String)(f: Query => A): A = {
    val spName = q.storeProcName
      .replaceAll("\\]", "")
      .replaceAll("\\[", "")
      .replaceAll("\\.", "")
      .replaceAll("dbo", "")
      .replaceAll("\\?", "")
      .replaceAll(" ", "")
    val metric = MetricData(
      "database",
      0L,
      Map(
        "serverName" -> name,
        "sp" -> spName,
        "endpoint" -> Profiler.endPointTeamName.value.map(_._1).getOrElse("Unknown"),
        "teamName" -> Profiler.endPointTeamName.value.map(_._2).getOrElse("Unknown"),
        "group" -> (if (DBGroups.isMDCGroup(group)) "mdc" else group),
        "rawGroup" -> group
      )
    )
    val params =
      if (spName.startsWith("save_customer")) None
      else
        Some(
          q.params.view
            .map {
              case string: String => if (QueryUtil.isEncrypted(string)) string else "?"
              case param => param
            }
            .mkString(","))
    timerWithError(stmt, metric.id, metric.tags, name, spName, params)(f(q))(Implicits.globalExecutor)
  }

  def timerWithError[A](
      stmt: PreparedStatement,
      metricName: String,
      tagMap: Map[String, String],
      name: String,
      spName: String,
      params: Option[String])(action: => A)(implicit context: ExecutionContext): A = {
    val start = System.currentTimeMillis()
    val result = Try(action)
    result match {
      case Failure(exception) =>
        val elapsed = System.currentTimeMillis() - start
        SpamLog.queryLogger.info(s"$name->:$spName [$params] failed:($elapsed)")
        val tags = tagMap + ("success" -> result.isSuccess.toString, "isBigQuery" -> "false")
        val basicTags = Map("elapsed" -> elapsed)
        MeasurementHelperHolder.create("winterfell.db", tags, elapsed)
        CapiLogMessage(
          "capi.db",
          LogLevel.ERROR,
          tags ++ params.map("params" -> _),
          basicTags,
          exception = Some(exception),
          exceptionMessage = Some(exception.getMessage))
        throw exception
      case Success(response) =>
        val elapsed = System.currentTimeMillis() - start
        val size = response match {
          case vvSQL: Vector[Vector[SQLRow]] => vvSQL.map(_.size).sum
          case vSQL: Vector[SQLRow] => vSQL.size
          case _ => 1
        }
        val hasResult = result.toOption.exists(_ != Vector.empty)
        val isBigQuery = size > CapiConfig.bigQuerySize
        if (isBigQuery)
          SpamLog.bigQueryLogger.info(s"$name->:$spName [$params] found:$hasResult, size:$size :($elapsed)")
        else
          SpamLog.queryLogger.info(s"$name->:$spName [$params] found:$hasResult, size:$size :($elapsed)")
        val tags = tagMap + ("success" -> result.isSuccess.toString, "isBigQuery" -> isBigQuery.toString)
        val basicTags = Map("elapsed" -> elapsed, "size" -> size.toLong)
        MeasurementHelperHolder.create("winterfell.db", tags, elapsed)
        if (elapsed > CapiConfig.spamElapsedThreshold)
          CapiLogMessage("capi.db", LogLevel.INFO, tags ++ params.map("params" -> _), basicTags)
        response
    }
  }

//  def time[A](sql: String, params: Any*)(f: Query => A): A = time(Query(sql, params))(f)
}

trait ReportingDBSupport extends ExtendedDBSupport

/**
  * TODO : rewrite this class again.
  * It's actually not all that great atm.
  */
trait ExtendedDBSupport extends DBMetrics {

  import DBGroups._

  case class MagicBeanHolder(r: SQLRow)

  def query(sql: String, params: Any*) = Query(sql, params)

  def db: DBPlugin

  /** @see [[https://docs.microsoft.com/en-us/sql/connect/jdbc/using-basic-data-types?view=sql-server-2017#data-type-mappings]] */
  private def getStatement(q: Query)(implicit con: Connection): PreparedStatement = {
    val MIN_JDBC_QUERY_TIMEOUT_IN_MS = 1000
    val outStmt = q.statement.map(_.toJDBC).getOrElse {
      val sql = q.sql.toLowerCase.trim match {
        case s
            if s.startsWith("exec ") || s.startsWith("select ") || s.startsWith("insert ") || s
              .startsWith("update ") || s.startsWith("delete ") || s.startsWith("declare ") =>
          q.sql
        case _ => s"EXEC ${q.sql}"
      }
      val stmt = con.prepareStatement(sql)

      q.params.zipWithIndex foreach {
        //TODO should support LocalDate
        case (p: Array[Byte], i) => stmt.setBytes(i + 1, p)
        case (p: ByteString, i) => stmt.setBytes(i + 1, p.unsafeArray)
        case (p: DateTime, i) => stmt.setTimestamp(i + 1, new Timestamp(p.getMillis))
        case (p: java.time.Instant, i) => stmt.setTimestamp(i + 1, new Timestamp(p.toEpochMilli))
        case (p: UUID, i) => stmt.setObject(i + 1, p.toString, microsoft.sql.Types.GUID)
        case (p: Option[_], i) =>
          p match {
            case Some(bytes: Array[Byte]) => stmt.setBytes(i + 1, bytes)
            case Some(bytes: ByteString) => stmt.setBytes(i + 1, bytes.unsafeArray)
            case Some(date: DateTime) => stmt.setTimestamp(i + 1, new java.sql.Timestamp(date.getMillis))
            case Some(date: java.time.Instant) => stmt.setTimestamp(i + 1, new java.sql.Timestamp(date.toEpochMilli))
            case Some(uuid: UUID) => stmt.setObject(i + 1, uuid.toString, microsoft.sql.Types.GUID)
            case Some(char: Char) => stmt.setString(i + 1, char.toString)
            case Some(bool: Boolean) => stmt.setBoolean(i + 1, bool)
            case Some(a) => stmt.setObject(i + 1, a)
            case None => stmt.setObject(i + 1, null)
          }
        case (p: Character, i) => stmt.setString(i + 1, p.toString)
        case (p: BigDecimal, i) ⇒ stmt.setBigDecimal(i + 1, p.bigDecimal)
        case (p: SQLServerDataTable, i) ⇒
          stmt.unwrap(classOf[SQLServerPreparedStatement]).setStructured(i + 1, p.getTvpName, p)
        case (p, i) => stmt.setObject(i + 1, p)
      }
      //Override min query timeout if specified less than 1 sec, as JDBC setQueryTimeout supports only int/1sec minimum
      val queryTimeOut = if (q.timeout.toMillis < MIN_JDBC_QUERY_TIMEOUT_IN_MS) 1 else q.timeout.toSeconds.toInt
      stmt.setQueryTimeout(queryTimeOut)
      stmt
    }
    outStmt
  }

  implicit class RichQuery(q: Query) {

    def execute(): Unit = executeOn(WriteGroup)

    def executeOn(group: String): Unit = executeWithOrOn(group)

    def executeWithOrOn(group: String): Unit = {
      if (DBGroups.MdcContexts.contains(group)) {
        db.withMDCConnection(group) { implicit c: Connection =>
          retrieve(group)
        }
      } else {
        db.withConnection(Some(group)) { implicit c: Connection =>
          retrieve(group)
        }
      }
    }

    def rowsSetFrom(groupName: String): Vector[Vector[SQLRow]] = {
      if (DBGroups.MdcContexts.contains(groupName)) {
        db.withMDCConnection(groupName) { implicit c: Connection =>
          sequenceOfRows(groupName)
        }
      } else {
        db.withConnection(Some(groupName)) { implicit c: Connection =>
          sequenceOfRows(groupName)
        }
      }
    }

    def rowsFromGroup(groupName: String): Vector[SQLRow] = {
      if (DBGroups.MdcContexts.contains(groupName)) {
        db.withMDCConnection(groupName) { implicit c: Connection =>
          retrieveRows(groupName)
        }
      } else {
        db.withConnection(Some(groupName)) { implicit c: Connection =>
          retrieveRows(groupName)
        }
      }
    }

    def rowFrom(group: String): Option[SQLRow] = {
      db.withConnection(Some(group)) { implicit c: Connection =>
        zeroOrOneRow(group)
      }
    }

    private def zeroOrOneRow(group: String)(implicit con: Connection): Option[SQLRow] = {
      val toVector: Option[Vector[Map[String, AnyRef]]] = retrieve(group).headOption
      if (toVector.size > 1) {
        throw new IllegalStateException(s"Expected a single result row but was ${toVector.size}")
      }
      toVector.flatMap(_.map(SQLRow).headOption)
    }

    private def retrieveRows(group: String)(implicit con: Connection): Vector[SQLRow] = {
      val r = retrieve(group)
      if (r.isEmpty) Vector.empty else r.head.map(SQLRow)
    }

    private def sequenceOfRows(group: String)(implicit con: Connection): Vector[Vector[SQLRow]] =
      retrieve(group).map(_.map(SQLRow))

    private def addTempSpIfNeed[T](stmt: PreparedStatement)(f: PreparedStatement => T)(implicit con: Connection): T = {
      Try(f(stmt)).recoverWith {
        case e: SQLException
            if e.getMessage.contains("Could not find stored procedure '#") && q.tempStoreProcOption.isDefined =>
          con.createStatement().execute(spToFile(q.tempStoreProcOption.get.split(" ").head.trim))
          Try(f(stmt))
      } get
    }

    private def timeWith[T](f: PreparedStatement => T, group: String)(implicit con: Connection): T = {
      val (_, serverName: String) = theServerName(con)

      Profiler.track(q.storeProcName + "-" + serverName) {
        val stmt = getStatement(q)
        stmt.unwrap(classOf[SQLServerPreparedStatement]).setResponseBuffering("full")
        val r = time(stmt, q, QueryLogger.theServerName(con)._2, group = group)(_ => {
          addTempSpIfNeed(stmt)(f)
        })
        stmt.close()
        r
      }
    }

    private def retrieve(group: String)(implicit con: Connection) = blocking {
      timeWith(
        { stmt =>
          statementToResultVectors(stmt, q.timeout)
        },
        group
      )
    }
  }

  //TODO : eventually we should support proper streaming
  private def statementToResultVectors(
      stmt: PreparedStatement,
      timeout: FiniteDuration): Vector[Vector[Map[String, AnyRef]]] = {
    def executable: ResultSet =
      MeasuredFuture.successful(stmt.executeQuery()).waitForDuration(timeout, "statementToResultVectors")
    val result: ResultSet = Try(executable).recoverWith {
      case s: SQLException if s.getMessage.contains("return a result set") =>
        return Vector.empty
      case ex: TimeoutException => {
        //Cancel the statement
        if (!stmt.isClosed) stmt.cancel()
        throw ex
      }
    }.get

    def convertToMap(theRs: ResultSet) = {
      val warning = theRs.getWarnings
      if (warning != null) {
        SpamLog.queryLogger.warn(s"Warning for $stmt : $warning")
      }
      val data: ResultSetMetaData = theRs.getMetaData
      val seq = for (i <- 1 to data.getColumnCount) yield {
        val col = data.getColumnName(i)
        /* TODO currently everything is boxed for primitives. If we changed the Map to Any instead of AnyRef we could work
           around this but we'd need to ensure nullability etc was properly checked. This code makes heavy use of nulls */
        val theVal = theRs.getObject(i)
        val res = theVal match {
          case clob: Clob => if (clob != null) clob.getSubString(1L, clob.length().toInt) else null
          case _ => theVal
        }
        val conv = data.getColumnClassName(i) match {
          case "java.math.BigDecimal" =>
            Option(res.asInstanceOf[java.math.BigDecimal]).map(scala.math.BigDecimal(_)).orNull
          case "java.sql.Timestamp" => Option(res.asInstanceOf[Timestamp]).map(x => new DateTime(x)).orNull
          case "java.sql.Date" => Option(res.asInstanceOf[Date]).map(x => new DateTime(x)).orNull
          //case "java.sql.Time" => Option(res.asInstanceOf[Time]).map(x => new LocalTime(x.getHours, x.getMinutes,x.getSeconds)).orNull //not used atm and deprecated
          case _ if data.getColumnTypeName(i) == "uniqueidentifier" =>
            Option(res).map(s => UUID.fromString(s.toString)).orNull
          case _ => res
        }
        val maybeDecrypted = conv match {
          case s: String =>
            //TODO temporary hack!
            if (col.equals("display_name") || col.endsWith("DisplayName") || col.endsWith("displayName")) {
              s.split(" ").map(t => QueryUtil.decrypt(t)).mkString(" ")
            } else {
              QueryUtil.decrypt(s)
            }
          case x => x
        }
        (col, maybeDecrypted)
      }
      ListMap(seq: _*)
    }

    def individualStream(innerRs: ResultSet) =
      new Iterator[Map[String, AnyRef]] {
        def hasNext: Boolean = {
          innerRs.next()
        }

        def next(): ListMap[String, AnyRef] = {
          convertToMap(innerRs)
        }
      }.toVector

    val fixed = Vector(individualStream(result))
    fixed ++ new Iterator[Vector[Map[String, AnyRef]]] {
      override def hasNext: Boolean = stmt.getMoreResults
      override def next(): Vector[Map[String, AnyRef]] = individualStream(stmt.getResultSet)
    }.toVector
  }

  def spToFile(spName: String): String = {
    val filePath =
      Option(System.getProperty("sqlDir", null)).map(x => s"$x/$spName.sql").getOrElse(s"src/main/sql/$spName.sql")
    Source.fromFile(filePath).getLines().mkString("\n")
  }
}
