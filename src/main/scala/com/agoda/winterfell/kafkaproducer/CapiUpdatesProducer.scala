package com.agoda.winterfell.kafkaproducer

import com.agoda.adp.messaging.exception.MessagingException
import com.agoda.adp.messaging.scala.message.Message
import com.agoda.adp.messaging.sender.SendResult
import com.agoda.winterfell.Constants
import com.agoda.winterfell.kafkaproducer.models.{
  CapiMemberRegistrationMessage,
  CapiMemberUpdateMessage,
  CapiPropertyLoginUserMessage,
  CapiSocialAuthenticateMessage
}
import com.agoda.winterfell.metrics.{CapiLogMessage, CapiMeasurementMessage}
import com.agoda.winterfell.output.MemberContactIdentifiers

import java.util.UUID
import scala.util.Try

object CapiUpdatesProducer {

  //send updates in kafka for partner communications team
  def sendPropertyLoginUser(userId: UUID, propertyId: Int, roleId: UUID, updatedBy: UUID, source: String): Unit = {
    val message = CapiPropertyLoginUserMessage(userId, propertyId, roleId, updatedBy)
    CapiMeasurementMessage(
      "capi.kafkaproducer.propertyloginuser",
      Map(
        "userId" -> userId.toString,
        "propertyId" -> propertyId.toString,
        "roleId" -> roleId.toString,
        "source" -> source))
    CapiLogMessage(
      "capi.kafkaproducer.propertyloginuser",
      stringTags = Map("userId" -> userId.toString, "propertyId" -> propertyId.toString, "roleId" -> roleId.toString))
    sendToKafka(message)
  }

  def sendMemberOnSuccessfulRegistration(
      memberId: Int,
      whitelabelId: Int,
      isNewsletter: Option[Boolean],
      memberContacts: List[MemberContactIdentifiers],
      sendToKafkaFn: Message[_] => Try[Unit] = sendToKafkaAsync,
      logError: (String, String, Throwable) => Unit = CapiLogMessage.error): Unit = {
    val loggerName = "capi.kafkaproducer.memberregistration"
    val message = CapiMemberRegistrationMessage(memberId, whitelabelId, isNewsletter, memberContacts)
    sendToKafkaFn(message)
      .fold(
        throwable => {
          CapiMeasurementMessage(loggerName, Map("whitelabelId" -> whitelabelId.toString, "status" -> "failed"))
          logError(
            loggerName,
            s"sendMemberOnSuccessfulRegistration to kafka failed for memberId: $memberId, wl: $whitelabelId",
            throwable)
        },
        _ => CapiMeasurementMessage(loggerName, Map("whitelabelId" -> whitelabelId.toString, "status" -> "success"))
      )
  }

  def sendMemberOnSuccessfulUpdate(
      memberId: Int,
      whitelabelId: Int,
      isNewsletter: Option[Boolean],
      source: Option[String],
      sendToKafkaFn: Message[_] => Try[Unit] = sendToKafkaAsync,
      logError: (String, String, Throwable) => Unit = CapiLogMessage.error): Unit = {
    val loggerName = "capi.kafkaproducer.memberupdate"
    val message = CapiMemberUpdateMessage(memberId, whitelabelId, isNewsletter, source)
    sendToKafkaFn(message)
      .fold(
        throwable => {
          CapiMeasurementMessage(loggerName, Map("whitelabelId" -> whitelabelId.toString, "status" -> "failed"))
          logError(
            loggerName,
            s"sendMemberOnSuccessfulUpdate to kafka failed for memberId: $memberId, wl: $whitelabelId",
            throwable)
        },
        _ => CapiMeasurementMessage(loggerName, Map("whitelabelId" -> whitelabelId.toString, "status" -> "success"))
      )
  }

  def sendSocialAuthenticateMessage(
      correlationId: String,
      whitelabelId: Int,
      memberId: Int,
      origin: Option[String],
      authenticationTypeId: Int,
      queryContext: Map[String, String],
      sendToKafkaFn: Message[_] => Try[Unit] = sendToKafkaAsync,
      logError: (String, String, Throwable) => Unit = CapiLogMessage.error): Unit = {
    val loggerName = "capi.kafkaproducer.socialauthenticate"
    val metricTags = Map(
      "whitelabelId" -> whitelabelId.toString,
      "authenticationTypeId" -> authenticationTypeId.toString
    )
    val message = CapiSocialAuthenticateMessage(
      correlationId = correlationId,
      whitelabelId = whitelabelId,
      memberId = memberId,
      origin = origin,
      authenticationTypeId = authenticationTypeId,
      queryContext = queryContext
    )
    sendToKafkaFn(message)
      .fold(
        throwable => {
          CapiMeasurementMessage(loggerName, metricTags ++ Map("status" -> "failed"))
          logError(
            loggerName,
            s"sendSocialAuthenticateMessage to kafka failed for memberId: $memberId, wl: $whitelabelId, authenticationTypeId: $authenticationTypeId",
            throwable
          )
        },
        _ => CapiMeasurementMessage(loggerName, metricTags ++ Map("status" -> "success"))
      )
  }

  private def sendToKafka(message: Message[_]) {
    //do sync call for reliability
    message.send(Constants.AdpApiKey)
  }

  private def sendToKafkaAsync(message: Message[_]) = {
    Try {
      message.sendAsync(Constants.AdpApiKey) match {
        case SendResult.OK =>
        case sr: SendResult => throw new MessagingException(sr.toString)
      }
    }
  }
}
