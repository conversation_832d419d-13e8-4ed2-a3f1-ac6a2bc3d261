package com.agoda.winterfell.kafkaproducer.models

import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.adp.messaging.scala.message.Message
import com.agoda.winterfell.output.MemberContactIdentifiers

@SchemaComment("This is the case class which send new member registration event to hadoop by ADP messaging")
case class CapiMemberRegistrationMessage(
    @SchemaComment("Customer memberId") memberId: Int,
    @SchemaComment("Customer whitelabelId from CapiRequestContextData") whitelabelId: Int,
    @SchemaComment("Customer newsletter preference") isNewsletter: Option[Boolean],
    @SchemaComment("LIst of active member contacts' identifiers") activeMemberContacts: List[MemberContactIdentifiers])
    extends Message[CapiMemberRegistrationMessage]
