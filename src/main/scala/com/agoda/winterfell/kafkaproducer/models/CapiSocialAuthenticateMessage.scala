package com.agoda.winterfell.kafkaproducer.models

import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.adp.messaging.scala.message.Message

case class CapiSocialAuthenticateMessage(
    correlationId: String,
    @SchemaComment("Customer whitelabelId from CapiRequestContextData") whitelabelId: Int,
    @SchemaComment("Customer memberId") memberId: Int,
    @SchemaComment("Customer origin") origin: Option[String],
    @SchemaComment("CAPI Authentication type ID") authenticationTypeId: Int,
    @SchemaComment("Query context from OAuth request") queryContext: Map[String, String])
    extends Message[CapiSocialAuthenticateMessage]
