package com.agoda.winterfell.kafkaproducer.models

import com.agoda.adp.messaging.scala.avro.annotation.SchemaComment
import com.agoda.adp.messaging.scala.message.Message

@SchemaComment("This is the case class which send new member registration event to hadoop by ADP messaging")
case class CapiMemberUpdateMessage(
    @SchemaComment("Customer memberId") memberId: Int,
    @SchemaComment("Customer whitelabelId from CapiRequestContextData") whitelabelId: Int,
    @SchemaComment("Customer newsletter preference") isNewsletter: Option[Boolean],
    @SchemaComment("Source of event") source: Option[String])
    extends Message[CapiMemberUpdateMessage]
