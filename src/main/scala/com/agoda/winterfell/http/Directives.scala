package com.agoda.winterfell.http

import java.util.UUID
import akka.http.scaladsl.marshalling.ToResponseMarshallable
import akka.http.scaladsl.model.headers.{`Remote-Address`, RawHeader}
import akka.http.scaladsl.model.{HttpEntity, HttpResponse, ResponseEntity, StatusCodes}
import akka.http.scaladsl.server.Directives.{as, complete, entity, extractRequestContext, mapResponse, path}
import akka.http.scaladsl.server._
import akka.http.scaladsl.server.directives.BasicDirectives
import akka.http.scaladsl.server.directives.RespondWithDirectives.respondWithHeaders
import akka.http.scaladsl.unmarshalling.Unmarshaller
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.usercontext.{UserContextParser, UserContextParserImpl}
import com.agoda.winterfell.constant.{AdditionalHeaderNames, PrivateCloudHeaderNames, WebgateHeaderNames}
import com.agoda.winterfell.helper.WhiteLabelHelper
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.metrics.{CapiLogMessage, CapiMeasurementMessage}
import com.agoda.winterfell.models.{CapiRequestContextData, QueryUtil}
import com.agoda.winterfell.output.{
  MemberDetails,
  MemberUserContextResponse,
  RewardsMemberUserInfoForLogin,
  TrustedHostInfo
}
import com.agoda.winterfell.security.ApiPolicy
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.services.jwt.TokenHelper
import com.agoda.winterfell.services.jwt.TokenHelper.TokenBox
import com.agoda.winterfell.services.jwt.{JWTUtilServer, TokenClaimsHolder}
import com.agoda.winterfell.unified.UnifiedCustomerService
import com.agoda.winterfell.utils.CapiConsul.ApiKey
import com.agoda.winterfell.utils.{CapiConsul, DalContext, EndpointUtil, ServiceAuthenticator, TeamNameUtil}
import com.agoda.winterfell.{AuthException, Constants, UnauthorizedException}
import org.apache.kafka.common.utils.Java
import sttp.model.HeaderNames

import java.time.{Instant, LocalDateTime, ZoneId}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

object Directives extends CapiRequestContextDirectives with CapiResponseMappingDirectives

/**
  * Get CapiRequestContextData from headers and pass to the route block.
  */
trait CapiRequestContextDirectives {
  def convertRequestContextToCapiRequestContext(ctx: RequestContext): CapiRequestContextData = {
    def getHeaderCaseInsensitive(name: String) =
      ctx.request.headers.find(x => x.name().equalsIgnoreCase(name)).map(_.value())
    val endpoint = EndpointUtil.shortenUri(ctx.request.uri.path.toString())
    val method = ctx.request.method.value
    val requestId =
      getHeaderCaseInsensitive(WebgateHeaderNames.REQUEST_ID).orElse(getHeaderCaseInsensitive("X-Request-ID"))
    val clientMachine = getHeaderCaseInsensitive("X-Agoda-ClientMachine")
    val clientApp = getHeaderCaseInsensitive("X-Agoda-ClientApp")
    val whiteLabelToken = getHeaderCaseInsensitive("X-WhiteLabel-Token")
    val chainSlug = getHeaderCaseInsensitive("X-Chain-Slug")
    val capiClientVersion = getHeaderCaseInsensitive("X-Agoda-CustomerApiClientVersion")
    val apiKeyHeader = getHeaderCaseInsensitive("X-API-Key")
    val partnerClaim = getHeaderCaseInsensitive(AdditionalHeaderNames.PARTNER_CLAIM)
    val mdcGroup = getHeaderCaseInsensitive("mdcGroup")
    val xAPIMock = getHeaderCaseInsensitive("X-API-Mock")
    val xAABMock = getHeaderCaseInsensitive("X-AAB-Mock")
    val isMock = xAABMock.map(x => x.toLowerCase.equals("true"))

    val capiMfaToken = getHeaderCaseInsensitive("X-CAPI-Mfa-Token")
    val correlationId = getHeaderCaseInsensitive(WebgateHeaderNames.CORRELATION_ID).orElse(
      getHeaderCaseInsensitive("X-Agoda-CorrelationId").orElse(Some(java.util.UUID.randomUUID.toString))
    )
    val sessionId = getHeaderCaseInsensitive("X-Agoda-SessionId").orElse(Some(java.util.UUID.randomUUID.toString))
    val agInitiatorIp = getHeaderCaseInsensitive(WebgateHeaderNames.INITIATOR_IP)
    val agUserId = getHeaderCaseInsensitive(WebgateHeaderNames.USER_ID)
    val agEnv = getHeaderCaseInsensitive(WebgateHeaderNames.AG_ENV)
    val agOrigin = getHeaderCaseInsensitive(WebgateHeaderNames.ORIGIN)
    val agAid = getHeaderCaseInsensitive(WebgateHeaderNames.AG_AID)
    val referralUrl = getHeaderCaseInsensitive(HeaderNames.Referer)
    val ULAppId = getHeaderCaseInsensitive(WebgateHeaderNames.APP_ID)
    val clientDataUserAgent = getHeaderCaseInsensitive(HeaderNames.UserAgent)
    val clientDataTransactionReferenceId = getHeaderCaseInsensitive(AdditionalHeaderNames.TRANS_REF_ID)
    val ul2FATransactionId = getHeaderCaseInsensitive(AdditionalHeaderNames.UL_2FA_TRANSACTION_ID)

    val whiteLabelId = mapWhiteLabelTokenToId(whiteLabelToken)
    val isWLTokenPresent = whiteLabelToken.flatMap(Option(_)).exists(_.nonEmpty)
    val ipAddress = getHeaderCaseInsensitive("X-Real-IP")
      .orElse(getHeaderCaseInsensitive("Remote-Address"))
      .orElse {
        ctx.request.headers.collectFirst {
          case remote: `Remote-Address` => remote.address.toOption.map(_.getHostAddress)
        }.flatten
      }
      .flatMap(_.split(":").headOption)
    val whitelabelMetadata = WhiteLabelHelper.getWhitelabelMetadata(whiteLabelId)
    val isUATWhitelabel = whitelabelMetadata.exists(_.isUAT)
    val agBotInfoValue = getHeaderCaseInsensitive(WebgateHeaderNames.BOT_INFO)

    val teamName = TeamNameUtil.getTeamName(apiKeyHeader, whiteLabelId, EndpointUtil.isBoPath(endpoint))
    val simplifiedSource = clientDataUserAgent.map(HttpService.getSimplifiedSourceFromUserAgent)

    val apcSourceServiceName = getHeaderCaseInsensitive(PrivateCloudHeaderNames.apcSourceServiceName)
    val xAgodaSsotName = getHeaderCaseInsensitive(PrivateCloudHeaderNames.xAgodaSsotName)
    val sourceServiceName = getHeaderCaseInsensitive(PrivateCloudHeaderNames.sourceServiceName)
    val xSourceServiceName = getHeaderCaseInsensitive(PrivateCloudHeaderNames.xSourceServiceName)
    val xEnvoyAttemptCount = getHeaderCaseInsensitive(PrivateCloudHeaderNames.xEnvoyAttemptCount)

    val forceExperiments = getHeaderCaseInsensitive(WebgateHeaderNames.FORCE_EXPERIMENTS).map(parseToMapString)

    // TODO: Update logic to select keystoreId with priority
    val keystoreId = DalContext.keystoreWhitelabelIdMapper.get(whiteLabelId.toString)

    CapiRequestContextData(
      requestId,
      endpoint,
      method,
      clientApp,
      clientMachine,
      whiteLabelToken,
      whiteLabelId,
      isWLTokenPresent,
      ipAddress,
      keystoreId,
      apiKeyHeader,
      capiClientVersion,
      mdcGroup,
      teamName,
      isUATWhitelabel,
      agBotInfoValue,
      CapiRequestContextData.parseAgBotInfo(agBotInfoValue),
      partnerClaim,
      xAPIMock,
      isMock,
      capiMfaToken,
      chainSlug,
      correlationId,
      sessionId,
      agInitiatorIp,
      agUserId,
      xAABMock,
      agEnv,
      agOrigin,
      agAid,
      referralUrl,
      ULAppId,
      ul2FATransactionId,
      clientDataUserAgent,
      clientDataTransactionReferenceId,
      simplifiedSource,
      apcSourceServiceName,
      xAgodaSsotName,
      sourceServiceName,
      xSourceServiceName,
      xEnvoyAttemptCount,
      Some(ctx), //: requestContext,
      forceExperiments
    )
  }

  def parseToMapString(header: String): Map[String, String] = {
    header.trim
      .split(",")
      .map { item =>
        item.trim.split("=")
      }
      .map { map =>
        map(0) -> map(1)
      }
      .toMap
  }

  def extractCapiRequestContext(ctx: RequestContext)(block: CapiRequestContextData ⇒ Route): Route = {
    val capiCtx = convertRequestContextToCapiRequestContext(ctx)
    validateApiKeyWlToken(ctx, capiCtx)
    block(capiCtx)
  }

  def mapWhiteLabelTokenToId(token: Option[String]): Int = {
    token.map(_.toUpperCase()) match {
      case None => Constants.AgodaId
      case Some("F1A5905F-9620-45E5-9D91-D251C07E0B42") => Constants.AgodaId
      case _ => WhiteLabelHelper.getWhiteLabelIdByToken(token)
    }
  }

  // TODO Only temporary for RM specific wl token and flow
  def validateApiKeyWlToken(reqCtx: RequestContext, capiCtx: CapiRequestContextData) = {
    val isApiKeyPath = reqCtx.request.uri.path.toString.startsWith("/v1/api-key")
    if (!isApiKeyPath && Constants.RocketMilesWhitelabelIdRange.contains(capiCtx.whiteLabelId)) {
      val wlToken = UUID.fromString(capiCtx.whiteLabelToken.get)
      if (!Constants.UseMocks && !ApiPolicy.validateApiKey(reqCtx, wlToken)) {
        val apiHeader = reqCtx.request.headers.find(_.name().toLowerCase == Constants.apiKeyHeader.toLowerCase)
        val apiKeyValue = apiHeader.map(_.value())
        CapiLogMessage(
          "capi.wl.apikey",
          logLevel = LogLevel.ERROR,
          message = Some("fail to validate apikey for WL"),
          stringTags = Map("apiKeyValue" -> QueryUtil.encryptO(apiKeyValue).getOrElse("None")) ++ capiCtx.toStringTags
        )
        throw new UnauthorizedException(
          "Invalid api key: " + QueryUtil.encryptO(apiKeyValue) + " or whitelabelToken: " + capiCtx.whiteLabelToken
            .getOrElse("") + " provided")
      }
    }
  }
  def extractCapiRequestContext(block: CapiRequestContextData ⇒ Route): Route = {
    extractRequestContext { ctx ⇒
      extractCapiRequestContext(ctx)(block)
    }
  }

  def pathCompleteP[T: Manifest](p: PathMatcher[Unit])(handler: T ⇒ ToResponseMarshallable): Route = {
    path(p)(extractRequestContext { ctx =>
      val body = Try(
        JacksonMarshallers.AllMapper.readValue[T](ctx.request.entity.asInstanceOf[HttpEntity.Strict].data.utf8String))
      if (body.isSuccess) complete(handler(body.get))
      else complete((StatusCodes.BadRequest, body.failed.get.toString))
    })
  }

  def pathComplete[T: Manifest](thePath: String)(handler: T ⇒ ToResponseMarshallable): Route = {
    path(thePath)(extractRequestContext { ctx =>
      val body = Try(
        JacksonMarshallers.AllMapper.readValue[T](ctx.request.entity.asInstanceOf[HttpEntity.Strict].data.utf8String))
      if (body.isSuccess) complete(handler(body.get))
      else complete((StatusCodes.BadRequest, body.failed.get.toString))
    })
  }

  def intCsvSeq: Unmarshaller[String, Array[Int]] = Unmarshaller.strict[String, Array[Int]] { string =>
    string.split(",").map(_.toInt)
  }

  def noPathComplete[T: Manifest](handler: T ⇒ ToResponseMarshallable): Route = {
    extractRequestContext { ctx =>
      val body = Try(
        JacksonMarshallers.AllMapper.readValue[T](ctx.request.entity.asInstanceOf[HttpEntity.Strict].data.utf8String))
      if (body.isSuccess) complete(handler(body.get))
      else complete((StatusCodes.BadRequest, body.failed.get.toString))
    }
  }
}

trait CapiAuthenticationDirective {
  protected def apiKey: ApiKey

  protected def authenticator: ServiceAuthenticator

  def authenticate[T: Manifest](handler: T ⇒ ToResponseMarshallable): Route = { reqCtx ⇒
    import JacksonMarshallers.jacksonEntityAllUnmarshaller
    val headers = reqCtx.request.headers
    val key = headers.find(_.name().toLowerCase == "x-api-key").map(header ⇒ ApiKey(header.value()))
    val isValid = authenticator.isValid(key, apiKey)
    if (!isValid) throw new UnauthorizedException()
    else {
      val route = entity(as[T])(entity ⇒ complete(handler(entity)))
      route(reqCtx)
    }
  }

  def withValidToken(
      implicit ctx: CapiRequestContextData,
      unified: UnifiedCustomerService,
      ec: ExecutionContext): Directive1[TokenBox] = {
    //The built in Akka stuff is more trouble than it is worth
    import akka.http.scaladsl.server.directives.HeaderDirectives._
    optionalHeaderValueByName("Authorization").flatMap { a: Option[String] =>
      val magic = "Bearer "
      val fixed = a.filter(_.startsWith(magic)).map(s => s.substring(magic.length))
      val r = fixed.map(t => unified.representationFromToken(t)(ctx))

      // check token validation
      val stringTags = ctx.toStringTags
      val tokenBox = (fixed, r) match {
        case (Some(t), Some(info)) =>
          if (CapiConsul.getGdprCachedConfig.enabled)
            TempSingletonHolder.accountDeletionCachedService.checkIsAccountDeleted(info.memberId)
          TokenBox(t, Try(info))
        case (None, _) =>
          CapiLogMessage(
            loggerName = "capi.authed.token",
            logLevel = LogLevel.ERROR,
            stringTags = stringTags,
            message = Some(QueryUtil.encrypt(a.getOrElse(""))),
            exceptionMessage = Some("Valid token was not found in the request")
          )
          throw AuthException("No valid token was found in request")
        case (Some(t), None) =>
          CapiLogMessage(
            loggerName = "capi.authed.token",
            logLevel = LogLevel.ERROR,
            stringTags = stringTags,
            message = Some(QueryUtil.encrypt(t)),
            exceptionMessage = Some("Token was found but invalid")
          )
          throw AuthException("No valid token was found in request")
        case _ =>
          CapiLogMessage(
            loggerName = "capi.authed.token",
            logLevel = LogLevel.ERROR,
            stringTags = stringTags,
            message = Some("No token is presented"))
          throw AuthException("No valid token was found in request")
      }

      val value = BasicDirectives.provide(tokenBox)

      val now = Instant.now().toEpochMilli
      val currentToken = tokenBox.info.get
      val (doReissue, remaining) = TokenHelper.doReissue(currentToken)

      val remainingInHours = if (remaining > 0) remaining / (60 * 60 * 1000) else -1
      val remainingHoursString = if (remainingInHours < 13) s"$remainingInHours" else "13+"
      val elapsedAfterIssueMilliSec = now - currentToken.issued.toEpochMilli
      val elapsedMinutes = elapsedAfterIssueMilliSec / (60 * 1000)

      val elapsedSinceIssued = if (elapsedMinutes < 60) {
        s"$elapsedMinutes minutes"
      } else {
        val elapsedHours = elapsedMinutes / 60
        if (elapsedHours < 24) {
          s"$elapsedHours hours"
        } else {
          val elapsedDays = elapsedHours / 24
          if (elapsedDays < 30) {
            s"$elapsedDays days"
          } else {
            "30+ days"
          }
        }
      }

      val additionalTags = Map(
        "hadExpired" -> currentToken.expiry.isBefore(Instant.now()).toString,
        "doReissue" -> doReissue.toString,
        "remainingHours" -> remainingHoursString,
        "elapsedSinceIssued" -> elapsedSinceIssued,
      )

      if (ctx.whiteLabelId > 10) {
        CapiMeasurementMessage("capi.token.validity", ctx.toMetricTags ++ additionalTags)
        CapiLogMessage(
          loggerName = "capi.token.validity",
          logLevel = LogLevel.INFO,
          stringTags = stringTags ++ additionalTags ++ Map("issuedAt" -> s"${currentToken.issued}"))
      }

      //Will Add the 12 month check after ensuring oiat is always present
      if (doReissue && tokenBox.token.startsWith("eyJ")) {
        val tags = Map("memberId" -> currentToken.memberId.toString)
        CapiLogMessage(loggerName = "capi.reissue.token", logLevel = LogLevel.INFO, stringTags = stringTags ++ tags)
        val directive =
          respondWithHeaders(RawHeader("X-Access-Token", JWTUtilServer.reissueFromString(tokenBox.token, ctx)))
        directive.tflatMap(_ => value)
      } else {
        value
      }
    }
  }

  def withAuthorizedTeam[T](authorizedTeams: Set[String])(f: => Future[T])(
      implicit ctx: CapiRequestContextData): Future[T] = {
    if (authorizedTeams.contains(ctx.teamName)) f
    else Future.failed(new UnauthorizedException())
  }
}
