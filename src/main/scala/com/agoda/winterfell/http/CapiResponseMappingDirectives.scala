package com.agoda.winterfell.http

import akka.http.scaladsl.model.{HttpEntity, HttpResponse, ResponseEntity}
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.Directives.mapResponse
import akka.http.scaladsl.server.{RequestContext, Route}
import com.agoda.usercontext.{UserContextParser, UserContextParserImpl}
import com.agoda.winterfell.Constants
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.output.{
  MemberDetails,
  MemberUserContextResponse,
  RewardsMemberUserInfoForLogin,
  TrustedHostInfo
}
import java.util.UUID

import akka.http.scaladsl.model.Uri.Path
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.unified.{LoginParameters, OAuth2Credentials}
import com.agoda.winterfell.utils.EndpointUtil

import scala.util.Try

trait CapiResponseMappingDirectives {
  val userContextParser: UserContextParser = UserContextParserImpl
  private val OauthPath = Path("/v2/public/oauth")

  // return custom header in case member didn't found
  private def legacyMemberMappingResponse(response: HttpResponse, path: String): HttpResponse = {
    val endpoint = path.split("/").last
    val respWithMissingHeader = response.withHeaders(response.headers ++ Seq(RawHeader("legacyMemberNotFound", "true")))

    endpoint match {
      case "GetMemberUserContext" =>
        marshallersResponse[MemberUserContextResponse](response.entity)
          .map { resp =>
            resp.userContext match {
              case Some(context)
                  if userContextParser
                    .parse(context.toString)
                    .loginTypes
                    .isDefined && userContextParser.parse(context.toString).emailDomain.isDefined =>
                response
              case _ => respWithMissingHeader
            }
          }
          .getOrElse(respWithMissingHeader)
      case "GetMember" =>
        marshallersResponse[MemberDetails](response.entity) match {
          case Some(memberDetails) if !Constants.emptyMemberDetailWithDefault.equals(memberDetails) => response
          case _ => respWithMissingHeader
        }
      case "TrustedHost" => // Seems like this endpoint already return Bad request for not found case
        marshallersResponse[TrustedHostInfo](response.entity) match {
          case Some(_) => response
          case _ => respWithMissingHeader
        }
      case "LoginWithToken" =>
        marshallersResponse[RewardsMemberUserInfoForLogin](response.entity) match {
          case Some(rewMember) if !rewMember.userId.equals(new UUID(0, 0)) => response
          case _ => respWithMissingHeader
        }
      case _ => response
    }
  }

  def mapCapiLegacyResponse(ctx: RequestContext)(innerRoutes: => Route): Route = {
    val requestedPath = ctx.request.uri.path.toString()
    val filtered = if (requestedPath.startsWith("/")) requestedPath.substring(1) else requestedPath
    filtered match {
      case path: String if Constants.isLegacyMemberEndpoints(path) =>
        mapResponse(legacyMemberMappingResponse(_, path))(innerRoutes)
      case _ => innerRoutes
    }
  }

  def v2PublicLoginMappingRequestContext(requestContext: RequestContext): RequestContext = {
    val request = requestContext.request
    val ctx = Directives.convertRequestContextToCapiRequestContext(requestContext)
    marshallersResponse[LoginParameters](request.entity)
      .map { params: LoginParameters =>
        params.cleaned.credentials match {
          case _: OAuth2Credentials =>
            CapiLogMessage("capi.incorrect.oauth", stringTags = ctx.toStringTags)
            requestContext.withRequest(request.copy(uri = request.uri.copy(path = OauthPath)))
          case _ =>
            requestContext
        }
      }
      .getOrElse(requestContext)
  }

  def mapCapiV2PublicRequest(requestContext: RequestContext): RequestContext = {
    val requestedPath = requestContext.request.uri.path.toString()
    val filtered = if (requestedPath.startsWith("/")) requestedPath.substring(1) else requestedPath
    filtered match {
      case path: String if EndpointUtil.shortenUri(path).equalsIgnoreCase("v2_public_login") =>
        v2PublicLoginMappingRequestContext(requestContext)
      case _ =>
        requestContext
    }
  }

  private def marshallersResponse[T: Manifest](entity: ResponseEntity): Option[T] =
    Try(JacksonMarshallers.AllMapper.readValue[T](entity.asInstanceOf[HttpEntity.Strict].data.utf8String)).toOption
}
