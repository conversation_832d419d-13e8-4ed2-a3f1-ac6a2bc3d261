package com.agoda.winterfell
package http

import akka.actor.ActorSystem
import akka.http.scaladsl.model._
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.RouteResult.{Complete, Rejected}
import akka.http.scaladsl.server._
import akka.stream.{ActorMaterializer, Materializer}
import akka.util.ByteString
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.capi.enigma.client.member.MemberService
import com.agoda.commons.http.client.v2.settings.{HttpClientSettings, HttpClientSettingsBuilder}
import com.agoda.commons.http.client.v2.{ClientSettings, HttpClient}
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.dal.spi.backend.proxy.SqlProxy
import com.agoda.dal.spi.backend.sqlserver.SyncSqlServerSessionProvider
import com.agoda.pie.client.apis.PartnerIntegrationEngineApi
import com.agoda.winterfell.backoffice.BackOfficeModule
import com.agoda.winterfell.backoffice.repository.BoPiiRepositoryImpl
import com.agoda.winterfell.basiclogin.fallback.rurubu.RurubuMigrateDataRepositoryImpl
import com.agoda.winterfell.basiclogin.model._
import com.agoda.winterfell.basiclogin.service.{BasicLoginService, BasicLoginServiceImpl}
import com.agoda.winterfell.client._
import com.agoda.winterfell.client.v2.AgHttpClient
import com.agoda.winterfell.constant.{PrivateCloudHeaderNames, WebgateHeaderNames}
import com.agoda.winterfell.graphql.GraphQLRoute
import com.agoda.winterfell.helper.WhiteLabelHelper
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.http.middleware.ApiKeyAuditLogger
import com.agoda.winterfell.http.routes._
import com.agoda.winterfell.metrics.{CapiLogMessage, CapiMeasurementMessage}
import com.agoda.winterfell.mocks.{NextCloudServiceMockImpl, WeChatClientServiceMockImpl}
import com.agoda.winterfell.models.{CapiRequestContextData, QueryUtil, UserPermittedIpRangeUpdateTrackingServiceImpl}
import com.agoda.winterfell.output.ServerStatusCode
import com.agoda.winterfell.repository._
import com.agoda.winterfell.repository.couchbase.{CacheRepositoryImpl, SecurityCouchbaseRepository}
import com.agoda.winterfell.risk.CapiLexisService
import com.agoda.winterfell.security.ApiPolicy
import com.agoda.winterfell.security.login.phone.{DefaultPhoneLoginServiceModule, PhoneLoginServiceModule}
import com.agoda.winterfell.security.model.CapiBoApiKeyHistory
import com.agoda.winterfell.security.service.{ApiKeyService, AssociatePhoneBlockService}
import com.agoda.winterfell.services.ExperimentNames.InsertOnSignup
import com.agoda.winterfell.services._
import com.agoda.winterfell.services.booking.BookingApiService
import com.agoda.winterfell.services.cart.CartApiService
import com.agoda.winterfell.services.cncustomers.{CNBookingDataPushService, CNCustomerDataPushService}
import com.agoda.winterfell.services.cusco.CuscoEmailService
import com.agoda.winterfell.services.ele.{LoyaltyExecutionApiServiceImpl, LoyaltyPlatformApiServiceImpl}
import com.agoda.winterfell.services.pie.PieProxyServiceImpl
import com.agoda.winterfell.swagger.{SwaggerDocService, SwaggerUIService}
import com.agoda.winterfell.unified._
import com.agoda.winterfell.unified.accessors.AsyncMutator
import com.agoda.winterfell.unified.clientSecret.ClientSecretServiceImpl
import com.agoda.winterfell.unified.login.UnifiedLoginFactory
import com.agoda.winterfell.unified.login.external.social.{AgodaDirectPieSSOLoginService, WLSSOLoginService}
import com.agoda.winterfell.unified.logout.UnifiedLogoutFactory
import com.agoda.winterfell.unified.loyalty.LoyaltyServiceImpl
import com.agoda.winterfell.unified.nextCloud.uploader.NextCloudHttpClient
import com.agoda.winterfell.unified.nextCloud.{
  NextCloudAuth,
  NextCloudHttp,
  NextCloudHttpConfig,
  NextCloudService,
  NextCloudServiceImpl
}
import com.agoda.winterfell.unified.otp.OtpServiceFactory
import com.agoda.winterfell.unified.password.CuscoPasswordSender
import com.agoda.winterfell.unified.signup.external.wl.RocketMilesSignupService
import com.agoda.winterfell.unified.wallet.WalletServiceImpt
import com.agoda.winterfell.unified.whitelabel.{RMCustomerService, WLCustomerServiceFactory}
import com.agoda.winterfell.utils.Profiler.MeasuredProfile
import com.agoda.winterfell.utils._
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import sttp.client3.asynchttpclient.future.AsyncHttpClientFutureBackend
import sttp.model.HeaderNames

import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.util.{Failure, Success, Try}

class HttpService(
    emailVerificationService: EmailVerificationService,
    userRepository: UserRepository,
    securityService: SecurityService,
    favoriteHotelService: FavoriteHotelService,
    favoriteFlightPassengersService: FavoriteFlightPassengersService,
    securityRepository: SecurityRepository,
    otpService: OTPService,
    couchbaseService: CouchbaseService,
    nhaCustomerService: NhaCustomerService,
    lexisService: CapiLexisService,
    ssoService: SSOService,
    authenticator: ServiceAuthenticator,
    cryptoService: ServerCryptoMetadataServiceImpl,
    tokenService: TokenService,
    apiKeyService: ApiKeyService,
    customerAuditService: CustomerAuditService,
    val giftCardService: GiftCardService,
    val db: DBPlugin,
    val gcApi: GiftCardApi,
    couchbaseRepository: SecurityCouchbaseRepository,
    whitelabelRepository: WhitelabelRepository,
    CNCustomerDataPushService: CNCustomerDataPushService,
    CNBookingDataPushService: CNBookingDataPushService,
    otpCacheRepository: CacheRepositoryImpl,
    partnerClaimHelperService: PartnerClaimHelperService,
    cuscoService: CuscoEmailService,
    singleSavedTravellerService: SavedTravellersService,
    dualSavedTravellerService: DualSavedTravellersService,
    cartApiService: CartApiService)(
    implicit val executionContext: ExecutionContext,
    materializer: ActorMaterializer,
    actorSystem: ActorSystem,
    experimentService: ExperimentService)
    extends AppLogger
    with CapiResponseMappingDirectives {

  import HttpService._

  val exceptionHandler = new ExceptionHandler
  val directiveFallback = new DirectiveFallback(exceptionHandler)

  private implicit val executionContextExecutor: ExecutionContextExecutor =
    Option(actorSystem).map(_.dispatcher).getOrElse(Implicits.globalExecutor)

  // Choose between dual service and single service based on enableDualReadWrite flag
  private val conditionalSavedTravellerService: SavedTravellersService = {
    if (CapiConsul.enableDualReadWrite) {
      dualSavedTravellerService
    } else {
      singleSavedTravellerService
    }
  }

  private val gitLogHeaders = List(
    RawHeader("version", BuildHelper.version),
    RawHeader("gitHash", BuildHelper.gitHash),
    RawHeader("gitBranch", BuildHelper.gitBranch),
    RawHeader("builtAt", BuildHelper.builtAt),
    RawHeader("host", HostUtils.getHostName),
    RawHeader("newPhone", CapiConsul.useNewPhoneLogin.toString)
  )

  //-- Routes Of This Module --//

  private val deviceRepository = new DeviceRepositoryImpl(db)

  implicit val dbPlugin = new DBPlugin

  val basicLogin: BasicLoginService = new BasicLoginServiceImpl(userRepository.customerRepository, deviceRepository)

  val inProgressBridge = new InProgressBridge(db, basicLogin)
  val cuscoPasswordSender: PasswordSender = if (Constants.UseMocks) {
    MemoryPasswordSender
  } else {
    new CuscoPasswordSender(emailVerificationService.cuscoEmailService)
  }
  val secOTPConfig =
    if (Constants.UseMocks) { () =>
      SendOTPSecurityConfig(
        OtpBlockingConfig(
          DeviceIdBlockConfig(true, 60, 3, 600),
          PhonenumberBlockConfig(true, 3, 30),
          IpAddressBlockConfig(true, 5, 30, 3, 60),
          UsernameBlockConfig(true, 3, 30)),
        false,
        15,
        Map.empty
      )
    } else { () =>
      CapiConsul.securityOtpConfig
    }
  //  val giftCardService2 = if(Constants.UseMocks) new MockGiftCardServiceImpl else giftCardService
  private val loyaltyExecutionService = new LoyaltyExecutionApiServiceImpl()
  val mdbSyncRepository: MDBSyncRepository = new MDBSyncRepositoryImpl(db)
  val customerCouchbase: CustomerCouchbase =
    new CustomerCouchbaseImpl(couchbaseRepository.couchbaseManager, CapiConfig.couchbaseCustomerCachePrefix)
  TempSingletonHolder.customerCouchbase = customerCouchbase
  val enigmaMemberService: MemberService = MemberService(config = EnigmaHttpClientConfig())
  implicit val customerServiceOps: CustomerServiceOps = new CustomerServiceOpsImpl(
    userRepository.customerRepository,
    TempSingletonHolder.customerEntityRepo.get(),
    TempSingletonHolder.verifiedContactRepo.get(),
    TempSingletonHolder.unverifiedContactRepo.get(),
    TempSingletonHolder.customerNoteRepo.get(),
    TempSingletonHolder.partnerMembershipRepo.get(),
    TempSingletonHolder.jtbPropertiesRepo.get(),
    TempSingletonHolder.japanicanPropertiesRepo.get(),
    TempSingletonHolder.rurubuPropertiesRepo.get(),
    TempSingletonHolder.rocketMilesPropertiesRepo.get(),
    TempSingletonHolder.rocketMilesReferralCodeRepo.get(),
    TempSingletonHolder.customerPasswordHistoryRepo.get(),
    TempSingletonHolder.wechatV1ProfileRepo.get(),
    deviceRepository,
    cryptoService,
    Some(tokenService),
    db,
    mdbSyncRepository,
    new WhitelabelRepositoryImpl(db, new RurubuMigrateDataRepositoryImpl),
    Some(giftCardService),
    Some(gcApi),
    Some(lexisService),
    Some(directiveFallback),
    customerCouchbase,
    enigmaMemberService,
    (context: CapiRequestContextData) => experimentService.isBVariant(InsertOnSignup, None)(context)
  )
  private val bookingApiClient = new BookingApiService(CapiConfig.config)(Implicits.slowExecutionContext)
  private val cegApiService =
    new CegApiServiceImpl(CapiConfig.config)(actorSystem, materializer, Boot.Instance.httpExecutionContext)
  private val userService =
    new UserServiceImpl(
      userRepository,
      customerServiceOps,
      db,
      lexisService,
      bookingApiClient,
      cegApiService,
      securityService)
  TempSingletonHolder.userService = userService
  TempSingletonHolder.customerServiceOps = customerServiceOps

  private val loginService = new LoginServiceImpl(customerServiceOps, userRepository, tokenService)
  private val bridge =
    new LegacyOpsBridge(userRepository.customerRepository, loginService, inProgressBridge, whitelabelRepository)(db)

  private val piiRepository = new BoPiiRepositoryImpl(
    userRepository.customerRepository,
    customerServiceOps,
    TempSingletonHolder.customerEntityRepo.get(),
    TempSingletonHolder.verifiedContactRepo.get(),
    TempSingletonHolder.unverifiedContactRepo.get(),
    TempSingletonHolder.customerNoteRepo.get(),
    TempSingletonHolder.partnerMembershipRepo.get()
  )
  private val moreRoutes = {
    import akka.http.scaladsl.server.Directives._
    BackOfficeModule(
      CapiConfig.config.getConfig("backoffice"),
      CapiConsul.config,
      customerServiceOps,
      userRepository,
      securityRepository,
      emailVerificationService.cuscoEmailService
    ).routes ~ lexisService.routes
  }

  private val unifiedPassword = new SQLUnifiedPasswordServiceImpl(
    cryptoService,
    new CuscoPasswordSender(emailVerificationService.cuscoEmailService),
    Some(couchbaseRepository),
    customerServiceOps)(db, couchbaseRepository.couchbaseManager)
  private val wlCustomerServiceFactory = new WLCustomerServiceFactory(
    db,
    bridge,
    customerServiceOps,
    unifiedPassword,
    giftCardService,
    whitelabelRepository,
    piiRepository,
    mdbSyncRepository,
    ssoService,
    partnerClaimHelperService,
    enigmaMemberService
  )
  private val userPermittedIpRangeService = new UserPermittedIpRangeServiceImpl(
    TempSingletonHolder.userPermittedIpRangeRepository
      .get(),
    new UserPermittedIpRangeUpdateTrackingServiceImpl())

  private val pieClientSettings: HttpClientSettings =
    HttpClientSettingsBuilder(serviceName = CapiConfig.pieAPIConfig.name)
      .withClientSettings(ClientSettings())
      .build()
  private val pieMeshSettings =
    ServiceMeshSettingsBuilder(
      CapiConfig.pieAPIConfig.name,
      CapiConfig.pieAPIConfig.meshConfig
    ).build()
  private val pieClient = new PartnerIntegrationEngineApi[Future](
    HttpClient[Future](pieClientSettings)(
      AsyncHttpClientFutureBackend()
    ),
    ServiceMesh(pieMeshSettings)
  )
  private val pieProxy = new PieProxyServiceImpl(pieClient)
  private val pieService = new PieApiServiceImpl(pieProxy)
  TempSingletonHolder.pieAPIService = pieService

  val rmCustomerService = new RMCustomerService(
    db,
    bridge,
    ssoService,
    partnerClaimHelperService,
    customerServiceOps,
    piiRepository,
    unifiedPassword,
    whitelabelRepository,
    mdbSyncRepository,
    enigmaMemberService
  )

  val loyaltyPlatformClient = new LoyaltyPlatformApiServiceImpl()
  val elapiService =
    new AgDirectExternalLoyaltyAPIService(loyaltyPlatformClient, partnerClaimHelperService, rmCustomerService)
  TempSingletonHolder.externalLoyaltyAPIService = elapiService

  val wlSsoLoginService = new WLSSOLoginService(partnerClaimHelperService, ssoService, bridge, rmCustomerService)

  val loyaltyExecutionClient = new LoyaltyExecutionApiServiceImpl()

  val setUpUserProfileService = new SetUpUserProfileServiceImpl(elapiService)
  TempSingletonHolder.setUpUserProfileService = setUpUserProfileService

  val agDirectExternalLoyaltyExecutionAPIService =
    new AgDirectExternalLoyaltyAPIService(loyaltyPlatformClient, partnerClaimHelperService, rmCustomerService)

  val agodaDirectPieSSOLoginService: AgodaDirectPieSSOLoginService = new AgodaDirectPieSSOLoginService(
    pieApiService = pieService,
    setupUserProfileService = setUpUserProfileService,
    agDirectExternalLoyaltyExecutionAPIService = agDirectExternalLoyaltyExecutionAPIService,
    partnerClaimHelperService = partnerClaimHelperService
  )
  TempSingletonHolder.agodaDirectPieSSOLoginService = agodaDirectPieSSOLoginService

  private val unifiedLoginFactory = new UnifiedLoginFactory(
    bridge,
    cryptoService,
    unifiedPassword,
    Some(tokenService),
    userRepository,
    customerServiceOps,
    securityService,
    giftCardService,
    whitelabelRepository,
    deviceRepository,
    db,
    piiRepository,
    unifiedPassword,
    mdbSyncRepository,
    ssoService,
    pieService,
    setUpUserProfileService,
    partnerClaimHelperService,
    loyaltyPlatformClient,
    loyaltyExecutionClient,
    couchbaseRepository,
    userPermittedIpRangeService,
    enigmaMemberService,
    secOTPConfig
  )
  private val unifiedLogoutFactory =
    new UnifiedLogoutFactory(ssoService, pieService = pieService, setUpUserProfileService = setUpUserProfileService)
  private val otpServiceFactory = new OtpServiceFactory(
    cuscoPasswordSender,
    cryptoService,
    secOTPConfig,
    couchbaseRepository,
    loyaltyExecutionService,
    pieProxy,
    partnerClaimHelperService,
    otpCacheRepository,
    experimentService
  )
  private val newLoginService = new UnifiedCustomerServiceImpl(
    db,
    bridge,
    cryptoService,
    unifiedPassword,
    Some(tokenService),
    userRepository,
    securityService,
    giftCardService,
    whitelabelRepository,
    deviceRepository,
    unifiedLoginFactory,
    customerServiceOps,
    wlCustomerServiceFactory,
    unifiedLogoutFactory,
    cartApiService
  )
  private val sqlProxy = new SqlProxy(CapiConfig.LocalDc, new SyncSqlServerSessionProvider(DBPlugin.DataSources) {})
  private val graphQLRoute = new GraphQLRoute(customerServiceOps)
  private val associatePhoneBlockService =
    new AssociatePhoneBlockService()(executionContext, couchbaseRepository.couchbaseManager)
  TempSingletonHolder.associatePhoneBlockService = associatePhoneBlockService
  private val walletServiceImpt = new WalletServiceImpt(customerServiceOps, cryptoService)
  private val nextCloudAuth = NextCloudAuth(
    username = CapiConsul.nextCloudUsername,
    password = CapiConsul.nextCloudPassword
  )
  val nextCloudHttpConfig: NextCloudHttpConfig = NextCloudHttpConfig(
    auth = nextCloudAuth,
    apiShareUrl = CapiConfig.nextCloudShareUrlApi,
    apiPathUrl = CapiConfig.nextCloudPathUrlApi
  )

  private val nextCloudAgHttpClient = new AgHttpClient("nextCloud")
  private val nextCloudUploaderHttpClient = new NextCloudHttpClient(nextCloudAgHttpClient)

  val nextCloudHttp = new NextCloudHttp(nextCloudHttpConfig, nextCloudUploaderHttpClient)
  val nextCloudService: NextCloudService =
    if (Constants.UseMocks) {
      NextCloudServiceMockImpl()
    } else {
      new NextCloudServiceImpl(nextCloudHttpConfig, nextCloudHttp)
    }
  private val clientSecretServiceImpl =
    new ClientSecretServiceImpl(userRepository.customerRepository, nextCloudService, cuscoService)
  private val wlElapiService = new WLExternalLoyaltyAPIService(
    loyaltyPlatformClient,
    partnerClaimHelperService,
    rmCustomerService
  )
  private val rocketMilesSignupService = new RocketMilesSignupService(
    customerServiceOps,
    wlElapiService,
    partnerClaimHelperService
  )(executionContextExecutor)
  private val loyaltyServiceImpl = new LoyaltyServiceImpl(loyaltyPlatformClient, partnerClaimHelperService)

  private val newLoginServiceRoute = new Version2Routes(
    newLoginService,
    cryptoService,
    otpService,
    Some(securityService),
    new CapiConsul.ApiKey.Backoffice(),
    authenticator,
    nhaCustomerService,
    userService,
    favoriteFlightPassengersService,
    favoriteHotelService,
    wlCustomerServiceFactory,
    customerAuditService,
    otpServiceFactory,
    sqlProxy,
    graphQLRoute,
    emailVerificationService,
    unifiedLoginFactory,
    associatePhoneBlockService,
    userPermittedIpRangeService,
    walletServiceImpt,
    clientSecretServiceImpl,
    conditionalSavedTravellerService,
    rocketMilesSignupService,
    loyaltyServiceImpl
  )(executionContext, customerServiceOps, partnerClaimHelperService)

  private val phoneLoginServiceModule: PhoneLoginServiceModule = new DefaultPhoneLoginServiceModule(
    db,
    () => CapiConsul.securityLoginConfig.otherBlocks,
    couchbaseRepository,
    userRepository.customerRepository,
    unifiedPassword,
    tokenService,
    lexisService,
    cryptoService
  )

  private val passwordServiceRoute =
    new PasswordServiceRoute(new CapiConsul.ApiKey.PasswordService(), authenticator, cryptoService, db)
  private val backofficeRoute = new BackofficeRoute(
    userService,
    customerServiceOps,
    otpService,
    couchbaseService,
    customerCouchbase,
    CNCustomerDataPushService,
    CNBookingDataPushService,
    otpServiceFactory,
    associatePhoneBlockService,
    new CapiConsul.ApiKey.Backoffice(),
    authenticator
  )
  private val favoriteHotelRoute = new FavoriteHotelRoute(favoriteHotelService, newLoginService)

  private val apiKeyAuditLogger: ApiKeyAuditLogger = new ApiKeyAuditLogger()(actorSystem)
  //Blows up with a NPE if you use async, don't use the async variant in tests
  private val hkClient: Option[CustomerApi] = CapiConfig.MultiWriteHost
    .map(h => s"$h/V1/RewardsApiService")
    .map(
      rewardsUrl =>
        CustomerApi(
          List(rewardsUrl),
          "customerapi",
          RewardsHttpConfig(
            apiKey = new CapiConsul.ApiKey.CapiInternal().value,
            completeHandler = new NopHandler,
            resolveDns = false)))
  //TODO just have one client, for now easier to use two to avoid multiple changes
  private val hkClient2: Option[CustomerApi] = CapiConfig.MultiWriteHost
    .map(h => s"$h/V1/SocialAppService")
    .map(
      rewardsUrl =>
        CustomerApi(
          List(rewardsUrl),
          "customerapi",
          RewardsHttpConfig(
            apiKey = new CapiConsul.ApiKey.CapiInternal().value,
            completeHandler = new NopHandler,
            resolveDns = false)))
  TempSingletonHolder.hkClient = if (CapiConfig.DcName.equals("HK")) {
    None
  } else {
    hkClient
  }
  private val weChatClientService: WeChatClientService = if (Constants.UseMocks) {
    WeChatClientServiceMockImpl()
  } else {
    WeChatClientServiceImpl()
  }
  private val weChatLoginService = new WeChatLoginServiceImpl(weChatClientService, loginService, hkClient, hkClient2)(
    executionContext,
    customerServiceOps)

  private val socialAppServiceRoute = new SocialAppServiceAndVerifyRoute(
    weChatLoginService,
    userService,
    AsyncMutator
      .apply(_: MeasuredFuture[UUID], _: Int, _: CapiRequestContextData)(executionContext, customerServiceOps),
    emailVerificationService
  )
  private val securityRoutes = new SecurityRoutes(
    customerServiceOps,
    securityService,
    securityRepository,
    userRepository.customerRepository,
    new CapiConsul.ApiKey.PasswordService(),
    authenticator)
  private val nhaServiceRoute = new NhaServiceRoute(nhaCustomerService)
  private val userServiceRoute = new UserServiceRoute(userService)
  private val apiKeyServiceRoute = new ApiKeyServiceRoute(apiKeyService, apiKeyAuditLogger)
  private val registrationRoute = new RegistrationRoute(customerServiceOps, userService)
  private val rewardsRoute = new RewardsRoute(db)
  private val loginServiceRoute = new LoginServiceRoute(loginService)

  def measureExecution(
      s: StatusCode,
      r: HttpResponse,
      ctx: RequestContext,
      start: Long,
      parent: MeasuredProfile): Long = {
    val finished = System.currentTimeMillis()
    val elapsed = finished - start
    Profiler.trackWith("all", start, finished, Some(parent))

    val shortened = EndpointUtil.shortenUri(ctx.request.uri.path.toString)

    val map = HttpService.respToMap(r, ctx.request, HttpService.speedTag(elapsed))

    //Log all non spammy security urls
    if (shortened.startsWith("v1") || shortened.startsWith("v2")) {
      val spammy = List(
        "v2_authorization_getpermissionbycomponentid",
        "v2_authorization_getprincipal",
        "v2_authorization_getroleobjectsbyroleid",
        "v2_authorization_getroleidlistbyuserid",
        "v1_nhaapiservice_propertylist",
        "v1_authorization_getuserinfo",
        "v2_authorization_getroleobjectsbyuserid",
        "v1_authorization_getuserinrole3",
        "v2_authorization_getsystemlist",
        //Exclude social, not security endpoints
        "v1_socialappservice_login",
        //Exclude v2
        "v2_public_login",
        "v2_public_signup",
        "v2_crypto_keys",
        "v2_crypto_blacklist"
      )
      val highRisk = List(
        "password",
        "token",
        "authentication",
        "createuser"
      )
      if (!spammy.contains(shortened)) {
        CapiMeasurementMessage("winterfell.sechttp", map, elapsed)
        if (highRisk.exists(h => shortened.contains(h))) {
          CapiMeasurementMessage("winterfell.sechttp.risk", map, elapsed)
        }
      }
    }

    CapiMeasurementMessage("winterfell.http", map, elapsed)

    if (HttpService.spamRequests) {
      val logger = if (ctx.unmatchedPath.toString().contains("GetMember")) {
        incomingHttpMember
      } else {
        incomingHttp
      }
      val extra = r.headers.find(_.name() == "myReq").map(_.value().replaceAll("\n", "")).getOrElse("")
      //Stop spamming favourites
      if (!ctx.unmatchedPath.toString().contains("GetFavoriteHotelsV3") && !ctx.unmatchedPath
          .toString()
          .contains("GetPermissionByComponentId")
        && !ctx.unmatchedPath.toString().contains("cuscoCallBack")) {
        val a = ctx.request.uri
          .withScheme("http")
          .withHost("")
          .withPort(80)
          .toString()
          .substring(11)
          .replaceAll("V1/RewardsApiService", "RAS")
        logger.info(s"$a [${ctx.request.method.value}] $s $extra ($elapsed ms)")
      }
    }
    Profiler.print(Some(parent))
    finished
    // Moved to Auditor
    // CapiMetric.send(ctx, s, client, elapsed, app)
  }

  def trackResponse(
      r: HttpResponse,
      ctx: RequestContext,
      start: Long,
      parent: MeasuredProfile): Future[HttpResponse] = {
    if (EndpointUtil.isTrackedEndpoint(ctx.request.uri.path.toString)) {
      val f = measureExecution(r.status, r, ctx, start, parent)
      Future.successful(
        r.withHeaders(
          r.headers ++ gitLogHeaders ++ headers(
            "start" -> start.toString,
            "end" -> f.toString,
            "et" -> (f - start).toString)))
    } else {
      Future.successful(r)
    }
  }

  //All implicits / handling below this point to speed up IDEA

  import akka.http.scaladsl.server.Directives._
  import com.agoda.winterfell.marshalling.JacksonMarshallers

  // @formatter:off
  private val thisModuleRoutes: Route = {
    val legacyRoute: Route = {
      val mappedRoutes = new StandardRoutes(customerServiceOps, giftCardService, elapiService)
      extractUnmatchedPath { path =>
        extractRequestContext { ctx =>
        extractCapiRequestContext(ctx) { capiCtx ⇒
        implicit val implicitCapiCtx = capiCtx

          val strict = ctx.request.entity match {
            case c:HttpEntity.Strict => c.data.utf8String
            case _ => ""
          }
          val legacyOption = LegacyRouteHandler.handleRequest(ctx.request.uri.path.toString(), strict)
          //noinspection ScalaRedundantCast
          legacyOption.map { m=>
            import JacksonMarshallers.jacksonWithNullsMarshaller
            complete(m)
          } getOrElse {
            mappedRoutes.otherRoutes(path.toString, ctx)
          }.asInstanceOf[akka.http.scaladsl.server.Route] //Stupid IDEA
        }
        }
      }
    }

    phoneLoginServiceModule.serviceRoute ~
      apiKeyServiceRoute.route ~
      securityRoutes.route ~
      //    reviewRoutes.route ~
//      referralService.routes ~
      newLoginServiceRoute.routes ~
      pathPrefix("V1") {
        pathPrefix("RewardsApiService" | "BORewardsApiService" | "UserApiService" | "NhaApiService") {
            val basicRoutes = backofficeRoute.route ~
            socialAppServiceRoute.route ~
            nhaServiceRoute.route ~
            loginServiceRoute.route ~
            registrationRoute.route ~
            rewardsRoute.route ~
            favoriteHotelRoute.route ~
            userServiceRoute.route ~
            passwordServiceRoute.route ~
            legacyRoute ~
            //Add nop for bootstrapping/testing
            path("nop") {
              complete(HttpResponse(entity = HttpEntity.Strict(ContentTypes.`application/json`, ByteString.apply("true"))))
            }
            if(CapiConfig.DcName == "BK") basicRoutes ~ deletingRoutes else basicRoutes
          } ~ socialAppServiceRoute.route2
      } ~ pathPrefix("api") {
      SwaggerDocService.routes ~
        SwaggerUIService.routes
    } ~ healthCheckRoutes
  }

  def deletingRoutes:Route = {
    extractCapiRequestContext { implicit capiCtx ⇒
      import JacksonMarshallers.primitiveMarshaller
      path("throwError") {
        throw new UnsupportedOperationException
      } ~ path("deleteByMemberId" / IntNumber) { i =>
        complete(Nuker.nukeByCustomerByMemberId(i))
      } ~ path("deleteByUserId" / JavaUUID) { i =>
        complete(Nuker.nukeByCustomerByUserId(i))
      }
    }
  }

  def healthCheckRoutes: Route = path("healthcheck") {
    complete("Ok")
  } ~ path("Online.txt") { //Agoda RR Library calls this
    complete("On: *")
  } ~ path("HealthCheck.aspx") { //This too
    complete(HttpResponse(entity = HttpEntity.Strict(ContentTypes.`application/json`, ByteString("{}"))))
  } ~ path("itassistant/ui/omabaseframe.htm") { //Stop 404 spam
    complete(HttpResponse(StatusCodes.NoContent))
  }  ~ path("wsman") {
    complete(HttpResponse(StatusCodes.NoContent))
  }

  private def normalRoutes: Route = {
    directiveFallback.mapRouteResultParallelWith {
      concat(thisModuleRoutes :: moreRoutes :: Nil: _*)
    }
  }

  // merge all routes from all modules
  private val allModuleRoutes = normalRoutes

  private val isLegacyRoute = ".*(role|savemember).*".r

  // routes with time measurement tracking
  private val timeTrackedRoutes: Route = requestContext => {
    val ctx = mapCapiV2PublicRequest(requestContext)
    val start = System.currentTimeMillis()
    val path = ctx.request.uri.path.toString
    val shortened = EndpointUtil.shortenUri(path)
    val apiHeader = ctx.request.headers.find(_.name.equalsIgnoreCase(Constants.apiKeyHeader))
    val apiKeyValue = apiHeader.map(_.value()).getOrElse("")
    //This doesn't include lower case v2, as we currently have separate handling for that except /v2/transit
    val legacyPath = path.startsWith("/V1") || path.startsWith("/V2") || path.startsWith("/v2/transit")
    val boPath = EndpointUtil.isBoPath(path)
    val matched = CapiConsul.keyList.contains(apiKeyValue)
    val boMatched = CapiConsul.boKeyList.contains(apiKeyValue)
    val apiKeyPath = path.toString.startsWith("/v1/api-key")
    val legacyKeyCheck = CapiConsul.legacyKeyCheck
    val isULEndpoints = CapiConsul.ulEndpoints.contains(path)
    val guardingULEndpoints = CapiConsul.ulEndpointApiKeyCheck && isULEndpoints
    // Whitelabel Id enforce for legacy exclude
    val whiteLabelHeader = ctx.request.headers.find(_.name().equalsIgnoreCase(Constants.whiteLabelTokenHeader))
    val whiteLabelHeaderValue = whiteLabelHeader.map(_.value()).getOrElse(Constants.agodaWhiteLabelToken)
    implicit val capiRequestContext = Try(Directives.convertRequestContextToCapiRequestContext(ctx)).toOption

    Try(UUID.fromString(whiteLabelHeaderValue)) match {
      case Failure(_) =>
        val msg = s"Invalid WhiteLabel : $whiteLabelHeaderValue"
        Future.successful(RouteResult.Complete(HttpResponse(StatusCodes.BadRequest, entity = HttpEntity.Strict(ContentTypes.`text/plain(UTF-8)`, ByteString(msg)))))
      case Success(whiteLabelToken) =>
        val whiteLabelId = WhiteLabelHelper.getWhiteLabelIdByToken(Some(whiteLabelToken.toString))
        //TODO: need to migrate to ApiPolicy (some of endpoint have condition itself)
        val isLegacyExclude =
          if (!CapiConsul.forceLegacyApiKeyList.exists(_.equalsIgnoreCase(path)) && legacyPath && whiteLabelToken == Constants.agodaUUID)
            CapiConfig.excludeApiKeyCheckEndpoints.exists(_.findFirstMatchIn(path.toString).nonEmpty)
          else false

        // TODO: remove this and move to ApiPolicy
        val rolePath = (whiteLabelToken, path.toString.toLowerCase) match {
          case (Constants.jtbWhiteLabelTokenUUID, isLegacyRoute(_)) => CapiConsul.jtbAllowList.contains(CapiConsul.apiKeyName(apiKeyValue).toLowerCase)
          case _ => true
        }


        val rawFailedCheckinDb = !ApiPolicy.validateApiKey(ctx, whiteLabelToken)
        // check only bo path and legacy path
        val failedCheckInDB = if( !isLegacyExclude && (legacyPath || boPath) ){
          rawFailedCheckinDb || (!rolePath)
        } else false

        val failedCheck = if (legacyKeyCheck && !guardingULEndpoints) (!matched && legacyPath) || (!boMatched && boPath) || (!CapiConsul.handleApiKey && apiKeyPath) || (!rolePath)
        else failedCheckInDB

        val isLegacyEnforce = getForcedCache
        val isBoEnforce = getBoForcedCache
        val isEnforce =
          if (legacyPath) isLegacyEnforce
          else if (boPath) isBoEnforce
          else if (apiKeyPath) true
          else false

        val remoteAddress = getIpFromRequest(ctx.request)

        val ipInWhitelist = CapiConsul.whiteListIps.contains(remoteAddress)
        if(CapiConfig.skipHealthCheckAudit(path.toString)){
          tmpApiKeysLog(failedCheck && !ipInWhitelist && !isLegacyExclude, ctx, apiHeader, remoteAddress,
            ipInWhitelist, isLegacyExclude, legacyKeyCheck, failedCheckInDB, whiteLabelId, isULEndpoints)
        }

        if (failedCheck && isEnforce && !ipInWhitelist && !isLegacyExclude) {
          val msg = if (apiHeader.isDefined) "The supplied authentication is invalid" else "No X-Api-Key header provided"
          val endpoint = path.toString

          val tags = Map(
            "endpoint" -> endpoint,
            "remoteAddress" -> remoteAddress,
            "apiHeader" -> QueryUtil.encrypt(apiKeyValue),
            "message" -> msg,
            "legacyKeyCheck" -> failedCheck.toString,
            "dbKeyCheck" -> failedCheckInDB.toString
          ) ++ capiRequestContext.map(_.toStringTags).getOrElse(Map.empty)
          CapiLogMessage("capi.forceApiKey", logLevel = LogLevel.ERROR, stringTags = tags)

          if (boPath) CapiBoApiKeyHistory(endpoint, remoteAddress, isSuccess = false, rejectionReason = Some(msg)).sendAsync(CapiConfig.AdpApiKey)

          Future.successful(RouteResult.Complete(HttpResponse(
            StatusCodes.Unauthorized,
            headers = headers("ServerStatusCode" -> ServerStatusCode.InvalidCAPIRequest.id.toString, "ServerStatus" -> ServerStatusCode.InvalidCAPIRequest.toString),
            entity = HttpEntity.Strict(ContentTypes.`text/plain(UTF-8)`, ByteString(msg)))))
        } else {
          if(CapiConfig.skipHealthCheckAudit(path.toString) && rawFailedCheckinDb && CapiConsul.enableForceApiKeyDBLog){
            val tags = Map(
              "endpoint" -> path,
              "remoteAddress" -> remoteAddress,
              "apiHeader" -> QueryUtil.encrypt(apiKeyValue),
              "message" -> "missing on apiKey DB",
              "legacyKeyCheck" -> failedCheck.toString,
              "dbKeyCheck" -> failedCheckInDB.toString
            ) ++ capiRequestContext.map(_.toStringTags).getOrElse(Map.empty)
            CapiLogMessage("capi.forceApiKeyDB", logLevel = LogLevel.WARN, stringTags = tags)
          }
          val teamName = TeamNameUtil.getTeamName(apiHeader.map(_.value()), whiteLabelId, boPath)
          if (boPath && isBoEnforce) {
            CapiBoApiKeyHistory(path, remoteAddress, isSuccess = true, teamName = Some(teamName)).sendAsync(CapiConfig.AdpApiKey)
          }

          val parent = Profiler.begin(shortened)
          Profiler.endPointTeamName.withValue(Some((shortened, teamName))) {
            implicit val um: ObjectMapper = JacksonMarshallers.AllMapper
            mapRouteResultFuture(rf => {
              rf fastFlatMap {
                case Complete(theResponse) if theResponse.headers.exists(_.name() == "NET") => Future.successful(Complete(theResponse))
                case Complete(theResponse) => trackResponse(theResponse, ctx, start, parent).fastMap(x => Complete(x))
                //TOD maybe redirect here? Also reports  400s and other rejections as a 404 sometimes
                case Rejected(r) =>
                  Future.successful(Rejected(r))
              }
            })(handleExceptions(exceptionHandler.exceptionHandler)(handleRejections(exceptionHandler.rejectionHandler)(HttpService.json8r(ctx)(allModuleRoutes))))(ctx)
          }
        }
    }
  }

  private def tmpApiKeysLog(failed: Boolean, ctx: RequestContext, apiHeader: Option[HttpHeader], remoteAddress: String,
      ipInWhitelist: Boolean, isLegacyExclude: Boolean, legacyKeyCheck: Boolean, failedCheckInDB: Boolean, whitelabelId: Int, isULEndpoints: Boolean)
                           (implicit capiRequestContext: Option[CapiRequestContextData]): Unit =
  {
    val (client: String, app: String, clientVersion: String) = ctxToHeaders(ctx.request)
    val path = ctx.request.uri.path.toString()
    val uri =
      if (path.contains("v2/transit/member/") || path.contains("v2/internal/members/") || path.contains("v2/internal/users/"))
        Constants.numericPattern.replaceAllIn(Constants.uuidPattern.replaceAllIn(path, "/UUID"), "/memberID")
      else path


    CapiMeasurementMessage("winterfell.apikey", stringTags = Map(
      "ip" -> remoteAddress,
      "endpoint" -> uri,
      "app" -> app,
      "failed" -> failed.toString,
      "ipInWhitelist" -> ipInWhitelist.toString,
      "failedCheckInDB" -> failedCheckInDB.toString,
      "isLegacyExclude" -> isLegacyExclude.toString,
      "isLegacyPolicy" -> legacyKeyCheck.toString
    ) ++ capiRequestContext.map(_.toMetricTags).getOrElse(Map.empty))

    val apiHeaderEncrypt = {
      if(capiRequestContext.isEmpty || capiRequestContext.exists(_.teamName.equalsIgnoreCase("Unknown")))
        apiHeader.map(_.value())
      else
        QueryUtil.encryptO(apiHeader.map(_.value()))
    }

    if (failed || ipInWhitelist || failedCheckInDB || isULEndpoints) {
      if(CapiConsul.reportLegacyApiKeyVerify) {
        CapiLogMessage("winterfell.apikey",
          stringTags = Map(
            "ip" -> remoteAddress,
            "failed" -> failed.toString,
            "failedCheckInDB" -> failedCheckInDB.toString,
            "whitelabelId" -> whitelabelId.toString,
            "ipInWhitelist" -> ipInWhitelist.toString,
            "endpoint" -> uri,
            "apiHeader" -> apiHeaderEncrypt.getOrElse("_"),
            "client" -> client,
            "clientVersion" -> clientVersion,
            "app" -> app,
            "isLegacyPolicy" -> legacyKeyCheck.toString
          ) ++ capiRequestContext.map(_.toStringTags).getOrElse(Map.empty))
      }
    }
  }

  def getForcedCache: Boolean = {
    val over = CapiConfig.overrideForceApiKey
    if (over != "none") over.toBoolean else CapiConsul.forceApiKey
  }

  def getBoForcedCache: Boolean = {
    val over = CapiConfig.overrideBoForceApiKey
    if (over != "none") over.toBoolean else CapiConsul.boForceApiKey
  }
  //exposed routes
  final def routes: Route = timeTrackedRoutes
}

//noinspection TypeAnnotation
object HttpService {
  val spamRequests = CapiConfig.config.getBoolean("spamAllRequests")
  val logWarnings = CapiConfig.config.getBoolean("logWarnings")
  val debugRequests = CapiConfig.config.getBoolean("debugRequests")
  val redirect_header = Set("redirect", "internal_redirect") // pragma: allowlist secret

  def headers(tuples: (String, String)*): List[HttpHeader] = tuples.map(x => RawHeader(x._1, x._2)).toList

  def addHeaders(headers: Seq[RawHeader]): RequestContext => RequestContext = { ctx =>
    val request = ctx.request
    ctx.withRequest(request.copy(headers = request.headers ++ headers))
  }

  def getSimplifiedSourceFromUserAgent(source: String): String = {
    val sourceLowercase = source.toLowerCase()
    if(sourceLowercase.contains("postman"))
      "postman"
    else if(sourceLowercase.contains("curl"))
      "curl"
    else
      "etc"
  }

  def respToMap(s: HttpResponse, request: HttpRequest, speedTag: String): Map[String, String] = {
    val hasSqlTimeout = if(s == null) false else s.entity match {
      case s: HttpEntity.Strict => s.data.utf8String.contains("The query has timed out")
      case _ => false
    }

    val endpoint = request.uri.path.toString
    val shortened = EndpointUtil.shortenUri(endpoint)

    val isLegacyPath = endpoint.startsWith("/V1") || endpoint.startsWith("/V2")
    val isBoPath = EndpointUtil.isBoPath(endpoint)

    val (client: String, app: String, clientVersion) = ctxToHeaders(request)

    val whiteLabelToken = request.headers.find(x => x.name.equalsIgnoreCase(Constants.whiteLabelTokenHeader)).map(_.value())
    val isWLTokenPresent = whiteLabelToken.flatMap(Option(_)).exists(_.nonEmpty)

    val whiteLabelId = Try{(WhiteLabelHelper.getWhiteLabelIdByToken(whiteLabelToken))}.getOrElse(0)

    val apiKeyValue = request.headers.find(_.name.equalsIgnoreCase(Constants.apiKeyHeader)).map(_.value())

    val teamName = TeamNameUtil.getTeamName(apiKeyValue, whiteLabelId, EndpointUtil.isBoPath(endpoint))

    val remoteAddress = getIpFromRequest(request)

    val agBotInfoValue = request.headers.find(_.name.equalsIgnoreCase(WebgateHeaderNames.BOT_INFO)).map(_.value())
    val agBotInfo = CapiRequestContextData.parseAgBotInfo(agBotInfoValue)
    val isBot = agBotInfo.map(botInfo => CapiRequestContextData.botTypes.flatMap(botInfo.botTypes.get(_)).filter(_ > 0).size > 0).getOrElse(false)

    val isRedirect = request.headers.exists(header => redirect_header.contains(header.name())).toString
    val isRemote = Option(s).map(_.headers.exists(header => header.is("isremote") && header.value == "true")).getOrElse(false).toString
    val isFallback = Option(s).map(_.headers.exists(header => header.is("isfallback") && header.value == "true")).getOrElse(false).toString

    val agEnv = getHeaderCaseInsensitive(request, WebgateHeaderNames.AG_ENV)
    val ULAppId = getHeaderCaseInsensitive(request, WebgateHeaderNames.APP_ID)
    val userAgent = getHeaderCaseInsensitive(request, HeaderNames.UserAgent)
    val simplifiedSource = userAgent.map(HttpService.getSimplifiedSourceFromUserAgent)

    val apcSourceServiceName = getHeaderCaseInsensitive(request, PrivateCloudHeaderNames.apcSourceServiceName)
    val xAgodaSsotName = getHeaderCaseInsensitive(request, PrivateCloudHeaderNames.xAgodaSsotName)
    val sourceServiceName = getHeaderCaseInsensitive(request, PrivateCloudHeaderNames.sourceServiceName)
    val xSourceServiceName = getHeaderCaseInsensitive(request, PrivateCloudHeaderNames.xSourceServiceName)
    val xEnvoyAttemptCount = getHeaderCaseInsensitive(request, PrivateCloudHeaderNames.xEnvoyAttemptCount)

    Map(
      "success" -> Option(s).map(_.status.isSuccess().toString).getOrElse("false"),
      "redirect" -> isRedirect,
      "isRemote" -> isRemote,
      "isFallback" -> isFallback,
      "isLegacyPath" -> isLegacyPath.toString,
      "isBoPath" -> isBoPath.toString,
      "status" -> Option(s).map(_.status.intValue()).getOrElse(500).toString,
      "endpoint" -> shortened,
      "teamName" -> teamName,
      "method" -> request.method.value,
      "client" -> client,
      "client_version" -> clientVersion,
      "app" -> app,
      "speed" -> speedTag,
      "ip" -> remoteAddress,
      "ipAddress" -> remoteAddress,
      "has_sql_timeout" -> hasSqlTimeout.toString,
      "whitelabelId" -> whiteLabelId.toString,
      "useWLToken" -> isWLTokenPresent.toString,
      "https" -> request.uri.scheme.equalsIgnoreCase("https").toString,
      "isBot" -> isBot.toString,
      "agEnv" -> agEnv.getOrElse("None"),
      "ULAppId" -> ULAppId.getOrElse("None"),
      "simplifiedSource" -> simplifiedSource.getOrElse("None"),
      "apcSourceServiceName" -> apcSourceServiceName.getOrElse("None"),
      "xAgodaSsotName" -> xAgodaSsotName.getOrElse("None"),
      "sourceServiceName" -> sourceServiceName.getOrElse("None"),
      "xSourceServiceName" -> xSourceServiceName.getOrElse("None"),
      "xEnvoyAttemptCount" -> xEnvoyAttemptCount.getOrElse("None")
    )
  }

  def speedTag(elapsed:Long):String = {
    elapsed match {
      case e if e >= 0 && e <= 100 => "fast"
      case e if e > 100 && e <= 1000 => "medium"
      case e if e > 1000 && e <= 10000 => "slow"
      case e if e > 10000 => "veryslow"
    }
  }

  def filtered(request: HttpRequest): immutable.Seq[HttpHeader] = request.headers.filterNot(x => x.name().contains("Timeout-Access") || x.name().contains("User-Agent") || x.name().contains("Origin"))

  def ctxToHeaders(request: HttpRequest) = {
    def getHeaderCaseInsensitive(name: String) = Try(request.headers.find(x => x.name().equalsIgnoreCase(name)).map(_.value())).toOption.flatten.getOrElse("Unknown")
    val client = getHeaderCaseInsensitive("X-Agoda-ClientMachine")
    val app = getHeaderCaseInsensitive("X-Agoda-ClientApp")
    val clientVersion = getHeaderCaseInsensitive("X-Agoda-CustomerApiClientVersion")
    val withIp = if(app == "Unknown") {
      getIpFromRequest(request)
    } else app
    (client, withIp,clientVersion)
  }

  def getIpFromRequest(request: HttpRequest): String ={
    getHeaderCaseInsensitive(request, "X-Real-IP").flatMap(_.split(":").headOption)
      .getOrElse(getHeaderCaseInsensitive(request, "Remote-Address").flatMap(_.split(":").headOption)
      .getOrElse {
        request.headers.collectFirst {
            case remote: akka.http.scaladsl.model.headers.`Remote-Address` =>
              remote.address.toOption.flatMap(HostResolver.hostNameFor)
                .orElse(remote.address.toIP.map(_.toString())).map(s => s.replaceAll("\\:" + remote.address.getPort().toString, ""))
          }.flatten.getOrElse("Unknown")
        }
      )
  }

  val incomingHttp = LoggerFactory.getLogger("httpLogger")
  val incomingHttpMember = LoggerFactory.getLogger("httpMemberLogger")

  import akka.http.scaladsl.server.Directives._
  import akka.http.scaladsl.server._
  import com.agoda.winterfell.marshalling.JsonMarshalling

  /**
    * Converts a potentially non application/json request to an application/json request.
    * Moving forward we want to not use  application/x-www-form-urlencoded
    * + multipart/form-data and do things either as primarily path parameters and a JSON
    * body for the input if the requirements are too complex for a simple query parameter or path variable.
    * We do want to support GET requests with query parameters and debug requests though, which this method does.
    *
    * @param c      route to run with the converted request
    * @param mapper to convert from the parameter Map to a JSON object
    * @return wrapped route
    */
  def json8r(ctx: RequestContext)(c: Route)(implicit mapper: ObjectMapper, mat: Materializer):Route = {
    parameterMap { m =>
      implicit val ec = Boot.Instance.httpExecutionContext
      val in = ctx.request.uri.toString
      if (in.contains("Online.txt") || in.contains("/HealthCheck.aspx")) {
        c
      }
      else {
        val start = System.currentTimeMillis()

        def handleStrict(theJson: String, strict: HttpEntity.Strict, wasStrict: Boolean) = {
          val ready = System.currentTimeMillis()
          val headers = ctx.request.headers.filterNot(x => x.name() == "Accept-Encoding" || x.name() == "Accept-Language" || x.name() == "Origin" || x.name() == "User-Agent" || x.name().contains("NewRelic") || x.name() == "Connection" || x.name() == "Accept" || (x.name().contains("Agoda") && x.name() != "X-Agoda-ClientMachine") || x.name().contains("Culture") || x.name().contains("PublicKey") || x.name() == "Host" || x.name() == "redirectOnError" || x.name() == "Timeout-Access").mkString(",")
          val parent = Profiler.retrieve

          val ip = ctx.request.headers.collectFirst {
            case remote: akka.http.scaladsl.model.headers.`Remote-Address` => remote.address.toOption.map(_.getHostAddress)
          }.flatten.getOrElse("Unknown")

          val tags = Map("is_strict" -> wasStrict.toString, "ip" -> ip)
          CapiMeasurementMessage("capi.http.delay", tags, ready - start)

          //We are getting spammed by GetFavoriteHotelsV3
          if (HttpService.spamRequests && !in.contains("GetFavoriteHotelsV3") && !in.contains("GetPermissionByComponentId")) {
            incomingHttp.info(s"${in.replaceAll("RewardsApiService", "RAS")} [${ctx.request.method.value}] <- $headers")
          }

          _: RequestContext => {
            val startExtra = System.currentTimeMillis()
            mapRouteResultFuture(rf => {
              rf.onComplete {
                case Failure(_: InvalidRequestException) => Unit
                //Stop NHA spam
                case Failure(t: Throwable) if HttpService.logWarnings && Option(t.getMessage).getOrElse("").contains("is inactive or invalid") =>
                  incomingHttp.warn(s"${ctx.request.uri} [${ctx.request.method.value}] -> ${t.getMessage}")
                case Failure(t: Throwable) if HttpService.logWarnings =>
                  incomingHttp.warn(s"${ctx.request.uri} [${ctx.request.method.value}] -> ${t.getMessage}",t)
                case _ => ()
              }
              rf fastFlatMap {
                case Complete(theResponse) =>
                  val completed = System.currentTimeMillis()
                  Profiler.trackWith("MappedInner", startExtra, completed, Some(parent))
                  Profiler.trackWith("MappedOuter", start, completed, Some(parent))
                  if (debugRequests && theResponse.entity.isInstanceOf[HttpEntity.Strict] && !in.contains("FetchMemberDetails") && !in.contains("GetMember") && !in.contains("GetPermissionByComponentId")) {
                    val bytes = theResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
                    incomingHttp.info(s"${ctx.request.uri} [${ctx.request.method.value}] -> \n$theJson".cyan + s"\n $bytes".green)
                  }
                 rf
                case Rejected(x) =>
                  val str = ctx.request.uri.path.toString().replaceAll("/", "")
                  if (!str.isEmpty && !(str.contains("3rd") && str.contains("risk")) && !str.contains("cuscoCallBack")) { //Stop spam for empty URL and others
                    val (client,app,_) = ctxToHeaders(ctx.request)
                    CapiMeasurementMessage("winterfell.404", Map("url"->ctx.request.uri.path.toString(),"client"->client,"app"->app) , 0)
                    CapiLogMessage("winterfell.404", stringTags =  Map ("url"-> ctx.request.uri.path.toString()),logLevel = LogLevel.WARN)
                    //Cusco callback spam
                    if (HttpService.logWarnings) incomingHttp.warn(s"Rejected2 ${ctx.request.uri} [${ctx.request.method.value}] : (${x.map(_.getClass.getSimpleName).mkString(",")})  -> \n$theJson")
                  }
                  rf
              }
            }).apply(c).apply(ctx.withRequest(ctx.request.withEntity(strict)))
          }
        }

        def handleMap(em: Map[String, AnyRef], isStrict: Boolean = false) = {
          val theJson = JsonMarshalling.pretty2(em ++ m)
          val strict = HttpEntity.Strict(Json, ByteString(theJson))
          handleStrict(theJson, strict, isStrict)
        }

        def handleNonStrict(content: AnyRef, isStrict: Boolean = false) = {
          val deserialized = content match {
            case deser: Vector[Map[String, AnyRef]@unchecked] => deser ++ m
            case deser: Map[String, AnyRef]@unchecked => deser ++ m
            case _ => Map.empty
          }
          val theJson = JsonMarshalling.pretty2(deserialized)
          val strict = HttpEntity.Strict(Json, ByteString(theJson))
          handleStrict(theJson, strict, isStrict)
        }

        if (ctx.request.entity.isKnownEmpty() || ctx.request.method == HttpMethods.GET) {
          handleMap(Map.empty, true)
        } else ctx.request.entity match {
           case value: HttpEntity.Strict =>
             handleStrict(value.data.utf8String, value, true)
          case _ =>
            extractRequest { req =>
              val um = getUnMarshallarNonStrict(req)
              entity(um) { em =>
                handleNonStrict(em)
              } ~ formFieldMap { fm =>
                handleMap(fm.asInstanceOf[Map[String, AnyRef]])
              }
            }

        }
      }
    }
  }
  def getHeaderCaseInsensitive(ctx: HttpRequest, name: String) = ctx.headers.find(x => x.name().equalsIgnoreCase(name)).map(_.value())

  // TODO - temp fix to allow huge bulk array request body
  def getUnMarshallarNonStrict(req:HttpRequest) = {
    import com.agoda.winterfell.marshalling.JacksonMarshallers.jacksonEntityUnmarshaller
    val urlPath = req.uri.path.toString()
    if((urlPath.endsWith("v2/internal/users") && (req.method == HttpMethods.POST || req.method == HttpMethods.PATCH)) ||
      (urlPath.contains("v2/internal/users/gdpr") && req.method == HttpMethods.PATCH) ||
      (urlPath.contains("v2/internal/audit") && req.method == HttpMethods.POST)){
      as[Vector[Map[String,AnyRef]]]
    }
    else as[Map[String,AnyRef]]
  }

  val Json: ContentType.WithFixedCharset = ContentTypes.`application/json`
}

