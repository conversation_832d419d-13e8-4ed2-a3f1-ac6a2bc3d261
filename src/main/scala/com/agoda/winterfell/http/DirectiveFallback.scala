package com.agoda.winterfell.http

import akka.actor.ActorSystem
import akka.http.scaladsl.model.HttpHeader.ParsingResult
import akka.http.scaladsl.model.{
  ContentTypes,
  HttpEntity,
  HttpHeader,
  HttpMethods,
  HttpRequest,
  HttpResponse,
  RequestEntity,
  StatusCode,
  StatusCodes
}
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.Directives.{
  as,
  complete,
  entity,
  extractRequest,
  handleExceptions,
  mapInnerRoute,
  mapRequestContext,
  pathPrefix,
  reject
}
import akka.http.scaladsl.server.RouteResult.{Complete, Rejected}
import akka.http.scaladsl.server.{RequestContext, Route, RouteResult, ValidationRejection}
import akka.util.ByteString
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.commons.serialization.v1.{Deserializer, Serializer}
import com.agoda.winterfell._
import com.agoda.commons.http.client.v2.{ClientSettings, HttpClient, RequestSettings}
import com.agoda.commons.http.client.v2.serialization.ResponseWithMetadata
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.mesh.v2.ServiceMesh.{Host, RequestTransformer}
import com.agoda.dal.http.client.{AKKAHTTPRequestRetry, UnhealthyHostException}
import com.agoda.winterfell.AppLogger
import com.agoda.winterfell.marshalling.JsonMarshalling
import com.agoda.winterfell.metrics.{CapiLogMessage, DalFallbackMetricReporter}
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.utils.{CapiConfig, CapiConsul, EndpointUtil, HostUtils}
import sttp.client3.UriContext
import sttp.model.Header
import cats.{MonadError => ME}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class DirectiveFallback(exceptionHandler: ExceptionHandler)(
    implicit val executionContext: ExecutionContext,
    actorSystem: ActorSystem)
    extends AppLogger {
  import HttpService._

  private val dalFallbackUtil = TempSingletonHolder.dalContext.get().dalFallbackUtil
  private val fallbackHandler = TempSingletonHolder.dalContext.get().fallbackHandler
  private val dalFallbackHttpClient =
    dalFallbackUtil.getHttpClient(new DalFallbackMetricReporter(Some("capi.dal.http_fallback.")))
  private val requestSettings =
    RequestSettings(ClientSettings(Some(CapiConsul.getAgHttpFallbackConfig.fallbackTimeout), None), Map.empty)

  private val forceRedirectRoute: Route =
    handleExceptions(exceptionHandler.redirectExceptionHandler) {
      import akka.http.scaladsl.server.Directives._
      pathPrefix("V1" | "V2" | "v2" | "v2expr" | "login" | "3rd") {
        extractRequest { req =>
          req.entity match {
            case s: HttpEntity.Strict => complete(tryRemote(req, s))
            case _ => nonStrictFallback(req)
          }
        }
      }
    }

  def toFutureOption[A](x: Option[Future[A]]): Future[Option[A]] =
    x match {
      case Some(f) => f.map(Some(_))
      case None => Future.successful(None)
    }

  def httpPost[Request, Response](path: String, body: Request, extraHeaders: Map[String, String])(
      implicit serializer: Serializer[Request, _],
      deserializer: Deserializer[_, Response],
      ME: ME[Future, Throwable]): Future[Option[Response]] = toFutureOption {
    for {
      httpClient <- dalFallbackHttpClient
      handler <- fallbackHandler
    } yield {
      def executeHttpRequest[Request, Response](
          host: Host,
          meshBuilder: RequestTransformer,
          body: Request,
          path: String,
          requestSettings: RequestSettings): Future[Response] = {
        val scheme = "http://"
        val prepare = meshBuilder.andThen(prepareRequestDelegate(extraHeaders))
        val uri = uri"$scheme${host.host}:${host.port}".withWholePath(path)
        httpClient.post[Request, Response](uri, body, prepare, requestSettings = requestSettings)(
          serializer.asInstanceOf[Serializer[Request, _]],
          deserializer.asInstanceOf[Deserializer[_, Response]])
      }
      val serviceMesh = handler.getMeshConfigType(CapiConsul.dalHttpFallbackType)

      val retryResult = AKKAHTTPRequestRetry.retryWithUnhealthyHostFilter[Response](
        serviceMesh,
        executeHttpRequest[Request, Response](_, _, body, path, requestSettings))
      retryResult.map(_.result)
    }
  }

  val AKKAHTTPRequestRetry = new AKKAHTTPRequestRetry {
    override def retry: Int = CapiConsul.getAgHttpFallbackConfig.maxAttempts
    override def futureDuration = CapiConsul.getAgHttpFallbackConfig.fallbackTimeout
    override def backOffDuration = CapiConsul.backoffDuration

    // override def validateHost for debug can tracking purpose
    override def validateHost(host: ServiceMesh.Host)(implicit serviceMesh: ServiceMesh): Unit = {
      import scala.collection.JavaConverters._
      //Check If host is dead, if yes, can throw new UnhealthyHostException({{host value}})
      //logger.warn(s"get ${host.host}:${host.port}")
      val current = System.currentTimeMillis
      val resource = serviceMesh.getRoundRobinCurrentResourceWeights()
      val listOfHost = resource.map(weight => s"${weight.resource.host.host}:${weight.resource.host.port}")
      val hostWithWeight =
        resource.map(weight => s"${weight.resource.host.host}:${weight.resource.host.port} (${weight.currentWeight})")
      val currentBlackListedHost = blacklistIp
        .getAllPresent(listOfHost.asJava)
        .asScala
        .filter(black => current - black._2 < backOffDuration.toMillis)
      if (CapiConsul.enableHttpFallbackKibanaLog) {
        val stringTags = Map(
          "inCache" -> currentBlackListedHost.map(t => s"${t._1} (${current - t._2})").mkString(","),
          "inCacheSize" -> currentBlackListedHost.size.toString,
          "hostWithWeight" -> hostWithWeight.mkString(","),
          "allDisable" -> (listOfHost.size == currentBlackListedHost.size).toString,
          "hostIsOff" -> currentBlackListedHost.contains(s"${host.host}:${host.port}").toString,
          "requestTo" -> s"${host.host}:${host.port}",
          "function" -> "validateHost"
        )
        CapiLogMessage(
          "capi.sh_fallback_response",
          LogLevel.INFO,
          stringTags = stringTags,
          message = Some(s"request to host ${host.host}"))
      }
      if (currentBlackListedHost.size < listOfHost.size && currentBlackListedHost.contains(
          s"${host.host}:${host.port}")) {
        throw new UnhealthyHostException(host.host)
      } else if (currentBlackListedHost.size == listOfHost.size) {
        blacklistIp.invalidateAll()
      }
    }
  }

  def mapRouteResultParallelWith = {
    mapInnerRoute { route => ctx =>
      if (CapiConsul.isDalFallbackEnableGlobal && CapiConsul.enableHttpFallback) {
        val start = System.currentTimeMillis()
        // check enable redirect
        val canFallback = checkCreateFallbackRequest(ctx)
        val executeOnlyLocal = checkExecuteOnlyLocalEndpoint(ctx)
        val executeOnlyRemote = checkExecuteOnlyRemoteEndpoint(ctx)
        // force select local or remote from header
        val forceSelect = getHeaderCaseInsensitive(ctx.request, "force")
        val isRedirect = getHeaderCaseInsensitive(ctx.request, "internal_redirect")
          .map(value => Try(value.toBoolean).toOption.getOrElse(false))
          .getOrElse(false) // pragma: allowlist secret
        val isMatchFallbackCondition = CapiConsul.isDalFallbackEnableGlobal && (forceSelect.isDefined || (CapiConsul.enableHttpFallback && canFallback))

        lazy val local = if (isMatchFallbackCondition) {
          mapRequestContext(addHeaders(Seq(RawHeader("mdcGroup", s"${CapiConfig.DcName.toLowerCase}-mdc")))) {
            route
          }(ctx)
        } else
          route(ctx)
        lazy val remote: Future[RouteResult] =
          if (!isRedirect && CapiConsul.isDalFallbackEnableGlobal && (forceSelect.isDefined || CapiConsul.enableHttpFallback))
            forceRedirectRoute(ctx)
          else
            reject(ValidationRejection("disabled remote"))(ctx)

        lazy val localWithIdentity = local.map("local" -> _)
        lazy val remoteWithIdentity = remote.map("remote" -> _)

        lazy val requests = if (forceSelect.isDefined) {
          if (forceSelect.get.equalsIgnoreCase("local"))
            Seq(localWithIdentity)
          else if (forceSelect.get.equalsIgnoreCase("remote"))
            Seq(remoteWithIdentity)
          else
            Seq(localWithIdentity, remoteWithIdentity)
        } else {
          if (executeOnlyRemote)
            Seq(remoteWithIdentity)
          else if (executeOnlyLocal)
            Seq(localWithIdentity)
          else if (CapiConsul.enableHttpFallback && CapiConsul.isDalFallbackEnableGlobal) {
            if (CapiConsul.alwaysFallback)
              Seq(remoteWithIdentity)
            else if (canFallback)
              Seq(localWithIdentity, remoteWithIdentity)
            else
              Seq(localWithIdentity)
          } else
            Seq(localWithIdentity)
        }

        lazy val additionalTags = Map(
          "canFallback" -> canFallback.toString,
          "executeOnlyLocal" -> executeOnlyLocal.toString,
          "executeOnlyRemote" -> executeOnlyRemote.toString,
          "isMatchFallbackCondition" -> isMatchFallbackCondition.toString,
          "requestSize" -> requests.size.toString,
          "forceSelectHeader" -> forceSelect.getOrElse("-"),
          "isFallback" -> "false"
        )

        val exec = Future.firstCompletedOf(requests).flatMap {
          case ("local", result) =>
            val (tags, elapsed) = getTagFromRouteResult(result, ctx, start)
            var stringTags = additionalTags ++ tags ++ Map("isRemote" -> "false")
            var longTags = Map("elapsed" -> elapsed)
            result match {
              case _ if executeOnlyLocal || forceSelect.getOrElse("-").equalsIgnoreCase("local") =>
                logFallbackResponse(ctx, stringTags = stringTags, longTags = longTags)
                Future.successful(mapRemoteAndFallbackRouteResult(result, isRemote = false))
              case _ if executeOnlyRemote || forceSelect.getOrElse("-").equalsIgnoreCase("remote") =>
                remoteWithIdentity.map(remoteResponse => {
                  val (tags, elapsed) = getTagFromRouteResult(remoteResponse._2, ctx, start)
                  stringTags = stringTags.map(map => s"local_${map._1}" -> map._2) ++ additionalTags ++ tags
                  longTags = longTags.map(map => s"local_${map._1}" -> map._2) ++ Map("elapsed" -> elapsed)
                  logFallbackResponse(ctx, stringTags = stringTags ++ Map("isRemote" -> "true"), longTags = longTags)
                  mapRemoteAndFallbackRouteResult(remoteResponse._2, isRemote = true)
                })
              case r: Complete if checkFallbackCondition(r, isMatchFallbackCondition) =>
                remoteWithIdentity.map(remoteResponse => {
                  val (tags, elapsed) = getTagFromRouteResult(remoteResponse._2, ctx, start)
                  stringTags = stringTags.map(map => s"local_${map._1}" -> map._2) ++ additionalTags ++ tags
                  longTags = longTags.map(map => s"local_${map._1}" -> map._2) ++ Map("elapsed" -> elapsed)
                  logFallbackResponse(
                    ctx,
                    stringTags = stringTags ++ Map("isRemote" -> "true") ++ Map("isFallback" -> "true"),
                    longTags = longTags)
                  mapRemoteAndFallbackRouteResult(remoteResponse._2, isRemote = true, isFallback = true)
                })
              case _ =>
                logFallbackResponse(ctx, stringTags = stringTags ++ Map("isFallback" -> "false"), longTags = longTags)
                Future.successful(mapRemoteAndFallbackRouteResult(result, isRemote = false))
            }
          case ("remote", result) =>
            val (tags, elapsed) = getTagFromRouteResult(result, ctx, start)
            var stringTags = additionalTags ++ tags ++ Map("isRemote" -> "true")
            var longTags = Map("elapsed" -> elapsed)
            result match {
              case _: Rejected =>
                localWithIdentity.map(localResponse => {
                  val (tags, elapsed) = getTagFromRouteResult(localResponse._2, ctx, start)
                  stringTags = stringTags.map(map => s"local${map._1}" -> map._2) ++ additionalTags ++ tags
                  longTags = longTags.map(map => s"local${map._1}" -> map._2) ++ Map("elapsed" -> elapsed)
                  logFallbackResponse(ctx, stringTags = stringTags ++ Map("isRemote" -> "false"), longTags = longTags)
                  mapRemoteAndFallbackRouteResult(localResponse._2, isRemote = false)
                })
              case _ =>
                logFallbackResponse(ctx, stringTags = stringTags, longTags = longTags)
                Future.successful(mapRemoteAndFallbackRouteResult(result, isRemote = true))
            }
        }
        exec
      } else {
        //disable create fallback request
        route(ctx)
      }
    }
  }

  def checkFallbackCondition(r: Complete, isMatchFallbackCondition: Boolean): Boolean = {
    //this should be similar to line generate remote value (line no 437)
    if (isMatchFallbackCondition) {
      if (Seq(StatusCode.int2StatusCode(400), StatusCode.int2StatusCode(401)).contains(r.response.status))
        true
      else if (r.response.headers.exists(header =>
          header.name.equalsIgnoreCase("legacyMemberNotFound") && header.value.equalsIgnoreCase("true")))
        true
      else
        false
    } else
      false
  }

  val spamList = Vector("healthcheck")
  def logFallbackResponse(ctx: RequestContext, stringTags: Map[String, String], longTags: Map[String, Long]): Unit = {
    val endpoint = ctx.request.uri.path.toString
    val shortened = EndpointUtil.shortenUri(endpoint)
    // log only when CapiConsul.enableHttpFallback && CapiConsul.isDalFallbackEnableGlobal are enable // avoid spam on other dc
    if (!spamList.exists(_.equalsIgnoreCase(shortened)) && CapiConsul.enableHttpFallback && CapiConsul.isDalFallbackEnableGlobal) {
      if (CapiConsul.enableHttpFallbackKibanaLog)
        CapiLogMessage(
          "capi.sh_fallback_response",
          logLevel = LogLevel.INFO,
          stringTags = stringTags,
          longTags = longTags)
    }
  }

  def nonStrictFallback(req: HttpRequest): Route = {
    import com.agoda.winterfell.marshalling.JacksonMarshallers.jacksonEntityAllUnmarshaller
    entity(as[Map[String, AnyRef]]) { em =>
      val theJson = JsonMarshalling.pretty2(em)
      val entity = HttpEntity.Strict(Json, ByteString(theJson))
      complete(tryRemote(req, entity))
    }
  }

  def tryRemote(request: HttpRequest, strictEntity: RequestEntity): Future[HttpResponse] = {
    def executeHttpRequest(host: Host, meshBuilder: RequestTransformer): Future[ResponseWithMetadata[String]] = {
      val start = System.currentTimeMillis
      val headers = request.headers.map(x => (x.name(), x.value())).toMap ++ Map(
        "content-type" -> "application/json",
        "internal_redirect" -> "true") // pragma: allowlist secret

      val withAdditionalHeaders = meshBuilder.andThen(prepareRequestDelegate(headers))
      val uri =
        uri"${request.uri.withScheme(CapiConsul.agHttpClientDalFallbackHttpsString).withHost(host.host).withPort(host.port).toString()}"
      import com.agoda.commons.http.client.v2.serialization.Serializer._
      import com.agoda.commons.http.client.v2.serialization.Deserializer._
      // ag-client v1 still have only get and post method, need to bump to v2
      val newRequest =
        if (request.method == HttpMethods.POST)
          dalFallbackHttpClient.get.post[String, ResponseWithMetadata[String]](
            uri,
            strictEntity.asInstanceOf[HttpEntity.Strict].data.utf8String,
            withAdditionalHeaders,
            requestSettings)
        else
          dalFallbackHttpClient.get.get[ResponseWithMetadata[String]](uri, withAdditionalHeaders, requestSettings)
      newRequest.onComplete {
        case Success(_) => // do nothing
        case Failure(exception) =>
          CapiLogMessage(
            "capi.sh_fallback_response",
            LogLevel.ERROR,
            stringTags = Map("function" -> "httpResponse"),
            longTags = Map("elapsed" -> (System.currentTimeMillis - start)),
            message = Some(s"request to host ${host.host}:${host.port}"),
            exceptionMessage = Some(exception.getMessage),
            exception = Some(exception)
          )
      }
      newRequest
    }
    def parseHttpResponse(res: ResponseWithMetadata[String]): HttpResponse = {
      val hosts = res.metadata.headers("host").map(header => Header("srcHost", header)) :+ Header(
        "originalHost",
        HostUtils.getHostName)
      // filter out for warning Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
      val toReturnHeaders = res.metadata.headers.filterNot(header =>
        header.is("Content-Type") || header.is("Content-Length") || header.is("Host"))
      val headers = (toReturnHeaders ++ hosts).map(header => {
        HttpHeader.parse(header.name, header.value) match {
          case ParsingResult.Ok(header, _) => header
          case ParsingResult.Error(error) => throw new Exception(s"Unable to convert to HttpHeader: ${error.summary}")
        }
      })
      HttpResponse(res.metadata.code.code, headers, HttpEntity.apply(ContentTypes.`application/json`, res.response))
    }

    val serviceMesh =
      fallbackHandler.map(_.getMeshConfigType(CapiConsul.dalHttpFallbackType)).getOrFail("unable to find serviceMesh")
    AKKAHTTPRequestRetry
      .retryWithUnhealthyHostFilter[ResponseWithMetadata[String]](serviceMesh, executeHttpRequest)
      .map(toReturn => {
        if (CapiConsul.enableHttpFallbackKibanaLog) {
          val stringTags = Map(
            "function" -> "checkResultFromRetry",
            "success" -> "true",
            "exceptionList" -> toReturn.exceptions.map(_.getClass.getName).mkString(","),
            "hostList" -> toReturn.hosts.mkString(","),
            "attempt" -> toReturn.attempts.toString,
            "statusCode" -> toReturn.result.metadata.code.code.toString,
            "statusText" -> toReturn.result.metadata.statusText,
            "isSuccess" -> toReturn.result.metadata.isSuccess.toString,
            "isClientError" -> toReturn.result.metadata.isClientError.toString,
            "isServerError" -> toReturn.result.metadata.isServerError.toString
          )
          CapiLogMessage("capi.sh_fallback_response", LogLevel.INFO, stringTags = stringTags)
        }
        parseHttpResponse(toReturn.result)
      })
      .recoverWith {
        case e: Throwable =>
          val stringTags = Map(
            "function" -> "checkResultFromRetry",
            "success" -> "false",
          )
          CapiLogMessage(
            "capi.sh_fallback_response",
            LogLevel.ERROR,
            stringTags = stringTags,
            exception = Some(e),
            exceptionMessage = Some(e.getMessage))
          throw e
      }
  }

  private def prepareRequestDelegate(
      additionalHeaders: Map[String, String]): HttpClient.SttpRequest[Any] => HttpClient.SttpRequest[Any] = {
    (req: HttpClient.SttpRequest[Any]) =>
      {
        var enrichedRequest = req
        additionalHeaders.foreach { header =>
          enrichedRequest = enrichedRequest.header(header._1, header._2, true)
        }
        enrichedRequest
      }
  }

  private def mapRemoteAndFallbackRouteResult(
      routeResult: RouteResult,
      isRemote: Boolean,
      isFallback: Boolean = false): RouteResult = {
    routeResult match {
      case complete: Complete =>
        complete.copy(
          response = complete.response.withHeaders(
            complete.response.headers ++ headers("isRemote" -> isRemote.toString, "isFallback" -> isFallback.toString)))
      case _ =>
        routeResult
    }
  }

  def checkCreateFallbackRequest(ctx: RequestContext): Boolean = {
    val endpoint = ctx.request.uri.path.toString
    val shortened = EndpointUtil.shortenUri(endpoint)
    val method = ctx.request.method
    CapiConsul.fallbackEndpoint.exists(permission =>
      permission.method.equals(method) && permission.endpoint.pattern.matcher(shortened).matches)
  }

  def checkExecuteOnlyLocalEndpoint(ctx: RequestContext): Boolean = {
    val endpoint = ctx.request.uri.path.toString
    val shortened = EndpointUtil.shortenUri(endpoint)
    val method = ctx.request.method
    CapiConsul.executeOnlyLocalEndpoint.exists(permission =>
      permission.method.equals(method) && permission.endpoint.pattern.matcher(shortened).matches)
  }

  def checkExecuteOnlyRemoteEndpoint(ctx: RequestContext): Boolean = {
    val endpoint = ctx.request.uri.path.toString
    val shortened = EndpointUtil.shortenUri(endpoint)
    val method = ctx.request.method
    CapiConsul.executeOnlyRemoteEndpoint.exists(permission =>
      permission.method.equals(method) && permission.endpoint.pattern.matcher(shortened).matches)
  }

  def getTagFromRouteResult(routeResult: RouteResult, ctx: RequestContext, startTime: Long) = {
    val elapsed = System.currentTimeMillis() - startTime
    routeResult match {
      case complete: Complete =>
        val response = complete.response
        (
          Map("isComplete" -> "true") ++ HttpService.respToMap(response, ctx.request, HttpService.speedTag(elapsed)),
          elapsed)
      case reject: Rejected =>
        (
          Map("isComplete" -> "false") ++ HttpService.respToMap(null, ctx.request, HttpService.speedTag(elapsed)),
          elapsed)
      case _ =>
        (
          Map("isComplete" -> "false") ++ HttpService.respToMap(null, ctx.request, HttpService.speedTag(elapsed)),
          elapsed)
    }
  }
}
