package com.agoda.winterfell.http.middleware

import akka.actor.ActorSystem
import akka.http.scaladsl.unmarshalling.Unmarshal
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.{Directive0, Route}
import akka.http.scaladsl.model.HttpRequest
import com.agoda.winterfell.backoffice.message.ApiKeyAuditLogMessageV1
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.models.{CapiRequestContextData, QueryUtil}
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.requests.{ApiConsumer, ApiKey, ApiKeyPolicyRequest, WhitelabelTokenRequest}
import com.agoda.winterfell.utils.CapiConfig

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

case class ApiKeyAuditLog(
    encryptedApiKey: Option[String] = None,
    apiPath: String,
    method: String,
    whitelabelTokens: Option[String] = None,
    teamName: Option[String] = None,
    endpoints: Option[String] = None,
    httpMethod: Option[String] = None,
    statusCode: Option[Int] = None)

class ApiKeyAuditLogger(implicit actorSystem: ActorSystem) {

  import JacksonMarshallers.jacksonEntityAllUnmarshaller

  private type DeserializationFunction = (HttpRequest, ApiKeyAuditLog) => Future[ApiKeyAuditLog]

  private def deserializeDeleteApiKey(request: HttpRequest, log: ApiKeyAuditLog): Future[ApiKeyAuditLog] = {
    Unmarshal(request.entity).to[ApiKey].map { apiKey =>
      log.copy(encryptedApiKey = Option(QueryUtil.encrypt(apiKey.key)))
    }
  }

  private def deserializeUpsertApiKey(request: HttpRequest, log: ApiKeyAuditLog): Future[ApiKeyAuditLog] = {
    Unmarshal(request.entity).to[ApiConsumer].map { apiConsumer =>
      val tokenList: String = Option(apiConsumer.whiteLabelTokens)
        .getOrElse(Seq.empty)
        .mkString(",")
      log.copy(
        encryptedApiKey = Option(QueryUtil.encrypt(apiConsumer.apiKey)),
        whitelabelTokens = if (tokenList.isEmpty) None else Some(tokenList),
        teamName = Some(apiConsumer.teamName)
      )
    }
  }

  private def deserializeUpsertAndRemoveApiKeyPolicy(
      request: HttpRequest,
      log: ApiKeyAuditLog): Future[ApiKeyAuditLog] = {
    Unmarshal(request.entity).to[ApiKeyPolicyRequest].map { apiKeyPolicyRequest =>
      val endpointsString: String = Option(apiKeyPolicyRequest.endpoints)
        .getOrElse(Seq.empty)
        .mkString(",")
      log.copy(
        encryptedApiKey = Option(QueryUtil.encrypt(apiKeyPolicyRequest.apiKey)),
        endpoints = if (endpointsString.isEmpty) None else Some(endpointsString),
        httpMethod = Some(apiKeyPolicyRequest.httpMethod)
      )
    }
  }

  private def deserializeUpsertAndRemoveWLToken(request: HttpRequest, log: ApiKeyAuditLog): Future[ApiKeyAuditLog] = {
    Unmarshal(request.entity).to[WhitelabelTokenRequest].map { whitelabelTokenRequest =>
      val tokenList: String = Option(whitelabelTokenRequest.whiteLabelTokens)
        .getOrElse(Seq.empty)
        .mkString(",")
      log.copy(
        encryptedApiKey = Option(QueryUtil.encrypt(whitelabelTokenRequest.apiKey)),
        whitelabelTokens = if (tokenList.isEmpty) None else Some(tokenList))
    }
  }

  private val deserializationMap: Map[String, DeserializationFunction] = Map(
    "/v1/api-key/deleteApiKey" -> deserializeDeleteApiKey,
    "/v1/api-key/upsertApiKey" -> deserializeUpsertApiKey,
    "/v1/api-key/upsertApiKeyPolicy" -> deserializeUpsertAndRemoveApiKeyPolicy,
    "/v1/api-key/removeApiKeyPolicy" -> deserializeUpsertAndRemoveApiKeyPolicy,
    "/v1/api-key/upsertWhitelabelToken" -> deserializeUpsertAndRemoveWLToken,
    "/v1/api-key/removeWhitelabelToken" -> deserializeUpsertAndRemoveWLToken,
  )

  def middleware(block: CapiRequestContextData => Route): Route = {
    extractRequestContext { ctx =>
      val path = ctx.request.uri.path.toString()
      val method = ctx.request.method.value

      val initialLog = ApiKeyAuditLog(apiPath = path, method = method)

      deserializationMap.get(path) match {
        case Some(deserializationFunction) =>
          try {
            val logFuture = deserializationFunction(ctx.request, initialLog)
            onSuccess(logFuture).flatMap { logWithRequestBody =>
              mapResponse { response =>
                val statusCode = response.status.intValue()
                val finalLog = logWithRequestBody.copy(statusCode = Some(statusCode))
                finalLog.encryptedApiKey.map { encryptedApiKey =>
                  {
                    CapiLogMessage(
                      "capi.api-key.audit.log",
                      stringTags = Map("path" -> path, "method" -> method, "success" -> true.toString))
                    ApiKeyAuditLogMessageV1(
                      encryptedApiKey = encryptedApiKey,
                      api = finalLog.apiPath,
                      whitelabelTokens = finalLog.whitelabelTokens,
                      teamName = finalLog.teamName,
                      endpoints = finalLog.endpoints,
                      statusCode = finalLog.statusCode
                    ).sendAsync(CapiConfig.AdpApiKey)
                  }
                }
                response
              }
            }
          } catch {
            case e: Exception =>
              CapiLogMessage(
                "capi.api-key.audit.log",
                stringTags = Map("path" -> path, "method" -> method, "success" -> false.toString),
                exception = Some(e))
              pass
          }
        case None =>
          pass
      }
      extractCapiRequestContext(ctx)(block)
    }
  }
}
