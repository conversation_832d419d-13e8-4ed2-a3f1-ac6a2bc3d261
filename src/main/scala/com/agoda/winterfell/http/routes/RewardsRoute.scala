package com.agoda.winterfell
package http.routes

import java.util.UUID

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.winterfell.http.Directives._
import com.agoda.winterfell.input.Inputs.AllTheRequests
import com.agoda.winterfell.input._
import com.agoda.winterfell.marshalling.{JacksonMarshallers, JsonMarshalling}
import com.agoda.winterfell.metrics.{CapiLogMessage, CapiMeasurementMessage}
import com.agoda.winterfell.models.ModelImplicits.{ActivityItemI, PointActivityI, ResponseItemI}
import com.agoda.winterfell.output._
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.unified.accessors.DefaultSyncer
import com.agoda.winterfell.utils.DBGroups.WriteGroup
import com.agoda.winterfell.utils.QueriesHelper.{result, resultSets}
import com.agoda.winterfell.utils.{CapiConsul, DBGroups, DBPlugin, QueriesHelper}
import org.joda.time.{DateTime, LocalDate}

import scala.collection.immutable.ListMap

// Everything should be migrated already to GiftCard
class RewardsRoute(dbPlugin: DBPlugin) {
  implicit val db: DBPlugin = dbPlugin
  val helper = new RewardsHelper()
  import JacksonMarshallers.jacksonWithNullsMarshaller

  val route: Route = {
    pathComplete("GetPointActivityList") { entity: MemberId ⇒
      helper.getPointActivities(entity.memberId)
    } ~ pathComplete("RefundRewards") { entity: RefundRewardsEntity ⇒
      helper.refundRewards(entity.get)
    } ~ pathCompleteP("ModifyRewardPoints2" | "ModifyRewardPoints3" | "ModifyRewardPoints4" | "ModifyRewardPoints") {
      e: AllTheRequests =>
        helper.modifyRewardsPoints(e)
    } ~ pathComplete("GetRedemptionOptions") { e: AllTheRequests =>
      helper.getRedemptionOptions(e)
    } ~ pathComplete("MigrateUserFromRewardsToGiftcards") { e: AllTheRequests =>
      helper.migrateUserToGiftCard(e)
    }
  }
}

class RewardsHelper(implicit db: DBPlugin) {
  import RewardsQueries._

  def getRedemptionOptions(e: AllTheRequests): Vector[RedemptionOption] = {
    val exchange = TempSingletonHolder.configRepository.getExchangeRate(e.requestedCurrency.getOrElse("USD"))
    val options = TempSingletonHolder.configRepository.getRedemptionOptions(elite = false, exchange)
    val pointsBalance = 0 // Rewards Points are not stored in CAPI anymore!
    options filter (_.convertedAmount.get <= e.finalPrice.get) filter (x => pointsBalance >= x.points)
  }

  case class MigrateUserFromRewardsToGiftcardsResult(
      isSuccessful: Boolean,
      exception: Option[String],
      pointActivityId: Int)

  def migrateUserToGiftCard(e: AllTheRequests): MigrateUserFromRewardsToGiftcardsResult = {
    val memberId = e.memberId.get
    val r = QueriesHelper
      .result(DBGroups.WriteGroup)
      .q(
        "api_disable_member_eligibility_and_set_points_to_zero_v2 ?,?,?,?,?,?",
        e.userId.get,
        memberId,
        "",
        0,
        1,
        e.giftCardLevel.get)
    if (r.isEmpty) MigrateUserFromRewardsToGiftcardsResult(false, None, 0)
    else {
      val value = r.get
      val message = value.strOption("message").getOrElse("")
      val pointActivityId = value.intOption("point_activity_id").getOrElse(0)
      val success =
        Try(value.booleanOption("isSuccess").getOrElse(false)).getOrElse(value.intOption("isSuccess").getOrElse(0) >= 1)
      MigrateUserFromRewardsToGiftcardsResult(success, Some(message).filterNot(_.isEmpty), pointActivityId)
    }
  }

  def modifyRewardsPoints(e: AllTheRequests): java.lang.Boolean = {
    Boolean.box(false)
  }

  def pointsInRange(e: AllTheRequests): ListMap[String, Any] = {
    val start = Try(JsonMarshalling.jacksonFormatterPlus7.parseDateTime(e.startDate.get.replaceAll("000", "00")))
      .map(_.toLocalDate)
      .getOrElse(new LocalDate(e.startDate.get.substring(0, 10)))
    val end = Try(JsonMarshalling.jacksonFormatterPlus7.parseDateTime(e.endDate.get.replaceAll("000", "00")))
      .map(_.toLocalDate)
      .getOrElse(new LocalDate(e.endDate.get.substring(0, 10)))
    val points = getPointsInRange(e.memberId.get, start, end)
    ListMap(
      "memberId" -> e.memberId.get,
      "startDate" -> e.startDate.get,
      "endDate" -> e.endDate.get,
      "pointsPending" -> points)
  }

  def getPointActivities(memberId: Int): PointActivity = {
    val results = apiPointSummarySelectCombined(memberId)
    if (results.isEmpty || results.head.isEmpty) {
      PointActivity(isEliteMember = false, 0, 0, 0, 0, 0, 0, 0, 0, hasCompletedBooking = false, 0, Vector.empty)
    } else {
      val points = PointActivityI.fromRow(results.head.head)
      val activities = ActivityItemI.fromRow(results.last)
      val elitePointRequired = {
        if (points.isEliteMember) 0
        else
          TempSingletonHolder.configRepository.getRewardsConfigurationDouble("ELITEP").toInt - results.head.head
            .int("points_booking_affected_year")
      }
      val minUSD = TempSingletonHolder.configRepository.getRewardsConfigurationDouble("MINRDM").toInt
      var balance = 0
      val adjusted = activities.map { a ⇒
        balance = balance + (a.pointEarned - a.pointUsed)
        a.copy(pointBalance = balance)
      }

      points.copy(
        amountOfPointUSD = points.totalPointAvailable / TempSingletonHolder.configRepository
          .getRewardsConfigurationDouble("RPPDOL")
          .toInt,
        activities = adjusted,
        requiredPointForElite = elitePointRequired,
        minRedeemUSD = minUSD
      )
    }
  }

  def refundRewards(entity: RefundRewardsEntity): RefundRewardsResponse = {
    val optRefundItem = refundRewards(
      entity.memberId,
      entity.bookingId,
      entity.bookingStatusId,
      entity.storefrontId,
      entity.bookingValue,
      entity.amount,
      Constants.AppId
    )

    val optResponse = optRefundItem.map { item ⇒
      try {
        val isMigrated = TempSingletonHolder.configRepository.getRewardsConfigurationDouble("RRGCIM").toInt == 1
        val isSuccessRequestStatus = item.rewardsRequestStatus == 1 // Success status

        if (isSuccessRequestStatus && isMigrated) {
          val (isSuccess, _, pointActivityId) = refundGiftCard(
            entity.memberId,
            5, //entity.storefrontId,
            s"Deleted refund for bookingid: ${entity.bookingId}. Will be refunded with gift card.",
            item.rewardsPoint
          )

          if (isSuccess) {
            val giftcardClient = TempSingletonHolder.giftcardClient

            giftcardClient
              .rewardsRefund(entity.memberId, entity.bookingId, item.rewardsPoint, entity.amount, Constants.AppId)
              .runSync
            val i = item.copy(
              rewardsRequestStatus = TempSingletonHolder.configRepository.getRewardsConfigurationDouble("RIGCSS").toInt,
              rewardsPoint = 0,
              pointActivityId = pointActivityId
            )
            RefundRewardsResponse(Some(i), isSuccess = true)
          } else {
            val i = item.copy(pointActivityId = pointActivityId)
            RefundRewardsResponse(Some(i), isSuccess = true)
          }

        } else RefundRewardsResponse(Some(item), isSuccess = true)
      } catch {
        case ex: Throwable ⇒
          RefundRewardsResponse(None, isSuccess = false, errorMsg = Some(ex.getMessage))
      }
    }

    val defaultResponse =
      RefundRewardsResponse(None, isSuccess = false, Some(s"Member with id: ${entity.memberId} does not exist."))
    optResponse.getOrElse(defaultResponse)
  }

  def refundRewards(
      memberId: Int,
      bookingId: Int,
      bookingStatusId: Int,
      storefrontId: Int,
      bookingValue: BigDecimal,
      amount: BigDecimal,
      userId: UUID): Option[ResponseItem] = {
    gpRewPointActivityRefundRewardsV3(memberId, bookingId, bookingStatusId, storefrontId, bookingValue, amount, userId)
      .map(ResponseItemI.fromRow(_))
  }

  def refundGiftCard(memberId: Int, storefrontId: Int, remarks: String, pointsAffected: Int): (Boolean, String, Int) = {
    val res = rew2PointActivityMain(
      memberId = memberId,
      storefrontId = storefrontId,
      remarks = remarks,
      userId = Constants.AppId,
      pointActivitySubtypeId = 700,
      pointsAffected = pointsAffected,
      affectedDate = DateTime.now(),
      isProcessBooking = false,
      runMode = "Normal_except_Expire2"
    )

    val isSuccess = res.exists(_.boolean("is_success") == true)
    val errorMessage = res.map(_.str("error_message")).getOrElse("")
    val pointActivityId = res.map(_.int("point_activity_id")).getOrElse(0)
    (isSuccess, errorMessage, pointActivityId)
  }

  private def getPointsInRange(memberId: Int, start: LocalDate, end: LocalDate): Int = {
    QueriesHelper
      .result(DBGroups.WriteGroup)
      .q(
        "EXEC api_get_pending_points_for_timespan ?, ?,?",
        memberId,
        start.toDateTimeAtStartOfDay,
        end.toDateTimeAtStartOfDay)
      .fold(0)(_.int("points_pending"))
  }

}

//noinspection TypeAnnotation
object RewardsQueries {
  def apiPointSummarySelectCombined(memberId: Int)(implicit db: DBPlugin) =
    resultSets(WriteGroup).q("EXEC dbo.api_point_summary_select_combined ?", memberId)

  //Below rewards
  def gpRewPointActivityRefundRewardsV3(
      memberId: Int,
      bookingId: Int,
      bookingStatusId: Int,
      storefrontId: Int,
      bookingValue: BigDecimal,
      amount: BigDecimal,
      userId: UUID)(implicit db: DBPlugin) = {
    result(WriteGroup).q(
      "EXEC dbo.gp_rew_point_activity_RefundReward_v3 ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?",
      memberId,
      bookingId,
      bookingStatusId,
      storefrontId,
      bookingValue,
      amount,
      userId,
      None,
      None,
      None,
      None
    )
  }

  def rew2PointActivityMain(
      memberId: Int,
      storefrontId: Int,
      remarks: String,
      userId: UUID,
      pointActivitySubtypeId: Int,
      pointsAffected: Int,
      affectedDate: DateTime,
      isProcessBooking: Boolean,
      runMode: String)(implicit db: DBPlugin) = {
    result(WriteGroup).q(
      "EXEC dbo.rew2_point_activity_main_v4 ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?",
      None,
      None,
      memberId,
      storefrontId,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      remarks,
      userId,
      pointActivitySubtypeId,
      pointsAffected,
      affectedDate,
      isProcessBooking,
      runMode
    )
  }
}
