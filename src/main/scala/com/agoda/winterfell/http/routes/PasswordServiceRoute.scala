package com.agoda.winterfell
package http.routes

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.{CustomerData, WhitelabelSupport}
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.winterfell.common.AuthenticationType
import com.agoda.winterfell.crypto.RandomUtil
import com.agoda.winterfell.http.{CapiAuthenticationDirective, CapiRequestContextDirectives}
import com.agoda.winterfell.input.Inputs.AllTheRequests
import com.agoda.winterfell.input.{
  BulkEmailPasswordResetting,
  BulkEmailPasswordResettingResponse,
  BulkPasswordResetting,
  BulkUsernamePasswordResetting,
  ChangePasswordWithTokenRequest2,
  EmailPasswordResettingResponse,
  PasswordResetting
}
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.requests._
import com.agoda.winterfell.models.{CapiRequestContextData, PasswordServiceGenericMessages, TokenizableCustomer}
import com.agoda.winterfell.output.ChangePasswordWithTokenResponseOutput
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.services.jwt.{TokenHelper, TokenHelperServer}
import com.agoda.winterfell.unified.progress.CryptoMetadataService
import com.agoda.winterfell.unified.{
  toLoginResponse,
  CaptchaInfo,
  ChangePasswordResult,
  Customer,
  CustomerConverters,
  ResetPasswordEmailArgs,
  UnifiedPasswordService
}
import com.agoda.winterfell.utils.CapiConsul.ApiKey
import com.agoda.winterfell.utils.{CapiConfig, CustomerRole, DBPlugin, ServiceAuthenticator}
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.ScalaObjectMapper
import monix.execution.{Ack, CancelableFuture}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import scala.concurrent.duration._

/**
  * Created by nalluri on 4/5/17.
  */
//TODO delete all usages of any of these calls, made obsolete by V2
class PasswordServiceRoute(
    override protected val apiKey: ApiKey,
    override protected val authenticator: ServiceAuthenticator,
    cryptoMetadataService: CryptoMetadataService,
    dbPlugin: DBPlugin
)(implicit val executionContext: ExecutionContext)
    extends CapiAuthenticationDirective
    with CapiRequestContextDirectives {

  implicit val asImplicit: DBPlugin = dbPlugin
  import JacksonMarshallers.{jacksonWithNullsMarshaller, primitiveMarshaller}

  implicit val unmap: ObjectMapper with ScalaObjectMapper = JacksonMarshallers.AllMapper
  implicit def staticCapiContext = CapiRequestContextData.emptyWlContext(Constants.AgodaId)

  val route: Route = pathComplete("ForgotPassword") { args: ForgotPasswordV1RequestHolder =>
    tmpLookup
      .resetPasswordEmail(
        ResetPasswordEmailArgs(
          args.username,
          CaptchaInfo(solved = true),
          CapiConfig.CuscoLostPasswordTemplateId.toString,
          None,
          None))
      .blockFor(10)
      .success
  } ~ pathComplete("ChangePasswordWithToken") { a: ChangePasswordWithTokenRequest2 =>
    tmpLookup
      .changePasswordWithTokenOnly(a.plainNewPassword, a.tokenData, endpoint = Some("ChangePasswordWithToken"))
      .fastMap(_.success)(Implicits.globalExecutor)
  } ~ pathComplete("ChangePasswordWithForgottenPasswordToken") { args: ChangePasswordWithTokenRequestHolder =>
    val theArg = args.arguments
    val r = tmpLookup
      .changePasswordWithTokenOnly(
        theArg.plainNewPassword,
        theArg.forgottenPasswordToken,
        endpoint = Some("ChangePasswordWithForgottenPasswordToken"))
      .blockFor(10)
    ChangePasswordWithTokenResponseOutput(r.toChangeResult, r.customer.map(c => toLoginResponse(c.originalModel())))
  } ~ withRequestTimeout(120.seconds) {
    path("ResetPasswordBulk") {
      authenticate { e: BulkPasswordResetting =>
        if (e.isAsync.getOrElse(true)) {
          implicit val context = Implicits.slowExecutionContext
          bulkReset(e);
          true
        } else {
          bulkReset(e)
        }
      }
    } ~
      path("ResetEmailPasswordBulk") {
        authenticate { e: BulkEmailPasswordResetting =>
          bulkReset(e)
            .map { resetResult =>
              val dictEmail = resetResult
                .flatMap(result => {
                  result.customer.map(
                    cus =>
                      cus.email.map(_.value.toLowerCase()).getOrElse("") -> EmailPasswordResettingResponse(
                        result.success,
                        result.success,
                        Some(cus.memberId)
                    ))
                })
                .toMap
              e.emailList.map(email =>
                email -> dictEmail.getOrElse(email.toLowerCase, EmailPasswordResettingResponse(false, false, None)))
            }
            .map(result => {
              val (success, failure) = result.partition { case (_, res) => res.success }
              BulkEmailPasswordResettingResponse(
                failure = failure.map { case (failure, _) => failure },
                success = success.flatMap { case (_, res) => res.memberId },
                rawResponse = if (e.returnRawResponse.getOrElse(false)) Some(result) else None
              )
            })
        }
      } ~
      path("ResetUsernamePasswordBulk") {
        authenticate { e: BulkUsernamePasswordResetting =>
          bulkReset(e)
            .map { resetResult =>
              val dictEmail = resetResult
                .flatMap(result => {
                  result.customer.map(cus =>
                    cus.email.map(_.value.toLowerCase()).getOrElse("") -> EmailPasswordResettingResponse(
                      result.success,
                      result.success && result.message.isEmpty,
                      Some(cus.memberId),
                      isYcs =
                        if (e.checkYcsRole.exists(y => y))
                          Some(CustomerRole.isYcsUser(cus.originalModel()))
                        else
                          None
                  ))
                })
                .toMap
              e.credentialList.map(credential =>
                credential.username -> dictEmail
                  .getOrElse(credential.username.toLowerCase, EmailPasswordResettingResponse(false, false, None)))
            }
            .map(result => {
              val (success, failure) = result.partition { case (_, res) => res.success }
              BulkEmailPasswordResettingResponse(
                failure = failure.map { case (failure, _) => failure },
                success = success.flatMap { case (_, res) => res.memberId },
                rawResponse = if (e.returnRawResponse.getOrElse(false)) Some(result) else None
              )
            })
        }
      }
  } ~ pathComplete("CheckForgottenPasswordToken") { e: AllTheRequests =>
    val token = e.forgottenPasswordToken.getOrFail("No token in CheckForgottenPasswordToken")
    TokenHelper.tryValidateToken(token, cryptoMetadataService, false, false)._2.map(_.email).toOption.flatten[String]
  }

  private def bulkReset(interface: PasswordResetting)(
      implicit executionContext: ExecutionContext,
      capiCtx: CapiRequestContextData): Future[Vector[ChangePasswordResult]] = {
    def createAsyncResetPasswordFuture(optionCustomer: Option[Customer], idx: Int): Future[ChangePasswordResult] = {
      optionCustomer
        .map(customer => {
          def logToHadoop =
            (success: Boolean, ex: String) =>
              PasswordServiceGenericMessages
                .BulkPasswordsResettingHistory(customer.memberId, success, interface.resetBy, false, None, None, ex)
                .sendAsync(Constants.AdpApiKey)
          def reset = () => {
            val newPassword = RandomUtil.randomAlphaNumeric(10)
            val fakeToken = TokenHelperServer.resetFor(TokenizableCustomer.from(customer))(capiCtx)
            val task = TempSingletonHolder.unifiedCustomerService.passwordService.changePasswordWithTokenOnly(
              newPassword,
              fakeToken.token,
              true,
              endpoint = Some("ResetPasswordBulk"))(capiCtx)
            task.onComplete { result =>
              result.fold(t => logToHadoop(false, t.toString), _ => logToHadoop(true, ""))
            }
            task
          }
          interface match {
            case e: BulkUsernamePasswordResetting =>
              if (customer
                  .originalModel()
                  .correctPassword(e.credentialList(idx).password.getOrElse(""))) { // password is match
                if (e.withoutReset.exists(t => t))
                  Future.successful(ChangePasswordResult(success = true, "skip", None, None, Some(customer)))
                else
                  reset()
              } else
                Future.successful(
                  ChangePasswordResult(success = true, "skip", Some("password isn't match"), None, Some(customer)))
            case _ =>
              reset()
          }
        })
        .getOrElse(Future.successful(
          ChangePasswordResult(success = false, "not found", Some("customer not found"), None)))
    }

    val customerList: Vector[Future[Option[Customer]]] = interface match {
      case e: BulkPasswordResetting =>
        e.memberIdList.map { m =>
          Future.successful(
            TempSingletonHolder.customerRepository.findCustomerByCustomerId(m, whitelabelId = Constants.AgodaId)(
              CustomerData.BaseCustomerData,
              WhitelabelSupport.NoWhitelabel,
              capiCtx))
        }
      case e: BulkEmailPasswordResetting =>
        if (e.emailList.length > CapiConfig.bulkEmailPasswordResettingLimit)
          throw new ArgumentInvalidException(
            s"Email Length shouldn't greater then ${CapiConfig.bulkEmailPasswordResettingLimit}")
        e.emailList.map { email =>
          TempSingletonHolder.customerRepository
            .measuredFindCustomerByUsernameAndAuth(
              email,
              AuthenticationType.Basic,
              whiteLabelId = Some(Constants.AgodaId))(CustomerData.BaseCustomerData, capiCtx)
            .toFuture
        }
      case e: BulkUsernamePasswordResetting =>
        if (e.credentialList.exists(_.password.isEmpty))
          throw new ArgumentInvalidException(s"CredentialList: password cannot be empty")
        if (e.credentialList.length > CapiConfig.bulkEmailPasswordResettingLimit)
          throw new ArgumentInvalidException(
            s"CredentialList Length shouldn't greater then ${CapiConfig.bulkEmailPasswordResettingLimit}")
        e.credentialList.map { credential =>
          TempSingletonHolder.customerRepository
            .measuredFindCustomerByUsernameAndAuth(
              credential.username,
              Option(credential.authType).flatten.map(_.toAuthType).getOrElse(AuthenticationType.Basic),
              whiteLabelId = Some(Constants.AgodaId))(CustomerData.BaseCustomerData, capiCtx)
            .toFuture
        }
    }

    val actions = customerList.zipWithIndex.map {
      case (customer, idx) =>
        customer.flatMap(createAsyncResetPasswordFuture(_, idx))
    }
    Future.sequence(actions)
  }

  private def tmpLookup: UnifiedPasswordService = TempSingletonHolder.unifiedCustomerService.passwordService
}
