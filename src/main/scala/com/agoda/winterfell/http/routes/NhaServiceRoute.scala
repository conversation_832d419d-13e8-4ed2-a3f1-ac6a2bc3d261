package com.agoda.winterfell.http.routes

import java.util.UUID
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.winterfell.Implicits
import com.agoda.winterfell.common.PropertyStatus
import com.agoda.winterfell.http.CapiRequestContextDirectives
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.input.PropertyListRequest
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.repository.NhaCustomerRepository
import com.agoda.winterfell.services.NhaCustomerService
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.module.scala.ScalaObjectMapper

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try
// legacy endpoint don't touch here
case class PropertyHostRequest(
    @JsonDeserialize(contentAs = classOf[java.lang.Integer]) propertyId: Option[Int],
    userId: Option[UUID],
    languageId: Int,
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean]) ignoreCache: Option[Boolean],
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean]) forceCache: Option[Boolean] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean]) reducedData: Option[Boolean] = None,
)

case class HostPropertyRequest(
    @JsonDeserialize(contentAs = classOf[java.lang.Integer]) propertyId: Option[Int],
    userId: Option[UUID],
    languageId: Int,
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean]) ignoreCache: Option[Boolean],
    @JsonDeserialize(contentAs = classOf[java.lang.Boolean]) forceCache: Option[Boolean] = None,
)

/**
  * Created by nrattanapolw on 6/26/2017.
  */
class NhaServiceRoute(nhaCustomerService: NhaCustomerService)(implicit ex: ExecutionContext)
    extends CapiRequestContextDirectives {

  import JacksonMarshallers.jacksonWithNullsMarshaller
  import com.agoda.winterfell.http.Directives._

  implicit val unmap: ObjectMapper with ScalaObjectMapper = JacksonMarshallers.AllMapper

  val route: Route = {
    extractRequestContext { ctx ⇒
      parameterMap { param =>
        extractCapiRequestContext { implicit capiCtx =>
          pathComplete("PropertyIds") { req: PropertyListRequest =>
            //Try(PropertyStatus(result.int("hotel_active_status")))
            val ycsRecStatus: Option[Int] = param.get("active").flatMap(s => Try(PropertyStatus(s.toInt).id).toOption)
            val isNHA: Option[Boolean] = param.get("isNHA").flatMap(s => Try(s.toBoolean).toOption)
            nhaCustomerService.propertyIds(req, ycsRecStatus, isNHA)
          } ~
            pathComplete("PropertyList") { req: PropertyListRequest =>
              nhaCustomerService.propertyList(req)
            } ~ path("BatchRun") {
            parameters('top ? "false", 'async ? "false") { (top, async) =>
              complete {
                if (async.toBoolean) {
                  Future {
                    nhaCustomerService.batchRun(top.toBoolean)
                  }
                  "async"
                } else {
                  nhaCustomerService.batchRun(top.toBoolean)
                }
              }
            }
          } ~
            mapCapiLegacyResponse(ctx) {
              pathComplete("TrustedHost") { p: PropertyHostRequest =>
                nhaCustomerService.infoForProperty(
                  p.propertyId,
                  p.userId,
                  p.languageId,
                  p.ignoreCache.getOrElse(false),
                  p.forceCache.getOrElse(false),
                  p.reducedData.getOrElse(false))
              }
            } ~ pathComplete("InvalidateNHATrustedHostCache") { p: PropertyHostRequest =>
            NhaCustomerRepository.invalidateCouchbaseCache(p).toString
          }
        }
      }
    }
  }
}
