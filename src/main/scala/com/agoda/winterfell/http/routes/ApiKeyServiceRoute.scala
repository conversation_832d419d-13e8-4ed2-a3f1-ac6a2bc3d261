package com.agoda.winterfell.http.routes

import java.util.UUID
import akka.http.scaladsl.server.Route
import com.agoda.winterfell.http.CapiRequestContextDirectives
import akka.http.scaladsl.server.Directives._
import com.agoda.winterfell.helper.WhiteLabelHelper
import com.agoda.winterfell.{Constants, Implicits}
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.models.requests.{<PERSON>piCons<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ApiKeyPolicyRequest, WhitelabelTokenRequest}
import com.agoda.winterfell.output.SuccessResponse
import com.agoda.winterfell.security.service.ApiKeyService
import com.agoda.winterfell.utils.CapiConsul
import com.agoda.winterfell.http.middleware.ApiKeyAuditLogger

import scala.concurrent.Future

class ApiKeyServiceRoute(apiKeyService: ApiKeyService, apiKeyAuditLogger: ApiKeyAuditLogger)
    extends CapiRequestContextDirectives {

  import Implicits.globalExecutor
  import JacksonMarshallers.{jacksonEntityAllUnmarshaller, jacksonWithNullsMarshaller}

  val route: Route =
    apiKeyAuditLogger.middleware { implicit ctx =>
      pathPrefix("v1") {
        pathPrefix("api-key") {
          path("syncApikeyFromLegacy") {
            get {
              def upsertApiKey(
                  apiKey: Map[String, String],
                  methods: Seq[String],
                  endpoint: Seq[String],
                  tokens: Seq[UUID]): Unit = {
                apiKey.foreach(apikey => {
                  apiKeyService
                    .upsertApiKey(
                      ApiConsumer(
                        apikey._2,
                        tokens,
                        apikey._1
                      ))
                    .flatMap {
                      case _ =>
                        Future.sequence(methods.map(method => {
                          apiKeyService.updateApiPolicy(
                            ApiKeyPolicyRequest(
                              apikey._2,
                              method,
                              endpoint
                            ),
                            false)
                        }))
                    }(Implicits.slowExecutionContext)
                })
              }
              val agodaToken = Constants.agodaUUID
              val jtbToken = UUID.fromString(WhiteLabelHelper.getWhiteLabelTokenFromId(Constants.JtbId))
              val japanicanToken = UUID.fromString(WhiteLabelHelper.getWhiteLabelTokenFromId(Constants.JapanicanId))
              val rurubuToken = UUID.fromString(WhiteLabelHelper.getWhiteLabelTokenFromId(Constants.RurubuId))
              val pricelineToken = UUID.fromString(WhiteLabelHelper.getWhiteLabelTokenFromId(Constants.PricelineId))
              // legacy path
              upsertApiKey(
                CapiConsul.keyMapList,
                Seq("get", "post"),
                Seq("/V1/.*", "/V2/.*", "/v2/transit/.*"),
                Seq(agodaToken, jtbToken, japanicanToken, rurubuToken, pricelineToken)
              )
              // backoffice path
              upsertApiKey(
                CapiConsul.boKeyMapList,
                Seq("get", "post", "patch", "delete"),
                Seq("/backoffice/.*"),
                Seq(agodaToken)
              )
              complete("Ok")
            }
          } ~
            path("getAllApiKeyPolicy") { get { complete(apiKeyService.getAllApiKey) } } ~
            path("getPolicyByApiKey") {
              post {
                entity(as[ApiKey]) { entity =>
                  complete(apiKeyService.getApiPolicyByApiKey(entity))
                }
              }
            } ~
            path("deleteApiKey") {
              delete {
                entity(as[ApiKey]) { entity =>
                  complete(apiKeyService.deleteApiKey(entity).map(_ => SuccessResponse.apply))
                }
              }
            } ~
            path("upsertApiKey") {
              put {
                entity(as[ApiConsumer]) { entity =>
                  complete(apiKeyService.upsertApiKey(entity).map(_ => SuccessResponse.apply))
                }
              }
            } ~
            path("upsertApiKeyPolicy") {
              put {
                entity(as[ApiKeyPolicyRequest]) { entity =>
                  complete(apiKeyService.updateApiPolicy(entity, false).map(_ => SuccessResponse.apply))
                }
              }
            } ~
            path("removeApiKeyPolicy") {
              delete {
                entity(as[ApiKeyPolicyRequest]) { entity =>
                  complete(apiKeyService.updateApiPolicy(entity, true).map(_ => SuccessResponse.apply))
                }
              }
            } ~
            path("upsertWhitelabelToken") {
              put {
                entity(as[WhitelabelTokenRequest]) { entity =>
                  complete(apiKeyService.updateWhitelabelTokens(entity, false).map(_ => SuccessResponse.apply))
                }
              }
            } ~
            path("removeWhitelabelToken") {
              delete {
                entity(as[WhitelabelTokenRequest]) { entity =>
                  complete(apiKeyService.updateWhitelabelTokens(entity, true).map(_ => SuccessResponse.apply))
                }
              }
            }
        }
      }
    }
}
