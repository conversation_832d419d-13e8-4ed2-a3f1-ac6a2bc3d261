package com.agoda.winterfell
package http.routes

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.CustomerData
import com.agoda.winterfell.common.{AuthenticationType, ResultStatus}
import com.agoda.winterfell.http.CapiAuthenticationDirective
import com.agoda.winterfell.http.Directives._
import com.agoda.winterfell.input._
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.repository.{CustomerRepository, SecurityRepository}
import com.agoda.winterfell.services.{SecurityService, _}
import com.agoda.winterfell.unified.CustomerServiceOps
import com.agoda.winterfell.utils.CapiConsul.ApiKey
import com.agoda.winterfell.utils.ServiceAuthenticator
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.ScalaObjectMapper

import scala.concurrent.Future

/**
  * Created by ysu<PERSON> on 5/23/2017.
  */
class SecurityRoutes(
    customerOps: CustomerServiceOps,
    securityService: SecurityService,
    securityRepository: SecurityRepository,
    customerRepository: CustomerRepository,
    override protected val apiKey: ApiKey,
    override protected val authenticator: ServiceAuthenticator)(implicit executionContext: ExecutionContext)
    extends CapiAuthenticationDirective {

  import JacksonMarshallers.{jacksonWithNullsMarshaller, primitiveMarshaller}

  implicit val unmap: ObjectMapper with ScalaObjectMapper = JacksonMarshallers.AllMapper

  private final val v1 = {

    val authorization = {
      extractCapiRequestContext { implicit ctx =>
        pathPrefix("Authorization") {
          pathComplete("IsEmailExist") { entity: SecurityRegisterCommand => //deprecated
            customerRepository.getCustomerByEmail(entity.email.getOrFail("No email for IsEmailExist")).isDefined
          } ~
            pathComplete("GetUserInfoByEmail") { entity: SecurityRegisterCommand =>
              TempSingletonHolder.delegator.getInfoByEmail(entity.email.getOrFail("No email"), entity.`type`.map(_.id))
            } ~
            pathComplete("IsUserInRoleApiKey") { entity: UserRoleApiKeyParam =>
              securityRepository.isUserInRoleApiKey(entity)
            } ~
            pathComplete("AddUserInRole2") { entity: AddUserInRole2Param =>
              securityService.addUserInRole2(entity)
            } ~
            pathComplete("GetUser") { entity: GetUserByUserNameParam =>
              customerRepository
                .measuredFindCustomerModelByUsernameAndAuth(
                  entity.username,
                  entity.authType,
                  whiteLabelId = Some(Constants.AgodaId))(CustomerData.BaseCustomerData, ctx)
                .map(result => result.flatMap(_.withAuthType(entity.authType.id)))
            } ~
            pathComplete("GetUserInfo") { entity: GetUserInfoParam =>
              TempSingletonHolder.delegator.getUserInfo(entity)
            } ~
            pathComplete("GetUserInRole3") { entity: GetUserInRole3Param =>
              securityService.getUsersByRole(entity, Some(ctx.whiteLabelId))
            } ~
            pathComplete("RemoveUserInRole3") { entity: RemoveUserInRoleParam =>
              securityService.removeUserInRole(entity)
            } ~
            pathComplete("RemoveSocialNetworkUser") { entity: SocialNetworkUserRemovingEntity =>
              securityService.removeSocialNetworkUser(entity)
            } ~
            pathComplete("GetPrincipal") { entity: GetPrincipalParam =>
              securityService.getPrincipal(entity)
            } ~
            pathComplete("GetSystemList") { entity: GetSystemListParam =>
              securityService.getSystemList(entity)
            } ~
            pathComplete("GetUserMappingList") { entity: UserMappingsGettingEntity => //Deprecated
              TempSingletonHolder.delegator.getUserMappings(entity)
            } ~
            pathComplete("GetAssignRoleAndApprovalList") { entity: AssignRoleListGettingEntity =>
              securityService.getAssignRoleAndApprovalList(entity)
            } ~
            pathComplete("GetAllLdapUsername") { entity: Object =>
              securityService.getAllLdapUsername()
            } ~
            pathComplete("GetAssignRoleList") { entity: AssignRoleListGettingEntity =>
              securityService.getAssignRoleList(entity)
            } ~
            pathComplete("GetAssignRoleListByObjectTypeAndObjectId") {
              entity: AssignRoleListWithObjectTypeAndObjectIdsGettingEntity =>
                securityService.getAssignRoleListByObjectTypeAndObjectId(entity)
            }
        }
      }
    }

    //Deprecated, delete after YCS
    val userManagement = {
      extractCapiRequestContext { implicit ctx =>
        pathPrefix("UserManagement") {
          pathComplete("ValidateUniqueEmailAddress") { entity: UserManagementParam => //deprecated
            customerRepository
              .getCustomerByEmail(entity.emailAddress.getOrFail("No email for ValidateUniqueEmailAddress"))
              .isEmpty
          } ~ pathComplete("ValidateUniqueUserName") { entity: UserManagementParam => //deprecated
            customerRepository
              .getCustomerByUsernameAndAuth(
                entity.userName.getOrFail("No email for IsEmailExist"),
                entity.authenticationType.getOrElse(AuthenticationType.Basic))
              .isEmpty
          } ~ pathComplete("CreateUser") { request: CreateUserParam =>
            customerOps
              .signup(request)
              .flatMap { result =>
                if (result.code == ResultStatus.EmailExists.id) {
                  customerOps
                    .getCustomerEntityFor(AuthenticationType.Basic, request.email, Constants.AgodaId)
                    .map(_.userId)
                } else
                  Future.successful(result.result.getOrFail(s"No result for create user $request : $result").userId)
              }
          }
        }
      }
    }

    //Deprecated, todo delete these all as soon as possible
    val token = {
      extractCapiRequestContext { implicit ctx =>
        pathPrefix("Token") {
          pathComplete("UriValidateRequest") { entity: UriValidateRequestParam =>
            TempSingletonHolder.delegator.mapLegacyValidateToToken(entity)
          } ~ pathComplete("Generate") { entity: GenerateTokenParam =>
            TempSingletonHolder.delegator.generateToken(entity)
          } ~ pathComplete("ValidateToken") { entity: ValidateTokenParam =>
            TempSingletonHolder.delegator.mapLegacyValidate(entity)
          } ~ pathComplete("GenerateForTransfer") { entity: GenerateTokenForTransferParam =>
            TempSingletonHolder.delegator.generateTokenForTransfer(entity)
          }
        }
      }
    }

    //Deprecated, but Teamwork is still using it
    val authentication = {
      extractCapiRequestContext { implicit ctx =>
        pathPrefix("Authentication") {
          pathComplete("BasicLogin") { entity: BasicLoginEntity =>
            TempSingletonHolder.delegator.authenticate(entity.userName, entity.password)
          }
        }
      }
    }

    pathPrefix("V1") {
      authorization ~ userManagement ~ token ~ authentication
    }
  }

  private final val v2 = {
    val user = {
      pathPrefix("User") {
        extractCapiRequestContext { implicit ctx =>
          path("BlockUserInRoleByProperty") {
            authenticate { entity: BlockUserInRoleByPropertyParam =>
              securityService.blockUserInRoleByProperty(entity)
            }
          } ~ pathComplete("GetUserByUserId") { entity: UserByUserIdGettingEntity =>
            TempSingletonHolder.delegator.getUserByUserId(entity)
          } ~ pathComplete("GetAuthUsers") { entity: GetAuthUsersRequest =>
            securityService.getAuthUsers(entity)
          } ~ pathComplete("GetAuthUsersByObjects") { entity: GetUserInRoleForObjectsRequest =>
            securityService.getAuthUsersByObjects(entity)
          } ~ pathComplete("GetAuthUsersByManager") { entity: GetAuthUsersByManagerRequest =>
            securityService.getAuthUsersByManager(entity)
          }
        }
      }
    }

    //TODO cupid still using it
    val basicAuthentication = {
      extractCapiRequestContext { implicit ctx =>
        pathPrefix("BasicAuthentication") {
          pathComplete("Login") { entity: SecurityLoginParam =>
            TempSingletonHolder.delegator
              .authenticate(entity.userName.getOrFail("No username"), entity.plainPassword.getOrFail("No password"))
          }
        }
      }
    }

    val authorization = {
      pathPrefix("Authorization") {
        extractCapiRequestContext { implicit ctx =>
          pathComplete("GetPrincipal") { entity: GetPrincipalParam =>
            securityService.getPrincipal(entity)
          } ~ pathComplete("GetRoleObjectsByRoleId") { entity: GetRoleObjectsByRoleIdParam =>
            securityRepository.getRoleObjectsByRoleId(entity.userId, entity.roleId, entity.objectType)
          } ~ pathComplete("GetPermissionByComponentId") { entity: GetPermissionByComponentIdParam =>
            securityService.getPermissionByComponentId(entity)
          } ~ pathComplete("GetSystemList") { entity: GetSystemListParam =>
            securityService.getSystemList(entity)
          } ~ pathComplete("GetRoleIdListByUserId") { entity: GetRoleIdListByUserIdParam =>
            securityRepository.getRoleIdListByUserId(entity.userId)
          } ~ pathComplete("GetRoleObjectsByRoleName") { entity: GetRoleObjectsByRoleNameParam =>
            securityRepository.getRoleObjectList(entity.userId, entity.roleName, entity.objectType)
          } ~ pathComplete("GetRoleObjectsByUserId") { entity: GetRoleObjectsByUserIdParam =>
            securityService.getRoleObjectsByUserId(entity)
          } ~ pathComplete("GetUserInRoleForObjects") { entity: GetUserInRoleForObjectsRequest =>
            securityService.getUserInRoleForObjects(entity)
          } ~ pathComplete("GetUserRelationsByObject") { entity: GetUserRelationsByObjectRequest =>
            securityService.getUserRelationsByObject(entity)
          } ~ pathComplete("GetUserRelationship") { entity: GetUserRelationshipRequest =>
            securityService.getUserRelationship(entity)
          } ~ pathComplete("GetUserRelationMoveToUser") { entity: GetUserRelationshipRequest =>
            securityService.getUserRelationMoveToUser(entity)
          } ~ pathComplete("GetUserRelationsInfoByObjectType") { entity: GetUserRelationsInfoByObjectTypeRequest =>
            securityService.getUserRelationsInfoByObjectType(entity)
          }
        }
      }
    }

    pathPrefix("V2") {
      user ~ basicAuthentication ~ authorization
    }

  }

  final val route: Route = v1 ~ v2
}
