package com.agoda.winterfell
package http.routes

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.input.FavoriteHotelParam
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.output.SuccessResponse
import com.agoda.winterfell.services.{FavoriteHotelService, TempSingletonHolder}
import com.agoda.winterfell.unified.UnifiedCustomerService

/**
  * Favorite Hotel end-points.
  * Created by trojana<PERSON><PERSON> on 4/26/17.
  */
//TODO replace usages with new model
class FavoriteHotelRoute(favoriteHotelService: FavoriteHotelService, customerService: UnifiedCustomerService) {

  import JacksonMarshallers.{jacksonEntityAllUnmarshaller, jacksonWithNullsMarshaller}
  import Implicits.globalExecutor

  val route: Route = {
    extractCapiRequestContext { implicit ctx =>
      entity(as[FavoriteHotelParam]) { param =>
        path("GetFavoriteHotelsV3") {
          complete(favoriteHotelService.getFavoriteHotel(param.memberId))
        } ~
          path("GetFavoriteHotelsByGroupV3") {
            complete(favoriteHotelService.getFavoriteHotelByGroup(param.memberId, param.groupId))
          } ~
          path("GetFavoriteHotelGroupsV3") {
            complete(favoriteHotelService.getFavoriteHotelGroup(param.memberId))
          } ~
          path("GetFavoriteHotelsBySharedHash") {
            complete(favoriteHotelService.getFavoriteHotelBySharedHash(param.sharedHash))
          } ~
          path("AddFavoriteHotelV3") {
            complete {
              val memberId = param.memberId.getOrFail("Empty member id")
              customerService
                .syncFavoriteHotels(
                  memberId,
                  toAdd = param.hotelIdList,
                  toReplace = Vector.empty,
                  toRemove = Vector.empty,
                  param.cityId)
                .map(_ => SuccessResponse.apply)
            }
          } ~
          path("RemoveFavoriteHotelV3") {
            complete {
              val memberId = param.memberId.getOrFail("Empty member id")
              customerService
                .syncFavoriteHotels(
                  memberId,
                  toAdd = Vector.empty,
                  toReplace = Vector.empty,
                  toRemove = param.hotelIdList,
                  enableCallCartApi = param.enableCallCartApi
                )
                .map(_ => SuccessResponse.apply)
                .recover { case _: ArgumentEmptyException => SuccessResponse.apply }
            }
          } ~
          path("GetFavoriteHotelsWithCityId") {
            complete(favoriteHotelService.getFavoriteHotelsWithCityId(param.memberId, ctx, param.enableCallCartApi))
          }
      }
    }
  }

}
