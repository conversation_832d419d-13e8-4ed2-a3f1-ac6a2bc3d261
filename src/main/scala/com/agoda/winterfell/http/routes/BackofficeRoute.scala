package com.agoda.winterfell
package http.routes

import java.util.UUID
import akka.http.scaladsl.marshalling.ToResponseMarshallable
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.server.directives.ParameterDirectives.parameters
import akka.http.scaladsl.server.directives.PathDirectives.pathPrefix
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.dal.crypto.KeystoreId
import com.agoda.dal.data.ByteString
import com.agoda.experiments.RequestContext
import com.agoda.winterfell.common.RecordStatus
import com.agoda.winterfell.http.CapiAuthenticationDirective
import com.agoda.winterfell.http.Directives._
import com.agoda.winterfell.input._
import com.agoda.winterfell.marshalling.{JacksonMarshallers, JsonMarshalling}
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.{QueryUtil, ToolExperimentRequest, ToolExperimentResponse}
import com.agoda.winterfell.models.errors.UnknownEntityException
import com.agoda.winterfell.output.{GenericAnyResponse, MemberContact}
import com.agoda.winterfell.security.service.AssociatePhoneBlockService
import com.agoda.winterfell.services.booking.bookingquery.{BookingDetailByBookingIdsRequest, BookingRoomIdRequest}
import com.agoda.winterfell.services.cncustomers.{CNBookingDataPushService, CNCustomerDataPushService}
import com.agoda.winterfell.services.cusco.CommunicationTemplate
import com.agoda.winterfell.services.{
  CouchbaseService,
  ExperimentContextConverter,
  OTPService,
  TempSingletonHolder,
  TokenService,
  UserService
}
import com.agoda.winterfell.unified.{BasicOtpArgs, CustomerCouchbase, CustomerServiceOps, SendOTPArgs}
import com.agoda.winterfell.unified.accessors.{AsyncMutator, DefaultSyncerHelper, ModelSyncer}
import com.agoda.winterfell.unified.otp.OtpServiceFactory
import com.agoda.winterfell.utils.CapiConsul.ApiKey
import com.agoda.winterfell.utils.{ByteStringHelper, CapiConsul, DalContext, EncryptionHelper, ServiceAuthenticator}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

/**
  * Created by ysubba on 2/27/2017.
  */
class BackofficeRoute(
    userService: UserService,
    customerOps: CustomerServiceOps,
    otpService: OTPService,
    couchbaseService: CouchbaseService,
    customerCouchbase: CustomerCouchbase,
    cnCustomerDataPushService: CNCustomerDataPushService,
    cnBookingDataPushService: CNBookingDataPushService,
    otpServiceFactory: OtpServiceFactory,
    associatePhoneBlockService: AssociatePhoneBlockService,
    override protected val apiKey: ApiKey,
    override protected val authenticator: ServiceAuthenticator)(implicit ec: ExecutionContext)
    extends CapiAuthenticationDirective {
  implicit val customerOpsImpt: CustomerServiceOps = customerOps

  val route: Route = {
    import JacksonMarshallers.{jacksonEntityAllUnmarshaller, jacksonWithNullsMarshaller, primitiveMarshaller}
    extractCapiRequestContext { implicit ctx =>
      pathComplete("VerifyOTP") { entity: OTPRequest ⇒
        otpService.verifyOTP(entity)
      } ~ pathComplete("SendOTP") { entity: OTPRequest ⇒
        otpService.sendOTP(entity)
      } ~ pathComplete("SendOTPv2") { entity: CombinedSendOTP ⇒
        otpService.sendOTPV2(entity)
      } ~
        pathComplete("VerifyOTPv2") { entity: CombinedVerifyOtp ⇒
          otpService.verifyOTPV2(entity)
        } ~ pathComplete("LegacySearchMember") { entity: LegacySearchMemberEntity ⇒
        userService.legacySearchMember(entity)
      } ~ pathComplete("GetMergedMember") { entity: MergedMemberGettingEntity ⇒
        userService.getMergedMembers(entity)
      } ~ pathComplete("MergeMember") { entity: MemberMergingEntity ⇒
        if (entity.invalidateMember.getOrElse(true)) {
          ModelSyncer.invalidateByMemberId(entity.activeId)
          ModelSyncer.invalidateByMemberId(entity.deactiveId)
        }
        userService.mergeMember(entity)
      } ~ pathComplete("InvalidateCachedMember") { entity: MemberIdUserId =>
        if (entity.memberId.isDefined)
          // got exception java.lang.String cannot be cast to java.lang.Integer
          ModelSyncer.invalidateByMemberId(Integer.parseInt(entity.memberId.get.toString))
        else
          ModelSyncer.invalidateByUserId(entity.userId.get)
      } ~ pathComplete("BulkInvalidateCachedMember") { entities: Vector[Int] =>
        entities.foreach(ModelSyncer.invalidateByMemberId); true
      } ~ pathComplete("InvalidateCouchbaseMember") { entity: MemberIdUserId =>
        customerCouchbase.wipeCustomer(entity.memberId.orElse(entity.userId).get)
      } ~ pathComplete("InvalidateDalMember") { entity: MemberIdUserId =>
        userService.invalidateDalMember(entity, isWipeData = true)
      } ~ pathComplete("BulkInvalidateDalMember") { entities: Vector[Int] =>
        entities.foreach(memberId =>
          userService
            .invalidateDalMember(MemberIdUserId(None, Some(memberId), Seq(RecordStatus.Active.id)), isWipeData = true));
        true
      } ~ pathComplete("readCidFromBookingQuery") { bookingIds: BookingDetailByBookingIdsRequest =>
        TempSingletonHolder.bookingQueryService.getBookingDetailByBookingId(bookingIds)
      } ~ pathComplete("writeCidFromBookingRoomId") { payload: BookingRoomIdRequest =>
        TempSingletonHolder.customerRepository.updateCidBookingPaxByBookingRoomId(
          payload.bookingRoomId.getOrFail("missing bookingRoomId"),
          payload.cidList.getOrFail("missing cidList"))
      } ~ pathComplete("readCidFromBookingQueryAndWriteToCnDB") { bookingIds: BookingDetailByBookingIdsRequest =>
        TempSingletonHolder.bookingQueryService.getBookingDetailByBookingId(bookingIds).map { bookingDetail =>
          Try {
            if (bookingDetail.bookingDetailsByBookingIds.isEmpty)
              throw new ArgumentEmptyException(s"bookingId: ${bookingIds.bookingIds.mkString(",")} not found")
            for {
              bookingRoomId <- bookingDetail.bookingDetailsByBookingIds
                .flatMap(
                  _.propertyBooking.flatMap(_.guests.map(_.headOption).headOption.flatten).flatMap(_.bookingRoomId))
                .headOption
                .orElse {
                  throw new ArgumentEmptyException(
                    s"bookingRoomId not found for bookingId: ${bookingIds.bookingIds.mkString(",")}")
                }
              cidList <- bookingDetail.bookingDetailsByBookingIds
                .flatMap(_.propertyBooking.flatMap(_.cidList))
                .headOption
                .orElse {
                  throw new ArgumentEmptyException(
                    s"cidList not found for bookingId: ${bookingIds.bookingIds.mkString(",")}")
                }
            } yield {
              TempSingletonHolder.customerRepository.updateCidBookingPaxByBookingRoomId(bookingRoomId, cidList)
            }
          } match {
            case Failure(exception) =>
              val stringTags = Map("bookingId" -> bookingIds.bookingIds.headOption.get.toString)
              CapiLogMessage(
                "capi.cid.backfilljob",
                logLevel = LogLevel.WARN,
                stringTags = stringTags,
                exception = Some(exception))
              Some(false)
            case Success(value) => value
          }
        }
      } ~ pathComplete("isNonLatinName") { entity: MemberId =>
        userService.isNonLatinName(entity.memberId, ctx.whiteLabelId)
      } ~ parameters("isPatchModifiedWhen".?) { isPatchModifiedWhen =>
        pathComplete("PushMemberToMDB") { entity: MemberId =>
          userService.pushMemberToMDB(
            entity.memberId,
            ctx.whiteLabelId,
            isPatchModifiedWhen = Try(isPatchModifiedWhen.exists(_.toBoolean)).getOrElse(false)
          )
        }
      } ~ pathComplete("PushMemberToMDC") { entity: MemberId =>
        userService.pushMemberToMDC(entity.memberId)
      } ~ pathComplete("patchDALRecCreatedWhen") { entity: MemberId =>
        userService.patchDALRecCreatedWhen(entity.memberId)
      } ~ pathComplete("PushMemberToSHMDB") { entity: MemberIdUserId =>
        val result =
          if (entity.memberId.isDefined)
            cnCustomerDataPushService.pushMDBDataToCNByMemberId(entity.memberId.get)
          else {
            cnCustomerDataPushService.pushMDBDataToCNByUserId(entity.userId.get)
          }
        result.recover({ case _ => false })(Implicits.globalExecutor)
      } ~ pathComplete("PushBookingToSHMDB") { entity: PushBookingToSHMDBRequest =>
        cnBookingDataPushService
          .pushBookingDataByBookingId(
            bookingId = entity.bookingId,
            whitelabelId = entity.whitelabelId,
            siteId = entity.siteId)
          .recover({ case _ => false })(Implicits.globalExecutor)
      } ~ pathComplete("PushMemberToDAL") { pushMemberToDalRequest: MigrateCustomerRequest =>
        userService.pushMemberToDAL(pushMemberToDalRequest.customerIds)
      } ~ pathComplete("PushNHAToDal") { entity: MemberIdUserId =>
        userService.pushNHAToDal(entity)
      } ~ pathComplete("PushExtraPropertyToDAL") { entity: MemberIdUserId =>
        userService.pushExtraPropertyToDAL(entity) //
      } ~ pathComplete("InvalidateNHADal") { entity: MemberIdUserId =>
        userService.invalidateNHADal(entity)
      } ~ pathComplete("CompareNHADal") { compareUserIds: CompareUserIds =>
        userService.compareNHADal(compareUserIds).filter(_.inConsistent)
      } ~ pathComplete("SetOrigin") { userOrigin: UserOrigin =>
        customerOps.setOrigin(userOrigin.userId, userOrigin.origin).map(_ => true)(Implicits.globalExecutor)
      } ~ pathComplete("NextCounterId") { entity: CounterEntity =>
        userService.nextCounterId(entity)
      } ~ pathComplete("GeniusEmailHash") { entity: MemberId =>
        userService.getGeniusEmailHash(entity.memberId)
      } ~ (path("GeniusEmailHashes") & parameter("memberIds".as(intCsvSeq))) { memberIds =>
        complete(userService.getGeniusEmailHashes(memberIds))
      } ~ pathComplete("BoGetMemberContactList") { entity: MemberId =>
        TempSingletonHolder.customerRepository
          .findCustomerByMemberId(entity.memberId, true)
          .map[ToResponseMarshallable] { customer =>
            if (CapiConsul.hideDeleteBoGetMemberContactList)
              customer.legacyActiveContacts(false)
            else
              customer.legacyAllContacts
          }
          .getOrElse(Vector.empty[MemberContact])
      } ~ pathComplete("GetBookingList") { entity: BookingListGettingEntiy =>
        userService.boGetBookings(entity)
      } ~ pathComplete("GetTransferredBookingList") { entity: MergeId =>
        userService.getTransferredBookingList(entity)
      } ~ pathPrefix("DebugCouchbase") {
        // These endpoints will only be used for debugging and testing OTP
        (post & parameter("dataType".as[String].?)) { dataType =>
          {
            entity(as[CouchbaseRequest]) { entity: CouchbaseRequest =>
              {
                complete(couchbaseService.update(entity, dataType.getOrElse("String")))
              }
            }
          }
        } ~ pathPrefix(Segment) { key: String =>
          (get & parameter("expiration".as[Int].?) & parameter("dataType".as[String].?)) { (expiration, dataType) =>
            complete(couchbaseService.read(key, expiration, dataType.getOrElse("String")))
          } ~
            delete {
              complete(couchbaseService.delete(key))
            }
        }
      }
    } ~
      // Internal endpoints for CAPI team only!
      // path /V1/RewardsApiService/tools/
      extractCapiRequestContext { implicit ctx =>
        (post & pathPrefix("tools") & withoutSizeLimit) {
          pathPrefix("verifyEmail") {
            authenticate { req: MemberIdUserId =>
              val asyncMutator: AsyncMutator = (req.userId, req.memberId) match {
                case (Some(userId), _) =>
                  AsyncMutator(userId, whitelabelId = ctx.whiteLabelId, ctx = ctx)
                case (_, Some(memberId)) =>
                  AsyncMutator(memberId, whitelabelId = ctx.whiteLabelId, ctx = ctx)
                case _ =>
                  throw new ArgumentInvalidException(
                    "Either userId or memberId must be provided, but both were empty or null")
              }
              asyncMutator.verifyEmail(ValidateEmailVerificationRequest(None, None))
            }
          } ~
            pathPrefix("experiment") {
              authenticate { experimentRequest: ToolExperimentRequest =>
                val requestContext: RequestContext = ExperimentContextConverter.fromCapiExperimentContext(
                  experimentRequest.experimentContext.getOrElse(ExperimentContext()))
                val determineVariant = TempSingletonHolder.experimentService.determineVariant(
                  experimentRequest.experimentName,
                  experimentRequest.experimentContext
                )
                ToolExperimentResponse(
                  determineVariant = determineVariant,
                  requestContext = requestContext
                )
              }
            } ~
            pathPrefix("otp") {
              path("resetCount") {
                authenticate { entity: BasicOtpArgs =>
                  otpService.cleanSendOtpBlockStatus(entity.target, entity.clientData)
                }
              } ~
                path("targetCheck") {
                  authenticate(otpServiceFactory.getOtpService(ctx, None).targetCheck(_: SendOTPArgs))
                }
            } ~
            pathPrefix("mobile") {
              path("encrypt") {
                authenticate(EncryptionHelper.encrypt(_: String))
              } ~
                path("decrypt") {
                  authenticate(EncryptionHelper.decrypt(_: String))
                }
            } ~
            pathPrefix("associatePhone") {
              path("resetCount") {
                authenticate { args: BasicOtpArgs =>
                  if (args.target.isEmpty)
                    associatePhoneBlockService.resetCountMemberId(args.memberId.requiredGet("missing memberId"))
                  else
                    associatePhoneBlockService.resetCount(
                      args.memberId.requiredGet("missing memberId"),
                      args.target.requiredGet("missing target"))
                }
              } ~
                path("isBlock") {
                  authenticate { args: BasicOtpArgs =>
                    if (args.target.isEmpty)
                      associatePhoneBlockService.isMemberIdAllow(args.memberId.requiredGet("missing memberId"))
                    else
                      associatePhoneBlockService.isAllow(
                        args.memberId.requiredGet("missing memberId"),
                        args.target.requiredGet("missing target"))
                  }
                }
            } ~
            pathPrefix("gdprCached") {
              path("isBlock") {
                authenticate { args: MemberId =>
                  TempSingletonHolder.accountDeletionCachedService.getAccountStatus(args.memberId).toString
                }
              } ~
                path("resetCount") {
                  authenticate { args: MemberId =>
                    TempSingletonHolder.accountDeletionCachedService.clean(args.memberId).toString
                  }
                }
            } ~
            pathPrefix("legacy") {
              path("encrypt") { authenticate(QueryUtil.encrypt(_: String)) } ~
                path("decrypt") { authenticate(QueryUtil.decrypt(_: String)) } ~
                path("hash") { authenticate(QueryUtil.hash(_: String)) } ~
                path("boPiiHash") { authenticate(QueryUtil.boPiiHash(_: String)) }
            } ~
            path("encrypt") {
              val keystoreId = KeystoreId(ctx.keystoreId.getOrElse(DalContext.defaultKeystoreId))
              authenticate(
                TempSingletonHolder.dalContext
                  .get()
                  .cryptoService
                  .encrypt(_: String, keystoreId = keystoreId)
                  .toHexString)
            } ~
            path("decrypt") {
              authenticate { ciphertext: String =>
                val normalizedCiphertext = ByteStringHelper.hexStringToByteString(ciphertext)
                TempSingletonHolder.dalContext.get().cryptoServiceV3.decrypt[String](normalizedCiphertext)
              }
            } ~
            path("reencrypt") {
              authenticate { request: ReencryptMemberRequest =>
                userService.reencryptMembers(request.userIds)
              }
            } ~
            path("maskingPII") {
              authenticate { request: MaskingMemberPIIRequest =>
                userService.maskingMembersPII(request.memberIds)
              }
            } ~
            path("migrateCustomerHashData") {
              authenticate { req: MigrateCustomerRequest =>
                customerOps.migrateCustomerHash(req.customerIds)
              }
            } ~
            path("deleteMigrationHashData") {
              authenticate { req: MigrateCustomerRequest =>
                customerOps.deleteMigrationHash(req.customerIds)
              }
            } ~
            path("compareMdbDal") {
              //Pass comma seperated uuid string ex 0A35C4C1-2B4C-4616-AFE5-007BA4CB8172,ec799791-8107-470c-9e2c-3a281089a95d
              authenticate { compareUserIds: CompareUserIds =>
                val result = customerOps.compareUserResult(compareUserIds.userIds)
                result.filter(_.inConsistent)
              }
            } ~
            path("updateMemberId") {
              authenticate { req: UpdateCustomerMemberId =>
                customerOps.updateCustomerMemberId(req)
              }
            } ~
            path("decryptNumber") {
              authenticate { ciphertext: String =>
                val normalizedCiphertext = ByteStringHelper.hexStringToByteString(ciphertext)
                TempSingletonHolder.dalContext.get().cryptoService.decrypt[Double](normalizedCiphertext)
              }
            } ~
            path("decrypt-token") {
              authenticate { ciphertext: String =>
                TokenService.parse(ciphertext).get
              }
            } ~
            path("hash") {
              authenticate(TempSingletonHolder.dalContext.get().cryptoService.hash(_: String).mkString("\n"))
            } ~
            path("mapping") {
              authenticate { payload: Map[String, Any] =>
                DefaultSyncerHelper
                  .userToUuidHash(
                    payload("username").toString,
                    payload("type").asInstanceOf[Int],
                    payload.get("whitelabel").map(_.asInstanceOf[Int]).getOrElse(Constants.AgodaId)
                  )
                  .toString
              }
            } ~
            path("role") {
              authenticate { payload: Map[String, Any] =>
                val userId = UUID.fromString(payload("userId").toString)
                val roleId = UUID.fromString(payload("roleId").toString)
                QueryUtil.roleHash(
                  userId,
                  roleId,
                  payload("objectType").toString,
                  payload("objectId").asInstanceOf[Int])
              }
            } ~
            path("consumer-offsets") {
              authenticate { request: ConsumerOffsetRequest =>
                userService.setConsumerOffset(request)
              }
            } ~
            path("replicator-status") {
              authenticate { empty: Map[String, String] =>
                TempSingletonHolder.dalContext.get().kafkaReplication.map(_.consumerStatus)
              }
            } ~
            pathPrefix("cusco") {
              path("templateVersion") {
                authenticate { _: Map[String, String] =>
                  GenericAnyResponse(CommunicationTemplate.getTemplateMapper)
                }
              }
            }
        }
      }
  }
}
