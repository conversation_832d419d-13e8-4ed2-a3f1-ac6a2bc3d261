package com.agoda.winterfell
package http.routes

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.winterfell.common.{Registration, ResultStatus}
import com.agoda.winterfell.http.CapiRequestContextDirectives
import com.agoda.winterfell.input._
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.QueryUtil
import com.agoda.winterfell.services.UserService
import com.agoda.winterfell.unified.CustomerServiceOps

/**
  * <AUTHOR>
  */
class RegistrationRoute(customerOps: CustomerServiceOps, userService: UserService)(
    implicit executionContext: ExecutionContext)
    extends CapiRequestContextDirectives {

  val route: Route = {
    import com.agoda.winterfell.marshalling.JacksonMarshallers.jacksonWithNullsMarshaller
    extractCapiRequestContext { implicit capiCtx =>
      pathComplete("InsertMemberOnBooking") { command: InsertMemberOnBookingCommand =>
        // TODO: drop PII from tags!
        val tags = Map(
          "email" -> QueryUtil.encrypt(command.command.email),
          "phone" -> QueryUtil.encrypt(command.command.phone),
          "userId" -> Option(command.command.userId).map(_.toString).orNull,
          "memberId" -> command.command.memberId.getOrElse(0).toString,
          "clientApp" -> capiCtx.clientApp.getOrElse("").toString,
          "whitelabelToken" -> capiCtx.whiteLabelToken.getOrElse("").toString,
          "whitelabelId" -> capiCtx.whiteLabelId.toString
        )
        CapiLogMessage("capi.insert_member_on_booking", LogLevel.INFO, tags, message = Some(s"InsertMemberOnBooking"))
        userService.insertMemberOnBookingV2(command.command)
      } ~
        pathCompleteP(
          "RegisterRewardsUserAndAssociateSocial" | "RegisterRewardsUserB" | "RegisterRewardsUserC" | "RegisterRewardsUserD" | "RegisterRewardsUser") {
          c: RegisterCommand =>
            customerOps
              .signup(c)
              .map { result =>
                if (result.code == ResultStatus.EmailExists.id) throw new IllegalStateException(result.errorMessage.get)
                else
                  unified.toRegisterResultDiscarding(
                    result,
                    c.discardFakeEmail,
                    c.registrationType.contains(Registration.Phone))
              }
              .report("capi.dal.member_register")
        }
    }
  }
}
