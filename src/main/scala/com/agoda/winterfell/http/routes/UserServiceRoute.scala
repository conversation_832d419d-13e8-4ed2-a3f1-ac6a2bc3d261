package com.agoda.winterfell.http.routes

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.winterfell.http.Directives._
import com.agoda.winterfell.input._
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.output.ContactMethod
import com.agoda.winterfell.services.UserService

import scala.concurrent.ExecutionContext
import scala.reflect.macros.whitebox

class UserServiceRoute(userService: UserService)(implicit executionContext: ExecutionContext) {

  import JacksonMarshallers.jacksonWithNullsMarshaller

  val route: Route = {
    extractCapiRequestContext { implicit ctx =>
      pathComplete("GetMemberContactList") { entity: MemberId ⇒
        userService.getMemberContactList(entity, ctx.whiteLabelId)
      } ~
        pathPrefix("GetMemberContactsByContactValue") {
          get {
            parameters('contactMethod, 'contactValue, 'readFromMDB.as[Boolean], 'isCustomerRequired.as[Boolean].?) {
              (contactMethod, contactValue, readFromMDB, isCustomerRequired) ⇒
                complete(
                  userService.getMemberContactByContactValue(
                    contactMethod = ContactMethod.withName(contactMethod),
                    contactValue = contactValue,
                    readFromMDB = readFromMDB,
                    whiteLabelId = ctx.whiteLabelId,
                    isCustomerRequired = isCustomerRequired.getOrElse(false),
                  )
                )
            }
          }
        } ~
        pathComplete("UserInRoleForObject") { entity: AuthorizedUsersRequest ⇒
          userService.getUserInRoleForObject(entity)
        } ~
        pathComplete("GetMember2") { entity: MemberGettingByMemberCode ⇒
          userService.getMemberByMemberCode(entity)
        } ~
        pathComplete("GetMemberByBookingId") { entity: BookingId ⇒
          userService.getMemberByBookingId(entity)
        } ~
        pathComplete("GetUserTypeByPhoneNumber") { entity: PhoneNumber ⇒
          userService.getUserTypeByPhoneNumber(entity.value)
        } ~
        pathComplete("UpdateMemberEmailAddress") { entity: MemberEmail =>
          import JacksonMarshallers.primitiveMarshaller
          userService.updateMemberEmail(entity)
        } ~
        pathComplete("CheckDuplicateEmailContactMethod") { entity: CheckDuplicateEmailContactMethod =>
          userService.checkDuplicateEmailContactMethod(entity)
        }
    }
  }
}
