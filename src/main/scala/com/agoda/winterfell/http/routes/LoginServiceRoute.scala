package com.agoda.winterfell
package http.routes

import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.Route
import com.agoda.winterfell.common.LoginStatusCode
import com.agoda.winterfell.http.Directives._
import com.agoda.winterfell.input.{InvalidateLegacyTokenArgs, LoginParam}
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.services.LoginService
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.ScalaObjectMapper

/**
  * Created by ysubba on 1/12/2017.
  */
class LoginServiceRoute(loginService: LoginService) {

  import JacksonMarshallers.{jacksonEntityAllUnmarshaller, jacksonWithNullsMarshaller}

  implicit val unmap: ObjectMapper with ScalaObjectMapper = JacksonMarshallers.AllMapper

  val route: Route = {
    extractRequestContext { ctx ⇒
      extractCapiRequestContext(ctx) { implicit capiCtx ⇒
        pathComplete("InvalidateToken") { args: InvalidateLegacyTokenArgs =>
          loginService.invalidateToken(args.tokenId)
          ""
        } ~
          entity(as[LoginParam]) { args =>
            mapCapiLegacyResponse(ctx) { loginRoutes(args) }
          } ~ reject
      }
    }
  }

  private def loginRoutes(args: LoginParam)(implicit ctxData: CapiRequestContextData) = {
    def exec = complete {
      loginService.consolidatedLogin(args.canonical).memberOrEmpty
    }

    def execFull = {
      complete {
        val r = loginService.consolidatedLogin(args.canonical)
        val res = r.copy(
          rewardsMemberUserInfo =
            if (r.loginStatusCode == LoginStatusCode.ValidityPassed
              || r.loginStatusCode == LoginStatusCode.ValidityFailure
              || r.loginStatusCode == LoginStatusCode.UserLocked) {
              Some(r.memberOrEmpty)
            } else {
              r.rewardsMemberUserInfo
            })
        //TODO temporary fix for website, the failed login count is not really needed
        //Return 3 to force them to show a captcha in the meantime if for any reason it was missed
        if (res.loginStatusCode == LoginStatusCode.ConfirmLogin & res.failedLoginAttempts < 3)
          res.copy(failedLoginAttempts = 3)
        else if (res.loginStatusCode == LoginStatusCode.Authenticated) res.copy(failedLoginAttempts = 0)
        else res
      }
    }

    path("LoginV3") { execFull } ~
      path("Login") { exec } ~ //Minimal calls from agoda.mobile.api.pci.web.global
      path("LoginAndGetToken") { exec } ~ //Mediums number of calls from agoda.mobile.api.pci.web.global
      path("LoginWithToken") {
        args.retrieveToken.filterNot(_.isEmpty).getOrFail("No token data provided")
        exec
      } ~
      path("LoginWithSocialWithClientTrackingData") { exec }
  }
}
