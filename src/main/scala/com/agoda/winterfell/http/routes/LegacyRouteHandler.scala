package com.agoda.winterfell
package http.routes

import scala.reflect.ClassTag
import com.agoda.dal.data.Default
import com.agoda.dal.concurrent.MeasuredFuture
import Constants.DefaultQueryParams
import com.agoda.{FallbackSupport, WhitelabelSupport}
import com.agoda.dal.model.EntityStatus
import com.agoda.winterfell.common.{AuthenticationType, RecordStatus}
import com.agoda.winterfell.input.MemberTraveler
import com.agoda.winterfell.input.Inputs.AllTheRequests
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.models.entities.{
  CustomerModel,
  CustomerNote,
  CustomerPartnerMembership,
  PartnerMembershipId
}
import com.agoda.winterfell.models.errors.UnknownEntityException
import com.agoda.winterfell.output._
import com.agoda.winterfell.repository.ConfigRepository
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.unified.Customer
import com.agoda.winterfell.unified.accessors.{AsyncMutator, DefaultSyncer}
import com.agoda.winterfell.utils.{CapiConsul, DBPlugin, DalContext}

import scala.util.{Failure, Success}
import scala.concurrent.ExecutionContext

/**
  * Not a real route, but maps a bunch of old (and mostly useless) routes
  * to equivalent responses.
  */
object LegacyRouteHandler {
  var config: Option[ConfigRepository] = None

  private val byMemberIdGets: List[(String, Customer => AnyRef with java.io.Serializable)] = List(
    ("GetMemberPreferredDestinationList", _.legacyFields.preferredDestinations),
    ("GetMemberAddressList", _.legacyFields.addresses),
    ("GetMemberStaffNoteList", _.legacyFields.notes),
    ("GetPartnerMemberships", _.fields.memberships),
    ("IsMemberCreditCardsOnFileOptOut", _.fields.ccof),
  )

  private val byMemberIdGetsMap = byMemberIdGets.toMap

  def handleRequest(path: String, body: String)(
      implicit context: ExecutionContext,
      capiCtx: CapiRequestContextData): Option[AnyRef with java.io.Serializable] = {
    implicit val db: DBPlugin = Option(TempSingletonHolder.userService).map(_.db).orNull
    val chopped = path.replaceAll("/V1/RewardsApiService/", "").replaceAll("/V1/BORewardsApiService/", "")
    val withoutBo = if (chopped.startsWith("Bo")) chopped.substring(2) else chopped

    lazy val req = JacksonMarshallers.AllMapper.readValue[AllTheRequests](body)

    lazy val theMemberId = req.memberId.getOrFail("No member id")

    if (withoutBo == "GetMemberIdListByEmail") {
      //Only called by EBE for duplicate booking checking : used to work with alternate email ids, but this support has been dropped
      Some(
        TempSingletonHolder.customerRepository
          .getCustomerByUsernameAndAuth(req.email.requireOrFail("No email"), AuthenticationType.Basic)
          .map(_.memberId)
          .toVector)
    } else {
      val memberGetResponse = byMemberIdGetsMap.get(withoutBo).map { m =>
        m(TempSingletonHolder.customerRepository.customerByMemberId(theMemberId, true))
      }

      //Just return response directly or via config
      val fixedResponse = Some(withoutBo) collect {
        case "GetMemberTitleList" => List("Mr.", "Ms.", "Mrs.", "Dr.")
        case "HasSignUpPoint" => Boolean.box(false)
        case "FetchRewardApprovalReasons" => MoreLegacyGarbage.fetchMemberReasons(req.`type`.getOrFail("No type"))
        case "FetchCustomerCommentList" => MoreLegacyGarbage.fetchCommentList
        case "GetTravelerType" =>
          config.map(_.getTravelerTypes(req.languageId.getOrFail("No language"))).getOrElse(Vector.empty)
      }

      lazy val makeMemberShip =
        PartnerMembership(req.programId.getOrFail("No program id"), req.membershipId.getOrElse(""))
      lazy val validateAndMakeMemberShip =
        PartnerMembership(req.programId.filter(_ > 0).getOrFail("Invalid program id"), req.membershipId.getOrElse(""))
      val updatedMemberships: (Vector[PartnerMembership], CustomerModel) => CustomerModel = (v, c) =>
        c.copy(fields = c.fields.copy(memberships = v))

      //Vector based updates
      val vectored = Some(withoutBo) collect {
        case "SaveMemberStaffNote" =>
          mergeProperty[StaffNote](
            req.note.getOrFail("No note"),
            false,
            false,
            req.note.get.memberId,
            _.legacyFields.notes,
            (v, c) => c.copy(legacyFields = c.legacyFields.copy(notes = v)))
        case "SaveMemberPreferredDestination" => req.destination.getOrFail("No destination")
        case "SaveMemberAddress" =>
          val memberAddress = req.memberAddress.getOrFail("No address")
          val isAddressToDelete = memberAddress.recordStatus.contains(-1)
          val isAddressToReplace = memberAddress.addressId.exists(_ > 0)
          val fixedMemberAddress = if (isAddressToReplace) memberAddress.copy(recordStatus = Some(1)) else memberAddress
          mergeProperty[MemberAddress](
            fixedMemberAddress,
            isAddressToDelete,
            isAddressToReplace,
            memberAddress.memberId,
            _.legacyFields.addresses,
            (v, c) => c.copy(legacyFields = c.legacyFields.copy(addresses = v))
          )
        case "AddMemberTravelerType" => MemberTraveler(req.memberId.get, req.travelerType.get, req.userId)
        case "RemoveMemberTravelerType" => Boolean.box(true)
        case "UpdateMembership" =>
          mergeProperty(validateAndMakeMemberShip, false, true, theMemberId, _.fields.memberships, updatedMemberships);
          Boolean.box(true)
        case "InsertMembership" =>
          mergeProperty(validateAndMakeMemberShip, false, false, theMemberId, _.fields.memberships, updatedMemberships);
          Boolean.box(true)
        case "DeleteMembership" =>
          mergeProperty(makeMemberShip, true, false, theMemberId, _.fields.memberships, updatedMemberships);
          Boolean.box(true)
      }

      val updateCC = if (withoutBo == "SaveMemberCreditCardsOnFileOptOut") {
        AsyncMutator(theMemberId, Constants.AgodaId, capiCtx)(
          Implicits.slowExecutionContext,
          TempSingletonHolder.customerServiceOps)
          .updateCCOF(req.optOut.getOrFail("No opt out"))
          .waitFor(5, "capi.dal.update_ccof")
      } else None

      val updatePrius = if (withoutBo == "UpdatePreferredPriusProgram") {
        AsyncMutator(theMemberId, Constants.AgodaId, capiCtx)(
          Implicits.slowExecutionContext,
          TempSingletonHolder.customerServiceOps)
          .updatePreferredMembership(req.programId)
          .recover { case _: UnknownEntityException => None }(Implicits.slowExecutionContext)
          .waitFor(5, "capi.dal.update_prius")
      } else None

      memberGetResponse orElse fixedResponse orElse vectored orElse updateCC orElse updatePrius
    }
  }

  def mergeProperty[T <: HasIdentifiers](
      item: T,
      isDelete: Boolean = false,
      isReplace: Boolean = false,
      memberId: Int,
      existing: CustomerModel => Vector[T],
      replace: (Vector[T], CustomerModel) => CustomerModel)(
      implicit DBPlugin: DBPlugin,
      context: ExecutionContext,
      capiCtx: CapiRequestContextData,
      tag: ClassTag[T]): HasIdentifiers = {
    def mergeVectors(in: Vector[T]): Vector[T] = {
      val (replace, other) = in.partition(_.id == item.id)
      if (isDelete) other //Delete  by filter it out
      else if (replace.isEmpty && !isReplace) {
        val lastId = other.sortBy(_.id).lastOption.map(_.id).getOrElse(0)
        other :+ item.withNewId(lastId).asInstanceOf[T]
      } //Insert
      else if (replace.isEmpty) other //Do nothing
      else
        in map { //Update
          case i if replace.contains(i) => item.asInstanceOf[T]
          case x => x
        }
    }
    implicit val whitelabelSupport = WhitelabelSupport.NoWhitelabel // make it compatible with wl-id 1
    val customerModel = TempSingletonHolder.customerServiceOps
      .getCustomerEntityFor(memberId, capiCtx.whiteLabelId, EntityStatus.OnlyActive)
      .flatMap(entity => TempSingletonHolder.customerServiceOps.getBaseCustomer(entity, EntityStatus.OnlyActive))
      .blockAndWait30s
      .originalModel()
    val existingVector = existing(customerModel)
    val updated = mergeVectors(existingVector)
    val result: HasIdentifiers = if (isDelete || isReplace) item else updated.lastOption.orNull

    new DefaultSyncer().syncFromMemberId(memberId, { c =>
      val existingVector = existing(c)
      val updated = mergeVectors(existingVector)

      val postUpdate = replace(updated, c)
      postUpdate.touch(None)
    })

    val activity = item match {
      case note: StaffNote =>
        val needDelete = note.recordStatus.contains(RecordStatus.Deleted.id)
        val noteId = CustomerNote.noteIdFor(note, updated.lastOption.map(_.id).getOrElse(1))
        if (needDelete) {
          val customerNote = Default[CustomerNote].copy(noteId = noteId, userId = customerModel.userId)
          TempSingletonHolder.customerNoteRepo.get().softDelete(customerNote, DefaultQueryParams)()
        } else {
          val toInsert = existingVector.isEmpty || existingVector.maxBy(_.id).id + 1 == updated.maxBy(_.id).id
          TempSingletonHolder.customerNoteRepo
            .get()
            .unsafeSave(
              CustomerNote.from(noteId, customerModel, if (toInsert) updated.last.asInstanceOf[StaffNote] else note),
              DefaultQueryParams)()
        }

      case address: MemberAddress =>
        val noteId = CustomerNote.noteIdFor(address, updated.lastOption.map(_.id).getOrElse(1))
        if (isDelete) {
          val customerNote = Default[CustomerNote].copy(noteId = noteId, userId = customerModel.userId)
          TempSingletonHolder.customerNoteRepo.get().softDelete(customerNote, DefaultQueryParams)()
        } else {
          val toInsert = existingVector.isEmpty || existingVector.maxBy(_.id).id + 1 == updated.maxBy(_.id).id
          TempSingletonHolder.customerNoteRepo
            .get()
            .unsafeSave(
              CustomerNote
                .from(noteId, customerModel, if (toInsert) updated.last.asInstanceOf[MemberAddress] else address),
              DefaultQueryParams)()
        }

      case membership: PartnerMembership =>
        val partnerMembershipId = PartnerMembershipId(customerModel.userId, membership.programId)
        if (isDelete) {
          val customerMembership = Default[CustomerPartnerMembership].copy(id = partnerMembershipId)
          TempSingletonHolder.partnerMembershipRepo.get().unsafeHardDelete(customerMembership, DefaultQueryParams)()
        } else {
          val isPreferred = membership.programId == customerModel.fields.prius.getOrElse(0)
          val membershipRepo = TempSingletonHolder.partnerMembershipRepo.get()
          membershipRepo
            .findById(partnerMembershipId, DefaultQueryParams)()
            .flatMap {
              case Some(partnerMembership) =>
                membershipRepo.save(
                  partnerMembership.copy(membershipId = membership.membershipId, isPreferred = isPreferred),
                  DefaultQueryParams)()
              case None =>
                membershipRepo.save(
                  CustomerPartnerMembership.from(customerModel, membership, isPreferred),
                  DefaultQueryParams)()
            }
        }

      // Ignore deprecated entities
      case _: TravellerBox => MeasuredFuture.unit()
      case _: MemberDestination => MeasuredFuture.unit()
    }
    DalContext.reportActivity("capi.dal.merge_property", activity)
    activity.andThen {
      case Success(_) =>
        TempSingletonHolder.customerCouchbase.wipeCustomer(customerModel.userId, customerModel.memberId)
    }(Implicits.slowExecutionContext)
    activity.blockFor(Constants.MdcQueryTimeout)
    result
  }
}

//Some extra garbage ...

object MoreLegacyGarbage {

  case class IdWithText(id: Int, text: String)

  def fetchCommentList: Vector[IdWithText] = Vector(
    IdWithText(1, "Customer Service"),
    IdWithText(2, "Media"),
    IdWithText(3, "Facebook Prize"),
    IdWithText(4, "Prize")
  )

  def fetchMemberReasons(id: Int): Vector[IdWithText] =
    if (id == 1)
      Vector(
        IdWithText(1, "Complaint About Agoda"),
        IdWithText(2, "[obsolete] Complaint About Hotel"),
        IdWithText(3, "[obsolete] YCS Mismanagement"),
        IdWithText(4, "[obsolete] Unable to Confirm (YCS)"),
        IdWithText(5, "[obsolete] Incorrect Content"),
        IdWithText(6, "Media"),
        IdWithText(7, "[obsolete] Prize"),
        IdWithText(8, "[obsolete] Rewards"),
        IdWithText(9, "Other"),
        IdWithText(27, "Did not log in"),
        IdWithText(28, "Expired points"),
        IdWithText(29, "Points from cancelled bookings"),
        IdWithText(30, "System error"),
        IdWithText(31, "Points for AAB")
      )
    else
      Vector(
        IdWithText(10, "Redemption Request"),
        IdWithText(11, "Transfer Booking Points"),
        IdWithText(122, "Remove Sign Up Points"),
        IdWithText(13, "Other")
      )
}
