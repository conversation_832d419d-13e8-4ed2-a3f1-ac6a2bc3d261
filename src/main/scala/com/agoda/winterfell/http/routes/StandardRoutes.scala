package com.agoda.winterfell
package http.routes

import akka.actor.ActorSystem
import akka.http.scaladsl.marshalling.ToResponseMarshallable
import akka.http.scaladsl.model.HttpEntity
import akka.http.scaladsl.server.Directives.{complete, reject}
import akka.http.scaladsl.server.{RequestContext, Route}
import akka.stream.Materializer
import com.agoda.winterfell.Boot.Instance.httpExecutionContext
import com.agoda.winterfell.common.AuthenticationType.{AuthenticationType, _}
import com.agoda.winterfell.common.{AuthenticationType, CustomerEmail}
import com.agoda.winterfell.input.Inputs.AllTheRequests
import com.agoda.winterfell.marshalling.{JacksonMarshallers, JsonMarshalling}
import com.agoda.winterfell.models.{CapiRequestContextData, QueryUtil}
import com.agoda.winterfell.models.errors.UnknownEntityException
import com.agoda.winterfell.output._
import com.agoda.winterfell.repository.{CustomerRepository, UserRepository}
import com.agoda.winterfell.services.{ExternalLoyaltyAPIService, GiftCardService, TempSingletonHolder}
import com.agoda.winterfell.unified.CustomerServiceOps
import com.agoda.winterfell.unified.accessors._
import com.agoda.winterfell.unified.models.HashedUsername
import com.agoda.winterfell.utils._
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.ScalaObjectMapper

import scala.concurrent.Future
import scala.util.{Failure, Success}

/**
  * <AUTHOR>
  */
class StandardRoutes(
    customerOps: CustomerServiceOps,
    implicit val giftCardService: GiftCardService,
    implicit val externalLoyaltyApi: ExternalLoyaltyAPIService)(
    implicit context: ExecutionContext,
    materializer: Materializer,
    actorSystem: ActorSystem)
    extends AppLogger {
  private implicit val unmap: ObjectMapper with ScalaObjectMapper = JacksonMarshallers.AllMapper
  private implicit val implicitDb: DBPlugin = TempSingletonHolder.db
  private implicit val userRepository: UserRepository = TempSingletonHolder.userRepository
  private implicit val cusRepo: CustomerRepository = TempSingletonHolder.customerRepository
  private implicit val syncer: DefaultSyncer = new DefaultSyncer()

  def otherRoutes(p: String, ctx: RequestContext): Route = {
    val inputUrl = p.toString.split("/").last
    import JacksonMarshallers.{jacksonWithNullsMarshaller, primitiveMarshaller}
    import com.agoda.winterfell.http.Directives._

    extractCapiRequestContext(ctx) { implicit capiCtx ⇒
      lazy val e: AllTheRequests = {
        val strict = ctx.request.entity.asInstanceOf[HttpEntity.Strict]
        val attempt = Try(unmap.readValue[AllTheRequests](strict.data.utf8String))
        if (attempt.isFailure) throw new ArgumentInvalidException(attempt.failed.get.toString)
        attempt.get
      }

      Profiler.record("deserialized")
      // TODO: remove sync mutators!
      def memberPayloadMutator = ProxiedMutator.byMemberId(
        e.member.map(_.memberId).orElse(e.memberContact.map(_.memberId)).getOrFail(s"No memberId in $inputUrl"),
        capiCtx.whiteLabelId
      )
      def mUUIDMut = ProxiedMutator.byUUID(
        e.userId.getOrFail(s"No userId in $inputUrl"),
        capiCtx.whiteLabelId
      )
      def asyncMutatorByMemberId: AsyncMutator =
        AsyncMutator(
          e.member.map(_.memberId).orElse(e.memberContact.map(_.memberId)).getOrFail(s"No memberId in $inputUrl"),
          capiCtx.whiteLabelId,
          capiCtx
        )(context, customerOps)
      def asyncMutatorByUserId: AsyncMutator =
        AsyncMutator(
          e.userId.getOrFail(s"No userId in $inputUrl"),
          capiCtx.whiteLabelId,
          capiCtx
        )(context, customerOps)
      def asyncMutatorByEmail: AsyncMutator =
        AsyncMutator(
          AuthenticationType.Basic,
          e.email.orElse(e.emailAddress).filterNot(_.isEmpty).getOrFail(s"No email in $inputUrl"),
          capiCtx.whiteLabelId,
          capiCtx
        )(context, customerOps)

      def emailRead =
        ProxiedAccessor.byEmail(
          e.email.orElse(e.emailAddress).filterNot(_.isEmpty).getOrFail(s"No email in $inputUrl"),
          capiCtx.whiteLabelId)
      def uuidRead = ProxiedAccessor.byUUID(e.userId.getOrFail(s"No userId in $inputUrl"))
      def mIdAcc = ProxiedAccessor.byMemberIdLegacy(e.memberId.getOrFail(s"No memberId in $inputUrl"))

      //All routes except email verification and NHA do some mutation
      val mutatingRoutes: Option[ToResponseMarshallable] = Some(inputUrl) collect {
        case "AssociateSocialUser" => asyncMutatorByUserId.associateSocialUser(e.username, e.socialNetworkType)
        case "SaveMember" | "BoSaveMember" | "SaveMemberB" =>
          asyncMutatorByMemberId.updateMember(e.member.getOrFail("No member for SaveMember"))
        case "PatchMember" => asyncMutatorByMemberId.patchMember(e.member.getOrFail("No member for PatchMember"))
        case "SaveMemberContact" | "BoSaveMemberContact" =>
          val contactMethod = e.memberContact.map(_.contactMethod)
          if (contactMethod.isDefined && contactMethod.exists(ContactMethod.isMFAMethod)) {
            throw new ArgumentInvalidException(
              s"${ContactMethod.nameOf(contactMethod.get)}: contactMethodId ${contactMethod.get.id} is not allow on this endpoint")
          }
          val contact = e.memberContact
            .map(contact => contact.copy(contactMethodValue = CustomerEmail.trimEmail(contact.contactMethodValue)))
            .getOrFail("No contact")
          if (contact.recordCreatedBy.contains(Constants.CuscoAppId))
            TempSingletonHolder.userRepository
              .saveCuscoContact(contact, capiCtx.whiteLabelId)(context, customerOps, capiCtx)
          else if (contact.recordStatus.getOrElse(1) < 1) {
            val isHardDelete = CapiConsul.isHardDeleteSaveMemberContact && CapiConsul.isHardDeleteSaveMemberContactTeamNames
              .exists(_.equalsIgnoreCase(capiCtx.teamName))
            asyncMutatorByMemberId.deleteUnverifiedContact(contact, isHardDelete = isHardDelete)
          } else {
            val saveRequest = contact.toSaveRequest
            val asyncMutatorByMemberIdResult = asyncMutatorByMemberId
            asyncMutatorByMemberIdResult
              .saveMemberContact(saveRequest)
              .withTags(
                result =>
                  if (result.isFailure)
                    Iterator(
                      "requestContactMethodValue" -> QueryUtil.encrypt(
                        JsonMarshalling.pretty(contact.contactMethodValue)),
                      "requestContactMethodId" -> contact.contactMethod.id.toString,
                      "requestContactMethodName" -> contact.contactMethodName.getOrElse("Unknown"),
                      "saveRequestContactMethodValue" -> QueryUtil.encrypt(
                        JsonMarshalling.pretty(saveRequest.contactMethodValue)),
                      "saveRequestContactMethodId" -> saveRequest.contactMethod.id.toString,
                      "teamName" -> capiCtx.teamName
                    )
                  else Iterator.empty)
              .andThen {
                case result: Try[_] =>
                  if (saveRequest.contactMethod == ContactMethod.PrimaryEmail && saveRequest.memberId > 0) {
                    asyncMutatorByMemberIdResult.originalEntity.map { customer =>
                      HashedUsername.send(
                        "SaveMemberContact",
                        saveRequest.contactMethodValue,
                        Some(AuthenticationType.Basic),
                        contact.recordModifiedBy,
                        customer.memberId,
                        customer.userId,
                        Some(capiCtx.whiteLabelId),
                        result,
                        Some(capiCtx)
                      )
                    }
                  }
              }(Implicits.slowExecutionContext)
          }
        case "SaveHostInfo" =>
          asyncMutatorByUserId
            .saveHostInfo(e.hostInfo.getOrFail("No hostInfo for SaveHostInfo"), e.isPatch)
            .report("capi.dal.SaveHostInfo")
        case "AssociateVerifiedPhoneNumber" =>
          asyncMutatorByUserId.associatePhoneNumber(e.countryCode, e.nationalNumber, None)
        case "DeleteVerifiedPhoneNumber" => asyncMutatorByUserId.deleteVerifiedContact(AuthenticationType.PhoneNumber)
      }

      val readOnly: Option[ToResponseMarshallable] = Some(inputUrl) collect {
        // Move below to social app service
        case "GetConnectedSocialApps" => uuidRead.getConnectedSocialApps()
        //GC
        case "RetrieveGiftcards" =>
          giftCardService.getGiftCards(
            e.memberId.get,
            Option(e.statusFilter).getOrElse(Vector.empty),
            e.pageSize,
            e.pageIndex,
            e.fromDate,
            e.toDate,
            e.balanceType,
            Option(e.sortingList).getOrElse(Vector.empty),
            capiCtx.whiteLabelId
          )
        case "GetGiftCardList" =>
          giftCardService.getGiftCardListResult(
            e.memberId.get,
            e.optCurrencyCode.getOrElse("USD"),
            Option(e.statusFilter).getOrElse(Vector.empty),
            e.pageSize,
            e.pageIndex,
            e.fromDate,
            e.toDate,
            e.balanceType,
            Option(e.sortingList).getOrElse(Vector.empty),
            capiCtx.whiteLabelId
          )
        case "GiftCardBalance" =>
          val balanceTypes = if (e.balanceTypes.isEmpty) Vector(1) else e.balanceTypes
          giftCardService.getMemberBalance(
            e.memberId.get,
            e.optCurrencyCode.getOrElse("USD"),
            balanceTypes,
            capiCtx.whiteLabelToken,
            capiCtx.whiteLabelId)
        case "SetExternalWalletBalance" =>
          giftCardService.setExternalWalletBalance(
            e.memberId.getOrElse(0),
            e.externalWalletBalance.getOrElse(0),
            capiCtx.whiteLabelToken.get,
            capiCtx.whiteLabelId)
        case "GetGiftCardCampaigns" => giftCardService.getGiftCardCampaigns(e.campaignIds, capiCtx.whiteLabelId)
        case "IssueGiftCardForCampaign" =>
          giftCardService.issueGiftCardForCampaign(
            e.memberId.getOrFail("No member for IssueGiftCardForCampaign"),
            e.campaignId,
            e.campaignToken,
            Option(e.optCurrencyCode).getOrElse(None),
            capiCtx.whiteLabelId
          )
        case "EnrollVipCampaign" =>
          giftCardService.enrollVipCampaign(
            e.memberId.getOrFail("No member for EnrollVipCampaign"),
            e.campaignId,
            e.campaignToken,
            capiCtx.whiteLabelId)
        case "GetEligibleVipCampaigns" =>
          giftCardService.getEligibleVipCampaignsWrapped(
            e.memberId.getOrFail("No member Id for GetEligibleVipCampaigns"),
            capiCtx.whiteLabelId)
        case "GetLoyaltyProfile" =>
          getLoyaltyProfile(e.memberId.get, e.optCurrencyCode.getOrElse("USD"), capiCtx.whiteLabelId)(capiCtx)
        case "GetLoyaltyProfileContext" =>
          getLoyaltyProfileContext(e.memberId.get, e.optCurrencyCode.getOrElse("USD"), capiCtx.whiteLabelId)(capiCtx)
        case "GetTravelStatistics" => getTravelStatistics(e.memberId.get, e.languageId.get, capiCtx.whiteLabelId)
        //CheckX methods
        case "CheckIfBasicUser" => emailRead.checkIfBasicUser()
        case "CheckIfRewardsUser" => uuidRead.exists()
        case "GetMember" =>
          if (e.memberId.getOrElse(0) <= 1) { //Avoid exception spam for now
            Future.successful(Constants.emptyMemberDetailWithDefault)
          } else {
            mIdAcc
              .memberById(e.optCurrencyCode, capiCtx, extractFeatureFlags(e.featureFlags))
              .fastMap(x => {
                externalLoyaltyApi.getMemberBalance(
                  x.withDefault,
                  e.productType.getOrElse("ACTIVITIES"),
                  e.partnerClaim.getOrElse(""))(capiCtx)
              })(httpExecutionContext)
          }
        case "FetchMemberDetails" =>
          emailRead
            .memberById(e.optCurrencyCode, capiCtx, extractFeatureFlags(e.featureFlags))
            .fastMap(x => {
              x.withDefault
            })(httpExecutionContext)
        case "GetMemberProfile" =>
          val c = e.command.getOrFail("No member profile command")
          ProxiedAccessor.byMemberIdLegacy(c.memberId).memberAsProfile(e.command.get.discardFakeEmail)
        case "GetMemberUserContext" =>
          implicit val ec: ExecutionContext = httpExecutionContext
          implicit val gc: GiftCardService = giftCardService
          mIdAcc.memberByIdWithUserContext(e.memberId.getOrFail("No memberId"), gc, cusRepo)(ec, capiCtx)

        // GDPR to restrict communication - use by Hermes, Janus, Ebe +(CusCo in the future)
        case "CheckIfUserRestricted" =>
          cusRepo.checkIfUserRestricted(e.memberId.getOrFail("No memberId for CheckIfUserRestricted"))
        case "GetAllRestrictedUsers" => cusRepo.getAllRestrictedUsers
        case "GetVerifiedPhoneNumber" => uuidRead.getVerifiedPhoneNumber()

        //TODO : check the validity of below, try to remove as many as possible
        case "BoGetMember" =>
          cusRepo
            .findCustomerByMemberId(e.memberId.getOrFail("No memberId"), true)
            .map(_.toLegacyMemberDetails)
            .getOrElse(Constants.emptyMemberDetail)
            .withDefault

        //Personalisation, not migrated to v2 model
        case "GetUserPreference" =>
          getUserPreference(e.memberId.getOrFail("No memberId"), e.preferenceId.getOrFail("No preferenceId"))
        case "GetUserPreferences" => getUserPreferences(e.memberId.getOrFail("No memberId"))
        //Host Info
        case "GetHostInfo" =>
          val email = e.email.orElse(e.emailAddress).filterNot(_.isEmpty).getOrFail(s"No email in $inputUrl")
          asyncMutatorByEmail.getHostUserInfo
            .report("capi.dal.GetHostInfo")
            .recover { case _: UnknownEntityException => None }
        //Reject
      }
      val finalRes = mutatingRoutes orElse readOnly
      mapCapiLegacyResponse(ctx) {
        finalRes
          .map { x =>
            Profiler.record("finished"); complete(x)
          }
          .getOrElse(reject)
      }
    }
  }

  def getUserPreference(memberId: Int, preferenceId: Int): Option[Int] = {
    cusRepo.getUserPreference(memberId, preferenceId)
  }

  def getUserPreferences(memberId: Int): Vector[MemberPreference] = {
    cusRepo.getUserPreferences(memberId)
  }

  def getLoyaltyProfile(memberId: Int, optCurrencyCode: String, whiteLabelId: Int)(
      implicit capiRequestContextData: CapiRequestContextData): Future[Option[AnyRef]] = {
    giftCardService
      .getMemberLoyaltyProfileWrapped(memberId, optCurrencyCode, false, whiteLabelId)
      .map { res: Option[LoyaltyProfile] =>
        res.map(_.loyaltyProfile)
      }(httpExecutionContext)
  }

  def getLoyaltyProfileContext(memberId: Int, optCurrencyCode: String, whiteLabelId: Int)(
      implicit capiRequestContextData: CapiRequestContextData): Future[Option[LoyaltyProfileResponse]] = {
    giftCardService
      .getMemberLoyaltyProfileWrapped(memberId, optCurrencyCode, true, whiteLabelId)
      .map { res: Option[LoyaltyProfile] =>
        res.map(lp => createLoyaltyProfileResponse(lp))
      }(httpExecutionContext)
  }

  def getTravelStatistics(memberId: Int, languageId: Int, whiteLabelId: Int): Future[Option[TravelStatistics]] = {
    giftCardService.getTravelStatistics(memberId, languageId, whiteLabelId)
  }

  def createLoyaltyProfileResponse(loyaltyProfileInfo: LoyaltyProfile): LoyaltyProfileResponse = {
    LoyaltyProfileResponse(
      loyaltyProfile = loyaltyProfileInfo.loyaltyProfile,
      eligibleVipCampaigns = loyaltyProfileInfo.eligibleVipCampaigns,
      memberBalance = loyaltyProfileInfo.memberBalance,
      achievements = loyaltyProfileInfo.achievements,
      cashBackBalance = loyaltyProfileInfo.cashBackBalance
    )
  }

  def extractFeatureFlags(featureFlags: Option[String]): Option[Vector[String]] = {
    featureFlags.map(_.split(",").toVector)
  }
}
