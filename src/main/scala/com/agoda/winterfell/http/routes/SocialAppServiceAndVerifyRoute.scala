package com.agoda.winterfell
package http.routes

import java.util.UUID
import akka.http.scaladsl.server.Directives.{pathPrefix, _}
import akka.http.scaladsl.server.Route
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.winterfell.http.CapiRequestContextDirectives
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.input._
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.services.{EmailVerificationService, UserService, WeChatLoginService}
import com.agoda.winterfell.unified.accessors.AsyncMutator
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.ScalaObjectMapper

/**
  * Created by kankit on 5/11/2017 AD.
  */
//TODO : delete this route, made obsolete by V2 Oauth login
class SocialAppServiceAndVerifyRoute(
    weChatLoginService: WeChatLoginService,
    userService: UserService,
    mutator: (MeasuredFuture[UUID], Int, CapiRequestContextData) => AsyncMutator,
    emailVerificationService: EmailVerificationService)(implicit executionContext: ExecutionContext)
    extends CapiRequestContextDirectives {

  import JacksonMarshallers.jacksonWithNullsMarshaller

  implicit val unmap: ObjectMapper with ScalaObjectMapper = JacksonMarshallers.AllMapper

  val route: Route = {
    extractCapiRequestContext { implicit ctx =>
      pathComplete("AssociateSocialApp") { args: AssociateSocialAppInput =>
        weChatLoginService.associateWeChatUser(args)
      } ~ pathComplete("GetSocialAppUserId") { args: GetSocialAppUserIdRequest =>
        userService.getSocialAppUserId(args)
      } ~
        pathComplete("DisconnectSocialApp") { args: DisconnectSocialAppRequest =>
          mutator(MeasuredFuture.successful(args.userId), ctx.whiteLabelId, ctx).disconnectSocialApp(args)
        } ~
        pathComplete("CreateEmailVerificationLinkData") { args: CreateEmailVerificationLinkDataRequestWrapper =>
          emailVerificationService.createEmailVerificationLinkData(args.request)
        } ~
        pathComplete("ValidateEmailVerification") { args: ValidateEmailVerificationRequestWrapper =>
          mutator(emailVerificationService.verifyEmailToken(args.request, ctx), ctx.whiteLabelId, ctx)
            .verifyEmail(args.request)
        }
    }
  }

  val route2: Route = {
    pathPrefix("SocialAppService") {
      extractCapiRequestContext { implicit ctx =>
        pathComplete("Login") { args: WeChatLoginParam =>
          weChatLoginService.weChatLogin(args)
        }
      }
    }
  }
}
