package com.agoda.winterfell.http

import akka.http.scaladsl.model.{HttpEntity, HttpResponse, StatusCode, StatusCodes}
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.{Exception<PERSON><PERSON><PERSON>, RejectionHandler, RequestContext, ValidationRejection}
import akka.pattern.AskTimeoutException
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.commons.http.api.v2.exceptions.HttpException
import com.agoda.winterfell.{
  AppLogger,
  AuthException,
  BlacklistedTokenException,
  Implicits,
  InternalNotFatalException,
  InvalidRequestException,
  JwtTokenExpiredException,
  UnauthorizedException
}
import com.agoda.winterfell.client.{InvalidResponseCodeException, ResponseCodeException}
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.http.HttpService.headers
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.QueryUtil
import com.agoda.winterfell.models.errors.{GDPREntityException, UnknownEntityException}
import com.agoda.winterfell.output.ServerStatusCode
import com.agoda.winterfell.utils.CapiConsul

import java.io.IOException
import scala.concurrent.Future
import scala.util.Try

class ExceptionHandler extends AppLogger {
  val redirectExceptionHandler = {
    ExceptionHandler {
      // ag-http-client exception
      case e: HttpException =>
        CapiLogMessage(
          "capi.sh_fallback_response",
          LogLevel.ERROR,
          message = Some(e.getMessage),
          exceptionMessage = Some(e.getMessage),
          exception = Some(e))
        complete(
          HttpResponse(
            StatusCode.int2StatusCode(e.statusCode),
            e.httpHeaders.map(header => RawHeader(header.name, header.value)).toList,
            e.message))
      case e: Throwable =>
        extractUri { uri =>
          extractRequestContext { ctx =>
            //noinspection ScalaUnusedSymbol
            val (client: String, _: String, _: String) = HttpService.ctxToHeaders(ctx.request)
            val messageWithUrl = s", RequestUrl: $uri|$client"
            val uuid = java.util.UUID.randomUUID()
            val str = Option(e.getMessage).getOrElse("")
            if (str.contains("No mappings") || str.contains("Invalid UUID string")) {
              logger.warn(s"$e [$uuid] $messageWithUrl")
            } else logger.error(s"$e : [$uuid] $messageWithUrl", e)
            CapiLogMessage(
              "capi.sh_fallback_response",
              LogLevel.ERROR,
              message = Some(messageWithUrl),
              exceptionMessage = Some(e.getMessage),
              exception = Some(e))
            complete(
              HttpResponse(
                StatusCodes.InternalServerError,
                headers("Error-id" -> uuid.toString),
                s"Internal server error :${e.toString}"))
          }
        }
    }
  }

  def getServerStatus(exception: Throwable): Option[ServerStatusCode.ServerStatusCode] = {
    exception match {
      case _: JwtTokenExpiredException =>
        Some(ServerStatusCode.SessionExpired)
      case _: BlacklistedTokenException =>
        Some(ServerStatusCode.BlackListedToken)
      case _: AuthException =>
        Some(ServerStatusCode.InvalidCAPIRequest)
      case _: UnauthorizedException =>
        Some(ServerStatusCode.InvalidCAPIRequest)
      case _: GDPREntityException =>
        Some(ServerStatusCode.DeletedMember)
      case ex: UnknownEntityException =>
        if (ex.message.contains("deleted")) Some(ServerStatusCode.DeletedMember)
        else Some(ServerStatusCode.NoMember)
      case _ =>
        None
    }
  }

  val exceptionHandler = {
    ExceptionHandler {
      case e: InternalNotFatalException =>
        extractUri { uri =>
          extractRequestContext { ctx =>
            //noinspection ScalaUnusedSymbol
            val (client: String, _: String, _: String) = HttpService.ctxToHeaders(ctx.request)
            val messageWithUrl = s", RequestUrl: $uri|$client"
            val uuid = java.util.UUID.randomUUID()
            logger.warn(s"$e : [$uuid]$messageWithUrl")
            complete(
              HttpResponse(
                StatusCodes.InternalServerError,
                headers("Error-id" -> uuid.toString),
                s"Error with request :${e.toString}"))
          }
        }

      case e: InvalidRequestException =>
        extractUri { uri =>
          extractRequestContext { ctx =>
            //noinspection ScalaUnusedSymbol
            val (client: String, _: String, _: String) = HttpService.ctxToHeaders(ctx.request)
            val messageWithUrl = s", RequestUrl: $uri|$client"
            val uuid = java.util.UUID.randomUUID()
            logger.warn(s"$e : [$uuid]$messageWithUrl")
            reportFailureRequest("badrequest", ctx, Some(e))
            val serverStatus = getServerStatus(e)
            val responseHeader = headers("Error-id" -> uuid.toString) ++
              serverStatus.toList.flatMap(code =>
                headers("ServerStatusCode" -> code.id.toString, "ServerStatus" -> code.toString))
            complete(HttpResponse(StatusCodes.BadRequest, responseHeader, s"Bad request :${e.toString}"))
          }
        }

      case ex: UnauthorizedException ⇒
        extractRequestContext { ctx =>
          reportFailureRequest("unauthorized", ctx, Some(ex))
          val serverStatus = getServerStatus(ex)
          val responseHeader = headers("Error-id" -> java.util.UUID.randomUUID().toString.toString) ++
            serverStatus.toList.flatMap(code =>
              headers("ServerStatusCode" -> code.id.toString, "ServerStatus" -> code.toString))
          complete(HttpResponse(StatusCodes.Unauthorized, responseHeader, s"${ex.toString}"))
        }
      case ex: AuthException ⇒
        extractRequestContext { ctx =>
          reportFailureRequest("unauthorized", ctx, Some(ex))
          val serverStatus = getServerStatus(ex)
          val responseHeader = headers("Error-id" -> java.util.UUID.randomUUID().toString.toString) ++
            serverStatus.toList.flatMap(code =>
              headers("ServerStatusCode" -> code.id.toString, "ServerStatus" -> code.toString))
          complete(HttpResponse(StatusCodes.Unauthorized, responseHeader, s"${ex.toString}"))
        }
      //specific to Actor ask timeout. if we handle the generic TimeoutException, the response code will be affected (change from 500 to 408)
      case ex: AskTimeoutException ⇒
        complete(
          HttpResponse(
            StatusCodes.RequestTimeout,
            headers("Error-id" → java.util.UUID.randomUUID().toString),
            s"${ex.toString}"))

      case error: InvalidResponseCodeException => complete(HttpResponse(error.statusCode, Nil, error.msg))
      case error: ResponseCodeException => complete(error.response)

      case e: Throwable =>
        extractUri { uri =>
          extractRequestContext { ctx =>
            //noinspection ScalaUnusedSymbol
            val (client: String, _: String, _: String) = HttpService.ctxToHeaders(ctx.request)
            val messageWithUrl = s", RequestUrl: $uri|$client"
            val uuid = java.util.UUID.randomUUID()
            val str = Option(e.getMessage).getOrElse("")
            if (str.contains("No mappings") || str.contains("Invalid UUID string")) {
              logger.warn(s"$e [$uuid] $messageWithUrl")
            } else logger.error(s"$e : [$uuid] $messageWithUrl", e)
            complete(
              HttpResponse(
                StatusCodes.InternalServerError,
                headers("Error-id" -> uuid.toString),
                s"Internal server error :${e.toString}"))
          }
        }
    }
  }

  //Present a sane error message instead of the stupid ones Akka HTTP gives you by default for JSON parsing errors
  val rejectionHandler = RejectionHandler
    .newBuilder()
    .handle {
      case ValidationRejection(msg, Some(x)) if Option(x.getCause).exists(_.isInstanceOf[IOException]) =>
        extractRequestContext { ctx =>
          reportFailureRequest("badrequest", ctx, Some(x))
          akka.http.scaladsl.server.Directives
            .complete((StatusCodes.BadRequest, msg + " due to : " + x.getCause.getMessage))
        }
      case ValidationRejection(msg, _) =>
        extractRequestContext { ctx =>
          reportFailureRequest("badrequest", ctx, None)
          akka.http.scaladsl.server.Directives.complete(StatusCodes.BadRequest -> msg)
        }
    }
    .result()

  def reportFailureRequest(suffixLoggerName: String, ctx: RequestContext, e: Option[Throwable]): Unit = {
    if (CapiConsul.debugFailureRequestBody) {
      Future {
        Try(
          (
            ctx.request.entity.asInstanceOf[HttpEntity.Strict].getData().utf8String,
            Directives.convertRequestContextToCapiRequestContext(ctx))).toOption match {
          case _ @Some((value: String, capiCtx: CapiRequestContextDirectives)) =>
            if (value.trim.nonEmpty) {
              CapiLogMessage(
                s"capi.failure.$suffixLoggerName",
                message = Some(QueryUtil.encrypt(value)),
                exception = e,
                exceptionMessage = e.map(_.getMessage).map(QueryUtil.encrypt), // prevent pii leak
                stringTags = capiCtx.toStringTags
              )
            }
          case _ => // do nothing
        }
      }(Implicits.slowExecutionContext)
    }
  }
}
