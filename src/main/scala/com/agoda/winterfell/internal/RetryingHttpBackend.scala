package com.agoda.winterfell.internal

import scala.language.higherKinds
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.util.Random
import akka.stream.Materializer

import java.net.InetAddress
import java.util.concurrent.{ConcurrentHashMap, TimeUnit}
import cats.effect.{ContextShift, IO}
import com.google.common.base.Function
import com.google.common.cache.{Cache<PERSON><PERSON>er, CacheLoader, LoadingCache}
import com.softwaremill.sttp.{
  ByteArrayBody,
  HeaderNames,
  Id,
  MediaTypes,
  Method,
  MonadError,
  Request,
  Response,
  StringBody
}
import com.softwaremill.sttp.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import org.slf4j.Logger
import org.asynchttpclient.{DefaultAsyncHttpClient, DefaultAsyncHttpClientConfig}
import org.asynchttpclient.proxy.ProxyType
import org.asynchttpclient.proxy.ProxyServer.{Builder => ProxyBuilder}
import com.agoda.winterfell.client.ProxyServer
import com.agoda.winterfell.metrics.CapiMeasurementMessage
import com.agoda.winterfell.utils.CapiConfig
import io.netty.handler.ssl.SslContextBuilder
import io.netty.handler.ssl.util.InsecureTrustManagerFactory

/**
  * Provides a retrying mechanism for any idempotent HTTP call.
  */
private[winterfell] class RetryingHttpBackend(
    retryLimit: Int = 3,
    dnsRefreshRate: Option[Duration],
    proxyServer: Option[ProxyServer],
    logger: Logger,
    timeout: FiniteDuration)(implicit materializer: Materializer, context: ExecutionContextExecutor)
    extends HttpBackend {

  private implicit val contextShift: ContextShift[IO] = IO.contextShift(context)

  private val (underlyingClient, httpBackend) = RetryingHttpBackend.backendFor(proxyServer, timeout)

  private val dnsCache: LoadingCache[String, Seq[String]] =
    CacheBuilder
      .newBuilder()
      .refreshAfterWrite(dnsRefreshRate.map(_.toMillis).getOrElse(5000L), TimeUnit.MILLISECONDS)
      .build(CacheLoader.asyncReloading(CacheLoader.from(RetryingHttpBackend.HostResolver), context))

  /**
    * Initializes a keep-alive connections for given hosts.
    */
  def warm(baseUrls: Seq[String]): Unit = {
    baseUrls.foreach(underlyingClient.prepareOptions(_).execute())
  }

  override def send[T](request: Request[T, HttpStream]): IO[Response[T]] = {
    val hosts =
      if (dnsRefreshRate.isDefined) Iterator.continually(Random.shuffle(dnsCache.get(request.uri.host))).flatten
      else Iterator.continually(request.uri.host)
    val httpPort = request.uri.port.filter(_ != -1)

    def retry(attempt: Int): IO[Response[T]] = {
      val isRetriable = RetryingHttpBackend.isIdempotent(request.method) && (attempt > 0)
      val resolvedUrl = request.uri.copy(host = hosts.next(), scheme = request.uri.scheme, port = httpPort)
      val resolvedRequest =
        if (dnsRefreshRate.isDefined)
          request.copy[Id, T, HttpStream](uri = resolvedUrl).header(HeaderNames.Host, request.uri.host)
        else request.copy[Id, T, HttpStream](uri = resolvedUrl)
      val requestLog = s"${request.method.m} ${resolvedRequest.uri}: ${printBody(request).getOrElse("N/A")}"

      responseMonad.map(responseMonad.handleError(httpBackend.send(resolvedRequest)) {
        case error: Throwable =>
          logger.error(s"Exception when sending (attempt #$attempt) $requestLog", error)
          // TODO: allow retry for any connection exception
          if (isRetriable) retry(attempt - 1)
          else responseMonad.error(error)
      }) { response =>
        val responseBody = if (logger.isInfoEnabled) response.body.fold(identity, _.toString) else "N/A"
        val responseLog = s"Request: $requestLog\nResponse ${response.code} ${response.statusText}: ${responseBody}"
        if (response.isSuccess) logger.info(responseLog) else logger.error(responseLog)
        response
      }
    }
    retry(retryLimit)
  }

  override def close(): Unit = httpBackend.close()

  override def responseMonad: MonadError[IO] = httpBackend.responseMonad

  /** Converts a HTTP request body to the readable string if the logging level is allow to do it. */
  private def printBody(request: Request[_, HttpStream]): Option[String] = request.body match {
    case StringBody(content, _, _) if logger.isInfoEnabled => Some(content)
    case ByteArrayBody(body, Some(MediaTypes.Json)) if logger.isInfoEnabled => Some(new String(body))
    case _ => None
  }
}

object RetryingHttpBackend {
  val minimumConnectionTimeout = 200

  /** Disable any SSL check because of self-signed certificates. */
  def DefaultInsecureConfig(timeout: FiniteDuration): DefaultAsyncHttpClientConfig.Builder = {
    new DefaultAsyncHttpClientConfig.Builder()
      .setUserAgent("Capi-Client")
      .setUseInsecureTrustManager(true)
      .setDisableHttpsEndpointIdentificationAlgorithm(true)
      .setConnectTimeout(Math.max(timeout.toMillis.toInt, minimumConnectionTimeout))
    //.setSslContext(SslContextBuilder.forClient.trustManager(InsecureTrustManagerFactory.INSTANCE).build)
  }

  /**
    * Checks whether the given HTTP method is idempotent.
    *
    * @see [[https://tools.ietf.org/html/rfc7231#section-4.2.2 Idempotent Methods]]
    */
  def isIdempotent(method: Method): Boolean = method match {
    case Method.GET | Method.PUT | Method.DELETE | Method.HEAD | Method.OPTIONS => true
    case _ => false
  }

  /**
    * Represents a function to resolve a host to IP addresses.
    */
  val HostResolver = new Function[String, Seq[String]] {
    override def apply(host: String): Seq[String] = InetAddress.getAllByName(host).map(_.getHostAddress)
  }

  private[internal] val backends = new ConcurrentHashMap[Option[ProxyServer], (DefaultAsyncHttpClient, HttpBackend)]()

  /**
    * Returns a cached HTTP backend per proxy.
    */
  private[internal] def backendFor(proxyServer: Option[ProxyServer], timeout: FiniteDuration)(
      implicit contextShift: ContextShift[IO]): (DefaultAsyncHttpClient, HttpBackend) = {
    backends.computeIfAbsent(
      proxyServer,
      _ match {
        case Some(proxy) =>
          val config = DefaultInsecureConfig(timeout)
            .setProxyServer(new ProxyBuilder(proxy.host, proxy.port).setProxyType(ProxyType.HTTP).build())
            .build()
          val client = new DefaultAsyncHttpClient(config)
          client -> AsyncHttpClientFs2Backend.usingClient[IO](client)

        case None =>
          val client = new DefaultAsyncHttpClient(DefaultInsecureConfig(timeout).build())
          client -> AsyncHttpClientFs2Backend.usingClient[IO](client)
      }
    )
  }
}
