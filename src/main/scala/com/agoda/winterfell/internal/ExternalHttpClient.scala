package com.agoda.winterfell
package internal

import akka.actor.ActorSystem

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.concurrent.duration._
import scala.reflect.ClassTag
import scala.xml.Elem
import java.nio.charset.StandardCharsets
import akka.http.scaladsl.marshalling.Marshal
import akka.http.scaladsl.model.{ContentType, HttpEntity, StatusCode}
import akka.http.scaladsl.model.{MediaTypes => AkkaMediaTypes}
import akka.stream.{ActorMaterializer, Materializer}
import cats.effect.{ContextShift, IO}
import com.agoda.winterfell.client.{CustomerApi, InvalidResponseCodeException}
import com.softwaremill.sttp.{sttp, BodySerializer, ByteArrayBody, Request, ResponseAsString, StringBody, UriContext}
import com.softwaremill.sttp.{MediaTypes => SttpMediaTypes}
import com.agoda.winterfell.client.{JacksonRewardsMarshalling, ProxyServer, RewardsMarshalling}
import com.agoda.winterfell.metrics.{CapiLogMessage, CapiMeasurementMessage}
import com.agoda.winterfell.utils.{CapiConfig, EndpointUtil}
import org.slf4j.Logger

import scala.util.{Failure, Success}

/**
  * Represents an asynchronous HTTP client for the communication with external services.
  *
  * @todo Implement metrics!
  * @todo Remove nasty Materializer, and other weird akka stuff!
  *
  * @param timeout     A maximum inactivity time of any HTTP request.
  * @param retryCount  A maximum number of attempts to retry the request.
  * @param marshalling A marshalling strategy. By default uses Jackson to serialize/deserialize.
  * @param proxy       An optional http/https proxy settings
  */
private[winterfell] class ExternalHttpClient(
    timeout: FiniteDuration = 3.seconds,
    retryCount: Int = 3,
    marshalling: RewardsMarshalling = new JacksonRewardsMarshalling,
    proxy: Option[ProxyServer] = CapiConfig.ProxySettings,
    dnsRefreshRate: Option[Duration] = None,
    logger: Logger = CustomerApi.log)(
    implicit materializer: Materializer,
    context: ExecutionContextExecutor = ExecutionContext.global) {

  private implicit val contextShift: ContextShift[IO] = IO.contextShift(context)
  private implicit val httpBackend: RetryingHttpBackend =
    new RetryingHttpBackend(retryCount, dnsRefreshRate, proxy, logger, timeout)
  private implicit def bodySerializer[T <: Serializable with AnyRef](
      implicit classTag: ClassTag[T]): BodySerializer[T] = { entity =>
    implicit val marshaller = marshalling.marshaller[T]
    ByteArrayBody(
      Marshal(entity).to.flatMap(_.toStrict(timeout)).blockFor(timeout).data.toArray,
      Some(SttpMediaTypes.Json))
  }

  /**
    * Sends a GET request to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param headers  an optional list of HTTP headers
    */
  def get[T: NotNothing: ClassTag: Manifest](url: String, headers: Map[String, String] = Map.empty): Future[T] = {
    apply(sttp.get(uri"$url").headers(headers))
  }

  /**
    * Sends a POST request to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param entity  a body of HTTP request
    * @param headers  an optional list of HTTP headers
    */
  def post[E <: Serializable with AnyRef: ClassTag, T: NotNothing: ClassTag: Manifest](
      url: String,
      entity: E,
      headers: Map[String, String] = Map.empty,
      duration: Option[FiniteDuration] = None): Future[T] = {
    apply(sttp.post(uri"$url").body(entity).headers(headers), duration)
  }

  /**
    * Sends a POST request as the form data to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param form  a body of HTTP request
    * @param headers  an optional list of HTTP headers
    */
  def postForm[T: NotNothing: ClassTag: Manifest](
      url: String,
      form: Map[String, String],
      formEncoding: String = StandardCharsets.UTF_8.name,
      headers: Map[String, String] = Map.empty): Future[T] = {
    apply(sttp.post(uri"$url").body(form, formEncoding).headers(headers))
  }

  /**
    * Sends a POST request as the form data to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param document  a body of HTTP request
    * @param headers  an optional list of HTTP headers
    */
  def postXml[T: NotNothing: ClassTag: Manifest](
      url: String,
      document: Elem,
      headers: Map[String, String] = Map.empty): Future[T] = {
    implicit val xmlSerializer = { xml: Elem =>
      StringBody(xml.toString(), StandardCharsets.UTF_8.name, Some(SttpMediaTypes.XML))
    }
    apply(sttp.post(uri"$url").body(document).headers(headers))
  }

  /**
    * Sends a PUT request to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param entity  a body of HTTP request
    * @param headers  an optional list of HTTP headers
    */
  def put[E <: Serializable with AnyRef: ClassTag, T: NotNothing: ClassTag: Manifest](
      url: String,
      entity: E,
      headers: Map[String, String] = Map.empty): Future[T] = {
    apply(sttp.put(uri"$url").body(entity).headers(headers))
  }

  /**
    * Sends a PATCH request to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param entity  a body of HTTP request
    * @param headers  an optional list of HTTP headers
    */
  def patch[E <: Serializable with AnyRef: ClassTag, T: NotNothing: ClassTag: Manifest](
      url: String,
      entity: E,
      headers: Map[String, String] = Map.empty): Future[T] = {
    apply(sttp.patch(uri"${url}").body(entity).headers(headers))
  }

  /**
    * Sends a DELETE request to the given URL address.
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param headers  an optional list of HTTP headers
    */
  def delete[T: NotNothing: ClassTag: Manifest](url: String, headers: Map[String, String] = Map.empty): Future[T] = {
    apply(sttp.delete(uri"${url}").headers(headers))
  }

  /**
    * Sends a DELETE request to the given URL address with request body
    *
    * @tparam T  a type of expected response
    * @param url  a destination of request
    * @param entity  a body of HTTP request
    * @param headers  an optional list of HTTP headers
    */
  def deleteWithBody[E <: Serializable with AnyRef: ClassTag, T: NotNothing: ClassTag: Manifest](
      url: String,
      entity: E,
      headers: Map[String, String] = Map.empty): Future[T] = {
    apply(sttp.delete(uri"${url}").body(entity).headers(headers))
  }

  /**
    * Sends the given HTTP request.
    *
    * @param request  a HTTP request to send
    */
  def apply[T: NotNothing: Manifest](request: Request[String, Nothing], duration: Option[FiniteDuration] = None)(
      implicit classTag: ClassTag[T]): Future[T] = {
    val stringResponse = ResponseAsString(StandardCharsets.UTF_8.name)
    val parser =
      if (classTag.runtimeClass == classOf[String]) stringResponse.map { response =>
        Future.successful(response.asInstanceOf[T])
      } else
        stringResponse.map { response =>
          marshalling
            .unmarshaller[T]
            .apply(HttpEntity(response).copy(contentType = ContentType(AkkaMediaTypes.`application/json`)))
        }

    val start = System.currentTimeMillis
    request
      .readTimeout(duration.getOrElse(timeout))
      .response(parser)
      .send()
      .flatMap { response =>
        response.body match {
          case Left(error) => throw new InvalidResponseCodeException(StatusCode.int2StatusCode(response.code), error)
          case Right(entity) => IO.fromFuture(IO(entity))
        }
      }
      .unsafeToFuture()
      .andThen {
        case result =>
          val elapsed = System.currentTimeMillis() - start
          val (httpCode, httpCodeNumber) = result match {
            case Failure(err) =>
              err match {
                case error: InvalidResponseCodeException =>
                  error.statusCode.toString() -> error.statusCode.intValue().toString
                case _ =>
                  "error" -> "999"
              }
            case Success(_) =>
              "success" -> "000"
          }
          val tags = Map(
            "host" -> request.uri.host,
            "is_success" -> result.isSuccess.toString,
            "httpCode" -> httpCode,
            "httpCodeNumber" -> httpCodeNumber)
          CapiMeasurementMessage(
            "capi.external",
            tags ++ Map("path" -> EndpointUtil.shortenUri(request.uri.path.mkString("/"))),
            elapsed)
          if (!result.isSuccess || elapsed > CapiConfig.spamElapsedThreshold)
            CapiLogMessage("capi.external", stringTags = tags, longTags = Map("elapsed" -> elapsed))
      }
  }

  /**
    * Initializes a keep-alive connections for given hosts.
    */
  def warm(baseUrls: Seq[String]): ExternalHttpClient = {
    httpBackend.warm(baseUrls)
    this
  }
}
