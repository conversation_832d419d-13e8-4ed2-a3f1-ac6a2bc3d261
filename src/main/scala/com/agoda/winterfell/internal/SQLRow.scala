package com.agoda.winterfell.internal

import java.util.{Date, UUID}

import com.agoda.winterfell.dbutiltomove.RSHelper
import org.joda.time.DateTime

private[winterfell] case class PreColumn(name: String)(implicit row: SQLRow)

private[winterfell] case class SQLRow(map: Map[String, AnyRef]) extends RSHelper {
  implicit val theRow = this
  def c(name: String) = PreColumn(name)

  def str(name: String): String = checkResult[String](name)
  def boolean(name: String): Boolean = checkResult[Boolean](name)
  def bigDecimal(name: String): BigDecimal = checkResult[BigDecimal](name)
  def bigInt(name: String): BigInt = checkResult[BigInt](name)
  def bytes(name: String): Array[Byte] = checkResult[Array[Byte]](name)
  def int(name: String): Int = checkResult[Int](name)
  def long(name: String): Long = checkResult[Long](name)
  def double(name: String): Double = checkResult[Double](name)
  def float(name: String): Float = checkResult[Float](name)
  def short(name: String): Short = checkResult[Short](name)
  def date(name: String): Date = checkResult[Date](name)
  def dateTime(name: String): DateTime = checkResult[DateTime](name)
  def uuid(name: String): UUID = checkResult[UUID](name)

  def strOption(name: String): Option[String] = checkOptionalResult[String](name)
  def bytesOption(name: String): Option[Array[Byte]] = checkOptionalResult[Array[Byte]](name)
  def booleanOption(name: String): Option[Boolean] = checkOptionalResult[Boolean](name)
  def bigDecimalOption(name: String): Option[BigDecimal] = checkOptionalResult[BigDecimal](name)
  def bigIntOption(name: String): Option[BigInt] = checkOptionalResult[BigInt](name)
  def intOption(name: String): Option[Int] = checkOptionalResult[Int](name)
  def longOption(name: String): Option[Long] = checkOptionalResult[Long](name)
  def doubleOption(name: String): Option[Double] = checkOptionalResult[Double](name)
  def floatOption(name: String): Option[Float] = checkOptionalResult[Float](name)
  def shortOption(name: String): Option[Short] = checkOptionalResult[Short](name)
  def dateOption(name: String): Option[Date] = checkOptionalResult[Date](name)
  def dateTimeOption(name: String): Option[DateTime] = checkOptionalResult[DateTime](name)
  def uuidOption(name: String): Option[UUID] = checkOptionalResult[UUID](name)
}
