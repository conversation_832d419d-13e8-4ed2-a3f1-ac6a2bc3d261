package com.agoda.winterfell.metrics

import com.agoda.winterfell.utils.DBPlugin

object HikariPoolMeasurement {
  def sendMeasurement(): Unit = {
    DBPlugin.allConnections.foreach(namedHikariDataSource => {
      val dataSource = namedHikariDataSource.dataSource
      if (dataSource != null) {
        val poolMxBean = namedHikariDataSource.dataSource.getHikariPoolMXBean
        if (poolMxBean != null) {

          val tags = Map(
            "name" -> namedHikariDataSource.name,
            "group" -> namedHikariDataSource.group
          )
          CapiMeasurementMessage("winterfell.hikari.activeConnections", tags, poolMxBean.getActiveConnections)
          CapiMeasurementMessage("winterfell.hikari.idleConnections", tags, poolMxBean.getIdleConnections)
          CapiMeasurementMessage("winterfell.hikari.totalConnections", tags, poolMxBean.getTotalConnections)
          CapiMeasurementMessage(
            "winterfell.hikari.threadsAwaitingConnections",
            tags,
            poolMxBean.getThreadsAwaitingConnection)
        }
      }
    })
  }
}
