package com.agoda.winterfell.metrics

import com.agoda.commons.logging.metrics.MetricsReporter

class DalFallbackMetricReporter(prefix: Option[String] = None) extends MetricsReporter {
  val METRIC_PREFIX = prefix.getOrElse("capi.dal.fallback.")
  override def report(metric: String, value: Long, tags: Map[String, String]): Unit = {
    CapiMeasurementMessage(METRIC_PREFIX + metric, tags, value)
  }
}
