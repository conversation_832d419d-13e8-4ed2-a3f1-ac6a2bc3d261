package com.agoda.winterfell.metrics

import akka.actor.ActorSystem
import akka.dispatch.{Dispatcher, ExecutorServiceDelegate}
import akka.dispatch.ForkJoinExecutorConfigurator.AkkaForkJoinPool
import com.agoda.winterfell
import com.agoda.winterfell.metrics.ThreadPoolMeasurement.{extractAkkaForkJoinPool, extractPoolFromExecutionContext}

import java.util.concurrent.{ExecutorService, ForkJoinPool, ThreadPoolExecutor}
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}
import scala.util.Try

object ThreadPoolMeasurement {
  def extractAkkaForkJoinPool(ec: ExecutionContextExecutor): Option[AkkaForkJoinPool] = {
    Try(ec match {
      case dispatcher: Dispatcher =>
        val ecGetterForAkkaDispatcher = classOf[Dispatcher].getDeclaredMethod("executorService")
        ecGetterForAkkaDispatcher.setAccessible(true)
        val del = ecGetterForAkkaDispatcher.invoke(dispatcher).asInstanceOf[ExecutorServiceDelegate].executor
        del match {
          case fjpC: AkkaForkJoinPool =>
            Some(fjpC)
          case _ =>
            None
        }
    }).toOption.flatten
  }

  private def extractPoolFromExecutionContext(executionContext: ExecutionContext): Option[ExecutorService] = {
    Try({
      val ec = executionContext
      val field = ec.getClass.getDeclaredField("executor")
      field.setAccessible(true)
      field.get(ec) match {
        case fjp: ForkJoinPool => Some(fjp)
        case tpe: ThreadPoolExecutor => Some(tpe)
        case _ => None
      }
    }).toOption.flatten
  }
}
case class ThreadPoolMeasurement(
    capi: ActorSystem,
    micro: ActorSystem,
    http: ExecutorService,
    cb: ExecutorService,
    cn: ExecutorService,
    featureStore: ExecutorService) {
  private val capiAkkaForkJoinPool = extractAkkaForkJoinPool(capi.dispatcher)
  private val microAkkaForkJoinPool = extractAkkaForkJoinPool(micro.dispatcher)
  private val globalPool = extractPoolFromExecutionContext(ExecutionContext.Implicits.global)

  def sendMeasurement(): Unit = {
    capiAkkaForkJoinPool.foreach(pool => akkaForkJoinPoolMetrics(pool, "customerApi"))
    microAkkaForkJoinPool.foreach(pool => akkaForkJoinPoolMetrics(pool, "micro"))
    executorServiceMetrics(http, "http")
    executorServiceMetrics(cb, "couchbase")
    executorServiceMetrics(cn, "cn")
    executorServiceMetrics(featureStore, "featureStore")
    globalPool.foreach(pool => executorServiceMetrics(pool, "global"))
    executorServiceMetrics(winterfell.Implicits.slowExecutionContextExecutorService, "slow")
  }

  private def akkaForkJoinPoolMetrics(pool: AkkaForkJoinPool, name: String): Unit = {
    val tags = Map(
      "poolName" -> name
    )
    CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.poolSize", tags, pool.getPoolSize)
    CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.activeThreadCount", tags, pool.getActiveThreadCount)
    CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.queuedTaskCount", tags, pool.getQueuedTaskCount)
    CapiMeasurementMessage(
      "winterfell.threadPools.forkJoinPool.queuedSubmissionCount",
      tags,
      pool.getQueuedSubmissionCount)
    CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.runningThreadCount", tags, pool.getRunningThreadCount)
    CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.stealCount", tags, pool.getStealCount)
  }

  private def executorServiceMetrics(ec: ExecutorService, name: String): Unit = {
    val tags = Map(
      "poolName" -> name,
    )
    ec match {
      case pool: ThreadPoolExecutor =>
        CapiMeasurementMessage("winterfell.threadPools.threadPoolExecutor.poolSize", tags, pool.getPoolSize)
        CapiMeasurementMessage(
          "winterfell.threadPools.threadPoolExecutor.maximumPoolSize",
          tags,
          pool.getMaximumPoolSize)
        CapiMeasurementMessage(
          "winterfell.threadPools.threadPoolExecutor.largestPoolSize",
          tags,
          pool.getLargestPoolSize)
        CapiMeasurementMessage("winterfell.threadPools.threadPoolExecutor.corePoolSize", tags, pool.getCorePoolSize)
        CapiMeasurementMessage("winterfell.threadPools.threadPoolExecutor.activeCount", tags, pool.getActiveCount)
        CapiMeasurementMessage("winterfell.threadPools.threadPoolExecutor.queueSize", tags, pool.getQueue.size())
        CapiMeasurementMessage("winterfell.threadPools.threadPoolExecutor.taskCount", tags, pool.getTaskCount)

        CapiMeasurementMessage(
          "winterfell.threadPools.threadPoolExecutor.completedTaskCount",
          tags,
          pool.getCompletedTaskCount)
      case pool: ForkJoinPool =>
        CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.poolSize", tags, pool.getPoolSize)
        CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.activeThreadCount", tags, pool.getActiveThreadCount)
        CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.queuedTaskCount", tags, pool.getQueuedTaskCount)
        CapiMeasurementMessage(
          "winterfell.threadPools.forkJoinPool.queuedSubmissionCount",
          tags,
          pool.getQueuedSubmissionCount)
        CapiMeasurementMessage(
          "winterfell.threadPools.forkJoinPool.runningThreadCount",
          tags,
          pool.getRunningThreadCount)
        CapiMeasurementMessage("winterfell.threadPools.forkJoinPool.stealCount", tags, pool.getStealCount)
      case _ =>
    }
  }

}
