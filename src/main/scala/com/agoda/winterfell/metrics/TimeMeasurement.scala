package com.agoda.winterfell.metrics

import com.agoda.winterfell.models.CapiRequestContextData

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object TimeMeasurement {

  def measureF[T](
      methodTag: String,
      tags: Map[String, String] = Map.empty,
      metricName: String = MeasurementConstants.API_TRACE_BASE_METRIC,
      forceStartTime: Option[Long] = None)(
      f: Future[T])(implicit executionContext: ExecutionContext, capiCtx: CapiRequestContextData): Future[T] = {
    val start = forceStartTime.getOrElse(System.nanoTime)
    f.andThen {
      case result: Try[T] =>
        val endTime = System.nanoTime - start
        CapiMeasurementMessage(
          metricName,
          stringTags = capiCtx.toMetricTags ++ Map(
            "success" -> result.isSuccess.toString,
            "exceptionClass" -> result.failed.toOption.map(_.getClass.getSimpleName).getOrElse("success"),
            MeasurementConstants.METHOD_TAG -> methodTag
          ) ++ tags,
          measurementValue = endTime
        )
    }
  }

  /**
    * measure sync block
    */
  def measure[T](
      methodTag: String,
      tags: Map[String, String] = Map.empty,
      metricName: String = MeasurementConstants.API_TRACE_BASE_METRIC,
      forceStartTime: Option[Long] = None)(f: => T)(implicit capiCtx: CapiRequestContextData): T = {
    val start = forceStartTime.getOrElse(System.nanoTime)
    Try(f) match {
      case result: Try[T] =>
        val endTime = System.nanoTime - start
        CapiMeasurementMessage(
          metricName,
          stringTags = capiCtx.toMetricTags ++ Map(
            "success" -> result.isSuccess.toString,
            "exceptionClass" -> result.failed.toOption.map(_.getClass.getSimpleName).getOrElse("success"),
            MeasurementConstants.METHOD_TAG -> methodTag
          ) ++ tags,
          measurementValue = endTime
        )
        result.get
    }
  }

}
