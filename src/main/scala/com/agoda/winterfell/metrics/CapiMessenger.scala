package com.agoda.winterfell.metrics

import com.agoda.winterfell.Constants

object CapiMessenger {
  def sendToKibana(kibanaMessage: CapiLogMessage): Unit = {
    kibanaMessage.getLogMessages().foreach { l =>
      l.sendAsync(Constants.AdpApiKey)
    }
  }

  def sendToWF(measurementMessage: CapiMeasurementMessage): Unit = {
    measurementMessage.getMeasurementMessages().foreach { m =>
      m.sendAsync(Constants.AdpApiKey)
    }
  }
}
