package com.agoda.winterfell.metrics

import com.agoda.adp.messaging.message.MeasurementMessage
import com.agoda.winterfell.Constants
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.utils.{CapiConfig, DalContext}

import scala.collection.JavaConverters._

trait CapiMeasurementMessage {

  def getMeasurementMessages(): List[MeasurementMessage]
}

object CapiMeasurementMessage {
  def create(
      measurementName: String,
      stringTags: Map[String, String] = Map.empty[String, String],
      measurementValue: Long = 1
  ): MeasurementMessage = {
    val message = new MeasurementMessage(measurementName)
    message.measurementValue = measurementValue
    message.setTags(
      stringTags
        .filter(tag => !DalContext.GrafanaBlacklist.exists(tag._1.toLowerCase.startsWith))
        .mapValues(s => s.substring(0, Math.min(s.length, Constants.MaxMeasurementsTagLength)))
        .asJava)
    message
  }

  def apply(
      measurementName: String,
      stringTags: Map[String, String] = Map.empty[String, String],
      measurementValue: Long = 1)(
      implicit capiCtx: CapiRequestContextData = CapiRequestContextData.emptyWlContext(1)): Unit = {
    val capiTags = Option(capiCtx).filterNot(_.isFakeContext).map(_.toMetricTags).getOrElse(Map.empty)
    create(measurementName, capiTags ++ stringTags, measurementValue).sendAsync(CapiConfig.AdpApiKey)
  }
}
