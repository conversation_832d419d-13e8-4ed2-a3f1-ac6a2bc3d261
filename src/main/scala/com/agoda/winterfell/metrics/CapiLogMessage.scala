package com.agoda.winterfell.metrics

import com.agoda.adp.messaging.message.LogMessage
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.{baseLogger, Constants}
import com.agoda.winterfell.utils.{<PERSON><PERSON><PERSON><PERSON><PERSON>, CapiConsul}
import org.slf4j.{Logger, LoggerFactory}

import scala.collection.JavaConverters._

trait CapiLogMessage {
  def getLogMessages(): List[LogMessage]
}

object CapiLogMessage {
  val replicatorLogger: Logger = LoggerFactory.getLogger("replicator")
  val replicatorLoggerName = Vector("capi.replication")
  def create(
      loggerName: String,
      logLevel: LogLevel = LogLevel.INFO,
      stringTags: Map[String, String] = Map.empty[String, String],
      longTags: Map[String, Long] = Map.empty[String, Long],
      message: Option[String] = None,
      exceptionMessage: Option[String] = None,
      exception: Option[Throwable] = None,
      writeToLocal: Boolean = false,
      correlationId: Option[String] = None,
      sessionId: Option[String] = None
  ): LogMessage = {
    val log: LogMessage = new LogMessage(Constants.AdpApplicationName, logLevel, message.getOrElse(""))

    // Filter out the tag that contains digit
    val (loggingStringTags, loggingLongTags) = if (CapiConsul.sanitizedDalLoggingTags) {
      val sanitizedStringTags = stringTags.filterNot(x => x._1.matches(Constants.strEndWithDigit))
      val sanitizedLongTags = longTags.filterNot(x => x._1.matches(Constants.strEndWithDigit))
      (sanitizedStringTags, sanitizedLongTags)
    } else {
      (stringTags, longTags)
    }

    log.setLoggerName(loggerName)
    log.setStringTags(loggingStringTags.asJava)
    log.setLongTags(loggingLongTags.mapValues(Long.box).asJava)
    log.setVersion(BuildHelper.version)
    correlationId.map(log.setCorrelationID(_))
    sessionId.map(log.setSessionId(_))
    exceptionMessage.foreach { exMsg =>
      log.setExceptionMessage(exMsg)
    }
    exception.foreach(e => log.setException(e))
    if (writeToLocal) writeLogsToLocal(loggerName, logLevel, message, exceptionMessage, exception)
    log
  }

  def info(loggerName: String, message: String): Unit = {
    create(
      loggerName = loggerName,
      message = Some(message)
    ).sendAsync(Constants.AdpApiKey)
  }

  def warn(loggerName: String, message: String, exception: Option[Throwable] = None): Unit = {
    create(
      loggerName = loggerName,
      logLevel = LogLevel.WARN,
      message = Some(message),
      exception = exception
    ).sendAsync(Constants.AdpApiKey)
  }

  def error(loggerName: String, exceptionMessage: String, exception: Throwable): Unit = {
    create(
      loggerName = loggerName,
      logLevel = LogLevel.ERROR,
      exceptionMessage = Some(exceptionMessage),
      exception = Some(exception)
    ).sendAsync(Constants.AdpApiKey)
  }

  // TODO: Drop exceptionMessage  as it will be overridden by exception!
  def apply(
      loggerName: String,
      logLevel: LogLevel = LogLevel.INFO,
      stringTags: Map[String, String] = Map.empty[String, String],
      longTags: Map[String, Long] = Map.empty[String, Long],
      message: Option[String] = None,
      exceptionMessage: Option[String] = None,
      exception: Option[Throwable] = None,
      writeToLocal: Boolean = false,
      correlationId: Option[String] = None,
      sessionId: Option[String] = None)(
      implicit capiCtx: CapiRequestContextData = CapiRequestContextData.emptyWlContext(1)): Unit = {
    val capiTags = Option(capiCtx).filterNot(_.isFakeContext).map(_.toStringTags).getOrElse(Map.empty)
    create(
      loggerName,
      logLevel,
      capiTags ++ stringTags,
      longTags,
      message,
      exceptionMessage,
      exception,
      writeToLocal,
      correlationId,
      sessionId).sendAsync(Constants.AdpApiKey)
  }

  def writeLogsToLocal(
      loggerName: String,
      logLevel: LogLevel = LogLevel.INFO,
      message: Option[String] = None,
      exceptionMessage: Option[String] = None,
      exception: Option[Throwable] = None) = {
    exception match {
      case Some(error) =>
        if (replicatorLoggerName.exists(loggerName.startsWith))
          replicatorLogger.error(exceptionMessage.getOrElse(message.getOrElse("Error in loggerName")))
        baseLogger.error(exceptionMessage.getOrElse(message.getOrElse("Error in loggerName")), error)
      case None => {
        if (replicatorLoggerName.exists(loggerName.startsWith))
          replicatorLogger.info(message.getOrElse("Successfully"))
        else
          baseLogger.info(message.getOrElse("Successfully"))
      }
    }
  }

}
