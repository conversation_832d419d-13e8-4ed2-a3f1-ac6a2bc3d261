package com.agoda.winterfell

package object metrics {

  /**
    * This metric captures a login/registration event with the following optional/mandatory tags:
    * isFirstLogin, socialAppType, emailState
    */
  val LoginMeasurementV2 = "customerapi.login"

  /**
    * This metric captures a latency to some external service with the mandatory tags `operation` and `isSuccess`.
    */
  val LatencyMeasurement = "customerapi.latency"

  /**
    * This metric captures an event which was sent through Kafka with mandatory tags `topic` and `isSuccess`.
    */
  val KafkaMeasurement = "customerapi.kafka"

  /**
    * This metric captures an event when stream with tag 'stream' has been terminated.
    */
  val StreamMeasurement = "customerapi.stream"

  /**
    * This metric captures a login without client tracking data in login request.
    */
  val NoTrackingDataMeasurement = "customerapi.noclienttrackingdata"

  /**
    * This metric captures a latency to graphQL query with the mandatory tags either `operationName` or `topLevelField` or both and `isSuccess`:
    *
    */
  val GQLLatencyMeasurement = "customerapi.gql"

  /**
    * This metric captures the difference in booking count between retrieval from db vs from feature store for the
    * trusted host endpoint.
    */
  val NhaBookingsDBToFeatureStoreDelta = "customerapi.trustedhost.booking.info.featurestore.delta"

}
