package com.agoda.winterfell.graphql

import sangria.execution.deferred.DeferredResolver

import scala.concurrent.{ExecutionContext, Future}
import io.circe.generic.auto._
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.services.jwt.TokenHelper.TokenBox
import com.agoda.winterfell.unified.CustomerServiceOps
import com.agoda.winterfell.unified.Customer

import java.util.UUID

case class GraphQLContext(
    customerServiceOps: CustomerServiceOps,
    capiCtx: CapiRequestContextData,
    tokenBox: Option[TokenBox] = None)(implicit ec: ExecutionContext) {}
