package com.agoda.winterfell.graphql.metrics

import com.agoda.winterfell.graphql.GraphQLContext
import com.agoda.winterfell.metrics.{CapiMeasurementMessage, GQLLatencyMeasurement}
import com.agoda.winterfell.utils.CapiConfig
import sangria.ast._
import sangria.execution._
import sangria.schema.Context

import scala.collection.mutable

case class QueryMetrics(elapsedTime: Long, isSuccess: Boolean)

class LatencyMetrics[Ctx <: GraphQLContext] extends Middleware[Ctx] with MiddlewareErrorField[Ctx] {

  type QueryVal = scala.collection.mutable.Map[String, List[String]]
  type FieldVal = Unit

  private val START_TIME_KEY = "start"
  private val ERROR_KEY = "errors"
  private val QUERY_TOP_LEVEL_FIELD = "queryField"
  private val QUERY_OPERATION_NAME = "operationName"

  override def beforeQuery(context: MiddlewareQueryContext[Ctx, _, _]): QueryVal = {
    scala.collection.mutable.Map(START_TIME_KEY -> List(System.currentTimeMillis().toString))
  }

  def getQueryMetrics(queryVal: QueryVal): QueryMetrics = {
    val startTime = queryVal.get(START_TIME_KEY).map(_.head).getOrElse(System.currentTimeMillis().toString).toLong
    val elapsedTime = System.currentTimeMillis() - startTime
    val isSuccess = queryVal.get(ERROR_KEY) match {
      case Some(errors) => false
      case None => true
    }
    QueryMetrics(elapsedTime, isSuccess)
  }

  override def afterQuery(queryVal: QueryVal, context: MiddlewareQueryContext[Ctx, _, _]): Unit = {
    val queryMetrics = getQueryMetrics(queryVal)
    val definition = context.queryAst.definitions.collect {
      case od: OperationDefinition if od.operationType == OperationType.Query => od
    }
    val fieldSelection =
      definition.headOption.map(d => d.selections.collect { case fd: sangria.ast.Field => fd.name }.toList)
    val queryFields = fieldSelection.getOrElse(List.empty)
    val successTags = queryMetrics.isSuccess match {
      case true =>
        Map("status" -> "success")
      case _ =>
        Map("status" -> "fail")
    }
    val measurementTags = successTags ++ Map(
      QUERY_OPERATION_NAME -> context.operationName.headOption.getOrElse(""),
      QUERY_TOP_LEVEL_FIELD -> queryFields.headOption.getOrElse(""))
    CapiMeasurementMessage
      .create(GQLLatencyMeasurement, measurementTags, queryMetrics.elapsedTime)
      .sendAsync(CapiConfig.AdpApiKey)
  }

  override def beforeField(queryVal: QueryVal, mctx: MiddlewareQueryContext[Ctx, _, _], ctx: Context[Ctx, _]) =
    continue()

  override def fieldError(
      queryVal: QueryVal,
      fieldVal: FieldVal,
      error: Throwable,
      mctx: MiddlewareQueryContext[Ctx, _, _],
      ctx: Context[Ctx, _]): Unit = {
    queryVal.get(ERROR_KEY) match {
      case Some(errors) => queryVal.update(ERROR_KEY, error.getStackTraceString :: errors)
      case None => queryVal.update(ERROR_KEY, List(error.getStackTraceString))
    }
  }

}
