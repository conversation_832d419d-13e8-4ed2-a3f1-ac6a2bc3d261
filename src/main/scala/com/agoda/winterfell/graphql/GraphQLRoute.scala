package com.agoda.winterfell.graphql

import akka.http.scaladsl.coding.{Deflate, Gzip, NoCoding}
import akka.http.scaladsl.model.HttpEntity
import akka.http.scaladsl.model.MediaTypes.`text/html`
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.{Directive0, Route}
import com.agoda.winterfell.graphql.GraphQLRequestUnmarshaller._
import com.agoda.winterfell.graphql.metrics.LatencyMetrics
import com.agoda.winterfell.graphql.schema.StaticSchema
import com.agoda.winterfell.http.Directives.extractCapiRequestContext
import com.agoda.winterfell.unified.CustomerServiceOps
import com.agoda.winterfell.graphql.exception._
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.services.jwt.TokenHelper.TokenBox
import com.agoda.winterfell.utils.CircuitBreakerWrapper
import de.heikoseeberger.akkahttpcirce.ErrorAccumulatingCirceSupport._
import io.circe.Json
import io.circe.optics.JsonPath.root
import io.circe.parser.parse
import sangria.ast.Document
import sangria.execution.{
  ErrorWithResolver,
  Executor,
  MaxQueryDepthReachedError,
  QueryAnalysisError,
  QueryReducer,
  QueryReducingError
}
import sangria.marshalling.circe._
import sangria.parser._

import java.util.UUID
import scala.collection.immutable
import scala.concurrent.duration.{FiniteDuration, MILLISECONDS}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success}

class GraphQLRoute(customerServiceOps: CustomerServiceOps)(implicit ex: ExecutionContext) {

  private val latencyMetrics = new LatencyMetrics[GraphQLContext]
  private val customerResolver = new CustomerResolver
  private val QUERY_MAX_DEPTH = 6
  private val maxRequestSize: Long = 16384L

  def gqlRoute(tokenBox: TokenBox)(implicit capiCtx: CapiRequestContextData) = route {
    (query, operationName, variables) ⇒
      {
        Executor.execute(
          StaticSchema.gqlSchema,
          query,
          GraphQLContext(customerServiceOps, capiCtx, Some(tokenBox)),
          variables = variables,
          operationName = operationName,
          deferredResolver = customerResolver,
          queryReducers = fetchQueryReducer,
          middleware = latencyMetrics :: Nil,
          exceptionHandler = ExceptionUtil.exceptionHandler
        )
      }
  }

  val gqlSchemaRoute = extractCapiRequestContext { ctx =>
    schemaIntrospectionRoute { (query, operationName, variables) ⇒
      {
        Executor.execute(
          StaticSchema.gqlSchema,
          query,
          GraphQLContext(customerServiceOps, ctx),
          variables = variables,
          operationName = operationName,
          middleware = latencyMetrics :: Nil,
        )
      }
    }
  }

  val gqlHtmlRoute = graphiqlRoute

  private def fetchQueryReducer: List[QueryReducer[GraphQLContext, GraphQLContext]] = {
    List(
      QueryReducer.rejectMaxDepth[GraphQLContext](QUERY_MAX_DEPTH),
      QueryReducer.hasIntrospection((hasIntro, ctx) ⇒ if (hasIntro) throw IntrospectionNotAllowedError() else ctx, true)
    )
  }

  private def safeDecodeRequest(maxBytes: Long): Directive0 = {
    decodeRequest & mapRequest(_.mapEntity {
      case c: HttpEntity.Chunked ⇒ c.copy(chunks = HttpEntity.limitableChunkSource(c.chunks))
      case e ⇒ e
    }) & withSizeLimit(maxBytes)
  }

  private def route(executeFn: (Document, Option[String], Json) ⇒ Future[Json])(
      implicit ex: ExecutionContext): Route = {
    val responseEncoders = immutable.Seq(Gzip, Deflate)
    safeDecodeRequest(maxRequestSize) {
      toStrictEntity(FiniteDuration(300, MILLISECONDS)) {
        (post & path("graphql")) {
          encodeResponseWith(NoCoding, responseEncoders: _*) {
            parameters('query.?, 'operationName.?, 'variables.?) { (queryParam, operationNameParam, variablesParam) ⇒
              entity(as[Json]) { body ⇒
                val query = queryParam orElse root.query.string.getOption(body)
                val operationName = operationNameParam orElse root.operationName.string.getOption(body)
                val variablesStr = variablesParam orElse root.variables.string.getOption(body)
                query.map(QueryParser.parse(_)) match {
                  case Some(Success(ast)) ⇒
                    variablesStr.map(parse) match {
                      case Some(Left(error)) ⇒ complete(BadRequest, formatError(error))
                      case Some(Right(json)) ⇒ {
                        executeGraphQL(executeFn, ast, operationName, json)
                      }
                      case None ⇒ {
                        executeGraphQL(
                          executeFn,
                          ast,
                          operationName,
                          root.variables.json.getOption(body) getOrElse Json.obj())
                      }
                    }
                  case Some(Failure(error)) ⇒ complete(BadRequest, formatError(error))
                  case None ⇒ complete(BadRequest, formatError("No query to execute"))
                }
              } ~
                entity(as[Document]) { document ⇒
                  variablesParam.map(parse) match {
                    case Some(Left(error)) ⇒ complete(BadRequest, formatError(error))
                    case Some(Right(json)) ⇒ executeGraphQL(executeFn, document, operationNameParam, json)
                    case None ⇒ executeGraphQL(executeFn, document, operationNameParam, Json.obj())
                  }
                }
            }
          }
        } ~ (get & pathEndOrSingleSlash) {
          redirect("/graphql", PermanentRedirect)
        }
      }
    }
  }

  private def graphiqlRoute()(implicit ex: ExecutionContext): Route =
    path("graphiql") {
      get {
        explicitlyAccepts(`text/html`) {
          getFromResource("graphql/graphiql.html")
        }
      } ~ (get & pathEndOrSingleSlash) {
        redirect("/graphiql", PermanentRedirect)
      }
    }

  private def schemaIntrospectionRoute(executeFn: (Document, Option[String], Json) ⇒ Future[Json])(
      implicit ex: ExecutionContext): Route =
    path("graphql") {
      get {
        executeGraphQL(executeFn, sangria.introspection.introspectionQuery, None, Json.obj())
      } ~ (get & pathEndOrSingleSlash) {
        redirect("/graphql", PermanentRedirect)
      }
    }

  private def executeGraphQL(
      executeFn: (Document, Option[String], Json) ⇒ Future[Json],
      query: Document,
      operationName: Option[String],
      variables: Json
  )(implicit ex: ExecutionContext) = {
    complete(
      executeFn(query, operationName, if (variables.isNull) Json.obj() else variables)
        .map(OK → _)
        .recover {
          case QueryReducingError(error: MaxQueryDepthReachedError, _) ⇒ BadRequest → formatError(error.getMessage)
          case QueryReducingError(error: IllegalStateException, _) ⇒ BadRequest → formatError(error.getMessage)
          case error: QueryAnalysisError ⇒ BadRequest → error.resolveError
          case error: ErrorWithResolver ⇒ InternalServerError → error.resolveError
        })
  }

  private def formatError(error: Throwable): Json = error match {
    case syntaxError: SyntaxError ⇒
      Json.obj(
        "errors" → Json.arr(Json.obj(
          "message" → Json.fromString(syntaxError.getMessage),
          "locations" → Json.arr(Json.obj(
            "line" → Json.fromBigInt(syntaxError.originalError.position.line),
            "column" → Json.fromBigInt(syntaxError.originalError.position.column)))
        )))
    case NonFatal(e) ⇒
      formatError(e.getMessage)
    case e ⇒
      throw e
  }

  private def formatError(message: String): Json =
    Json.obj("errors" → Json.arr(Json.obj("message" → Json.fromString(message))))

}
