package com.agoda.winterfell.graphql

import com.agoda.winterfell.graphql.model.GQLCustomerRequest
import com.agoda.winterfell.unified.{Customer, CustomerServiceOps}
import sangria.execution.deferred.{Deferred, DeferredResolver}
import io.circe.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

case class CustomerByMemberIdDeferred(customerRequest: GQLCustomerRequest) extends Deferred[Customer]

class CustomerResolver extends DeferredResolver[GraphQLContext] {

  override def resolve(deferred: Vector[Deferred[Any]], ctx: GraphQLContext, queryState: Any)(
      implicit ec: ExecutionContext): Vector[Future[Any]] = {
    implicit val capiCtx = ctx.capiCtx
    val defMap = deferred.collect {
      case CustomerByMemberIdDeferred(customerRequest) =>
        "getCustomerByMemberId" -> {
          ctx.customerServiceOps.memberByMemberId(customerRequest.memberId, customerRequest.whiteLabelId)
        }
    }

    deferred
      .flatMap({
        case CustomerByMemberIdDeferred(customerRequest) => defMap.filter(_._1 == "getCustomerByMemberId").map(_._2)
      })
      .map(Future(_))
  }

}
