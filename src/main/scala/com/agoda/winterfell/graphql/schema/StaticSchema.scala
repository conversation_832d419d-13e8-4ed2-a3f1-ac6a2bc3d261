package com.agoda.winterfell.graphql.schema

import RequestSchema.CustomerRequestTypeArgs
import ResponseSchema._
import com.agoda.winterfell.graphql.model.GQLCustomerRequest
import com.agoda.winterfell.graphql.{CustomerByMemberIdDeferred, GraphQLContext}
import com.agoda.winterfell.helper.WhiteLabelHelper
import sangria.schema._

import java.util.UUID
import scala.util.Try

object StaticSchema {

  val Query = ObjectType(
    "Query",
    fields[GraphQLContext, Unit](
      Field(
        name = "CustomerByMemberId",
        description = Some("Customer Query"),
        fieldType = CustomerType,
        arguments = Nil,
        resolve = c =>
          c.ctx.tokenBox.get.info.get.whiteLabelToken match {
            case wlToken: Option[UUID] =>
              CustomerByMemberIdDeferred(
                GQLCustomerRequest(
                  c.ctx.tokenBox.get.info.get.memberId,
                  WhiteLabelHelper.getWhiteLabelIdByToken(Try(wlToken.get.toString).toOption)))
            case _ => CustomerByMemberIdDeferred(GQLCustomerRequest(c.ctx.tokenBox.get.info.get.memberId))
        }
      ),
      Field(
        name = "CustomerByMemberIdWithArgs",
        description = Some("Customer Query"),
        fieldType = CustomerType,
        arguments = CustomerRequestTypeArgs :: Nil,
        resolve = c => CustomerByMemberIdDeferred(c.arg(CustomerRequestTypeArgs))
      )
    )
  )

  private val _gqlSchema = Schema(Query)

  def gqlSchema: Schema[GraphQLContext, Unit] = _gqlSchema

}
