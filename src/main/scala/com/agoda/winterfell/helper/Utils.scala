package com.agoda.winterfell.helper

import com.agoda.winterfell.utils.ExceededRetryException

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

object Utils {
  def retry[T](numberOfRetry: Int, delayInMS: Int)(fn: => T)(until: (T) => Boolean): T = {
    Thread.sleep(delayInMS)
    val result = fn
    if (!until(result) && numberOfRetry >= 0) {
      retry(numberOfRetry - 1, delayInMS)(fn)(until)
    } else {
      result
    }
  }

  /**
    * Retry with side effects (exception). Prefer to use #retryWithStat pure version
    * @param n total number of retries
    * @param fn function to be retried
    * @throws ExceededRetryException that would be generated by @fn in case of all failed attempts
    */
  def retryStandard[T](n: Int = 3)(fn: => T): T = {
    tryRetryStandard(n)(fn).get
  }

  /**
    * Retry with side effects (exception). Prefer to use #retryWithStat pure version
    * @param n total number of retries
    * @param fn function to be retried
    * @throws ExceededRetryException that would be generated by @fn in case of all failed attempts
    */
  def tryRetryStandard[T](n: Int = 3)(fn: => T): Try[T] = {
    val (result, _) = retryWithStat(n)(fn)
    result
  }

  /**
    * Anything in the block would be retried n times, on final failure exception would be thrown
    * @param n total number of retries
    * @param fn function to be retried
    * @return pair of [Try, Int] with potential result or latest exception AND number of retries
    */
  def retryWithStat[T](n: Int)(fn: => T): (Try[T], Int) = {
    require(n > 0, "Number of retries should be positive number")
    retryWithStat(n, currentRetry = 1)(fn)
  }

  @annotation.tailrec
  private def retryWithStat[T](maxRetries: Int, currentRetry: Int)(fn: => T): (Try[T], Int) = {
    Try { fn } match {
      case arg @ Success(_) => arg -> currentRetry
      case _ if maxRetries > currentRetry => retryWithStat(maxRetries, currentRetry + 1)(fn)
      case arg => arg -> maxRetries
    }
  }
}
