package com.agoda.winterfell
package helper

import java.util.UUID
import com.agoda.adp.messaging.message.logging.LogLevel
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.dal.data.Default
import com.agoda.dal.model.EntityOrigin
import io.circe.generic.extras.Configuration
import io.circe.generic.extras.auto._
import io.circe.parser._
import com.agoda.whitelabel.client.WhiteLabelClientApi
import com.agoda.whitelabel.client.constants.WhitelabelMetadata
import com.agoda.whitelabel.client.constants.WhitelabelMetadata.WhitelabelMetadataValue
import com.agoda.whitelabel.client.model.CustomDomains
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.whitelabel.client.settings.Constants.WhiteLabelToken
import com.agoda.winterfell.Constants.RadissonChainSlug
import com.agoda.winterfell.common.AuthenticationType.{
  AuthenticationType,
  RMReservedAuthType10,
  RMReservedAuthType7,
  RMReservedAuthType8,
  RMReservedAuthType9,
  <PERSON><PERSON><PERSON>ChainSSO,
  RmAAdvantage,
  RmAirAsiaBigHotels,
  RmAlaskaAirlineHotels,
  RmAllegiantAir,
  RmCitiHkHotels,
  RmClubPremierHotels,
  RmConnectMilesHotels,
  RmDeutscheBahnHotels,
  RmEmirates,
  RmEnrichHotels,
  RmEvaAirHotels,
  RmExtraMiles,
  RmInterMiles,
  RmLatamPassBrasil,
  RmLatamPassHotels,
  RmLifemiles,
  RmNectarHotels,
  RmOpenTableHotels,
  RmRefAirlines,
  RmSSOAuthType,
  RmShopYourWayHotels,
  RmSmiles,
  TheClubTravellerAuthType
}
import com.agoda.winterfell.marshalling.JacksonMarshallers
import com.agoda.winterfell.metrics.CapiLogMessage
import com.agoda.winterfell.models.{CapiRequestContextData, CompareMapsResult}
import com.agoda.winterfell.models.entities.{
  CustomerModel,
  JapanicanAdditionalWlProperties,
  JtbAdditionalWlProperties,
  RocketMilesAdditionalWlProperties,
  RurubuAdditionalWlProperties
}
import com.agoda.winterfell.repository.CustomerRepository
import com.agoda.winterfell.services.TempSingletonHolder
import com.agoda.winterfell.unified.wl.{RocketMilesWlProperties, WlAdditionalProperties, WlEmptyProp}
import com.agoda.winterfell.utils.{CapiConfig, CapiConsul, DalContext}
import com.google.common.cache.CacheBuilder
import generated.model.{EmailUserOperationsV4, LoginV4, LoyaltyProgramV4, PartnerClaimToken, PiiDataFencing}

import java.util.concurrent.{ExecutorService, Executors, ThreadFactory, TimeUnit}
import scala.annotation.tailrec
import scala.util.control.NonFatal
import scala.concurrent.{ExecutionContext, Future}

class WhiteLabelHelper(customerRepository: CustomerRepository) {
  def isCustomerValid(userId: UUID, whiteLabelId: Int)(implicit capiCtx: CapiRequestContextData): Boolean = {
    val isValid =
      customerRepository.customerByUserId(userId, whitelabelId = Some(whiteLabelId)).whiteLabelId.map(_ == whiteLabelId)
    isValid.getOrElse(false)
  }
}

object WhiteLabelHelper {
  // TODO: remove!
  private val featureCache = CacheBuilder
    .newBuilder()
    .maximumSize(Short.MaxValue)
    .expireAfterWrite(5, TimeUnit.SECONDS)
    .build[java.lang.Integer, FeaturesConfiguration]
  private val tokenCache = CacheBuilder
    .newBuilder()
    .maximumSize(Short.MaxValue)
    .expireAfterWrite(5, TimeUnit.SECONDS)
    .build[String, java.lang.Integer]

  implicit val customConfig: Configuration = Configuration.default.withDefaults

  implicit val clientAppName = CapiConfig.AdpAppName
  private val isolatedWLExecutor = ExecutionContext.fromExecutorService(Executors.newSingleThreadExecutor { callback =>
    val thread = new Thread(callback)
    thread.setName("WLClient")
    thread.setContextClassLoader(Boot.getClass.getClassLoader)
    thread
  })
  private val whiteLabelClientApi = Future {
    val client =
      WhiteLabelClientApi(CapiConfig.whiteLabelHostsSettingsList, CapiConfig.AdpApiKey, CapiConfig.AdpAppName)
    client
  }(isolatedWLExecutor)
  //TODO We need to migrate all these repo to CustomerServiceOps, for now keep them lazy to avoid any partial initialisation due to singleton/object scope of TempSingletonHolder
  private def jtbPropertiesRepo = TempSingletonHolder.jtbPropertiesRepo.get()
  private def japanicanPropertiesRepo = TempSingletonHolder.japanicanPropertiesRepo.get()
  private def rurubuPropertiesRepo = TempSingletonHolder.rurubuPropertiesRepo.get()
  private def rocketMilesPropertiesRepo = TempSingletonHolder.rocketMilesPropertiesRepo.get()

  def getWhiteLabelTokenFromId(id: Int): String = {
    if (id == Constants.AgodaId) WhiteLabelToken.AGODA
    else {
      whiteLabelClientApi.map(_.getTokenAccordingToWhiteLabelId(id))(isolatedWLExecutor).blockAndWait30s
    }
  }

  def getWhitelabelMetadata(id: Int): Option[WhitelabelMetadata.WhitelabelMetadata] = {
    Try(WhitelabelMetadata.apply(id).asInstanceOf[WhitelabelMetadata.WhitelabelMetadata]).toOption
  }

  def getVerifyEmailTemplate(whiteLabelId: Int): Option[Int] = {
    val token = getWhiteLabelTokenFromId(whiteLabelId)
    whiteLabelClientApi
      .map(_.getFeature[EmailUserOperationsV4]('emailUserOperations, token).verifyEmailTemplateId)(isolatedWLExecutor)
      .blockAndWait30s
  }

  def getLoginFeature(whitelabelId: Int): LoginV4 = {
    val token = getWhiteLabelTokenFromId(whitelabelId)
    val result = whiteLabelClientApi
      .map(_.getFeature[LoginV4]('login, token))(isolatedWLExecutor)
      .blockAndWait30s
    if (result == null) LoginV4()
    else result
  }

  def getLoyaltyProgramFeature(whitelabelId: Int): LoyaltyProgramV4 = {
    val token = getWhiteLabelTokenFromId(whitelabelId)
    val result = whiteLabelClientApi
      .map(_.getFeature[LoyaltyProgramV4]('loyaltyProgram, token))(isolatedWLExecutor)
      .blockAndWait30s
    if (result == null) LoyaltyProgramV4()
    else result
  }

  def getPiiDataFencingFeature(whitelabelId: Int): Option[PiiDataFencing] = {
    val token = getWhiteLabelTokenFromId(whitelabelId)
    whiteLabelClientApi
      .map(_.getFeature[Option[PiiDataFencing]]('piiDataFencing, token))(isolatedWLExecutor)
      .recover {
        case _: Throwable => None
      }(isolatedWLExecutor)
      .blockAndWait30s
  }

  def getPartnerClaimTokenFeature(whitelabelId: Int): Option[PartnerClaimToken] = {
    val token = getWhiteLabelTokenFromId(whitelabelId)
    whiteLabelClientApi
      .map(_.getFeature[Option[PartnerClaimToken]]('partnerClaimToken, token))(isolatedWLExecutor)
      .recover {
        case _: Throwable => None
      }(isolatedWLExecutor)
      .blockAndWait30s
  }

  def getWLDomains(whitelabelId: Int): CustomDomains = {
    val token = getWhiteLabelTokenFromId(whitelabelId)
    whiteLabelClientApi
      .map(_.getWhitelabelInfo(token))(isolatedWLExecutor)
      .map(_.customDomains)(isolatedWLExecutor)
      .recover { case _ => CustomDomains("", "", "", "") }(isolatedWLExecutor)
      .blockAndWait30s
  }

  def getWhiteLabelIdByToken(token: Option[String]): Int = token match {
    case None => Constants.AgodaId
    case Some(WhiteLabelToken.AGODA) => Constants.AgodaId
    case Some(token) =>
      tokenCache.get(
        token,
        () => {
          try whiteLabelClientApi
            .map(_.getWhiteLabelIdByToken(Some(token.toUpperCase)))(isolatedWLExecutor)
            .blockAndWait30s
          catch { case NonFatal(error) => throw new ArgumentInvalidException(error.getMessage) }
        }
      )
  }

  def isLoginEnabled(
      whiteLabelKey: Option[String],
      origin: Option[String] = None,
      deviceTypeId: Option[Int] = None): Boolean =
    isFeatureEnabled(WhiteLabelFeatureNames.login, whiteLabelKey, origin, deviceTypeId)

  def isPiiDataFencingEnabled(
      whitelabelId: Option[Int],
      origin: Option[String] = None,
      deviceTypeId: Option[Int] = None): Boolean = {
    val token = whitelabelId.map(getWhiteLabelTokenFromId)
    isFeatureEnabled(WhiteLabelFeatureNames.piiDataFencing, token, origin, deviceTypeId)
  }

  // TODO: move to CustomerServiceOps
  def getDalWpProperties(whitelabelId: Int, userId: UUID)(
      implicit capiCtx: CapiRequestContextData): Future[Map[String, String]] = {
    import Implicits.slowExecutionContext
    val entity = whitelabelId match {
      case Constants.JtbId | Constants.JtbUatId =>
        jtbPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().map(_.map(_.toMap))
      case Constants.JapanicanId | Constants.JapanicanUatId =>
        japanicanPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().map(_.map(_.toMap))
      case Constants.RurubuId | Constants.RurubuUatId =>
        rurubuPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().map(_.map(_.toMap))
      case id: Int if Constants.isRmWhitelabelId(id) =>
        rocketMilesPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().map(_.map(_.toMap))
      case _ => Future.successful(None)
    }
    entity.map {
      case Some(fields) =>
        val filteredFields = fields.collect {
          case (k, Some(v)) => k -> v.toString
          case (k, v) if v != None => k -> v.toString
        }
        if (whitelabelId == Constants.JapanicanId) filteredFields + ("analysisID" -> userId.toString)
        else filteredFields
      case None => Map.empty[String, String]
    }
  }

  def dalWlPropsDiff(model: CustomerModel)(implicit capiCtx: CapiRequestContextData): Future[Map[String, String]] =
    dalWlPropsDiff(model.memberDetails.whiteLabelId.getOrElse(Constants.AgodaId), model)
  def dalWlPropsDiff(whitelabelId: Int, model: CustomerModel)(
      implicit capiCtx: CapiRequestContextData): Future[Map[String, String]] = {
    import Implicits.slowExecutionContext
    if (!CapiConsul.logWlpropsDiffDAL) Future.failed(new IllegalStateException("Validation is disabled"))
    else if (model.memberDetails.whiteLabelId.contains(Constants.AgodaId)) Future.successful(Map.empty)
    else {
      WhiteLabelHelper.getDalWpProperties(whitelabelId, model.userId).map { map =>
        if (map.nonEmpty) {
          val currentWlProp = model.fields.whiteLabelProperties
          val currentMap = JacksonMarshallers
            .mappify(currentWlProp.getOrElse(WlEmptyProp()))
            .filter(kv => Option(kv._2).isDefined && !Seq("typeName", "extraFields", "tempJsonParser").contains(kv._1))
          val filteredMap = map.filterKeys(k => currentMap.keys.toList.contains(k))
          val check = propDiffCheck(currentMap, filteredMap)
          if (check.diff) {
            val tags = check.diffFields + ("memberId" -> model.memberId.toString)
            CapiLogMessage(
              "capi.foundDiffDalWlProp",
              LogLevel.WARN,
              message = Some("Found Difference on Dal white label properties"),
              stringTags = tags)
          }
          check.diffFields
        } else {
          if (model.fields.whiteLabelProperties.isDefined && !CapiConsul.ignoreLogWlpropsMissingDAL)
            CapiLogMessage(
              "capi.missingDalWlProp",
              LogLevel.WARN,
              message = Some("Not found any Dal white label properties"),
              stringTags = Map("memberId" -> model.memberId.toString))
          model.fields.whiteLabelProperties
            .map { case p: Product => (p.toFlatMap) }
            .getOrElse(Map.empty)
            .filter(kv => Option(kv._2).isDefined && !Seq("typeName", "extraFields", "tempJsonParser").contains(kv._1))
        }
      }
    }
  }

  // TODO: move to CustomerServiceOps
  def updateDalWlProperties(
      userId: UUID,
      whitelabelId: Int,
      wlProp: Option[WlAdditionalProperties],
      partialUpdate: Boolean = true,
      origin: Option[String] = None)(
      implicit capiCtx: CapiRequestContextData): MeasuredFuture[WlAdditionalProperties] = {
    import Implicits.slowExecutionContext
    var propsMap = wlProp.map(prop => JacksonMarshallers.mappify(prop))
    def updateOrigin = {
      if (origin.isDefined)
        propsMap = origin.flatMap(ori => propsMap.map(_.updated("origin", ori)))
    }
    val activity = whitelabelId match {
      case (Constants.JtbId | Constants.JtbUatId) if propsMap.isDefined =>
        jtbPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().flatMap {
          case Some(entity) =>
            val updatedEntity =
              if (partialUpdate) entity.mergeWith(propsMap.get)
              else entity.mergeWith(Default[JtbAdditionalWlProperties].mergeWith(propsMap.get).copy(userId = userId))
            jtbPropertiesRepo.save(updatedEntity, Constants.DefaultQueryParams)().map(_.toResponse)
          case _ =>
            val newEntity = Default[JtbAdditionalWlProperties].mergeWith(propsMap.get).copy(userId = userId)
            jtbPropertiesRepo.save(newEntity, Constants.DefaultQueryParams)().map(_.toResponse)
        }
      case (Constants.JapanicanId | Constants.JapanicanUatId) if propsMap.isDefined =>
        japanicanPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().flatMap {
          case Some(entity) =>
            val updatedEntity =
              if (partialUpdate) entity.mergeWith(propsMap.get)
              else
                entity.mergeWith(Default[JapanicanAdditionalWlProperties].mergeWith(propsMap.get).copy(userId = userId))
            japanicanPropertiesRepo.save(updatedEntity, Constants.DefaultQueryParams)().map(_.toResponse)
          case _ =>
            updateOrigin
            val newEntity = Default[JapanicanAdditionalWlProperties].mergeWith(propsMap.get).copy(userId = userId)
            japanicanPropertiesRepo.save(newEntity, Constants.DefaultQueryParams)().map(_.toResponse)
        }
      case (Constants.RurubuId | Constants.RurubuUatId) if propsMap.isDefined =>
        rurubuPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().flatMap {
          case Some(entity) =>
            val updatedEntity =
              if (partialUpdate) entity.mergeWith(propsMap.get)
              else entity.mergeWith(Default[RurubuAdditionalWlProperties].mergeWith(propsMap.get).copy(userId = userId))
            rurubuPropertiesRepo.save(updatedEntity, Constants.DefaultQueryParams)().map(_.toResponse)
          case _ =>
            updateOrigin
            val newEntity = Default[RurubuAdditionalWlProperties].mergeWith(propsMap.get).copy(userId = userId)
            rurubuPropertiesRepo.save(newEntity, Constants.DefaultQueryParams)().map(_.toResponse)
        }
      case id: Int if Constants.isRmWhitelabelId(id) =>
        val requestedWlProps = propsMap.getOrElse(Map.empty[String, Any])
        rocketMilesPropertiesRepo.findById(userId, Constants.DefaultQueryParams)().flatMap {
          case Some(entity) =>
            val updatedEntity =
              if (partialUpdate) entity.mergeWith(requestedWlProps)
              else
                entity.mergeWith(
                  Default[RocketMilesAdditionalWlProperties].mergeWith(requestedWlProps).copy(userId = userId))
            rocketMilesPropertiesRepo.save(updatedEntity, Constants.DefaultQueryParams)().map(_.toResponse)
          case _ =>
            updateOrigin
            val newEntity = Default[RocketMilesAdditionalWlProperties].mergeWith(requestedWlProps).copy(userId = userId)
            rocketMilesPropertiesRepo.save(newEntity, Constants.DefaultQueryParams)().map(_.toResponse)
        }
      case _ => MeasuredFuture.successful(WlEmptyProp())
    }
    DalContext.reportActivity("capi.dal.update_wl_props", activity)
    activity.blockFor(Constants.MdcQueryTimeout)
    activity
  }

  //TODO can be removed after we are sure no extra fields in JTB
  val mapFieldsToSkip = Seq("extraFields")

  def propDiffCheck(currentProp: Map[String, Any], dalProp: Map[String, String]): CompareMapsResult = {
    val currentMap = currentProp.filter(kv => !mapFieldsToSkip.contains(kv._1)).map(kv => kv._1 -> kv._2.toString)
    val dalMap = dalProp.filter(kv => !mapFieldsToSkip.contains(kv._1))
    @tailrec
    def comparingMap(key: List[String], track: CompareMapsResult): CompareMapsResult = {
      key match {
        case Nil => track
        case x :: tail =>
          if (!dalMap.contains(x) || dalMap.get(x) != currentMap.get(x)) {
            comparingMap(tail, track.copy(true, diffFields = track.diffFields + (x -> "1")))
          } else {
            comparingMap(tail, track)
          }
      }
    }

    comparingMap(currentMap.keys.toList, CompareMapsResult(false, Map.empty[String, String]))
  }

  // SSO supported Rocketmiles external partner Whitelabel Id

  lazy val RMAirasiabig = 12
  lazy val RMCitiHk = 13
  lazy val RMClubpremier = 14
  lazy val RMConnectmiles = 15
  lazy val RMEmirates = 16
  lazy val RMEnrich = 17
  lazy val RMExtramiles = 18
  lazy val RMJetprivilege = 20
  lazy val RMLatam = 21
  lazy val RMLifemiles = 22
  lazy val RMMileageplan = 23
  lazy val RMMultiplus = 24
  lazy val RMNectar = 25
  lazy val RMOpentable = 26
  lazy val RMShopyourway = 28
  lazy val RMSmiles = 29
  lazy val RMReserved1 = 34
  lazy val RMReserved2 = 35
  lazy val RMGoogle = 36
  lazy val RmRefAirlinesWl = 37
  lazy val RMReserved5 = 38
  lazy val RMReserved6 = 39
  lazy val RMReserved7 = 40
  lazy val RMReserved8 = 41
  lazy val RMReserved9 = 42
  lazy val RMReserved10 = 43
  lazy val RMAAdvantage = 44
  lazy val TheClubTraveller = 45
  lazy val RMEvaAir = 46

  def ssoToAuthType(chainSlug: String): AuthenticationType = {
    chainSlug.toLowerCase() match {
      case RadissonChainSlug => RadissonChainSSO
      case _ => throw new IllegalArgumentException("SSO Login not supported for chain: " + chainSlug)
    }
  }

  def ssoToAuthType(whitelabelId: Int) = {
    whitelabelId match {
      case RMAirasiabig => RmAirAsiaBigHotels
      case RMCitiHk => RmCitiHkHotels
      case RMClubpremier => RmClubPremierHotels
      case RMConnectmiles => RmConnectMilesHotels
      case RMEmirates => RmEmirates
      case RMEnrich => RmEnrichHotels
      case RMEvaAir => RmEvaAirHotels
      case RMExtramiles => RmExtraMiles
      case RMJetprivilege => RmInterMiles
      case RMLatam => RmLatamPassHotels
      case RMLifemiles => RmLifemiles
      case RMMileageplan => RmAlaskaAirlineHotels
      case RMMultiplus => RmLatamPassBrasil
      case RMNectar => RmNectarHotels
      case RMOpentable => RmOpenTableHotels
      case RMShopyourway => RmShopYourWayHotels
      case RMSmiles => RmSmiles
      case RMReserved1 => RmAllegiantAir
      case RMReserved2 => RmDeutscheBahnHotels
      case RmRefAirlinesWl => RmRefAirlines
      case RMAAdvantage => RmAAdvantage
      case RMReserved7 => RMReservedAuthType7
      case RMReserved8 => RMReservedAuthType8
      case RMReserved9 => RMReservedAuthType9
      case RMReserved10 => RMReservedAuthType10
      case TheClubTraveller => TheClubTravellerAuthType
      case wlId: Int => {
        if (Constants.SSOWhitelabelIdList.contains(wlId)) RmSSOAuthType
        else throw new IllegalArgumentException("SSO Login not supported for WhitelabelId: " + wlId)
      }
    }
  }

  private def isFeatureEnabled(
      featureName: String,
      whiteLabelKey: Option[String],
      origin: Option[String],
      deviceTypeId: Option[Int],
      isBVariant: String => Boolean = _ => false): Boolean = {
    whiteLabelClientApi
      .map(_.isFeatureEnabled(featureName, whiteLabelKey, origin, deviceTypeId, isBVariant))(isolatedWLExecutor)
      .blockAndWait30s
  }
}
