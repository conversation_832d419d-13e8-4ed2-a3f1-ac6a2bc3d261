package com.agoda.winterfell.mocks.cusco

import com.agoda.cusco.communication.client.model.{
  FetchSubscriptionMetadataRequest,
  ResponseContextSeqFetchSubscriptionMetadataResponse,
  ResponseContextSubscriptionOperationResponse,
  ResponseContextUpdateReferenceBlockedResponse,
  SubscriptionOperationResponse,
  UpdateReferenceBlockedRequest,
  UpdateReferenceBlockedResponse,
  UpdateSubscriptionsRequest
}
import com.agoda.winterfell.services.cusco.CommunicationClient

import java.util.UUID
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class CommunicationClientMock extends CommunicationClient {

  override def communicationPreferenceFetchSubscriptionMetadata(
      xAPIKEY: String,
      fetchSubscriptionMetadataRequest: FetchSubscriptionMetadataRequest)
    : Future[ResponseContextSeqFetchSubscriptionMetadataResponse] = {
    Future.successful(
      ResponseContextSeqFetchSubscriptionMetadataResponse()
    )
  }

  override def communicationPreferenceUpdateReferenceBlocked(
      xAPIKEY: String,
      updateReferenceBlockedRequest: UpdateReferenceBlockedRequest)
    : Future[ResponseContextUpdateReferenceBlockedResponse] = {
    Future.successful(
      ResponseContextUpdateReferenceBlockedResponse(
        result = UpdateReferenceBlockedResponse(
          correlationId = UUID.randomUUID().toString,
          serviceCode = "RequestAccepted",
          serviceMessage = "Your request is accepted"
        )
      )
    )
  }

  override def communicationPreferenceUpdateSubscriptions(
      xAPIKEY: String,
      updateSubscriptionsRequest: UpdateSubscriptionsRequest): Future[ResponseContextSubscriptionOperationResponse] = {
    Future.successful(
      ResponseContextSubscriptionOperationResponse(
        result = SubscriptionOperationResponse(
          correlationId = UUID.randomUUID().toString,
          serviceCode = "RequestAccepted",
          serviceMessage = "Your request is accepted"
        )
      ))
  }
}
