package com.agoda.winterfell.mocks

import com.agoda.cusco.centralized.modules.legacyapi.v2.client.model._
import com.agoda.cusco.communication.client.model._
import com.agoda.dal.concurrent.MeasuredFuture
import com.agoda.packages.cartapi.api.contract.models.CartResult
import com.agoda.winterfell.Constants
import com.agoda.winterfell.common.SocialNetworkType.SocialNetworkType
import com.agoda.winterfell.input._
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.models.entities.CustomerModel
import com.agoda.winterfell.output._
import com.agoda.winterfell.services._
import com.agoda.winterfell.services.cart.CartApiService
import com.agoda.winterfell.services.cusco.{CuscoEmailService, CuscoResponse}
import com.agoda.winterfell.unified.CuscoSendOTPResult
import com.agoda.winterfell.unified.nextCloud.NextCloudService
import com.agoda.winterfell.utils.CapiConfig
import com.fasterxml.jackson.databind.node.ObjectNode
import org.joda.time.DateTime

import java.nio.file.Path
import scala.concurrent.{ExecutionContext, Future}

object MobileNumberMapping {
  val responseSuccessDeliverySuccess = "952747240"
  val responseSuccessDeliveryFailure = "952747241"
  val responseFailure = "933333333"
  val wrongFormatNumber = "952747!!240"
  val invalidNumberParseError = "95274"
}

object EmailMapping {
  val responseSuccessDeliverySuccessEmail = "<EMAIL>"
  val responseSuccessDeliveryFailEmail = "<EMAIL>"

}

case class ExternalEmailServiceMockImpl() extends ExternalEmailService {
  override def cuscoEmailService: CuscoEmailService = CuscoEmailServiceMockImpl()
  override def sendVerificationEmail(
      customer: CustomerModel,
      langId: Int,
      token: String,
      checksum: String,
      socialNetworkTypeString: Option[String])(implicit context: CapiRequestContextData): Boolean = true
}

case class CuscoEmailServiceMockImpl() extends CuscoEmailService {
  override def sendEmailVerification(customer: CustomerModel, langId: Int, token: String, checksum: String)(
      implicit context: CapiRequestContextData): Boolean = true

  override def sendEmailVerificationForWeChat(
      member: CustomerModel,
      socialAppUserInfo: SocialAppUserInfo,
      langId: Int,
      token: String,
      checksum: String): Boolean = true

  override def sendFraudulentPropertyEmails(users: Vector[BlockUserInRoleByPropertyResult], propertyId: Int)(
      implicit context: CapiRequestContextData): Boolean = true

  override def sendEmailWithResponse(
      email: String,
      fullName: String,
      returnUrl: String,
      token: String,
      languageId: Int,
      options: Option[String],
      templateId: Int,
      preference: Option[String]): CuscoResponse = CuscoEmailService.ImmediateSuccessResponse
  override def sendWithResponse(request: LegacyDeliveryApiV1SendEmailByTemplateRequest, templateName: Option[String])(
      implicit context: CapiRequestContextData): CuscoResponse = CuscoEmailService.ImmediateSuccessResponse

  override def sendOtpEmail(
      otpRequest: EmailSendOtp,
      capiProvidedData: Option[Map[String, String]] = None,
      isVerified: Boolean)(implicit context: CapiRequestContextData): CuscoResponse = {
    import EmailMapping._
    if (otpRequest.emailTo.equalsIgnoreCase(responseSuccessDeliverySuccessEmail))
      CuscoResponse(Constants.BlankId.toString, CuscoEmailService.QueueStatusSuccess)
    else
      CuscoResponse(Constants.BlankId.toString, CuscoEmailService.MessageImmediateCall)
  }

  override def sendOtpWhatsapp(
      otpRequest: WhatsappSendOtp,
      capiProvidedData: Option[Map[String, String]] = None,
      isVerified: Boolean)(implicit context: CapiRequestContextData): CuscoResponse = {
    // Mock WhatsApp OTP sending - using phone number for logic similar to SMS
    val phoneNumber = s"${otpRequest.countryCode}${otpRequest.nationalNumber}"
    import MobileNumberMapping._
    if (phoneNumber == responseSuccessDeliveryFailure || phoneNumber == responseFailure)
      CuscoResponse(Constants.BlankId.toString, CuscoEmailService.MessageImmediateCall)
    else
      CuscoResponse(Constants.BlankId.toString, CuscoEmailService.QueueStatusSuccess)
  }

  override def sendMulticastRequest(request: MessagingSendMulticastRequest, isVerified: Boolean)(
      implicit context: CapiRequestContextData): MeasuredFuture[ResponseContextMessagingSendMulticastResponse] =
    MeasuredFuture.successful(
      ResponseContextMessagingSendMulticastResponse(
        MessagingSendMulticastResponse(
          "",
          "",
          "",
          MessagingSendMulticastChannelResponse(
            email = Some(MessagingSendMulticastChannelResponseDetail("", "", "", None, None)),
            push = Some(MessagingSendMulticastChannelResponseDetail("", "", "", None, None)),
            sms = Some(MessagingSendMulticastChannelResponseDetail("", "", "", None, None)),
            whatsApp = Some(MessagingSendMulticastChannelResponseDetail("", "", "", None, None))
          )
        )))

  override def sendPhoneOTP(sendOtpInput: OTPRequest, phoneNumber: String, otp: String)(
      implicit context: CapiRequestContextData): CuscoSendOTPResult = {
    import MobileNumberMapping._
    CuscoSendOTPResult(!List(responseSuccessDeliveryFailure, responseFailure).contains(phoneNumber), None)
  }
}

case class NextCloudServiceMockImpl() extends NextCloudService {

  override def uploadFileAndCreateShareLink(filePath: Path, fileName: String, sharePassword: String)(
      implicit ec: ExecutionContext): Future[Option[String]] = Future.successful(Some("mock-link"))

  override def uploadBytesAndCreateShareLink(data: Array[Byte], fileName: String, sharePassword: String)(
      implicit ec: ExecutionContext): Future[Option[String]] = Future.successful(Some("mock-link"))
}

class MockGiftCardServiceImpl extends GiftCardService {
  override def getMemberBalance(
      memberId: Int,
      currencyCode: String,
      balanceTypes: Vector[Int],
      whiteLabelToken: Option[String] = Some(Constants.agodaWhiteLabelToken),
      whiteLabelId: Int)(implicit capiCtx: CapiRequestContextData): Future[GiftCardBalanceInfo] = null
  override def getGiftCards(
      memberId: Int,
      statusFilter: Vector[Int],
      pageSize: Option[Int],
      pageIndex: Option[Int],
      fromDate: Option[DateTime],
      toDate: Option[DateTime],
      balanceType: Option[Int],
      sortingList: Vector[RetrieveSorting],
      whiteLabelId: Int): Future[Vector[CardItem]] = null
  override def getGiftCardListResult(
      memberId: Int,
      currencyCode: String,
      statusFilter: Vector[Int],
      pageSize: Option[Int],
      pageIndex: Option[Int],
      fromDate: Option[DateTime],
      toDate: Option[DateTime],
      balanceType: Option[Int],
      sortingList: Vector[RetrieveSorting],
      whiteLabelId: Int): Future[GiftCardListResult] = null
  override def getGiftCardCampaigns(
      campaignIds: Vector[Long],
      whiteLabelId: Int): Future[Vector[GiftCardCampaignItem]] = null
  override def issueGiftCardForCampaign(
      memberId: Int,
      campaignId: Option[Long],
      campaignToken: Option[String],
      currencyCode: Option[String],
      whiteLabelId: Int): Future[IssueCardForCampaignResponse] = null
  override def enrollVipCampaign(
      memberId: Int,
      campaignId: Option[Long],
      campaignToken: Option[String],
      whiteLabelId: Int): Future[EnrollVipCampaignResponse] = null
  override def enrollEmployeeVip(memberId: Int): Future[EnrollVipCampaignResponse] =
    Future.successful(
      new EnrollVipCampaignResponse(status = 200, eligibleVipCampaigns = null, loyaltyProfile = null, errorList = null))
  override def getMemberLoyaltyProfile(memberId: Int, currencyCode: String, whiteLabelId: Int)(
      implicit capiRequestContextData: CapiRequestContextData): Future[Option[LoyaltyProfile]] = null
  override def getMemberLoyaltyProfileWrapped(
      memberId: Int,
      currencyCode: String,
      includeCampaign: Boolean,
      whiteLabelId: Int)(implicit capiRequestContextData: CapiRequestContextData): Future[Option[LoyaltyProfile]] = null
  override def getEligibleVipCampaigns(memberId: Int, whiteLabelId: Int): Future[Vector[ObjectNode]] = null
  override def getEligibleVipCampaignsWrapped(memberId: Int, whiteLabelId: Int)(
      implicit capiCtx: CapiRequestContextData): Future[Vector[ObjectNode]] = null
  override def setExternalWalletBalance(memberId: Int, balance: Double, whiteLabelToken: String, whiteLabelId: Int)(
      implicit capiCtx: CapiRequestContextData): Future[ExternalWalletSetResult] =
    Future.successful(new ExternalWalletSetResult(success = true, message = "success"))
  override def getTravelStatistics(
      memberId: Int,
      languageId: Int,
      whiteLabelId: Int): Future[Option[TravelStatistics]] = null
  override def getCashbackBalance(
      memberId: Int,
      currencyCode: String,
      userOrigin: String,
      balanceTypes: Vector[Int],
      whiteLabelId: Int,
      isForceMDB: Option[Boolean]): Future[GCGetCashbackBalanceResponse] =
    Future.successful(GCGetCashbackBalanceResponse())
}

case class WeChatClientServiceMockImpl() extends WeChatClientService {

  private def getWeChatAuthUserInfo(code: String) = code match {
    case "invalid_token" =>
      AuthUserInfo(
        accessToken = None,
        expiresIn = None,
        refreshToken = None,
        openId = None,
        scope = None,
        unionId = None,
        errorCode = Some(1),
        errorMessage = Some("error message"),
        timestamp = Some(DateTime.now()).map(_.toString(CapiConfig.WechatAuthInfoTimestampFormat))
      )
    case _ =>
      AuthUserInfo(
        accessToken = Some(s"access_token_$code"),
        expiresIn = Some(7200),
        refreshToken = Some(s"refresh_token_$code"),
        openId = Some(s"openid_$code"),
        scope = None,
        unionId = Some(s"unionid_$code"),
        errorCode = None,
        errorMessage = None,
        timestamp = Some(DateTime.now()).map(_.toString(CapiConfig.WechatAuthInfoTimestampFormat))
      )
  }

  private def getWeChatOpenPlatformUserInfo(openId: String) = {
    val code = openId.replaceAll("openid_", "")
    OpenPlatformUserInfo(
      openId = Some(openId),
      nickname = Some(s"nickname_$code"),
      sex = Some(1),
      language = Some("EN_US"),
      province = Some(s"province_$code"),
      city = Some("Shanghai"),
      country = Some("CH"),
      headImageUrl = Some(s"headimgurl_$code"),
      privilege = None,
      unionId = Some(s"unionid_$code"),
      errorCode = None,
      errorMessage = None
    )
  }

  override def getWeChatOpenPlatformUserInfo(
      socialNetworkType: SocialNetworkType,
      accessToken: String,
      openId: String): Option[OpenPlatformUserInfo] = {
    Some(getWeChatOpenPlatformUserInfo(openId))
  }

  override def getWeChatAuthUserInfo(socialNetworkType: SocialNetworkType, authCode: String): Option[AuthUserInfo] = {
    Some(getWeChatAuthUserInfo(authCode))
  }
}

class MockCartApiServiceImpl extends CartApiService {
  override def getHotelCartItems(memberId: Int, capiCtx: CapiRequestContextData)(
      implicit ec: ExecutionContext): Future[Seq[CartResult]] = {
    Future.successful(Seq.empty)
  }

  override def removeHotelsFromCart(memberId: Int, capiCtx: CapiRequestContextData, hotelIds: List[Int])(
      implicit ec: ExecutionContext): Future[Boolean] = {
    Future.successful(true)
  }
}
