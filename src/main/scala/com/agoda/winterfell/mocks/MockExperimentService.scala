package com.agoda.winterfell.mocks

import com.agoda.winterfell.input.ExperimentContext
import com.agoda.winterfell.models.CapiRequestContextData
import com.agoda.winterfell.services.{ExperimentNames, ExperimentServiceImpl}

class MockExperimentService extends ExperimentServiceImpl {
  override def determineVariant(experimentName: String, experimentContext: Option[ExperimentContext])(
      implicit capiCtx: CapiRequestContextData): Char = {
    experimentName match {
      case _ => 'A'
    }
  }
  override def isBVariant(experimentName: String, experimentContext: Option[ExperimentContext])(
      implicit capiCtx: CapiRequestContextData): Boolean = {
    experimentName match {
      case ExperimentNames.EnablePieOTP => true
      case _ => false
    }
  }
}
