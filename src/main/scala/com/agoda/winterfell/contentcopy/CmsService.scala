package com.agoda.winterfell.contentcopy

import com.agoda.experiments.{ExperimentManager, RequestContext}
import com.agoda.winterfell.AppLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.language.implicitConversions
import scala.util.{Failure, Success, Try}

sealed trait Variant {
  def toChar: Char = toString.toCharArray.head
}

object Variant {

  def apply(v: String): Try[Variant] = v match {
    case A(_) => Success(A)
    case B(_) => Success(B)
    case _ => Failure(IllegalVariantException(s"Value $v is not a valid variant for an experiment"))
  }

  def apply(v: Char): Try[Variant] = v match {
    case A(_) => Success(A)
    case B(_) => Success(B)
    case _ => Failure(IllegalVariantException(s"Value $v is not a valid variant for an experiment"))
  }

  implicit def toOption(tv: Try[Variant]): Option[Variant] = tv.toOption

  implicit class VariantFilter(lhs: Map[String, Try[Variant]]) {
    def valid: Map[String, Variant] = lhs collect { case (id, Success(v)) => id -> v }
  }
}

case object A extends Variant {
  override def toString = "A"
  def unapply(a: String): Option[Variant] = if (a == toString) Some(A) else None
  def unapply(a: Char): Option[Variant] = if (a == toChar) Some(A) else None
}

case object B extends Variant {
  override def toString = "B"
  def unapply(b: String): Option[Variant] = if (b == toString) Some(B) else None
  def unapply(b: Char): Option[Variant] = if (b == toChar) Some(B) else None
}

case class IllegalVariantException(message: String) extends Exception

trait CmsService {
  val experimentManager: ExperimentManagerAdapter
  val platform: String
  val storeProcedure: String
  def toReviewScoreText(rating: Double, languageId: Long): String
  def toCmsText(cmsId: Long, languageId: Long): Option[String]
  def toCmsTextFuture(cmsId: Long, languageId: Long)(implicit ec: ExecutionContext): Future[Option[String]] = {
    Future.successful(toCmsText(cmsId, languageId))
  }
  //To make sure store proc contains 3 exact variables needed by the library.
  lazy val isValidStoreProcedure: Boolean =
    storeProcedure.contains("@language_id") &&
      storeProcedure.contains("@cms_id") &&
      storeProcedure.contains("@cms_version")
}

//Can't use the one in commons as it is incompatible with our version of experiment manager.
//Well actually now we can but it's easier to remove the dep
class CopyNoOpExperimentManagerAdapter extends ExperimentManagerAdapter {
  override val userId: String = null
  override val forceVariant: Option[Variant] = None
  override val cid: String = ""
  override val origin: String = ""
  override val deviceTypeId: String = ""
  override val forceExperiments: Map[String, Variant] = Map.empty
  override val experimentManager: ExperimentManager = null

  override def inferCmsVariantForLanguage(cmsId: Long, languageId: Int): Variant = Variant.apply('A').get
  override def runExperiment[A](expName: String)(variantA: => A, variantB: => A): A = variantA
  override def isBVariant(expName: String): Boolean = false
  override def isBVariant(expName: String, entryCriteria: => Boolean): Boolean = false

  override def sendObservations(): Unit = {}
}

trait ExperimentManagerAdapter extends AppLogger {
  val userId: String
  val forceVariant: Option[Variant]
  val cid: String
  val origin: String
  val deviceTypeId: String
  val forceExperiments: Map[String, Variant]

  val experimentManager: ExperimentManager
  def sendObservations(): Unit
  def inferCmsVariantForLanguage(cmsId: Long, languageId: Int): Variant =
    inferCmsVariantForLanguage(userId, cmsId, languageId)
  def runExperiment[A](expName: String)(variantA: => A, variantB: => A): A = {
    runExperimentForUser(expName)(variantA, variantB)
  }
  def isBVariant(expName: String): Boolean = runExperimentForUser(expName)(false, true)
  def isBVariant(expName: String, entryCriteria: => Boolean): Boolean =
    if (entryCriteria) runExperimentForUser(expName)(false, true) else false

  private def runExperimentForUser[A](expName: String)(variantA: => A, variantB: => A): A = {
    val forcedVariant = determineForcedVariant(expName)
    if (forcedVariant.isDefined) if (forcedVariant.get.toChar == 'B') variantB else variantA
    else
      experimentManager.determineVariant(
        expName,
        buildRequestContext(userId, "", forceExperiments, deviceTypeId, cid, origin)) match {
        case 'A' => variantA
        case 'B' => variantB
        case _ => variantA
      }
  }

  private def inferCmsVariantForLanguage(userId: String, cmsId: Long, languageId: Int): Variant = {
    if (forceVariant.isDefined) if (forceVariant.get.toChar == 'B') B else A
    else
      experimentManager.determineVariantForCmsItem(
        cmsId.toInt,
        buildRequestContext(userId, languageId.toString, forceExperiments, deviceTypeId, cid, origin)) match {
        case 'A' => A
        case 'B' => B
        case _ => A
      }
  }

  private def determineForcedVariant(expName: String): Option[Variant] =
    forceExperiments.find { case (name, _) => expName == name }.map(_._2).orElse(forceVariant)

  private def buildRequestContext(
      userId: String,
      languageId: String,
      forceExperiments: Map[String, Variant],
      deviceTypeId: String,
      cid: String,
      origin: String): RequestContext =
    RequestContext(
      languageId = languageId,
      deviceTypeId = deviceTypeId,
      trafficGroup = "",
      cId = cid,
      aId = "",
      serverName = "",
      origin = origin,
      userId = userId,
      overridenAllocationVariantMap = buildOverrideAllocateVariantMap(forceExperiments)
    )

  private def buildOverrideAllocateVariantMap(forceExperiments: Map[String, Variant]): Option[Map[String, Char]] = {
    if (forceExperiments.isEmpty) None
    else Some(forceExperiments.mapValues(_.toChar))
  }
}
