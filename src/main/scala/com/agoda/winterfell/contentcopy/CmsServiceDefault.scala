package com.agoda.winterfell.contentcopy

import java.util.concurrent.ConcurrentHashMap

import com.agoda.winterfell.utils.{DBGroups, DBPlugin, QueriesHelper}
import com.github.blemale.scaffeine.{Cache, Scaffeine}

import scala.concurrent.ExecutionContext

class CmsServiceDefault(
    val experimentManager: ExperimentManagerAdapter,
    val cmsOrigin: Boolean,
    val platform: String,
    val storeProcedure: String)(implicit val ec: ExecutionContext, val db: DBPlugin)
    extends CmsService {

  require(isValidStoreProcedure, "Store procedure must contain @language_id, @cms_id and @cms_version")

  private val toCmsTextCache = new ConcurrentHashMap[(Long, Long), Option[String]]()

  override def toReviewScoreText(rating: Double, languageId: Long): String = {
    val cmsValueId = toReviewScoreCmsId(rating)
    val variant = experimentManager.inferCmsVariantForLanguage(cmsValueId, languageId.toInt)
    getCmsValue(cmsValueId, languageId, variant.toString, cmsOrigin).getOrElse("")
  }

  override def toCmsText(cmsId: Long, languageId: Long): Option[String] =
    Option(toCmsTextCache.get((cmsId, languageId))).getOrElse({
      val variant = experimentManager.inferCmsVariantForLanguage(cmsId, languageId.toInt)
      val output = getCmsValue(cmsId.toInt, languageId, variant.toString, cmsOrigin)
      toCmsTextCache.put((cmsId, languageId), output)
      output
    })

  private def toReviewScoreCmsId(rating: Double) = rating match {
    case _ if rating >= 9.1 => 20725
    case _ if rating >= 8.1 => 20490
    case _ if rating >= 7.6 => 20491
    case _ if rating >= 7 => 20492
    case _ if rating >= 6.1 => 20726
    case _ if rating >= 5.1 => 20727
    case _ => 20496
  }

  private def getCmsValue(cmsId: Int, languageId: Long, cmsVersion: String, origin: Boolean): Option[String] = {
    val data = CmsServiceDefault.getCmsValueCache.get(
      (cmsId, languageId, cmsVersion), { key: (Int, Long, String) =>
        {
          QueriesHelper
            .result(DBGroups.ReadGroup)
            .q(storeProcedure, key._2.toInt, key._1, key._3)
            .flatMap(r => r.strOption("cms_data"))
        }
      }
    )
    data.filter(_ => origin).map(t => s"$t ($cmsId) ($platform)").orElse(data)
  }
}

object CmsServiceDefault {
  import scala.concurrent.duration._
  import scala.language.postfixOps

  private val getCmsValueCache: Cache[(Int, Long, String), Option[String]] =
    Scaffeine()
      .maximumSize(300000)
      .expireAfterWrite(3 hour)
      .build[(Int, Long, String), Option[String]]()
}
