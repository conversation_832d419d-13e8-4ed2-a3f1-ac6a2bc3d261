{"target": {"path": "deployment/customerapi/container/main"}, "accept_invalid_certificates": false, "feature": {"network": {"incoming": {"mode": "steal", "port_mapping": [[8080, 80]], "ignore_localhost": false}, "outgoing": true}, "fs": "read", "env": true}, "agent": {"check_out_of_pods": false, "log_level": "mirrord=debug,warn"}, "sip_binaries": "bash;python;java", "skip_build_tools": true, "skip_processes": "bash;node;sbt;xsbt", "kubeconfig": "~/.devstack/KUBECONFIG"}