
include:
  - local: .gitlab/base_templates.yml

stages:
  - test

Kafka Compatibility Test:
  stage: test
  extends: .devstack_test_replicator_ci_base
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    - when: always
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: Set Sbt Version
    - pipeline: $PARENT_PIPELINE_ID
      job: Build and push Docker
  variables:
    SBT_HEAP_SIZE: "-Xms1g -Xmx4g"
    TEST_CONF: "test.conf"
    CONSUL_CONF: "docker-consul.conf"
    DEVSTACK_WAIT: "true"
    DEVSTACK_ENV: dev
    DEVSTACK_DEBUG: "false"
    DEVSTACK_CONFIG_FILE: "$CI_PROJECT_DIR/devstack-replicator-test.yaml"
    DEVSTACK_OVERRIDE: |
      environments:
        dev:
          spec:
            artifact:
              image:
                tag: ${CUSTOMER_API_VERSION}
    JAVA_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"
  script:
    - free -h
    - sbt "set Test / parallelExecution := false" "coverage; it:testOnly *replicator.* ; set coverageOutputHTML := false; coverageReport"
    - free -h
  artifacts:
    when: always
    paths:
      - "target/test-reports/*.xml"
      - "target/scoverage-report/scoverage.xml"
    reports:
      junit:
        - ./target/test-reports/*.xml