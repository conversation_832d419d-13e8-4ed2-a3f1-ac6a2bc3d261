include:
  - project: "devops/ci-templates"
    ref: master
    file:
        - '/templates/Docker/Dind.gitlab-ci.yml'
        - '/templates/Docker/Kaniko.gitlab-ci.yml'
        - '/templates/Scala/Sbt.gitlab-ci.yml'
        - '/templates/Scala/Sbt_Docker.gitlab-ci.yml'
        - '/templates/Scala/Sbt_Dind_Localhost.gitlab-ci.yml'
        - '/templates/Scala/Sbt_Version_Suffix.gitlab-ci.yml'
        - '/templates/Bash/Bash.gitlab-ci.yml'
        - '/templates/CSpider/Upload.gitlab-ci.yml'
        - '/templates/CSpider/Deploy.gitlab-ci.yml'
        - '/templates/Security/Provenance.yml'
        - '/templates/PrivateCloud/Deployment.gitlab-ci.yml'
        - '/templates/Devstack/Devstack.gitlab-ci.yml'
        - '/templates/Scala/Sbt_Devstack.gitlab-ci.yml'
        - '/templates/Security/SAST.gitlab-ci.yml'
        - '/templates/Security/SecretDetection.gitlab-ci.yml'
  - project: "data-protection/capi-gitlab-templates"
    ref: master
    file:
      - '/templates/Customer_Docker_Tags.gitlab-ci.yml'
      - '/templates/Slack_Notify.gitlab-ci.yml'
      - '/templates/PII_review_label_check.gitlab-ci.yml'
  - project: "full-stack/templates/code-generation-client"
    ref: "${DOTNET_VERSION}"
    file:
      - '/dotnet-standard2.0/gen-client.yml'
  - project: 'bpf-velocity/ci-templates'
    ref: master
    file:
      - 'templates/bpf-release-note.gitlab-ci.yml'


.sbt_base:
  extends: .sbt
  variables:
    CACHE_KEY: "${CI_PROJECT_NAME}-sbt-cache"

.test_base:
  extends: .sbt_local_docker
  tags:
    - m1.xlarge
  variables:
    TEST_CONF: ""
    CONSUL_CONF: "docker-consul.conf"
    CACHE_KEY: "${CI_PROJECT_NAME}-sbt-cache"
    SBT_HEAP_SIZE: "-Xms1g -Xmx4g"
    SBT_DEFAULT_OPTS: "-Dsbt.global.base=sbt-cache/sbtboot
                        -Dsbt.boot.directory=sbt-cache/boot
                        -Dsbt.ivy.home=sbt-cache/ivy
                        -Dsun.net.http.allowRestrictedHeaders=true
                        -XX:ReservedCodeCacheSize=512m
                        -Dhttp.proxyHost=hk-agcprx-2000.corpdmz.agoda.local
                        -Dhttp.proxyPort=8080
                        -Dhttps.proxyHost=hk-agcprx-2000.corpdmz.agoda.local
                        -Dhttps.proxyPort=8080
                        -Dhttp.nonProxyHosts=localhost|127.0.0.1|repo-hkg.agodadev.io|repo.hkg.sdlc.agoda.local|10.*|172.*|192.168.*|*.agoda.local|*.agodadev.io|docker|*.agoda.is|*.qa.agoda.is"
    START_CONTAINERS: "consul mssqlmdb mssqlmdc couchbase kafka mssqlhdb"
    DOCKER_COMPOSE_FILE: "docker-compose.yml"
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: Set Sbt Version
    - pipeline: $PARENT_PIPELINE_ID
      job: Build Sbt
    - pipeline: $PARENT_PIPELINE_ID
      job: Build and push Docker
  before_script:
    - !reference [.sbt_local_docker, before_script]
    - docker-compose -f $DOCKER_COMPOSE_FILE up -d $START_CONTAINERS
    - docker-compose -f $DOCKER_COMPOSE_FILE up mssql-tools
    - docker-compose -f $DOCKER_COMPOSE_FILE up syncdb
    - export JAVA_OPTS="$SBT_HEAP_SIZE -Dtest.config=$TEST_CONF -Dconsul.config=$CONSUL_CONF $SBT_DEFAULT_OPTS"
  artifacts:
    when: always
    reports:
      junit:
        - ./**/target/test-reports/*.xml
  retry: 2

# Call the template project CI
.generate-capi-client:
  stage: publish_dotnet_client
  extends: .generate-swagger-client
  needs: []
  variables:
    PARENT_CI_PIPELINE_SOURCE: $CI_PIPELINE_SOURCE
    VERSION: "2.1.${CI_PIPELINE_IID}-local"
    SPEC_PATH: "${CI_PROJECT_DIR}/src/main/resources/openapi.yml"
    APP_NAME: "Agoda.CustomerApi.Client.V2"
    SHOULD_PUSH_NUGET: "false"
    USE_OPENAPI_V3: "true"
    ARTIFACTS_PATH: "${CI_PROJECT_DIR}/src/output"
    VARIABLE_KEY: "DOTNET_CURRENT_VERSION"
    CURRENT_VERSION: "${DOTNET_CURRENT_VERSION}"

.devstack_test_base:
  extends: .sbt_devstack
  tags:
    - m1.xlarge
  variables:
    DEVSTACK_TTL: 120m
    DEVSTACK_OUTPUT_FILE: "$CI_PROJECT_DIR/devstack_output.json"
    DEVSTACK_WAIT_TIMEOUT: "600"
    ENV_FILE: devstack.env
    CACHE_KEY: "${CI_PROJECT_NAME}-sbt-cache"
    TEST_CONF: ""
    CONSUL_CONF: "docker-consul.conf"
    SBT_HEAP_SIZE: "-Xms2g -Xmx6g"
    SBT_DEFAULT_OPTS: "-Dsbt.global.base=sbt-cache/sbtboot
                        -Dsbt.boot.directory=sbt-cache/boot
                        -Dsbt.ivy.home=sbt-cache/ivy
                        -Dsun.net.http.allowRestrictedHeaders=true
                        -XX:ReservedCodeCacheSize=512m
                        -XX:+UseG1GC 
                        -XX:MaxGCPauseMillis=200 
                        -XX:+UseStringDeduplication
                        -Dhttp.proxyHost=hk-agcprx-2000.corpdmz.agoda.local
                        -Dhttp.proxyPort=8080
                        -Dhttps.proxyHost=hk-agcprx-2000.corpdmz.agoda.local
                        -Dhttps.proxyPort=8080
                        -Dhttp.nonProxyHosts=localhost|127.0.0.1|repo-hkg.agodadev.io|repo.hkg.sdlc.agoda.local|10.*|172.*|192.168.*|*.agoda.local|*.agodadev.io|docker|*.agoda.is|*.qa.agoda.is"
  retry: 0
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: Set Sbt Version
    - pipeline: $PARENT_PIPELINE_ID
      job: Build Sbt
  artifacts:
    when: always
    paths:
      - ./**/coverage-report/**/*
      - ./**/scoverage-report/**/*
      - ./**/test-reports/**/*
      - ./logs/*
    reports:
      junit:
        - ./**/target/test-reports/*.xml
      coverage_report:
        coverage_format: cobertura
        path: ./**/coverage-report/cobertura.xml
  before_script:
    - !reference [ .sbt_devstack, before_script ]
    - |
        . $CI_PROJECT_DIR/devstack/export_services.sh
    - kubectl --kubeconfig <(echo $KUBECONFIG64 | base64 -d) get services
    - . $CI_PROJECT_DIR/devstack/forward_ports.sh
    - |
      sbt applySqlScripts
    - export JAVA_OPTS="$SBT_HEAP_SIZE -Dtest.config=$TEST_CONF -Dconsul.config=$CONSUL_CONF -DDOCKER_MDB_HOST=${MDB_HOST}:${MDB_PORT} -DDOCKER_MDC_HOST=${MDC_HOST}:${MDC_PORT} -DDOCKER_HDB_HOST=${HDB_HOST}:${HDB_PORT} -DCOUCHBASE_HOST=${COUCHBASE_8091_HOST} -DCOUCHBASE_8091_PORT=${COUCHBASE_8091_PORT} -DCOUCHBASE_11210_PORT=${COUCHBASE_11210_PORT} $SBT_DEFAULT_OPTS"
    - env

.devstack_test_ci_base:
  extends: .sbt_devstack
  tags:
    - m1.xlarge
  variables:
    DEVSTACK_TTL: 120m
    DEVSTACK_CONFIG_FILE: "$CI_PROJECT_DIR/devstack-integration-test.yaml"
    DEVSTACK_OUTPUT_FILE: "$CI_PROJECT_DIR/devstack_output.json"
    DEVSTACK_BUILD: "true"
    DEVSTACK_WAIT_TIMEOUT: "600"
    ENV_FILE: devstack.env
    CACHE_KEY: "${CI_PROJECT_NAME}-sbt-cache"
    TEST_CONF: ""
    CONSUL_CONF: "docker-consul.conf"
    SBT_HEAP_SIZE: "-Xms2g -Xmx6g"
    SBT_DEFAULT_OPTS: "-Dsbt.global.base=sbt-cache/sbtboot
                        -Dsbt.boot.directory=sbt-cache/boot
                        -Dsbt.ivy.home=sbt-cache/ivy
                        -Dsun.net.http.allowRestrictedHeaders=true
                        -XX:ReservedCodeCacheSize=512m
                        -XX:+UseG1GC
                        -XX:MaxGCPauseMillis=200
                        -XX:+UseStringDeduplication
                        -Dhttp.proxyHost=hk-agcprx-2000.corpdmz.agoda.local
                        -Dhttp.proxyPort=8080
                        -Dhttps.proxyHost=hk-agcprx-2000.corpdmz.agoda.local
                        -Dhttps.proxyPort=8080
                        -Dhttp.nonProxyHosts=localhost|127.0.0.1|repo-hkg.agodadev.io|repo.hkg.sdlc.agoda.local|10.*|172.*|192.168.*|*.agoda.local|*.agodadev.io|docker|*.agoda.is|*.qa.agoda.is"
  retry: 0
  needs: []
  artifacts:
    when: always
    paths:
      - ./**/coverage-report/**/*
      - ./**/scoverage-report/**/*
      - ./**/test-reports/**/*
      - ./logs/*
    reports:
      junit:
        - ./**/target/test-reports/*.xml
      coverage_report:
        coverage_format: cobertura
        path: ./**/coverage-report/cobertura.xml
  before_script:
    - !reference [ .sbt_devstack, before_script ]
    - |
      . $CI_PROJECT_DIR/devstack/export_services.sh
    - kubectl --kubeconfig <(echo $KUBECONFIG64 | base64 -d) get services
    - . $CI_PROJECT_DIR/devstack/forward_ports.sh
    - |
      sbt applySqlScripts
    - export JAVA_OPTS="$SBT_HEAP_SIZE -Dtest.config=$TEST_CONF -Dconsul.config=$CONSUL_CONF -DDOCKER_MDB_HOST=${MDB_HOST}:${MDB_PORT} -DDOCKER_MDC_HOST=${MDC_HOST}:${MDC_PORT} -DDOCKER_HDB_HOST=${HDB_HOST}:${HDB_PORT} -DCOUCHBASE_HOST=${COUCHBASE_8091_HOST} -DCOUCHBASE_8091_PORT=${COUCHBASE_8091_PORT} -DCOUCHBASE_11210_PORT=${COUCHBASE_11210_PORT} $SBT_DEFAULT_OPTS"
    - env

.devstack_test_replicator_ci_base:
  extends: .devstack_test_ci_base
  variables:
    DEVSTACK_CONFIG_FILE: "$CI_PROJECT_DIR/devstack-replicator-test.yaml"


