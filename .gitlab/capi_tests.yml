include:
  - local: .gitlab/base_templates.yml

stages:
  - test

workflow:
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME == "develop"'
    - if: '$CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_REF_NAME == "develop"'



Unit-Tests:
  stage: test
  extends: .sbt_base
  tags:
    - m1.xlarge
  variables:
    SBT_HEAP_SIZE: "-Xms2g -Xmx6g"
    JAVA_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"
  script:
    - TEST_EXIT_CODE=0
    - sbt coverage test || TEST_EXIT_CODE=$?
    - echo "Checking for test report files..."
    - find . -name "*.xml" -path "*/test-reports/*" -exec ls -la {} \; || echo "No test report files found"
    - sbt coverageReport coverageAggregate || true
    - bash <(curl -s $CODECOV_BASH_URL)
    - exit $TEST_EXIT_CODE
  artifacts:
    when: always
    expire_in: 30 days
    paths:
      - ./**/scoverage-data/**
      - ./**/scoverage-report/**
      - ./**/target/test-reports/**
    reports:
      junit:
        - target/test-reports/*.xml
        - ./**/target/test-reports/*.xml

Test Docker:
  stage: test
  extends: .devstack_test_ci_base
  needs: []
  variables:
    SBT_HEAP_SIZE: "-Xms2g -Xmx6g"
    TEST_CONF: "docker-test.conf"
    CONSUL_CONF: "docker-consul.conf"
    DEVSTACK_WAIT: "true"
    DEVSTACK_ENV: dev
    DEVSTACK_DEBUG: "false"
    JAVA_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"
  script:
    - env
    - sbt 'set IntegrationTest / testOptions += Tests.Filter(t => !t.contains("replicator.")); coverage; it:test; coverageReport; coverageAggregate'
    - echo "Checking for test report files..."
    - find . -name "*.xml" -path "*/test-reports/*" -exec ls -la {} \; || echo "No test report files found"
    - bash <(curl -s $CODECOV_BASH_URL)
  artifacts:
    when: always
    expire_in: 30 days
    paths:
      - ./**/scoverage-data/**
      - ./**/scoverage-report/**
      - ./**/target/test-reports/**
    reports:
      junit:
        - target/test-reports/*.xml
        - ./**/target/test-reports/*.xml